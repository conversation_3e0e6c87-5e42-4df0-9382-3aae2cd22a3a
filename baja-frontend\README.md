# BAJA Event Organizer - Frontend

Frontend aplikasi untuk sistem manajemen event olahraga bela diri BAJA Event Organizer.

## Tech Stack

- **Next.js 14** - React framework dengan App Router
- **React 18** - UI library
- **TypeScript** - Programming language
- **Tailwind CSS** - CSS framework
- **Headless UI** - Unstyled UI components
- **Heroicons** - Icon library
- **React Hook Form** - Form management
- **Axios** - HTTP client
- **React Hot Toast** - Toast notifications
- **Zustand** - State management
- **Framer Motion** - Animation library

## Features

- 🎨 **Modern UI/UX** - Clean dan responsive design dengan Tailwind CSS
- 🔐 **Authentication** - Login/register dengan JWT
- 👥 **Role-based Access** - Different interfaces untuk Admin, Admin Event, dan Ketua Kontingen
- 📱 **Responsive Design** - Mobile-first approach
- 🚀 **Performance** - Optimized dengan Next.js 14
- 🎭 **Animations** - Smooth animations dengan <PERSON>amer Motion
- 📊 **Dashboard** - Interactive dashboard dengan charts
- 🖼️ **Gallery** - Image gallery dengan lightbox
- 📋 **Forms** - Advanced form handling dengan validation

## Installation

1. Clone repository
```bash
git clone <repository-url>
cd baja-frontend
```

2. Install dependencies
```bash
npm install
```

3. Setup environment variables
```bash
cp .env.local.example .env.local
```

Edit file `.env.local`:
```env
NEXT_PUBLIC_API_URL=http://localhost:5000/api/v1
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME=BAJA Event Organizer
```

4. Run development server
```bash
npm run dev
```

Aplikasi akan berjalan di `http://localhost:3000`

## Project Structure

```
baja-frontend/
├── app/                    # Next.js App Router
│   ├── auth/              # Authentication pages
│   ├── dashboard/         # Dashboard pages
│   ├── events/            # Event pages
│   ├── gallery/           # Gallery page
│   ├── packages/          # Packages page
│   ├── profile/           # Profile page
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── components/            # Reusable components
│   ├── auth/              # Auth-related components
│   ├── layout/            # Layout components
│   └── ui/                # UI components
├── contexts/              # React contexts
├── hooks/                 # Custom hooks
├── lib/                   # Utility libraries
├── services/              # API services
├── types/                 # TypeScript types
└── utils/                 # Utility functions
```

## Pages

### Public Pages
- **Home** (`/`) - Landing page dengan hero section, features, dan pricing
- **Events** (`/events`) - Daftar event yang tersedia
- **Gallery** (`/gallery`) - Gallery foto event
- **Packages** (`/packages`) - Paket berlangganan
- **Login** (`/auth/login`) - Halaman login
- **Register** (`/auth/register`) - Halaman registrasi

### Protected Pages
- **Dashboard** (`/dashboard`) - Dashboard utama (role-based)
- **Profile** (`/profile`) - Manajemen profil user

### Admin Pages
- **Users Management** - CRUD users
- **Events Management** - CRUD events
- **Packages Management** - CRUD packages
- **Gallery Management** - CRUD gallery

### Admin Event Pages
- **My Events** - Manajemen event sendiri
- **Athletes** - Manajemen atlet yang terdaftar
- **Teams** - Manajemen kontingen

### Ketua Kontingen Pages
- **My Team** - Manajemen kontingen sendiri
- **My Athletes** - Manajemen atlet kontingen
- **My Officials** - Manajemen official kontingen
- **Event Registration** - Pendaftaran ke event

## Components

### UI Components
- **Button** - Customizable button dengan variants
- **Input** - Form input dengan validation
- **Card** - Container component
- **Modal** - Modal dialog
- **Table** - Data table
- **Badge** - Status badge

### Layout Components
- **Navbar** - Navigation bar dengan auth state
- **Footer** - Footer dengan links
- **DashboardLayout** - Layout untuk dashboard pages
- **ProtectedRoute** - Route protection component

### Auth Components
- **AuthProvider** - Authentication context provider
- **ProtectedRoute** - Component untuk protected routes

## State Management

### Auth Context
Mengelola state authentication:
- User data
- Login/logout functions
- Loading states
- Token management

### API Integration
- Axios instance dengan interceptors
- Automatic token attachment
- Error handling
- Request/response transformation

## Styling

### Tailwind CSS
- Custom color palette
- Responsive design utilities
- Component classes
- Animation utilities

### Custom CSS
- Global styles
- Component-specific styles
- Animation keyframes
- Utility classes

## User Roles & Permissions

### Admin
- Full access ke semua fitur
- User management
- Event management
- Package management
- Gallery management

### Admin Event
- Manajemen event sendiri
- View registered athletes/teams
- Event analytics

### Ketua Kontingen
- Team management
- Athlete registration
- Official management
- Event registration

## API Integration

### Authentication
- Login/logout
- Profile management
- Token refresh

### Data Management
- CRUD operations
- File uploads
- Search & filtering
- Pagination

### Real-time Features
- Live updates
- Notifications
- Status changes

## Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - TypeScript type checking

## Deployment

### Vercel (Recommended)
```bash
npm run build
vercel --prod
```

### Manual Deployment
```bash
npm run build
npm start
```

## Environment Variables

```env
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:5000/api/v1
NEXT_PUBLIC_APP_URL=http://localhost:3000

# App Configuration
NEXT_PUBLIC_APP_NAME=BAJA Event Organizer
NEXT_PUBLIC_APP_VERSION=1.0.0

# Development
NODE_ENV=development
```

## Contributing

1. Fork repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## License

This project is licensed under the MIT License.
