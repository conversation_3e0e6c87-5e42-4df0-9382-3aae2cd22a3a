import api from './api';
import {
  ApiResponse,
  AdminUsersResponse,
  AdminEventsResponse,
  AdminPackagesResponse,
  AdminGalleryResponse,
  AdminUser,
  AdminEvent,
  AdminPackage,
  User
} from '@/types';

export const adminService = {
  // User Management
  async getUsers(params?: {
    page?: number;
    limit?: number;
    search?: string;
    role?: string;
  }): Promise<AdminUsersResponse> {
    const response = await api.get<ApiResponse<AdminUsersResponse>>('/admin/users', { params });
    
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    
    throw new Error(response.data.message || 'Failed to fetch users');
  },

  async getUserById(id: number): Promise<AdminUser> {
    const response = await api.get<ApiResponse<AdminUser>>(`/admin/users/${id}`);
    
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    
    throw new Error(response.data.message || 'Failed to fetch user');
  },

  async createUser(userData: Partial<AdminUser>): Promise<AdminUser> {
    const response = await api.post<ApiResponse<AdminUser>>('/admin/users', userData);
    
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    
    throw new Error(response.data.message || 'Failed to create user');
  },

  async updateUser(id: number, userData: Partial<AdminUser>): Promise<AdminUser> {
    const response = await api.put<ApiResponse<AdminUser>>(`/admin/users/${id}`, userData);
    
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    
    throw new Error(response.data.message || 'Failed to update user');
  },

  async deleteUser(id: number): Promise<void> {
    const response = await api.delete<ApiResponse<void>>(`/admin/users/${id}`);
    
    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to delete user');
    }
  },

  async updateUserStatus(id: number, status: number): Promise<void> {
    const response = await api.patch<ApiResponse<void>>(`/admin/users/${id}/status`, { status });
    
    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to update user status');
    }
  },

  // Event Management
  async getEvents(params?: {
    page?: number;
    limit?: number;
    search?: string;
    status?: string;
  }): Promise<AdminEventsResponse> {
    const response = await api.get<ApiResponse<AdminEventsResponse>>('/admin/dashboard-events', { params });

    if (response.data.success && response.data.data) {
      return response.data.data;
    }

    throw new Error(response.data.message || 'Failed to fetch events');
  },

  // Get all events (admin only)
  async getAllEvents(params?: {
    page?: number;
    limit?: number;
    search?: string;
    status?: string;
  }): Promise<AdminEventsResponse> {
    const response = await api.get<ApiResponse<AdminEventsResponse>>('/admin/events', { params });

    if (response.data.success && response.data.data) {
      return response.data.data;
    }

    throw new Error(response.data.message || 'Failed to fetch all events');
  },

  // Package Management
  async getPackages(params?: {
    page?: number;
    limit?: number;
    search?: string;
  }): Promise<AdminPackagesResponse> {
    const response = await api.get<ApiResponse<AdminPackagesResponse>>('/admin/packages', { params });
    
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    
    throw new Error(response.data.message || 'Failed to fetch packages');
  },

  // Gallery Management
  async getGallery(params?: {
    page?: number;
    limit?: number;
    search?: string;
  }): Promise<AdminGalleryResponse> {
    const response = await api.get<ApiResponse<AdminGalleryResponse>>('/admin/gallery', { params });

    if (response.data.success && response.data.data) {
      return response.data.data;
    }

    throw new Error(response.data.message || 'Failed to fetch gallery');
  },

  async deleteGalleryItem(id: string): Promise<void> {
    const response = await api.delete<ApiResponse<void>>(`/gallery/${id}`);

    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to delete gallery item');
    }
  },

  // Event Management
  async createEvent(eventData: Partial<AdminEvent>): Promise<AdminEvent> {
    const response = await api.post<ApiResponse<AdminEvent>>('/events', eventData);

    if (response.data.success && response.data.data) {
      return response.data.data;
    }

    throw new Error(response.data.message || 'Failed to create event');
  },

  async updateEvent(id: number, eventData: Partial<AdminEvent>): Promise<AdminEvent> {
    const response = await api.put<ApiResponse<AdminEvent>>(`/events/${id}`, eventData);

    if (response.data.success && response.data.data) {
      return response.data.data;
    }

    throw new Error(response.data.message || 'Failed to update event');
  },

  async deleteEvent(id: number): Promise<void> {
    const response = await api.delete<ApiResponse<void>>(`/events/${id}`);

    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to delete event');
    }
  },

  // Package Management
  async createPackage(packageData: Partial<AdminPackage>): Promise<AdminPackage> {
    const response = await api.post<ApiResponse<AdminPackage>>('/paket', packageData);

    if (response.data.success && response.data.data) {
      return response.data.data;
    }

    throw new Error(response.data.message || 'Failed to create package');
  },

  async updatePackage(id: number, packageData: Partial<AdminPackage>): Promise<AdminPackage> {
    const response = await api.put<ApiResponse<AdminPackage>>(`/paket/${id}`, packageData);

    if (response.data.success && response.data.data) {
      return response.data.data;
    }

    throw new Error(response.data.message || 'Failed to update package');
  },

  async deletePackage(id: number): Promise<void> {
    const response = await api.delete<ApiResponse<void>>(`/paket/${id}`);

    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to delete package');
    }
  },

  async setPopularPackage(id: number): Promise<AdminPackage> {
    const response = await api.patch<ApiResponse<AdminPackage>>(`/paket/${id}/set-popular`);

    if (response.data.success && response.data.data) {
      return response.data.data;
    }

    throw new Error(response.data.message || 'Failed to set package as popular');
  },

  async unsetPopularPackage(id: number): Promise<AdminPackage> {
    const response = await api.patch<ApiResponse<AdminPackage>>(`/paket/${id}/unset-popular`);

    if (response.data.success && response.data.data) {
      return response.data.data;
    }

    throw new Error(response.data.message || 'Failed to unset package as popular');
  },

  async setFeaturedPackage(id: number, featured_order?: number): Promise<AdminPackage> {
    const response = await api.patch<ApiResponse<AdminPackage>>(`/paket/${id}/set-featured`, {
      featured_order
    });

    if (response.data.success && response.data.data) {
      return response.data.data;
    }

    throw new Error(response.data.message || 'Failed to set package as featured');
  },

  async unsetFeaturedPackage(id: number): Promise<AdminPackage> {
    const response = await api.patch<ApiResponse<AdminPackage>>(`/paket/${id}/unset-featured`);

    if (response.data.success && response.data.data) {
      return response.data.data;
    }

    throw new Error(response.data.message || 'Failed to unset package as featured');
  },

  async getFeaturedPackages(): Promise<AdminPackage[]> {
    const response = await api.get<ApiResponse<AdminPackage[]>>('/paket/featured');

    if (response.data.success && response.data.data) {
      return response.data.data;
    }

    throw new Error(response.data.message || 'Failed to fetch featured packages');
  },

  async getPopularPackage(): Promise<AdminPackage | null> {
    const response = await api.get<ApiResponse<AdminPackage>>('/paket/popular');

    if (response.data.success) {
      return response.data.data || null;
    }

    throw new Error(response.data.message || 'Failed to fetch popular package');
  },

};
