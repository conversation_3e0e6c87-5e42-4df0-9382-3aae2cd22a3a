import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../config/database';
import { Paket as PaketInterface } from '../types';

interface PaketCreationAttributes extends Optional<PaketInterface, 'id' | 'created_at' | 'updated_at'> {}

class Paket extends Model<PaketInterface, PaketCreationAttributes> implements PaketInterface {
  public id!: number;
  public name!: string;
  public images?: string;
  public description?: string;
  public is_popular?: boolean;
  public is_featured?: boolean;
  public featured_order?: number;
  public readonly created_at!: Date;
  public readonly updated_at!: Date;
}

Paket.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    images: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    is_popular: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false,
    },
    is_featured: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false,
    },
    featured_order: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    tableName: 'paket',
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  }
);

export default Paket;
