import { Response } from 'express';
import { AuthRequest } from '../middleware/auth.middleware';
import Gallery from '../models/Gallery';
import { Op } from 'sequelize';
import { galleryUpload, deleteFromCloudinary, getOptimizedUrl, getThumbnailUrl, extractPublicId } from '../services/cloudinary.service';

export const upload = galleryUpload;

export const getAllGallery = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { page = 1, limit = 12, search = '', status = '' } = req.query;
    const offset = (Number(page) - 1) * Number(limit);

    const whereClause: any = {};
    
    if (search) {
      whereClause[Op.or] = [
        { description: { [Op.like]: `%${search}%` } }
      ];
    }

    const { count, rows } = await Gallery.findAndCountAll({
      where: whereClause,
      limit: Number(limit),
      offset,
      order: [['created_at', 'DESC']]
    });

    res.json({
      success: true,
      message: 'Gallery retrieved successfully',
      data: {
        gallery: rows,
        pagination: {
          total: count,
          page: Number(page),
          limit: Number(limit),
          totalPages: Math.ceil(count / Number(limit))
        }
      }
    });
  } catch (error) {
    console.error('Get all gallery error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const getGalleryById = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    
    const gallery = await Gallery.findByPk(id);

    if (!gallery) {
      res.status(404).json({
        success: false,
        message: 'Gallery item not found'
      });
      return;
    }

    res.json({
      success: true,
      message: 'Gallery item retrieved successfully',
      data: gallery
    });
  } catch (error) {
    console.error('Get gallery by id error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const createGallery = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const user = req.user;

    // Hanya admin yang bisa membuat gallery
    if (user.role !== 'admin') {
      res.status(403).json({
        success: false,
        message: 'Access denied. Only admin can create gallery items.'
      });
      return;
    }

    const { description } = req.body;

    if (!req.file) {
      res.status(400).json({
        success: false,
        message: 'Image file is required'
      });
      return;
    }

    const gallery = await Gallery.create({
      description,
      images: req.file.path // Cloudinary returns the full URL in req.file.path
    });

    res.status(201).json({
      success: true,
      message: 'Gallery item created successfully',
      data: {
        ...gallery.toJSON(),
        imageUrl: req.file.path,
        thumbnailUrl: getThumbnailUrl(req.file.filename)
      }
    });
  } catch (error) {
    console.error('Create gallery error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const updateGallery = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const user = req.user;

    // Hanya admin yang bisa update gallery
    if (user.role !== 'admin') {
      res.status(403).json({
        success: false,
        message: 'Access denied. Only admin can update gallery items.'
      });
      return;
    }

    const gallery = await Gallery.findByPk(id);
    if (!gallery) {
      res.status(404).json({
        success: false,
        message: 'Gallery item not found'
      });
      return;
    }

    const updateData: any = req.body;

    // If new image is uploaded, update the URL and delete old image from Cloudinary
    if (req.file) {
      updateData.images = req.file.path; // Cloudinary URL

      // Delete old image from Cloudinary
      if (gallery.images) {
        const publicId = extractPublicId(gallery.images);
        await deleteFromCloudinary(publicId);
      }
    }

    await gallery.update(updateData);

    res.json({
      success: true,
      message: 'Gallery item updated successfully',
      data: gallery
    });
  } catch (error) {
    console.error('Update gallery error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const deleteGallery = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const user = req.user;

    // Hanya admin yang bisa delete gallery
    if (user.role !== 'admin') {
      res.status(403).json({
        success: false,
        message: 'Access denied. Only admin can delete gallery items.'
      });
      return;
    }

    const gallery = await Gallery.findByPk(id);
    if (!gallery) {
      res.status(404).json({
        success: false,
        message: 'Gallery item not found'
      });
      return;
    }

    // Delete image from Cloudinary
    if (gallery.images) {
      const publicId = extractPublicId(gallery.images);
      await deleteFromCloudinary(publicId);
    }

    await gallery.destroy();

    res.json({
      success: true,
      message: 'Gallery item deleted successfully'
    });
  } catch (error) {
    console.error('Delete gallery error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const changeGalleryStatus = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const user = req.user;

    // Hanya admin yang bisa mengubah status gallery
    if (user.role !== 'admin') {
      res.status(403).json({
        success: false,
        message: 'Access denied. Only admin can change gallery status.'
      });
      return;
    }

    const gallery = await Gallery.findByPk(id);
    if (!gallery) {
      res.status(404).json({
        success: false,
        message: 'Gallery item not found'
      });
      return;
    }

    res.json({
      success: true,
      message: 'Gallery item status changed successfully',
      data: { id: gallery.id }
    });
  } catch (error) {
    console.error('Change gallery status error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const getActiveGallery = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const gallery = await Gallery.findAll({
      order: [['created_at', 'DESC']]
    });

    res.json({
      success: true,
      message: 'Active gallery retrieved successfully',
      data: gallery
    });
  } catch (error) {
    console.error('Get active gallery error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Admin-specific gallery management
export const getAllGalleryAdmin = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { page = 1, limit = 10, search = '' } = req.query;
    const offset = (Number(page) - 1) * Number(limit);

    const whereClause: any = {};

    if (search) {
      whereClause.description = { [Op.like]: `%${search}%` };
    }

    const { count, rows } = await Gallery.findAndCountAll({
      where: whereClause,
      limit: Number(limit),
      offset,
      order: [['created_at', 'DESC']]
    });

    res.json({
      success: true,
      message: 'Gallery retrieved successfully',
      data: {
        gallery: rows,
        pagination: {
          total: count,
          page: Number(page),
          limit: Number(limit),
          totalPages: Math.ceil(count / Number(limit))
        }
      }
    });
  } catch (error) {
    console.error('Get gallery admin error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};
