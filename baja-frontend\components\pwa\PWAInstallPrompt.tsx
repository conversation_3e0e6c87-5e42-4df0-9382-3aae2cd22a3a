'use client';

import React, { useState, useEffect } from 'react';
import Button from '@/components/ui/Button';
import { XMarkIcon, ArrowDownTrayIcon } from '@heroicons/react/24/outline';

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

const PWAInstallPrompt: React.FC = () => {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [showInstallPrompt, setShowInstallPrompt] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);
  const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    // Check if app is already installed
    const checkIfInstalled = () => {
      if (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) {
        setIsInstalled(true);
        return;
      }
      
      // Check for iOS Safari
      if ((window.navigator as any).standalone === true) {
        setIsInstalled(true);
        return;
      }
    };

    checkIfInstalled();

    // Listen for beforeinstallprompt event
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault();
      setDeferredPrompt(e as BeforeInstallPromptEvent);
      
      // Show install prompt after a delay if not dismissed before
      setTimeout(() => {
        const dismissed = localStorage.getItem('pwa-install-dismissed');
        const dismissedTime = localStorage.getItem('pwa-install-dismissed-time');
        const now = Date.now();

        // Show again after 24 hours if previously dismissed
        if (!dismissed || (dismissedTime && now - parseInt(dismissedTime) > 24 * 60 * 60 * 1000)) {
          if (!isInstalled) {
            setShowInstallPrompt(true);
            setShowModal(true);
          }
        }
      }, 5000); // Show after 5 seconds
    };

    // Listen for app installed event
    const handleAppInstalled = () => {
      setIsInstalled(true);
      setShowInstallPrompt(false);
      setDeferredPrompt(null);
      localStorage.removeItem('pwa-install-dismissed');
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  }, [isInstalled]);

  const handleInstallClick = async () => {
    if (!deferredPrompt) return;

    try {
      await deferredPrompt.prompt();
      const { outcome } = await deferredPrompt.userChoice;
      
      if (outcome === 'accepted') {
        console.log('User accepted the install prompt');
      } else {
        console.log('User dismissed the install prompt');
      }
      
      setDeferredPrompt(null);
      setShowInstallPrompt(false);
    } catch (error) {
      console.error('Error during installation:', error);
    }
  };

  const handleDismiss = () => {
    setShowInstallPrompt(false);
    setShowModal(false);
    localStorage.setItem('pwa-install-dismissed', 'true');
    localStorage.setItem('pwa-install-dismissed-time', Date.now().toString());
  };

  const handleRemindLater = () => {
    setShowInstallPrompt(false);
    setShowModal(false);
    // Don't set dismissed flag, so it will show again after page reload
  };

  const handleManualInstall = () => {
    // Show manual installation instructions
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    const isAndroid = /Android/.test(navigator.userAgent);
    
    let instructions = '';
    
    if (isIOS) {
      instructions = 'To install this app on iOS:\n1. Tap the Share button in Safari\n2. Scroll down and tap "Add to Home Screen"\n3. Tap "Add" to confirm';
    } else if (isAndroid) {
      instructions = 'To install this app on Android:\n1. Tap the menu button (⋮) in your browser\n2. Tap "Add to Home screen" or "Install app"\n3. Tap "Add" to confirm';
    } else {
      instructions = 'To install this app:\n1. Look for the install icon in your browser\'s address bar\n2. Click it and follow the prompts\n3. Or use your browser\'s menu to find "Install" option';
    }
    
    alert(instructions);
  };

  // Don't show if already installed
  if (isInstalled) return null;

  // Show modal popup
  if (showModal) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        {/* Backdrop */}
        <div
          className="absolute inset-0 bg-black bg-opacity-75 backdrop-blur-sm"
          onClick={handleRemindLater}
        />

        {/* Modal */}
        <div className="relative bg-gradient-to-br from-gray-900 to-black border border-gold-500/30 rounded-2xl shadow-2xl max-w-md w-full mx-4 overflow-hidden">
          {/* Header with BAJA logo */}
          <div className="bg-gradient-to-r from-gold-500 to-gold-600 p-6 text-center">
            <div className="flex justify-center mb-4">
              <img
                src="/baja.jpeg"
                alt="BAJA"
                className="w-16 h-16 rounded-full border-4 border-white shadow-lg"
              />
            </div>
            <h2 className="text-2xl font-bold text-black">Install BAJA SMS</h2>
            <p className="text-black/80 mt-2">Get the full app experience!</p>
          </div>

          {/* Content */}
          <div className="p-6">
            <div className="space-y-4 mb-6">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gold-500/20 rounded-full flex items-center justify-center">
                  <ArrowDownTrayIcon className="h-4 w-4 text-gold-400" />
                </div>
                <span className="text-white">Works offline with cached data</span>
              </div>

              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gold-500/20 rounded-full flex items-center justify-center">
                  <ArrowDownTrayIcon className="h-4 w-4 text-gold-400" />
                </div>
                <span className="text-white">Faster loading and better performance</span>
              </div>

              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gold-500/20 rounded-full flex items-center justify-center">
                  <ArrowDownTrayIcon className="h-4 w-4 text-gold-400" />
                </div>
                <span className="text-white">Native app-like experience</span>
              </div>
            </div>

            {/* Buttons */}
            <div className="space-y-3">
              {deferredPrompt ? (
                <Button
                  onClick={handleInstallClick}
                  className="w-full bg-gradient-to-r from-gold-500 to-gold-600 text-black font-semibold py-3 hover:from-gold-600 hover:to-gold-700 transition-all duration-300"
                >
                  <ArrowDownTrayIcon className="h-5 w-5 mr-2" />
                  Pasang sekarang
                </Button>
              ) : (
                <Button
                  onClick={handleManualInstall}
                  className="w-full bg-gradient-to-r from-gold-500 to-gold-600 text-black font-semibold py-3 hover:from-gold-600 hover:to-gold-700 transition-all duration-300"
                >
                  <ArrowDownTrayIcon className="h-5 w-5 mr-2" />
                  How to Install
                </Button>
              )}

              <div className="flex space-x-2">
                <Button
                  onClick={handleRemindLater}
                  variant="outline"
                  className="flex-1 border-gray-600 text-gray-300 hover:bg-gray-800"
                >
                  Remind Later
                </Button>

                <Button
                  onClick={handleDismiss}
                  variant="outline"
                  className="flex-1 border-gray-600 text-gray-300 hover:bg-gray-800"
                >
                  Don't Show Again
                </Button>
              </div>
            </div>
          </div>

          {/* Close button */}
          <button
            onClick={handleRemindLater}
            className="absolute top-4 right-4 text-black hover:text-gray-700 transition-colors"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>
      </div>
    );
  }

  // Show bottom banner if not modal
  if (showInstallPrompt && !showModal) {
    return (
      <div className="fixed bottom-4 left-4 right-4 z-50 md:left-auto md:right-4 md:w-96">
        <div className="bg-gradient-to-r from-gold-500 to-gold-600 text-black rounded-lg shadow-lg p-4 border border-gold-400">
          <div className="flex items-start justify-between mb-3">
            <div className="flex items-center space-x-2">
              <img src="/baja.jpeg" alt="BAJA" className="w-8 h-8 rounded-full" />
              <h3 className="font-semibold text-lg">Install BAJA SMS</h3>
            </div>
            <button
              onClick={handleDismiss}
              className="text-black hover:text-gray-700 transition-colors"
            >
              <XMarkIcon className="h-5 w-5" />
            </button>
          </div>

          <p className="text-sm mb-4 text-black/80">
            Install our app for offline access and better performance!
          </p>

          <div className="flex space-x-2">
            <Button
              onClick={() => setShowModal(true)}
              className="flex-1 bg-black text-gold-400 hover:bg-gray-800 border-black"
              size="sm"
            >
              Learn More
            </Button>

            <Button
              onClick={handleDismiss}
              variant="outline"
              className="border-black text-black hover:bg-black/10"
              size="sm"
            >
              Later
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return null;
};

export default PWAInstallPrompt;
