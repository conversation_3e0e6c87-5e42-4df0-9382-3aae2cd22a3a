<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateOfficialTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 5,
                'unsigned' => true,
                'auto_increment' => true
            ],
            'profile' => [
                'type' => 'VARCHAR',
                'constraint' => '255',
                'null' => true    
            ],
            'name' => [
                'type' => 'VARCHAR',
                'constraint' => '255',
            ],
            'no_hp' => [
                'type' => 'VARCHAR',
                'constraint' => '255',
            ],
            'alamat' => [
                'type' => 'VARCHAR',
                'constraint' => '255',
            ],
            'agama' => [
                'type' => 'VARCHAR',
                'constraint' => '255',
            ],
            'jenis_kelamin' => [
                'type' => 'ENUM',
                'constraint' => ['M', 'F'],
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'id_kontingen' => [
                'type' => 'INT',
                'constraint' => 5,
                'unsigned' => true, 
            ]
            ]);
        $this->forge->addKey('id', true);
        $this->forge->addForeignKey('id_kontingen', 'kontingen', 'id', 'CASCADE', 'CASCADE');
        $this->forge->createTable('official');
    }

    public function down()
    {
        $this->forge->dropTable('official');
    }
}
