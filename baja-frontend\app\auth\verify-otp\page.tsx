'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import OTPInput from '@/components/auth/OTPInput';
import toast from 'react-hot-toast';

export default function VerifyOTPPage() {
  const [loading, setLoading] = useState(false);
  const [resendLoading, setResendLoading] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [otpError, setOtpError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [lastSubmittedOTP, setLastSubmittedOTP] = useState('');
  
  const router = useRouter();
  const searchParams = useSearchParams();
  const { verifyRegistrationOTP, resendOTP } = useAuth();
  
  const email = searchParams.get('email');
  const type = searchParams.get('type') || 'registration';

  useEffect(() => {
    if (!email) {
      router.push('/auth/register');
      return;
    }

    // Start countdown for resend button
    setCountdown(60);
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [email, router]);

  const handleOTPComplete = async (otp: string) => {
    if (!email || isSubmitting || loading) return;

    // Prevent duplicate submissions with same OTP
    if (otp === lastSubmittedOTP) {
      console.log('🔄 Duplicate OTP submission prevented:', otp);
      return;
    }

    try {
      console.log('🔐 Submitting OTP:', otp);
      setIsSubmitting(true);
      setLoading(true);
      setOtpError('');
      setLastSubmittedOTP(otp);

      if (type === 'registration') {
        await verifyRegistrationOTP(email, otp);
        toast.success('Registrasi berhasil! Silakan login.');
        router.push('/auth/login?message=registration-success');
      } else {
        // Handle other OTP types if needed
        toast.success('Verifikasi berhasil!');
        router.push('/dashboard');
      }
    } catch (error: any) {
      console.error('❌ OTP verification failed:', error);
      setOtpError(error.message || 'Kode OTP tidak valid');
      setLastSubmittedOTP(''); // Reset to allow retry
    } finally {
      setLoading(false);
      // Keep isSubmitting true for a short time to prevent rapid resubmission
      setTimeout(() => {
        setIsSubmitting(false);
      }, 2000);
    }
  };

  const handleResendOTP = async () => {
    if (!email || countdown > 0) return;

    try {
      setResendLoading(true);
      await resendOTP(email, type as any);
      setCountdown(60);
      
      // Restart countdown
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } catch (error) {
      // Error is handled by the context
    } finally {
      setResendLoading(false);
    }
  };

  if (!email) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black flex items-center justify-center p-4">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-white mb-2">
            Verifikasi Email
          </h1>
          <p className="text-gray-300 mb-8">
            Kami telah mengirim kode verifikasi 6 digit ke
          </p>
          <p className="text-yellow-400 font-semibold mb-8">
            {email}
          </p>
        </div>

        <div className="bg-white rounded-lg shadow-xl p-8">
          <OTPInput
            length={6}
            onComplete={handleOTPComplete}
            disabled={loading || isSubmitting}
            error={otpError}
          />

          {loading && (
            <div className="mt-6 flex items-center justify-center">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-yellow-500"></div>
              <span className="ml-2 text-gray-600">Memverifikasi...</span>
            </div>
          )}

          <div className="mt-8 text-center">
            <p className="text-gray-600 text-sm mb-4">
              Tidak menerima kode?
            </p>
            
            <button
              onClick={handleResendOTP}
              disabled={countdown > 0 || resendLoading}
              className={`
                text-sm font-medium
                ${countdown > 0 || resendLoading
                  ? 'text-gray-400 cursor-not-allowed'
                  : 'text-yellow-600 hover:text-yellow-700 cursor-pointer'
                }
              `}
            >
              {resendLoading ? (
                'Mengirim ulang...'
              ) : countdown > 0 ? (
                `Kirim ulang dalam ${countdown}s`
              ) : (
                'Kirim ulang kode'
              )}
            </button>
          </div>

          <div className="mt-6 text-center">
            <button
              onClick={() => router.push('/auth/register')}
              className="text-sm text-gray-500 hover:text-gray-700"
            >
              ← Kembali ke registrasi
            </button>
          </div>
        </div>

        <div className="text-center">
          <p className="text-gray-400 text-sm">
            Sudah punya akun?{' '}
            <button
              onClick={() => router.push('/auth/login')}
              className="text-yellow-400 hover:text-yellow-300 font-medium"
            >
              Masuk di sini
            </button>
          </p>
        </div>
      </div>
    </div>
  );
}
