<?= $this->extend('layouts/backend/template') ?>
<?= $this->section('content') ?>

<div id="content">
<div class="container-fluid">

                    <!-- Page Heading -->
                    <h1 class="h3 mb-2 text-gray-800">Event Master</h1>
                    <?php if (session('success')): ?>
                        <div class="alert alert-success"><?= session('success') ?></div>
                    <?php endif ?>
                    
                    <?php if (session('error')): ?>
                        <div class="alert alert-danger"><?= session('error') ?></div>
                    <?php endif ?>
                    <!-- DataTales Example -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Data Event Master</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>No</th>
                                            <th>Gambar</th>
                                            <th>Nama Event</th>
                                            <th>Deskripsi</th>
                                            <th>Lokasi</th>
                                            <th>Tanggal Mulai</th>
                                            <th>Tanggal Selesai</th>
                                            <th>Biaya Registrasi</th>
                                            <th>Metode Pembayaran</th>
                                            <th>Proposal</th>
                                            <th>Pemenang</th>
                                            <th>Status</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $no = 1; foreach ($events as $row) : ?>
                                            <tr>
                                            <td><?= $no++ ?></td>
                                            <td><img src="<?= base_url('uploads/file-event/'. strtolower(str_replace(' ', '-', $row['name'])) . '/' . $row['event_image']) ?>" width="100"></td>
                                            <td><?= $row['name'] ?></td>
                                            <td><?= $row['description'] ?></td>
                                            <td><?= $row['lokasi'] ?></td>
                                            <td><?= $row['start_date'] ?></td>
                                            <td><?= $row['end_date'] ?></td>
                                            <td><?= $row['biaya_registrasi'] ?></td>
                                            <td><?= $row['metode_pembayaran'] ?></td>
                                            <td><a href="<?= base_url('uploads/file-event/' . strtolower(str_replace(' ', '-', $row['name'])) . '/' . $row['event_proposal']) ?>" class="btn btn-success"><i class="fa fa-download"></a></td>
                                            <?php if ($row['event_pemenang'] == null) : ?>
                                                <td><span class="badge badge-danger">Belum Ada Pemenang</td>
                                            <?php else : ?>
                                                <td><a href="<?= base_url('uploads/file-event/' . strtolower(str_replace(' ', '-', $row['name'])) . '/' . $row['event_pemenang']) ?>" class="btn btn-success"><i class="fa fa-download"></a></td>
                                            <?php endif; ?>
                                            <?php if ($row['status'] == 'active') : ?>
                                                <td><span class="badge badge-success">Aktif</td>
                                            <?php else : ?>
                                                <td><span class="badge badge-danger">Selesai</td>
                                            <?php endif; ?>
                                            <td>
                                                <a href="<?= base_url('event-master/delete/'.$row['id']) ?>" class="btn btn-danger"><i class="fa fa-trash"></i></a>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

<?= $this->endSection(); ?>