import { Request, Response } from 'express';
import { PendaftaranAtlet, PendaftaranAtletDetail, Atlet, PendaftaranEvent, Event, Kontingen } from '../models';
import { AuthRequest } from '../middleware/auth.middleware';

export const createPendaftaranAtlet = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { 
      id_pendaftaran_event, 
      jenis_tanding, 
      kategori_umur, 
      kelas_tanding, 
      athlete_ids 
    } = req.body;
    const user = req.user;

    // Validate required fields
    if (!id_pendaftaran_event || !jenis_tanding || !athlete_ids || athlete_ids.length === 0) {
      res.status(400).json({
        success: false,
        message: 'Missing required fields: id_pendaftaran_event, jenis_tanding, athlete_ids'
      });
      return;
    }

    // Check if pendaftaran event exists and belongs to user's kontingen
    const whereClause: any = { id: id_pendaftaran_event };
    
    if (user.role === 'ketua-kontingen') {
      const kontingen = await Kontingen.findOne({ where: { id_user: user.id } });
      if (!kontingen) {
        res.status(404).json({
          success: false,
          message: 'Kontingen not found'
        });
        return;
      }
      whereClause.id_kontingen = kontingen.id;
    }

    const pendaftaranEvent = await PendaftaranEvent.findOne({ where: whereClause });
    if (!pendaftaranEvent) {
      res.status(404).json({
        success: false,
        message: 'Event registration not found'
      });
      return;
    }

    // Validate athletes belong to the same kontingen
    const atletWhereClause: any = { id: athlete_ids };
    if (user.role === 'ketua-kontingen') {
      const kontingen = await Kontingen.findOne({ where: { id_user: user.id } });
      if (kontingen) {
        atletWhereClause.id_kontingen = kontingen.id;
      }
    }

    const athletes = await Atlet.findAll({ where: atletWhereClause });
    if (athletes.length !== athlete_ids.length) {
      res.status(400).json({
        success: false,
        message: 'Some athletes not found or do not belong to your kontingen'
      });
      return;
    }

    // Create pendaftaran atlet
    const pendaftaranAtlet = await PendaftaranAtlet.create({
      id_pendaftaran_event,
      jenis_tanding,
      kategori_umur,
      kelas_tanding,
      status: 'pending'
    });

    // Create pendaftaran atlet details for each athlete
    const atletDetails = await Promise.all(
      athlete_ids.map((atletId: number) =>
        PendaftaranAtletDetail.create({
          id_pendaftaran_atlet: pendaftaranAtlet.id,
          id_atlet: atletId
        })
      )
    );

    res.status(201).json({
      success: true,
      message: 'Athletes registered successfully',
      data: {
        pendaftaran_atlet: pendaftaranAtlet,
        atlet_details: atletDetails
      }
    });
  } catch (error) {
    console.error('Create pendaftaran atlet error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const getPendaftaranAtletByEvent = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params; // pendaftaran_event id
    const user = req.user;

    // Check if pendaftaran event exists and belongs to user's kontingen
    const whereClause: any = { id };
    
    if (user.role === 'ketua-kontingen') {
      const kontingen = await Kontingen.findOne({ where: { id_user: user.id } });
      if (!kontingen) {
        res.status(404).json({
          success: false,
          message: 'Kontingen not found'
        });
        return;
      }
      whereClause.id_kontingen = kontingen.id;
    }

    const pendaftaranEvent = await PendaftaranEvent.findOne({ where: whereClause });
    if (!pendaftaranEvent) {
      res.status(404).json({
        success: false,
        message: 'Event registration not found'
      });
      return;
    }

    // Get all athlete registrations for this event
    const pendaftaranAtlet = await PendaftaranAtlet.findAll({
      where: { id_pendaftaran_event: id },
      include: [
        {
          model: PendaftaranAtletDetail,
          as: 'atletDetails',
          include: [
            {
              model: Atlet,
              as: 'atlet'
            }
          ]
        }
      ]
    });

    res.json({
      success: true,
      data: pendaftaranAtlet
    });
  } catch (error) {
    console.error('Get pendaftaran atlet error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const updatePendaftaranAtlet = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { status, bukti_pembayaran } = req.body;
    const user = req.user;

    const pendaftaranAtlet = await PendaftaranAtlet.findByPk(id, {
      include: [
        {
          model: PendaftaranEvent,
          as: 'pendaftaranEvent',
          include: [
            {
              model: Kontingen,
              as: 'kontingen'
            }
          ]
        }
      ]
    });

    if (!pendaftaranAtlet) {
      res.status(404).json({
        success: false,
        message: 'Athlete registration not found'
      });
      return;
    }

    // Check authorization
    if (user.role === 'ketua-kontingen') {
      const kontingen = await Kontingen.findOne({ where: { id_user: user.id } });
      const pendaftaranEvent = await PendaftaranEvent.findByPk(pendaftaranAtlet.id_pendaftaran_event);
      if (!kontingen || !pendaftaranEvent || pendaftaranEvent.id_kontingen !== kontingen.id) {
        res.status(403).json({
          success: false,
          message: 'Access denied'
        });
        return;
      }
    }

    const updateData: any = {};
    if (status) updateData.status = status;
    if (bukti_pembayaran) updateData.bukti_pembayaran = bukti_pembayaran;

    await pendaftaranAtlet.update(updateData);

    res.json({
      success: true,
      message: 'Athlete registration updated successfully',
      data: pendaftaranAtlet
    });
  } catch (error) {
    console.error('Update pendaftaran atlet error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const deletePendaftaranAtlet = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const user = req.user;

    const pendaftaranAtlet = await PendaftaranAtlet.findByPk(id);

    if (!pendaftaranAtlet) {
      res.status(404).json({
        success: false,
        message: 'Athlete registration not found'
      });
      return;
    }

    // Check authorization
    if (user.role === 'ketua-kontingen') {
      const kontingen = await Kontingen.findOne({ where: { id_user: user.id } });
      const pendaftaranEvent = await PendaftaranEvent.findByPk(pendaftaranAtlet.id_pendaftaran_event);
      if (!kontingen || !pendaftaranEvent || pendaftaranEvent.id_kontingen !== kontingen.id) {
        res.status(403).json({
          success: false,
          message: 'Access denied'
        });
        return;
      }
    }

    // Delete athlete details first
    await PendaftaranAtletDetail.destroy({
      where: { id_pendaftaran_atlet: id }
    });

    // Delete the registration
    await pendaftaranAtlet.destroy();

    res.json({
      success: true,
      message: 'Athlete registration deleted successfully'
    });
  } catch (error) {
    console.error('Delete pendaftaran atlet error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const getAllPendaftaranAtlet = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const user = req.user;
    const { page = 1, limit = 10, search = '' } = req.query;

    const offset = (Number(page) - 1) * Number(limit);
    const whereClause: any = {};

    // Filter by kontingen for ketua-kontingen
    if (user.role === 'ketua-kontingen') {
      const kontingen = await Kontingen.findOne({ where: { id_user: user.id } });
      if (kontingen) {
        whereClause['$pendaftaranEvent.id_kontingen$'] = kontingen.id;
      }
    }

    // Filter by event for admin-event
    if (user.role === 'admin-event' && user.event_id) {
      whereClause['$pendaftaranEvent.id_event$'] = user.event_id;
    }

    const { count, rows } = await PendaftaranAtlet.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: PendaftaranEvent,
          as: 'pendaftaranEvent',
          include: [
            {
              model: Event,
              as: 'event'
            },
            {
              model: Kontingen,
              as: 'kontingen'
            }
          ]
        },
        {
          model: PendaftaranAtletDetail,
          as: 'atletDetails',
          include: [
            {
              model: Atlet,
              as: 'atlet'
            }
          ]
        }
      ],
      limit: Number(limit),
      offset,
      order: [['created_at', 'DESC']]
    });

    res.json({
      success: true,
      data: rows,
      pagination: {
        total: count,
        page: Number(page),
        limit: Number(limit),
        totalPages: Math.ceil(count / Number(limit))
      }
    });
  } catch (error) {
    console.error('Get all pendaftaran atlet error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};
