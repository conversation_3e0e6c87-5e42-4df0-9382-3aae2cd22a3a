import dotenv from 'dotenv';
import { v2 as cloudinary } from 'cloudinary';
import { CloudinaryStorage } from 'multer-storage-cloudinary';
import multer from 'multer';

// Load environment variables first
dotenv.config();

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

// Gallery storage configuration
export const galleryStorage = new CloudinaryStorage({
  cloudinary: cloudinary,
  params: {
    folder: 'baja/gallery',
    allowed_formats: ['jpg', 'jpeg', 'png'],
    transformation: [
      { width: 1200, height: 800, crop: 'limit' },
      { quality: 'auto' },
      { fetch_format: 'auto' }
    ],
  } as any,
});

// Event storage configuration
export const eventStorage = new CloudinaryStorage({
  cloudinary: cloudinary,
  params: {
    folder: 'baja/events',
    allowed_formats: ['jpg', 'jpeg', 'png'],
    transformation: [
      { width: 1000, height: 600, crop: 'limit' },
      { quality: 'auto' },
      { fetch_format: 'auto' }
    ],
  } as any,
});

// Package storage configuration
export const packageStorage = new CloudinaryStorage({
  cloudinary: cloudinary,
  params: {
    folder: 'baja/packages',
    allowed_formats: ['jpg', 'jpeg', 'png'],
    transformation: [
      { width: 800, height: 600, crop: 'limit' },
      { quality: 'auto' },
      { fetch_format: 'auto' }
    ],
  } as any,
});

// Profile storage configuration
export const profileStorage = new CloudinaryStorage({
  cloudinary: cloudinary,
  params: {
    folder: 'baja/profiles',
    allowed_formats: ['jpg', 'jpeg', 'png'],
    transformation: [
      { width: 400, height: 400, crop: 'fill', gravity: 'face' },
      { quality: 'auto' },
      { fetch_format: 'auto' }
    ],
  } as any,
});

// Atlet storage configuration
export const atletStorage = new CloudinaryStorage({
  cloudinary: cloudinary,
  params: {
    folder: 'baja/atlet',
    allowed_formats: ['jpg', 'jpeg', 'png'],
    transformation: [
      { width: 400, height: 500, crop: 'limit' },
      { quality: 'auto' },
      { fetch_format: 'auto' }
    ],
  } as any,
});

// Official storage configuration
export const officialStorage = new CloudinaryStorage({
  cloudinary: cloudinary,
  params: {
    folder: 'baja/official',
    allowed_formats: ['jpg', 'jpeg', 'png'],
    transformation: [
      { width: 400, height: 500, crop: 'limit' },
      { quality: 'auto' },
      { fetch_format: 'auto' }
    ],
  } as any,
});

// Document storage configuration (for proposals, etc.)
export const documentStorage = new CloudinaryStorage({
  cloudinary: cloudinary,
  params: {
    folder: 'baja/documents',
    allowed_formats: ['pdf', 'doc', 'docx'],
    resource_type: 'raw',
  } as any,
});

// Multer configurations
export const galleryUpload = multer({
  storage: galleryStorage,
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB
});

export const eventUpload = multer({
  storage: eventStorage,
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB
});

export const packageUpload = multer({
  storage: packageStorage,
  limits: { fileSize: 3 * 1024 * 1024 }, // 3MB
});

export const profileUpload = multer({
  storage: profileStorage,
  limits: { fileSize: 2 * 1024 * 1024 }, // 2MB
});

export const atletUpload = multer({
  storage: atletStorage,
  limits: { fileSize: 2 * 1024 * 1024 }, // 2MB
});

export const officialUpload = multer({
  storage: officialStorage,
  limits: { fileSize: 2 * 1024 * 1024 }, // 2MB
});

export const documentUpload = multer({
  storage: documentStorage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB
});

// Flexible upload for both images and documents
export const flexibleStorage = new CloudinaryStorage({
  cloudinary: cloudinary,
  params: (req: any, file: any) => {
    const isDocument = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'].includes(file.mimetype);

    if (isDocument) {
      return {
        folder: 'baja/documents',
        allowed_formats: ['pdf', 'doc', 'docx'],
        resource_type: 'raw',
      };
    } else {
      return {
        folder: 'baja/files',
        allowed_formats: ['jpg', 'jpeg', 'png'],
        transformation: [
          { width: 800, height: 600, crop: 'limit' },
          { quality: 'auto' },
          { fetch_format: 'auto' }
        ],
      };
    }
  },
} as any);

export const flexibleUpload = multer({
  storage: flexibleStorage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB
});

// Utility functions
export const extractPublicId = (cloudinaryUrl: string): string => {
  try {
    // Extract public_id from Cloudinary URL
    // Example: https://res.cloudinary.com/dzkqrfdiq/image/upload/v1234567890/baja/gallery/abc123.jpg
    const urlParts = cloudinaryUrl.split('/');
    const uploadIndex = urlParts.findIndex(part => part === 'upload');
    if (uploadIndex !== -1 && uploadIndex + 2 < urlParts.length) {
      // Skip version if present (v1234567890)
      const startIndex = urlParts[uploadIndex + 1].startsWith('v') ? uploadIndex + 2 : uploadIndex + 1;
      const pathParts = urlParts.slice(startIndex);
      const fullPath = pathParts.join('/');
      // Remove file extension
      return fullPath.replace(/\.[^/.]+$/, '');
    }
    return '';
  } catch (error) {
    console.error('Error extracting public_id:', error);
    return '';
  }
};

export const deleteFromCloudinary = async (publicId: string, resourceType: 'image' | 'raw' = 'image'): Promise<void> => {
  try {
    if (publicId) {
      await cloudinary.uploader.destroy(publicId, { resource_type: resourceType });
    }
  } catch (error) {
    console.error('Error deleting from Cloudinary:', error);
  }
};

export const getOptimizedUrl = (publicId: string, options?: any): string => {
  return cloudinary.url(publicId, {
    fetch_format: 'auto',
    quality: 'auto',
    ...options,
  });
};

export const getThumbnailUrl = (publicId: string): string => {
  return cloudinary.url(publicId, {
    width: 300,
    height: 200,
    crop: 'fill',
    fetch_format: 'auto',
    quality: 'auto',
  });
};

export default cloudinary;
