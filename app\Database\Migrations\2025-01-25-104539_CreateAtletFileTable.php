<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateAtletFileTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 5,
                'unsigned' => true,
                'auto_increment' => true
            ],
            'file' => [
                'type' => 'VARCHAR',
                'constraint' => '255',
            ],
            'file_type' => [
                'type' => 'ENUM',
                'constraint' => '"rapor","kk_ktp","surat_kesehatan"',
                'null' => true
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'id_atlet' => [
                'type' => 'INT',
                'constraint' => 5,
                'unsigned' => true
            ],
            ]);
        $this->forge->addKey('id', true);
        $this->forge->addForeignKey('id_atlet', 'atlet', 'id', 'CASCADE', 'CASCADE');
        $this->forge->createTable('atlet_file');
    }

    public function down()
    {
        $this->forge->dropTable('atlet_file');
    }
}
