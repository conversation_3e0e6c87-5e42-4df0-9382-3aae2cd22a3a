'use client';

import { useState, useRef, useEffect } from 'react';

interface OTPInputProps {
  length?: number;
  onComplete: (otp: string) => void;
  disabled?: boolean;
  error?: string;
}

export default function OTPInput({ 
  length = 6, 
  onComplete, 
  disabled = false,
  error 
}: OTPInputProps) {
  const [otp, setOtp] = useState<string[]>(new Array(length).fill(''));
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  useEffect(() => {
    if (inputRefs.current[0]) {
      inputRefs.current[0].focus();
    }
  }, []);

  const handleChange = (element: HTMLInputElement, index: number) => {
    if (isNaN(Number(element.value))) return false;

    setOtp([...otp.map((d, idx) => (idx === index ? element.value : d))]);

    // Focus next input
    if (element.nextSibling && element.value !== '') {
      (element.nextSibling as HTMLInputElement).focus();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>, index: number) => {
    if (e.key === 'Backspace') {
      if (otp[index] === '' && index > 0) {
        // Focus previous input if current is empty
        inputRefs.current[index - 1]?.focus();
      }
      // Clear current input
      setOtp([...otp.map((d, idx) => (idx === index ? '' : d))]);
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pasteData = e.clipboardData.getData('text');
    const pasteArray = pasteData.slice(0, length).split('');
    
    if (pasteArray.every(char => !isNaN(Number(char)))) {
      const newOtp = [...otp];
      pasteArray.forEach((char, index) => {
        if (index < length) {
          newOtp[index] = char;
        }
      });
      setOtp(newOtp);
      
      // Focus the next empty input or the last input
      const nextEmptyIndex = newOtp.findIndex(val => val === '');
      const focusIndex = nextEmptyIndex !== -1 ? nextEmptyIndex : length - 1;
      inputRefs.current[focusIndex]?.focus();
    }
  };

  useEffect(() => {
    const otpValue = otp.join('');
    if (otpValue.length === length && !disabled) {
      // Add small delay to prevent multiple rapid calls
      const timer = setTimeout(() => {
        onComplete(otpValue);
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [otp, length, onComplete, disabled]);

  return (
    <div className="space-y-4">
      <div className="flex justify-center space-x-3">
        {otp.map((data, index) => (
          <input
            key={index}
            type="text"
            maxLength={1}
            ref={(el) => (inputRefs.current[index] = el)}
            value={data}
            onChange={(e) => handleChange(e.target, index)}
            onKeyDown={(e) => handleKeyDown(e, index)}
            onPaste={handlePaste}
            disabled={disabled}
            className={`
              w-12 h-12 text-center text-xl font-bold border-2 rounded-lg text-black
              focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent
              transition-all duration-200
              ${error
                ? 'border-red-500 bg-red-50 text-black'
                : 'border-gray-300 bg-white hover:border-gray-400 text-black'
              }
              ${disabled
                ? 'bg-gray-100 cursor-not-allowed text-gray-500'
                : 'bg-white text-black'
              }
            `}
          />
        ))}
      </div>
      
      {error && (
        <p className="text-red-500 text-sm text-center">{error}</p>
      )}
      
      <div className="text-center text-sm text-gray-600">
        <p>Masukkan kode 6 digit yang dikirim ke email Anda</p>
      </div>
    </div>
  );
}
