'use client';

import React, { useState, useEffect } from 'react';
import Button from '@/components/ui/Button';
import { ArrowDownTrayIcon, CheckCircleIcon } from '@heroicons/react/24/outline';

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

const PWAInstallButton: React.FC = () => {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [isInstalled, setIsInstalled] = useState(false);
  const [isInstalling, setIsInstalling] = useState(false);

  useEffect(() => {
    // Check if app is already installed
    const checkIfInstalled = () => {
      if (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) {
        setIsInstalled(true);
        return;
      }
      
      // Check for iOS Safari
      if ((window.navigator as any).standalone === true) {
        setIsInstalled(true);
        return;
      }
    };

    checkIfInstalled();

    // Listen for beforeinstallprompt event
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault();
      setDeferredPrompt(e as BeforeInstallPromptEvent);
    };

    // Listen for app installed event
    const handleAppInstalled = () => {
      setIsInstalled(true);
      setDeferredPrompt(null);
      setIsInstalling(false);
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  }, []);

  const handleInstallClick = async () => {
    if (!deferredPrompt) {
      // Show manual installation instructions
      handleManualInstall();
      return;
    }

    try {
      setIsInstalling(true);
      await deferredPrompt.prompt();
      const { outcome } = await deferredPrompt.userChoice;
      
      if (outcome === 'accepted') {
        console.log('User accepted the install prompt');
      } else {
        console.log('User dismissed the install prompt');
        setIsInstalling(false);
      }
      
      setDeferredPrompt(null);
    } catch (error) {
      console.error('Error during installation:', error);
      setIsInstalling(false);
    }
  };

  const handleManualInstall = () => {
    // Show manual installation instructions
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    const isAndroid = /Android/.test(navigator.userAgent);
    
    let instructions = '';
    
    if (isIOS) {
      instructions = 'Untuk memasang aplikasi ini di iOS:\n\n1. Ketuk tombol Bagikan (□↗) di Safari\n2. Gulir ke bawah dan ketuk "Tambahkan ke Layar Utama"\n3. Ketuk "Tambah" untuk konfirmasi\n\nAplikasi akan muncul di layar utama Anda!';
    } else if (isAndroid) {
      instructions = 'Untuk memasang aplikasi ini di Android:\n\n1. Ketuk tombol menu (⋮) di browser Anda\n2. Ketuk "Tambahkan ke layar utama" atau "Pasang aplikasi"\n3. Ketuk "Tambah" untuk konfirmasi\n\nAplikasi akan muncul di layar utama Anda!';
    } else {
      instructions = 'Untuk memasang aplikasi ini:\n\n1. Cari ikon pasang (⊕) di bilah alamat browser Anda\n2. Klik dan ikuti petunjuknya\n3. Atau gunakan menu browser untuk menemukan opsi "Pasang"\n\nAplikasi akan ditambahkan ke aplikasi Anda!';
    }
    
    alert(instructions);
  };

  // Don't show if already installed
  if (isInstalled) {
    return (
      <div className="flex items-center space-x-2 text-green-400">
        <CheckCircleIcon className="h-5 w-5" />
        <span className="text-sm font-medium">Aplikasi terpasang</span>
      </div>
    );
  }

  return (
    <Button
      onClick={handleInstallClick}
      variant="outline"
      size="sm"
      disabled={isInstalling}
  className="border-gold-500/30 text-gold-400 hover:bg-gold-500/10 hover:border-gold-400 hover:text-white transition-all duration-300"
    >
      <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
      {isInstalling ? 'Memasang...' : 'Pasang Aplikasi'}
    </Button>
  );
};

export default PWAInstallButton;
