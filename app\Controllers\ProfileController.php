<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use CodeIgniter\HTTP\ResponseInterface;
use App\Models\User;

class ProfileController extends BaseController
{
    public function index()
    {
        $userId = session()->get('id');
        if (!$userId) {
            return redirect()->to('/login')->with('error', 'Anda harus login terlebih dahulu.');
        }

        $userModel = new User();

        $data['user'] = $userModel->find($userId);

        return view('profile/index', $data);
    }

    public function update()
    {
        $userId = session()->get('id');
        if (!$userId) {
            return redirect()->to('/login')->with('error', 'Anda harus login terlebih dahulu.');
        }
        
        $userModel = new User();

        $data = [
            'name' => $this->request->getPost('name'),
            'email' => $this->request->getPost('email'),
            'no_hp' => $this->request->getPost('no_hp'),
        ];

        // Jika password diisi, update juga
        $password = $this->request->getPost('password');
        if (!empty($password)) {
            $data['password'] = password_hash($password, PASSWORD_DEFAULT);
        }

        // Upload foto profil jika ada
        $file = $this->request->getFile('profile');
        if ($file && $file->isValid() && !$file->hasMoved()) {
            $newName = $file->getRandomName();
            $file->move('uploads/file-profile', $newName); // Folder tujuan
            $data['profile'] = $newName;
        }

        $userModel->update($userId, $data);

        return redirect()->to('/profile')->with('success', 'Profil berhasil diperbarui.');
    }
}
