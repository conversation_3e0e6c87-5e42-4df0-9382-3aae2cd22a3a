<?= $this->extend('layouts/backend/template') ?>
<?= $this->section('content') ?>

<div id="content">
    <div class="container-fluid">

        <!-- Page Heading -->
        <h1 class="h3 mb-2 text-gray-800">Tambah Users</h1>

        <!-- Form Tambah Users -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Form Tambah Users</h6>
            </div>
            <div class="card-body">

            <form action="<?= base_url('users/store') ?>" method="post" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="profile">Profile (Foto)</label>
                    <input type="file" class="form-control" id="profile" name="profile">
                    <?php if(isset(session('errors')['profile'])): ?>
                        <small class="text-danger"><?= session('errors')['profile'] ?></small>
                    <?php endif; ?>
                </div>
                <div class="form-group">
                    <label for="nama_lengkap"><PERSON><PERSON></label>
                    <input type="text" class="form-control" id="nama_lengkap" name="name" required value="<?= old('name') ?>">
                    <?php if(isset(session('errors')['name'])): ?>
                        <small class="text-danger"><?= session('errors')['name'] ?></small>
                    <?php endif; ?>
                </div>
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" class="form-control" id="email" name="email" required value="<?= old('email') ?>">
                    <?php if(isset(session('errors')['email'])): ?>
                        <small class="text-danger"><?= session('errors')['email'] ?></small>
                    <?php endif; ?>
                </div>
                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" class="form-control" id="password" name="password" required>
                    <?php if(isset(session('errors')['password'])): ?>
                        <small class="text-danger"><?= session('errors')['password'] ?></small>
                    <?php endif; ?>
                </div>
                <div class="form-group">
                    <label for="no_hp">No HP</label>
                    <input type="text" class="form-control" id="no_hp" name="no_hp" required value="<?= old('no_hp') ?>">
                    <?php if(isset(session('errors')['no_hp'])): ?>
                        <small class="text-danger"><?= session('errors')['no_hp'] ?></small>
                    <?php endif; ?>
                </div>
                <div class="form-group">
                    <label for="alamat">Alamat</label>
                    <textarea class="form-control" id="alamat" name="alamat" rows="3" required><?= old('alamat') ?></textarea>
                    <?php if(isset(session('errors')['alamat'])): ?>
                        <small class="text-danger"><?= session('errors')['alamat'] ?></small>
                    <?php endif; ?>
                </div>
                <div class="form-group">
                    <label for="agama">Agama</label>
                    <select class="form-control" id="agama" name="agama" required>
                        <option value="">-- Pilih Agama --</option>
                        <option value="Islam" <?= old('agama')=='Islam' ? 'selected' : '' ?>>Islam</option>
                        <option value="Kristen" <?= old('agama')=='Kristen' ? 'selected' : '' ?>>Kristen</option>
                        <option value="Katolik" <?= old('agama')=='Katolik' ? 'selected' : '' ?>>Katolik</option>
                        <option value="Hindu" <?= old('agama')=='Hindu' ? 'selected' : '' ?>>Hindu</option>
                        <option value="Buddha" <?= old('agama')=='Buddha' ? 'selected' : '' ?>>Buddha</option>
                        <option value="Konghucu" <?= old('agama')=='Konghucu' ? 'selected' : '' ?>>Konghucu</option>
                    </select>
                    <?php if(isset(session('errors')['agama'])): ?>
                        <small class="text-danger"><?= session('errors')['agama'] ?></small>
                    <?php endif; ?>
                </div>
                <div class="form-group">
                    <label for="status">Status</label>
                    <select class="form-control" id="status" name="status" required>
                        <option value="">-- Pilih Status --</option>
                        <option value="1" <?= old('status')=='1' ? 'selected' : '' ?>>Active</option>
                        <option value="0" <?= old('status')=='0' ? 'selected' : '' ?>>Inactive</option>
                    </select>
                    <?php if(isset(session('errors')['status'])): ?>
                        <small class="text-danger"><?= session('errors')['status'] ?></small>
                    <?php endif; ?>
                </div>
                <div class="form-group">
                    <label for="role">Role</label>
                    <select class="form-control" id="role" name="role" required>
                        <option value="">-- Pilih Role --</option>
                        <option value="admin-event" <?= old('role')=='admin-event' ? 'selected' : '' ?>>Admin Event</option>
                        <option value="ketua-kontingen" <?= old('role')=='ketua-kontingen' ? 'selected' : '' ?>>Ketua Kontingen</option>
                    </select>
                    <?php if(isset(session('errors')['role'])): ?>
                        <small class="text-danger"><?= session('errors')['role'] ?></small>
                    <?php endif; ?>
                </div>
                <button type="submit" class="btn btn-primary">Simpan</button>
                <a href="<?= base_url('users') ?>" class="btn btn-secondary">Batal</a>
            </form>
        </div>
        </div>

    </div>
    <!-- /.container-fluid -->

</div>
<!-- End of Main Content -->

<?= $this->endSection(); ?>
