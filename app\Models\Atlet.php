<?php

namespace App\Models;

use CodeIgniter\Model;

class Atlet extends Model
{
    protected $table            = 'atlet';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
   
    protected $allowedFields    = [
        'nik',
        'name',
        'no_hp',
        'tanggal_lahir',
        'jenis_kelamin',
        'agama',
        'alamat',
        'umur',
        'berat_badan',
        'tinggi_badan',
        'status_verifikasi',
        'id_user',
        'id_kontingen'
    ];

    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    protected $skipValidation   = false;
}
