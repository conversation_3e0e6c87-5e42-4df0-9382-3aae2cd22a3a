<?= $this->extend('layouts/backend/template') ?>
<?= $this->section('content') ?>

<div id="content">
    <div class="container-fluid">
        <h1 class="h3 mb-2 text-gray-800">Daftar Atlet</h1>
        <h6 class="m-0 mb-3 font-weight-bold text-primary">
            <a href="<?= base_url('pendaftaran-atlet/detail/' . $event['id'] . '/create') ?>" class="btn btn-primary">+ Tanding</a>
        </h6>
        
        <?php if (session('success')): ?>
            <div class="alert alert-success"><?= session('success') ?></div>
        <?php endif ?>
        
        <?php if (session('error')): ?>
            <div class="alert alert-danger"><?= session('error') ?></div>
        <?php endif ?>

        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Data Atlet</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" id="dataTable">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>Atlet</th>
                                <th>Jenis Tanding</th>
                                <th>Kategori Umur</th>
                                <th>Kelas</th>
                                <th>Status Pembayaran</th>
                                <th>Bukti Pembayaran</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $no = 1; foreach ($registrations as $row): ?>
                                <tr>
                                    <td><?= $no++ ?></td>
                                    <td><?= $row['atlet_names'] ?></td>
                                    <td><?= $row['jenis_tanding'] ?></td>
                                    <td><?= $row['kategori_umur'] ?></td>
                                    <td><?= $row['kelas_tanding'] ?></td>
                                    <td>
                                        <?php 
                                            if ($row['status'] === 'paid') {
                                                echo '<span class="badge badge-success">Lunas</span>';
                                            } elseif ($row['status'] === 'oncheck') {
                                                echo '<span class="badge badge-warning">Proses</span>';
                                            } else {
                                                echo '<span class="badge badge-secondary">Belum Lunas</span>';
                                            }
                                        ?>
                                    </td>
                                    <td>
                                        <?php if (empty($row['bukti_pembayaran'])): ?>
                                            <button type="button" class="btn btn-warning" 
                                                data-toggle="modal" data-target="#modalUploadBukti"
                                                data-id="<?= $row['id'] ?>">
                                                Upload Bukti
                                            </button>
                                        <?php else: ?>
                                            <span class="badge badge-success">Sudah Upload</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <a href="<?= base_url('pendaftaran-atlet/delete/' . $row['id']) ?>" class="btn btn-danger" onclick="return confirm('Apakah Anda yakin ingin menghapus data ini?')"><i class="fa fa-trash"></i></a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Upload Bukti -->
<div class="modal fade" id="modalUploadBukti" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Upload Bukti Pembayaran</h5>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <form id="uploadBuktiForm" action="" method="post" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" name="id" id="pendaftaranId">
                    <div class="form-group">
                        <label>File Bukti (PDF)</label>
                        <input type="file" class="form-control-file" name="bukti_pembayaran" accept="application/pdf" required>
                    </div>
                    <div class="form-group">
                        <label for="metode_pembayaran">Tujuan Pembayaran</label>
                        <input type="text" class="form-control" id="metode_pembayaran" value="<?= $event['metode_pembayaran'] ?>" required disabled>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Upload</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="<?= base_url('assets-be/js/jquery.min.js') ?>"></script>
<script src="<?= base_url('assets-be/js/bootstrap.bundle.min.js') ?>"></script>
<script>
$(document).ready(function(){
    $('#modalUploadBukti').on('show.bs.modal', function (e) {
        var id = $(e.relatedTarget).data('id');
        $(this).find('#pendaftaranId').val(id);
        $('#uploadBuktiForm').attr('action', '<?= base_url('pendaftaran-atlet/uploadBukti') ?>/' + id);
    });
});
</script>

<?= $this->endSection(); ?>
