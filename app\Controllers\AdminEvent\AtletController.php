<?php

namespace App\Controllers\AdminEvent;

use App\Controllers\BaseController;
use App\Models\Kontingen;
use App\Models\Atlet;
use App\Models\AtletFile;
use App\Models\PendaftaranEvent;
use App\Models\Event;

class AtletController extends BaseController
{
    public function index()
    {
        // Ambil ID event dari session
        $eventId = session('event_id');

        // Pastikan event_id ada
        if (!$eventId) {
            return view('admin-event/atlet-kontingen/index', ['atlet_kontingen' => []]);
        }

        // Periksa status event saat ini
        $eventModel = new Event();
        $currentEvent = $eventModel->find($eventId);
        if (!$currentEvent || $currentEvent['status'] !== 'active') {
            // Jika event tidak aktif, maka tidak tampilkan data master
            return view('admin-event/atlet-kontingen/index', ['atlet_kontingen' => []]);
        }

        // Load model-model yang diperlukan
        $pendaftaranEvent = new PendaftaranEvent();
        $atlet            = new Atlet();
        $atletFile        = new AtletFile();

        // Ambil semua ID kontingen yang terdaftar di event saat ini
        $kontingenIds = $pendaftaranEvent
            ->where('id_event', $eventId)
            ->select('id_kontingen')
            ->findColumn('id_kontingen');

        // Jika tidak ada kontingen yang mendaftar, kembalikan array kosong
        if (empty($kontingenIds)) {
            return view('admin-event/atlet-kontingen/index', ['atlet_kontingen' => []]);
        }

        // Ambil data atlet dari kontingen yang terdaftar pada event saat ini
        $atletData = $atlet
            ->select('
                atlet.id,
                atlet.name,
                atlet.nik,
                atlet.no_hp,
                atlet.tanggal_lahir,
                atlet.jenis_kelamin,
                atlet.agama,
                atlet.alamat,
                atlet.umur,
                atlet.berat_badan,
                atlet.tinggi_badan,
                atlet.status_verifikasi,
                kontingen.name as kontingen_name
            ')
            ->join('kontingen', 'kontingen.id = atlet.id_kontingen')
            ->whereIn('atlet.id_kontingen', $kontingenIds)
            ->findAll();

        // Ambil file atlet untuk setiap atlet
        foreach ($atletData as &$row) {
            $row['files'] = $atletFile
                ->select('file, file_type')
                ->where('id_atlet', $row['id'])
                ->findAll();
        }

        return view('admin-event/atlet-kontingen/index', ['atlet_kontingen' => $atletData]);
    }

    public function verify($id)
    {
        // Pastikan $id valid (misalnya berupa angka)
        if (!is_numeric($id)) {
            return redirect()->back()->with('error', 'ID atlet tidak valid.');
        }

        $atlet = new Atlet();
        $existingAtlet = $atlet->find($id);
        if (!$existingAtlet) {
            return redirect()->back()->with('error', 'Atlet tidak ditemukan.');
        }

        // Update status_verifikasi menjadi 'verified'
        $data = [
            'status_verifikasi' => 'verified'
        ];

        if ($atlet->update($id, $data)) {
            return redirect()->to(base_url('atlet-kontingen'))
                ->with('success', 'Atlet berhasil diverifikasi.');
        } else {
            return redirect()->back()
                ->with('error', 'Gagal memverifikasi atlet.');
        }
    }
}
