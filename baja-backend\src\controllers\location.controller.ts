import { Request, Response } from 'express';
import <PERSON>egara from '../models/Negara';
import <PERSON>vinsi from '../models/Provinsi';
import KabupatenKota from '../models/KabupatenKota';

export const getAllNegara = async (req: Request, res: Response): Promise<void> => {
  try {
    const negara = await Negara.findAll({
      order: [['name', 'ASC']]
    });

    res.json({
      success: true,
      message: 'Countries retrieved successfully',
      data: negara
    });
  } catch (error) {
    console.error('Get all negara error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const getProvinsiByNegara = async (req: Request, res: Response): Promise<void> => {
  try {
    const { negaraId } = req.params;

    const provinsi = await Provinsi.findAll({
      where: { id_negara: negaraId },
      order: [['name', 'ASC']]
    });

    res.json({
      success: true,
      message: 'Provinces retrieved successfully',
      data: provinsi
    });
  } catch (error) {
    console.error('Get provinsi by negara error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const getKabupatenKotaByProvinsi = async (req: Request, res: Response): Promise<void> => {
  try {
    const { provinsiId } = req.params;

    const kabupatenKota = await KabupatenKota.findAll({
      where: { id_provinsi: provinsiId },
      order: [['name', 'ASC']]
    });

    res.json({
      success: true,
      message: 'Cities/Regencies retrieved successfully',
      data: kabupatenKota
    });
  } catch (error) {
    console.error('Get kabupaten kota by provinsi error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};
