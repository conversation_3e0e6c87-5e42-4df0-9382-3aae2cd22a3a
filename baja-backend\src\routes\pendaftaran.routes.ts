import { Router } from 'express';
import { authenticate, authorize } from '../middleware/auth.middleware';
import {
  getAllPendaftaran,
  getPendaftaranById,
  createPendaftaran,
  updateStatusPendaftaran,
  deletePendaftaran
} from '../controllers/pendaftaran.controller';

const router = Router();

// All routes require authentication
router.use(authenticate);

router.get('/', getAllPendaftaran);
router.post('/', authorize('ketua-kontingen'), createPendaftaran);
router.get('/:id', getPendaftaranById);
router.patch('/:id/status', authorize('admin', 'admin-event'), updateStatusPendaftaran);
router.delete('/:id', authorize('admin', 'admin-event', 'ketua-kontingen'), deletePendaftaran);

export default router;
