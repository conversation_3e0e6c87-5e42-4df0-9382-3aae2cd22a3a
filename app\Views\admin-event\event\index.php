<?= $this->extend('layouts/backend/template') ?>
<?= $this->section('content') ?>

<div id="content">
    <div class="container-fluid">
        <!-- Page Heading -->
        <h1 class="h3 mb-2 text-gray-800">Event</h1>
        <?php if (!$hasActiveEvent): ?>
            <h6 class="m-0 mb-3 font-weight-bold text-primary">
                <a href="<?= base_url('event/create') ?>" class="btn btn-primary">+ Event</a>
            </h6>
        <?php else: ?>
            <h6 class="m-0 mb-3 font-weight-bold text-primary">
                <button class="btn btn-secondary" disabled>(Anda sudah memiliki event)</button>
            </h6>
        <?php endif; ?>
        <?php if (session('success')): ?>
            <div class="alert alert-success"><?= session('success') ?></div>
        <?php endif ?>
        
        <?php if (session('error')): ?>
            <div class="alert alert-danger"><?= session('error') ?></div>
        <?php endif ?>
        <div class="row">
            <?php if (!empty($events)): ?>
                <?php foreach ($events as $event): ?>
                    <div class="col-lg-4">
                        <!-- Event Card -->
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary"><?= $event['name'] ?></h6>
                            </div>
                            <div class="card-body">
                                <div class="text-center mb-4">
                                    <?php if ($event['event_image']): ?>
                                        <img src="<?= base_url('uploads/file-event/' . strtolower(str_replace(' ', '-', $event['name'])) . '/' . $event['event_image']) ?>" alt="Event Image" class="img-fluid" style="max-height: 200px; object-fit: cover;">
                                    <?php else: ?>
                                        <p>Tidak ada gambar.</p>
                                    <?php endif; ?>
                                </div>
                                <h5 class="card-title text-primary"><?= $event['name'] ?></h5>
                                <p class="card-text text-dark">
                                    <strong>Deskripsi:</strong> <?= $event['description'] ?>
                                </p>
                                <p class="card-text text-dark">
                                    <strong>Tanggal Mulai:</strong> <?= date('d-m-Y', strtotime($event['start_date'])) ?><br>
                                    <strong>Tanggal Selesai:</strong> <?= date('d-m-Y', strtotime($event['end_date'])) ?>
                                </p>
                                <p class="card-text text-dark">
                                    <strong>Biaya Registrasi:</strong> Rp. <?= number_format($event['biaya_registrasi'], 0, ',', '.') ?>,-
                                </p>
                                <p class="card-text text-dark">
                                    <strong>Status:</strong> 
                                    <?php if ($event['status'] === 'active'): ?>
                                        <span class="badge badge-success">Aktif</span>
                                    <?php elseif ($event['status'] === 'completed'): ?>
                                        <span class="badge badge-secondary">Selesai</span>
                                    <?php endif; ?>
                                </p>
                                <div class="text-center mt-3">
                                    <?php if ($event['id_user'] == session()->get('id')): ?>
                                        <?php if ($event['status'] === 'active'): ?>
                                            <a href="<?= base_url('event/edit/' . $event['id']) ?>" class="btn btn-warning">Edit</a>
                                        <?php elseif ($event['status'] === 'completed'): ?>
                                            <button class="btn btn-warning btn-upload-pemenang" data-toggle="modal" data-target="#modalUploadPemenang" data-event-id="<?= $event['id'] ?>">Upload Pemenang</button>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <a href="<?= base_url('uploads/file-event/' . strtolower(str_replace(' ', '-', $event['name'])) . '/' . $event['event_pemenang']) ?>" 
                                            class="btn btn-success" 
                                            target="_blank">
                                                Unduh Pemenang
                                        </a>
                                    <?php endif; ?>
                                    <button class="btn btn-info btn-lihat-detail" data-toggle="modal" data-target="#modalDetail" 
                                        data-event-id="<?= $event['id'] ?>" 
                                        data-event-image="<?= base_url('uploads/file-event/' . strtolower(str_replace(' ', '-', $event['name'])) . '/' . $event['event_image']) ?>"
                                        data-event-name="<?= $event['name'] ?>" 
                                        data-event-description="<?= $event['description'] ?>" 
                                        data-event-lokasi="<?= $event['lokasi'] ?>" 
                                        data-event-start-date="<?= $event['start_date'] ?>" 
                                        data-event-end-date="<?= $event['end_date'] ?>" 
                                        data-event-proposal="<?= base_url('uploads/file-event/' . strtolower(str_replace(' ', '-', $event['name'])) . '/' . $event['event_proposal']) ?>"
                                        data-event-status="<?= $event['status'] ?>">
                                        Lihat Detail
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="col-lg-12">
                    <div class="alert alert-info" role="alert">
                        Tidak ada event yang tersedia.
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Modal Detail Event (Modal Besar) -->
<div class="modal fade" id="modalDetail" tabindex="-1" role="dialog" aria-labelledby="modalDetailLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document"> <!-- Modal besar -->
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalDetailLabel">Detail Event</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-4">
                    <img id="detailImage" src="" alt="Event Image" class="img-fluid" style="max-height: 400px; object-fit: cover;">
                </div>
                <p><strong>Nama Event:</strong> <span id="detailName"></span></p>
                <p><strong>Deskripsi:</strong> <span id="detailDescription"></span></p>
                <p><strong>Lokasi:</strong> <span id="detailLocation"></span></p>
                <p><strong>Tanggal Mulai:</strong> <span id="detailStartDate"></span></p>
                <p><strong>Tanggal Selesai:</strong> <span id="detailEndDate"></span></p>
                <strong>Status:</strong> 
                                    
            </div>
            <div class="modal-footer">
                <!-- Tombol Unduh Proposal di sebelah kiri -->
                <a class="btn btn-primary mr-auto" id="detailProposal" href="#" target="_blank">Unduh Proposal</a>
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Upload Pemenang -->
<div class="modal fade" id="modalUploadPemenang" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Upload Pemenang</h5>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <form id="uploadPemenangForm" action="<?= base_url('event/upload') ?>" method="post" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" name="id" id="eventId">
                    <div class="form-group">
                        <label>File Pemenang (PDF)</label>
                        <input type="file" class="form-control-file" name="event_pemenang" accept="application/pdf" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Upload</button>
                </div>
            </form>
        </div>
    </div>
</div>


<script src="<?= base_url('assets-be/js/jquery.min.js') ?>"></script>
<script src="<?= base_url('assets-be/js/bootstrap.bundle.min.js') ?>"></script>
<script>
    // Set nilai detail event saat tombol "Lihat Detail" ditekan
    document.querySelectorAll('.btn-lihat-detail').forEach(button => {
        button.addEventListener('click', function () {
            const eventId = this.getAttribute('data-event-id');
            const eventName = this.getAttribute('data-event-name');
            const eventDescription = this.getAttribute('data-event-description');
            const eventLokasi = this.getAttribute('data-event-lokasi');
            const eventStartDate = this.getAttribute('data-event-start-date');
            const eventEndDate = this.getAttribute('data-event-end-date');
            const eventProposal = this.getAttribute('data-event-proposal');
            const eventStatus = this.getAttribute('data-event-status');
            const eventImage = this.getAttribute('data-event-image');
            
            // Isi detail event ke modal
            document.getElementById('detailName').textContent = eventName;
            document.getElementById('detailDescription').textContent = this.getAttribute('data-event-description');
            document.getElementById('detailLocation').textContent = this.getAttribute('data-event-lokasi');
            document.getElementById('detailStartDate').textContent = this.getAttribute('data-event-start-date');
            document.getElementById('detailEndDate').textContent = this.getAttribute('data-event-end-date');
            document.getElementById('detailStatus').textContent = eventStatus;
            document.getElementById('detailProposal').href = this.getAttribute('data-event-proposal');
            document.getElementById('detailImage').src = `<?= base_url('uploads/file-event/') ?>${eventName.toLowerCase().replace(/ /g, '-')}/${eventImage}`;
        });
    });
    
    $('#modalUploadPemenang').on('show.bs.modal', function (e) {
        // Ambil nilai ID dari atribut data-id pada tombol
        var id = $(e.relatedTarget).data('id'); 
        console.log("Event ID yang diambil:", id);
        // Set nilai input tersembunyi
        $('#eventId').val(id);
        // Set action form secara dinamis
        $('#uploadPemenangForm').attr('action', '<?= base_url('event/upload') ?>/' + id);
    });
</script>
<?= $this->endSection(); ?>