@echo off
echo Stopping BAJA Development Environment...
echo.

echo Killing processes on port 3000 (Frontend)...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :3000') do (
    echo Killing PID %%a
    taskkill /f /pid %%a 2>nul
)

echo.
echo Killing processes on port 5000 (Backend)...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :5000') do (
    echo Killing PID %%a
    taskkill /f /pid %%a 2>nul
)

echo.
echo Development environment stopped!
pause
