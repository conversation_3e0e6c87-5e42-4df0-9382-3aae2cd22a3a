<?= $this->extend('layouts/backend/template') ?>
<?= $this->section('content') ?>

<div id="content">
    <div class="container-fluid">
        <!-- Page Heading -->
        <h1 class="h3 mb-2 text-gray-800">Pendaftaran Atlet</h1>
        <?php if (session('success')): ?>
                <div class="alert alert-success"><?= session('success') ?></div>
            <?php endif ?>
            
            <?php if (session('error')): ?>
                <div class="alert alert-danger"><?= session('error') ?></div>
            <?php endif ?>
        <?php if (!empty($events)): ?>
            <div class="row">
                <?php foreach ($events as $event): ?>
                    <div class="col-lg-4">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary"><?= $event['name'] ?></h6>
                            </div>
                            <div class="card-body">
                            <div class="text-center mb-4">
                                    <?php if ($event['event_image']): ?>
                                        <img src="<?= base_url('uploads/file-event/' . strtolower(str_replace(' ', '-', $event['name'])) . '/' . $event['event_image']) ?>" 
                                             alt="Event Image" 
                                             class="img-fluid event-image" 
                                             style="max-height: 200px; width: 100%; object-fit: cover;">
                                    <?php else: ?>
                                        <p>Tidak ada gambar.</p>
                                    <?php endif; ?>
                                </div>
                                <h5 class="card-title text-primary"><?= $event['name'] ?></h5>
                                <p class="card-text text-dark">
                                    <strong>Deskripsi:</strong> <?= $event['description'] ?>
                                </p>
                                <p class="card-text text-dark">
                                    <strong>Tanggal Mulai:</strong> <?= date('d-m-Y', strtotime($event['start_date'])) ?><br>
                                    <strong>Tanggal Selesai:</strong> <?= date('d-m-Y', strtotime($event['end_date'])) ?>
                                </p>
                                <p class="card-text text-dark">
                                    <strong>Biaya Registrasi:</strong> Rp. <?= number_format($event['biaya_registrasi'], 0, ',', '.') ?>,-
                                </p>
                                <p class="card-text text-dark">
                                    <strong>Status:</strong> 
                                    <?php if ($event['status'] === 'active'): ?>
                                        <span class="badge badge-success">Aktif</span>
                                    <?php elseif ($event['status'] === 'completed'): ?>
                                        <span class="badge badge-secondary">Selesai</span>
                                    <?php endif; ?>
                                </p>
                                <?php if ($event['status'] === 'active'): ?>
                                <a href="<?= base_url('pendaftaran-atlet/detail/'. $event['id'])?>" class="btn btn-primary btn-block">Detail</a>
                                <?php else: ?>
                                    <?php if ($event['status'] === 'completed' && $event['event_pemenang']): ?>
                                        <a href="<?= base_url('uploads/file-event/' . strtolower(str_replace(' ', '-', $event['name'])) . '/' . $event['event_pemenang']) ?>" 
                                            class="btn btn-success btn-block" 
                                            target="_blank">
                                                Unduh Pemenang
                                        </a>
                                    <?php elseif ($event['status'] === 'completed'): ?>
                                        <button class="btn btn-secondary btn-block" disabled>
                                            Event Selesai
                                        </button>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div class="col-lg-12">
                <div class="alert alert-info" role="alert">
                    Tidak ada event yang terdaftar.
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<?= $this->endSection(); ?>