const { createPopper } = window.Popper;

const NavItem = {
    inheritAttrs: false,
    props: {
        meta: {
            type: Object,
            default: () => ({})
        },
        params: {
            type: Object,
            default: () => ({})
        },
        defaultClass: {
            type: String,
            default: "nav-item"
        },
        name: String,
        strictMatch: <PERSON>olean,
        preventDefault: Boolean
    },
    computed: {
        isActive() {
            const { name = "" } = this.$route;
            return this.strictMatch
                ? name === this.name
                : !!name.startsWith(this.name);
        },
        routeTo() {
            const { resolved } = this.$router.resolve({ name: this.name });
            return resolved.matched.length
                ? { name: this.name, params: this.params }
                : {};
        }
    },
    watch: {
        isActive: {
            immediate: true,
            handler(isActive) {
                if (isActive) {
                    this.$nextTick(() => this.$emit("change", this.$el));
                }
            }
        }
    },
    template: `
        <router-link
            :event="preventDefault ? '' : 'click'"
            :to="routeTo"
            v-on="$listeners"
            :class="[defaultClass, { [defaultClass + '-active']: isActive }]"
        >
            <slot />
        </router-link>
    `
};

const NavContainer = {
    props: {
        depth: {
            type: Number,
            default: 0,
            validator(value) {
                return [0, 1].includes(value);
            }
        }
    },

    data() {
        return {
            activeEl: null,
            indicator: {
                offsetY: 0,
                offsetX: 0,
                scaleX: 1
            }
        };
    },

    computed: {
        routes() {
            return this.$router.options.routes;
        },

        childRoutes() {
            if (!this.depth) return [];

            return (
                this.routes.find(
                    (route) => route.path === this.$route.matched[0].path
                )?.children || []
            );
        },

        computedRoutes() {
            const routes = this.depth ? this.childRoutes : this.routes;

            return routes
                .filter(({ meta = {} }) => !!meta.position && meta.navigable)
                .sort((a, b) => a.meta.position - b.meta.position);
        }
    },
    methods: {
        setIndicatorRef(el) {
            this.activeEl = el;
            this.updateIndicator();
        },
        updateIndicator() {
            if (!this.activeEl) return;
            // parent element must be relative or absolute
            const baseWidth = 100;
            this.indicator.offsetY = this.activeEl.offsetTop;
            this.indicator.offsetX = this.activeEl.offsetLeft;
            this.indicator.scaleX = this.activeEl.offsetWidth / baseWidth;
        }
    },
    mounted() {
        window.addEventListener("resize", this.updateIndicator);
    },
    beforeDestory() {
        window.removeEventListener("resize", this.updateIndicator);
    },
    render() {
        const { computedRoutes, indicator, setIndicatorRef } = this;
        return this.$scopedSlots.default({
            routes: computedRoutes,
            indicator,
            setIndicatorRef
        });
    }
};

const NavIndicator = {
    name: "NavIndicator",
    functional: true,
    props: {
        offsetX: {
            type: Number,
            default: 0
        },
        offsetY: {
            type: Number,
            default: 0
        },
        scaleX: {
            type: Number,
            default: 1
        },
        horizontal: Boolean,
        vertical: Boolean
    },
    render(h, { props, data }) {
        return h("div", {
            class: [
                "nav-indicator",
                { "nav-indicator-vertical": props.vertical },
                { "nav-indicator-horizontal": props.horizontal }
            ],
            style: {
                ...data.style,
                transform: `translate3d(${props.offsetX}px, ${props.offsetY}px, 0) scale3d(${props.scaleX}, 1, 1)`
            }
        });
    }
};

const defaultOptions = {
    placement: "right"
};
const defaultModifiers = [
    {
        name: "offset",
        options: {
            offset: [8, 8]
        }
    },
    {
        name: "preventOverflow",
        options: {
            padding: 16
        }
    }
];
const PopperContainer = {
    props: {
        options: {
            type: Object,
            default: () => ({})
        },
        modifiers: {
            type: Array,
            default: () => []
        },
        mouseEnterDelay: {
            type: Number,
            default: 100
        },
        mouseLeaveDelay: {
            type: Number,
            default: 300
        },
        hoverable: Boolean
    },
    data() {
        return {
            isActive: false,
            activatorRef: null,
            instance: null,
            timer: null
        };
    },
    computed: {
        computedOptions() {
            const computedModifiers = [
                ...defaultModifiers,
                {
                    name: "arrow",
                    options: {
                        element: this.$refs.arrow,
                        padding: 16
                    }
                },
                ...this.modifiers
            ];
            return {
                ...defaultOptions,
                onFirstUpdate: () => {
                    this.$emit("created");
                    this.$nextTick(this.updatePopper);
                },
                ...this.options,
                modifiers: computedModifiers
            };
        },
        listeners() {
            return (
                this.hoverable && {
                    mouseenter: this.onMouseEnter,
                    mouseleave: this.onMouseLeave
                }
            );
        }
    },
    watch: {
        isActive(isActive) {
            if (isActive) {
                this.$emit("show");
                this.updatePopper();
            } else {
                this.$emit("hide");
            }
        }
    },
    mounted() {
        document.addEventListener("click", this.handleClickaway);
    },
    beforeDestroy() {
        this.destroyPopper();
        document.removeEventListener("click", this.handleClickaway);
    },
    methods: {
        toggle({ target }) {
            if (!this.activatorRef) this.activatorRef = target;
            this.isActive = !this.isActive;
        },
        open({ target }) {
            if (!this.activatorRef) this.activatorRef = target;
            this.isActive = true;
        },
        close() {
            this.isActive = false;
        },
        createPopper() {
            this.$nextTick(() => {
                if (this.instance?.destroy) this.instance.destroy();

                this.instance = createPopper(
                    this.activatorRef,
                    this.$refs.popper,
                    this.computedOptions
                );
            });
        },
        updatePopper() {
            this.instance ? this.instance.forceUpdate() : this.createPopper();
        },
        destroyPopper() {
            if (!this.instance) return;

            this.instance.destroy();
            this.instance = null;
        },
        onMouseEnter(evt) {
            clearTimeout(this.timer);
            this.timer = setTimeout(() => {
                this.open(evt);
            }, this.mouseEnterDelay);
        },
        onMouseLeave(evt) {
            clearTimeout(this.timer);
            this.timer = setTimeout(() => {
                this.close(evt);
            }, this.mouseLeaveDelay);
        },
        handleClickaway({ target }) {
            if (this.$el.contains(target)) return;

            this.isActive = false;
        }
    },
    template: `
        <div class="popper-container" v-on="listeners">
            <transition name="v-popper" @after-leave="destroyPopper">
                <div ref="popper" v-show="isActive" class="popper">
                    <div class="popper-inner">
                        <slot name="content" v-bind="{ toggle, open, close }" />
                        <div ref="arrow" class="popper-arrow"></div>  
                    </div>
                </div>
            </transition>

            <slot name="activator" v-bind="{ toggle, open, close }" />
        </div>
    `
};

const NavBar = {
    components: {
        NavContainer,
        NavItem,
        NavIndicator,
        PopperContainer
    },
    template: `
        <div class="nav-bar">
            <div class="nav-block logo mt-4 mb-8 text-white fas fa-dragon"></div>

            <nav-container>
                <template v-slot="{ routes, indicator, setIndicatorRef }">
                    <div class="nav-bar-content">
                        <nav>
                            <nav-item 
                                v-for="item in routes" 
                                :key="item.name" 
                                class="nav-block" 
                                v-bind="item" 
                                @change="setIndicatorRef" 
                            >
                                <i v-if="item.meta.icon" :class="['icon fas text-white', item.meta.icon]"></i>
                                <i v-if="item.path === '/projects' && !$route.path.startsWith('/projects')" class="alert-badge">5</i>
                                <i v-else-if="item.path === '/assets' && !$route.path.startsWith('/assets')" class="alert-badge">1</i>
                            </nav-item>
                        </nav>

                        <popper-container hoverable class="mt-8 mb-4">
                            <template #content="{ close }">
                                <p class="text-lg mb-3 whitespace-no-wrap">Hello, Samuel</p>
                                <p class="py-2">Profile</p>
                                <nav-item 
                                    name="settings" 
                                    class="py-2"
                                    default-class="popper-item"
                                    @change="setIndicatorRef($refs.avatar.$el)"
                                    @click.native="close"
                                >
                                    Settings
                                </nav-item>
                                <p class="py-2">Help</p>
                                <p class="py-2">Log out</p>
                            </template>

                            <template #activator>
                                <nav-item 
                                    ref="avatar" 
                                    name="settings" 
                                    preventDefault
                                    class="nav-block" 
                                >
                                    <div class="avatar cursor-pointer"></div>
                                </nav-item>
                            </template>
                        </popper-container>

                        <nav-indicator vertical :offset-y="indicator.offsetY" />
                    </div>
                </template>
            </nav-container>

        </div>
    `
};

const ToolBar = {
    components: {
        NavContainer,
        NavIndicator,
        NavItem
    },
    template: `
        <nav-container :depth="1">
            <template v-slot="{ routes, indicator, setIndicatorRef }">
                <div class="tool-bar">
                    <template v-if="routes.length">
                        <nav>
                            <nav-item 
                                v-for="item in routes"
                                :key="'nav-tab-' + item.name"
                                v-bind="item"
                                default-class="nav-tab"
                                strictMatch
                                @change="setIndicatorRef" 
                            >
                                {{ item.meta.label }}
                            </nav-item>

                            <nav-indicator
                                horizontal 
                                :offset-x="indicator.offsetX" 
                                :scale-x="indicator.scaleX" 
                            />
                        </nav>

                    </template>

                    <div class="tool-bar-append"><slot name="append" /></div>
                </div>
            </template>
        </nav-container>
    `
};

const ToolBarLayout = {
    components: {
        ToolBar
    },
    template: `
        <div class="flex flex-col flex-grow">
            <tool-bar>
                <template #append>
                    <portal-target name="toolbar" />
                </template>
            </tool-bar>
            <router-view />
        </div>
`
};

const DemoPage = {
    methods: {
        showAlert() {
            alert("Using a portal to add dynamic content to ToolBar");
        }
    },
    template: `
        <div class="grid grid-cols-3 gap-4 p-4 flex-grow">
            <portal to="toolbar">
                <button 
                    type="button"
                    class="bg-white hover:bg-gray-100 text-gray-800 font-semibold py-2 px-4 rounded"
                    @click="showAlert"
                >
                    {{ $route.matched[0].meta.label }}
                </button>
            </portal>

            <div class="col-span-3 p-4 bg-gray-300">
                <div class="text-4xl">{{ $route.name }}</div>  
            </div>

            <div class="col-span-2 bg-gray-200"></div>
            <div class="col-span-1 bg-gray-300"></div>
            <div class="col-span-1 bg-gray-200"></div>
            <div class="col-span-2 bg-gray-100"></div>
        </div>
    `
};

new Vue({
    el: "#app",
    router: new VueRouter({
        routes: [
            {
                path: "*",
                redirect: { name: "chat" }
            },
            {
                path: "/chat",
                name: "chat",
                meta: {
                    icon: "fa-comment",
                    navigable: true,
                    position: 1,
                    label: "Chat"
                },
                component: DemoPage
            },
            {
                path: "/timer",
                name: "timer",
                meta: {
                    icon: "fa-stopwatch",
                    navigable: true,
                    position: 2,
                    label: "Timer"
                },
                component: DemoPage
            },
            {
                path: "/tasks",
                name: "tasks",
                meta: {
                    icon: "fa-tasks",
                    navigable: true,
                    position: 3,
                    label: "Tasks"
                },
                component: DemoPage
            },
            {
                path: "/team",
                name: "team",
                meta: {
                    icon: "fa-users",
                    navigable: true,
                    position: 4,
                    label: "Team"
                },
                component: DemoPage
            },
            {
                path: "/projects",
                name: "projects",
                component: ToolBarLayout,
                meta: {
                    icon: "fa-project-diagram",
                    navigable: true,
                    position: 5,
                    label: "Projects"
                },
                // redirect: { name: 'projects.list' }
                children: [
                    {
                        path: "",
                        // VueRouter might cry here about duplicate names when using a bundler like Webpack -
                        // I've solved it by using the commented code instead.
                        name: "projects",
                        // name: "projects.list",
                        meta: {
                            navigable: true,
                            position: 1,
                            label: "Projects"
                        },
                        component: DemoPage
                    },
                    {
                        path: "/projects/work",
                        name: "projects.work",
                        meta: { navigable: true, position: 2, label: "Work" },
                        component: DemoPage
                    },
                    {
                        path: "/projects/documents",
                        name: "projects.documents",
                        meta: {
                            navigable: true,
                            position: 3,
                            label: "Documents"
                        },
                        component: DemoPage
                    },
                    {
                        path: "/projects/gallery",
                        name: "projects.gallery",
                        meta: {
                            navigable: true,
                            position: 4,
                            label: "Gallery"
                        },
                        component: DemoPage
                    }
                ]
            },
            {
                path: "/assets",
                name: "assets",
                meta: {
                    icon: "fa-cubes",
                    navigable: true,
                    position: 6,
                    label: "Assets"
                },
                component: ToolBarLayout,
                children: [
                    {
                        path: "",
                        name: "assets",
                        meta: {
                            navigable: true,
                            position: 1,
                            label: "Assets"
                        },
                        component: DemoPage
                    },
                    {
                        path: "/assets/reports",
                        name: "assets.reports",
                        meta: {
                            navigable: true,
                            position: 2,
                            label: "Reports"
                        },
                        component: DemoPage
                    }
                ]
            },
            {
                path: "/settings",
                name: "settings",
                meta: {},
                component: DemoPage
            }
        ]
    }),
    components: {
        NavBar,
        ToolBar
    }
});
