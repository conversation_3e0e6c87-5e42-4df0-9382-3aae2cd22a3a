import { Response } from 'express';
import { AuthRequest } from '../middleware/auth.middleware';
import User from '../models/User';
import Event from '../models/Event';
import Kontingen from '../models/Kontingen';
import Atlet from '../models/Atlet';
import Official from '../models/Official';
import PendaftaranEvent from '../models/PendaftaranEvent';

export const getDashboardStats = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const user = req.user;
    const userId = user.id;
    const role = user.role;

    let stats = {
      count_event: 0,
      count_kontingen: 0,
      count_atlet: 0,
      count_official: 0,
    };

    if (role === 'admin') {
      // For Admin: count all data
      stats.count_event = await Event.count();
      stats.count_kontingen = await Kontingen.count();
      stats.count_atlet = await Atlet.count();
      stats.count_official = await Official.count();
      
    } else if (role === 'admin-event') {
      // For Admin-Event: only events created by this admin
      stats.count_event = await Event.count({ where: { id_user: userId } });

      // Count kontingen, atlet, official registered to this admin's events
      const eventIds = await Event.findAll({
        where: { id_user: userId },
        attributes: ['id']
      });
      const eventIdList = eventIds.map(event => event.id);

      if (eventIdList.length > 0) {
        const kontingenIds = await PendaftaranEvent.findAll({
          where: { id_event: eventIdList },
          attributes: ['id_kontingen']
        });
        const kontingenIdList = kontingenIds.map(p => p.id_kontingen);

        stats.count_kontingen = kontingenIdList.length;
        stats.count_atlet = await Atlet.count({ where: { id_kontingen: kontingenIdList } });
        stats.count_official = await Official.count({ where: { id_kontingen: kontingenIdList } });
      }
      
    } else if (role === 'ketua-kontingen') {
      // For Ketua Kontingen: their own data
      const kontingen = await Kontingen.findOne({ where: { id_user: userId } });

      if (kontingen) {
        stats.count_kontingen = 1;
        stats.count_event = await PendaftaranEvent.count({ where: { id_kontingen: kontingen.id } });
        stats.count_atlet = await Atlet.count({ where: { id_kontingen: kontingen.id } });
        stats.count_official = await Official.count({ where: { id_kontingen: kontingen.id } });
      }
    }

    res.json({
      success: true,
      message: 'Dashboard stats retrieved successfully',
      data: stats,
    });
  } catch (error) {
    console.error('Get dashboard stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
};
