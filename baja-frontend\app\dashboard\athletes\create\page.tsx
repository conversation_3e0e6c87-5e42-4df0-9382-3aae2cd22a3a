'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';
import { atletService, CreateAtletData } from '@/services/atletService';
import { kontingenService, Kontingen } from '@/services/kontingenService';

export default function CreateAtletPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [kontingenList, setKontingenList] = useState<Kontingen[]>([]);
  
  const [formData, setFormData] = useState<CreateAtletData>({
    nik: '',
    name: '',
    tanggal_lahir: '',
    jenis_kelamin: 'M',
    alamat: '',
    no_hp: '',
    agama: '',
    umur: 0,
    berat_badan: 0,
    tinggi_badan: 0
  });

  useEffect(() => {
    fetchKontingen();
  }, []);

  const fetchKontingen = async () => {
    try {
      const data = await kontingenService.getAllKontingen(1, 100); // Get all kontingen
      setKontingenList(data.kontingen);
    } catch (error: any) {
      toast.error(error.message);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? (value === '' ? 0 : Number(value)) : value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.nik || !formData.name || !formData.tanggal_lahir) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      setLoading(true);
      await atletService.createAtlet(formData);
      toast.success('Athlete created successfully!');
      router.push('/dashboard/athletes');
    } catch (error: any) {
      toast.error(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-black text-white p-6">
      <div className="max-w-2xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-yellow-400 mb-2">Create New Athlete</h1>
          <p className="text-gray-300">Register a new athlete</p>
        </div>

        <div className="bg-gray-900 rounded-lg p-6 border border-yellow-400">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* NIK */}
            <div>
              <label htmlFor="nik" className="block text-sm font-medium text-yellow-400 mb-2">
                NIK (ID Number) *
              </label>
              <input
                type="text"
                id="nik"
                name="nik"
                value={formData.nik}
                onChange={handleInputChange}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-yellow-400"
                placeholder="Enter NIK"
                required
              />
            </div>

            {/* Name */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-yellow-400 mb-2">
                Full Name *
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-yellow-400"
                placeholder="Enter full name"
                required
              />
            </div>

            {/* Birth Date */}
            <div>
              <label htmlFor="tanggal_lahir" className="block text-sm font-medium text-yellow-400 mb-2">
                Birth Date *
              </label>
              <input
                type="date"
                id="tanggal_lahir"
                name="tanggal_lahir"
                value={formData.tanggal_lahir}
                onChange={handleInputChange}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-yellow-400"
                required
              />
            </div>

            {/* Gender */}
            <div>
              <label htmlFor="jenis_kelamin" className="block text-sm font-medium text-yellow-400 mb-2">
                Gender *
              </label>
              <select
                id="jenis_kelamin"
                name="jenis_kelamin"
                value={formData.jenis_kelamin}
                onChange={handleInputChange}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-yellow-400"
                required
              >
                <option value="M">Male</option>
                <option value="F">Female</option>
              </select>
            </div>

            {/* Kontingen (for admin only) */}
            {kontingenList.length > 0 && (
              <div>
                <label htmlFor="id_kontingen" className="block text-sm font-medium text-yellow-400 mb-2">
                  Kontingen
                </label>
                <select
                  id="id_kontingen"
                  name="id_kontingen"
                  value={formData.id_kontingen || ''}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-yellow-400"
                >
                  <option value="">Select Kontingen (Optional for Ketua Kontingen)</option>
                  {kontingenList.map((kontingen) => (
                    <option key={kontingen.id} value={kontingen.id}>
                      {kontingen.name}
                    </option>
                  ))}
                </select>
              </div>
            )}

            {/* Address */}
            <div>
              <label htmlFor="alamat" className="block text-sm font-medium text-yellow-400 mb-2">
                Address
              </label>
              <textarea
                id="alamat"
                name="alamat"
                value={formData.alamat}
                onChange={handleInputChange}
                rows={3}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-yellow-400"
                placeholder="Enter address"
              />
            </div>

            {/* Phone */}
            <div>
              <label htmlFor="no_hp" className="block text-sm font-medium text-yellow-400 mb-2">
                Phone Number
              </label>
              <input
                type="tel"
                id="no_hp"
                name="no_hp"
                value={formData.no_hp}
                onChange={handleInputChange}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-yellow-400"
                placeholder="Enter phone number"
              />
            </div>

            {/* Religion */}
            <div>
              <label htmlFor="agama" className="block text-sm font-medium text-yellow-400 mb-2">
                Religion
              </label>
              <input
                type="text"
                id="agama"
                name="agama"
                value={formData.agama}
                onChange={handleInputChange}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-yellow-400"
                placeholder="Enter religion"
              />
            </div>

            {/* Physical Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label htmlFor="umur" className="block text-sm font-medium text-yellow-400 mb-2">
                  Age
                </label>
                <input
                  type="number"
                  id="umur"
                  name="umur"
                  value={formData.umur || ''}
                  onChange={handleInputChange}
                  min="0"
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-yellow-400"
                  placeholder="Age"
                />
              </div>
              <div>
                <label htmlFor="berat_badan" className="block text-sm font-medium text-yellow-400 mb-2">
                  Weight (kg)
                </label>
                <input
                  type="number"
                  id="berat_badan"
                  name="berat_badan"
                  value={formData.berat_badan || ''}
                  onChange={handleInputChange}
                  min="0"
                  step="0.1"
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-yellow-400"
                  placeholder="Weight"
                />
              </div>
              <div>
                <label htmlFor="tinggi_badan" className="block text-sm font-medium text-yellow-400 mb-2">
                  Height (cm)
                </label>
                <input
                  type="number"
                  id="tinggi_badan"
                  name="tinggi_badan"
                  value={formData.tinggi_badan || ''}
                  onChange={handleInputChange}
                  min="0"
                  step="0.1"
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-yellow-400"
                  placeholder="Height"
                />
              </div>
            </div>

            {/* Submit Buttons */}
            <div className="flex gap-4 pt-4">
              <button
                type="button"
                onClick={() => router.back()}
                className="flex-1 px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
                disabled={loading}
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className="flex-1 px-4 py-2 bg-yellow-600 text-black font-semibold rounded-md hover:bg-yellow-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Creating...' : 'Create Athlete'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
