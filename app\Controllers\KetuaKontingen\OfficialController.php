<?php

namespace App\Controllers\KetuaKontingen;

use App\Controllers\BaseController;
use App\Models\Official;
use App\Models\Kontingen;

class OfficialController extends BaseController
{
    public function index()
    {
        $userId = session()->get('id');
        if (!$userId) {
            return redirect()->to('/login')->with('error', 'Anda harus login terlebih dahulu.');
        }

        $kontingenModel = new Kontingen();
        $officialModel  = new Official();

        // Ambil data kontingen milik ketua-kontingen
        $kontingenData = $kontingenModel->where('id_user', $userId)->first();
        $data['kontingen'] = $kontingenData;
        
        if ($kontingenData) {
            // Ambil data official berdasarkan ID kontingen dengan join (untuk menampilkan nama kontingen)
            $data['official'] = $officialModel
                ->select('official.*, kontingen.name as kontingen_name')
                ->join('kontingen', 'kontingen.id = official.id_kontingen', 'left')
                ->where('official.id_kontingen', $kontingenData['id'])
                ->findAll();
        } else {
            $data['official'] = [];
        }

        return view('ketua-kontingen/official/index', $data);
    }

    public function create()
    {
        return view('ketua-kontingen/official/create');
    }

    public function store()
    {
        $userId = session()->get('id');
        if (!$userId) {
            return redirect()->to('/login')->with('error', 'Anda harus login terlebih dahulu.');
        }
        
        $kontingenModel = new Kontingen();
        $officialModel  = new Official();

        // Ambil data kontingen milik ketua-kontingen
        $kontingenData = $kontingenModel->where('id_user', $userId)->first();
        if (!$kontingenData) {
            return redirect()->back()->with('error', 'Data kontingen tidak ditemukan.');
        }

        // Validasi input
        $validationRules = [
            'name'           => 'required',
            'no_hp'          => 'required|numeric',
            'alamat'         => 'required',
            'agama'          => 'required',
            'jenis_kelamin'  => 'required',
            'file'           => 'uploaded[file]|max_size[file,2048]|ext_in[file,jpg,jpeg,png]',
        ];

        if (!$this->validate($validationRules)) {
            return redirect()->back()
                ->withInput()
                ->with('errors', $this->validator->getErrors());
        }

        // Upload file foto profil
        $file = $this->request->getFile('file');
        $profilePictureName = null;
        if ($file->isValid() && !$file->hasMoved()) {
            $profilePictureName = $file->getRandomName();
            if (!$file->move(ROOTPATH . 'public/uploads/file-profile', $profilePictureName)) {
                return redirect()->back()->with('error', 'Gagal menyimpan file foto profil.');
            }
        }

        // Siapkan data untuk disimpan
        $dataInsert = [
            'profile'       => $profilePictureName,
            'name'          => trim($this->request->getPost('name')),
            'no_hp'         => $this->request->getPost('no_hp'),
            'alamat'        => $this->request->getPost('alamat'),
            'agama'         => $this->request->getPost('agama'),
            'jenis_kelamin' => $this->request->getPost('jenis_kelamin'),
            'id_kontingen'  => $kontingenData['id'],
        ];

        if (!$officialModel->insert($dataInsert)) {
            return redirect()->back()->with('error', 'Gagal menyimpan data official.');
        }

        return redirect()->to('/official')->with('success', 'Data official berhasil disimpan.');
    }

    public function edit($id)
    {
        $officialModel = new Official();
        $data['official'] = $officialModel->find($id);
        if (!$data['official']) {
            return redirect()->to('/official')->with('error', 'Data official tidak ditemukan.');
        }
        return view('ketua-kontingen/official/edit', $data);
    }

    public function update($id)
    {
        $officialModel = new Official();

        // Validasi input
        $validationRules = [
            'name'           => 'required',
            'no_hp'          => 'required|numeric',
            'alamat'         => 'required',
            'agama'          => 'required',
            'jenis_kelamin'  => 'required',
            'file'           => 'uploaded[file]|max_size[file,2048]|ext_in[file,jpg,jpeg,png]',
        ];

        if (!$this->validate($validationRules)) {
            return redirect()->back()
                ->withInput()
                ->with('errors', $this->validator->getErrors());
        }

        $file = $this->request->getFile('file');
        $profilePictureName = null;
        if ($file->isValid() && !$file->hasMoved()) {
            $profilePictureName = $file->getRandomName();
            if (!$file->move(ROOTPATH . 'public/uploads/file-profile', $profilePictureName)) {
                return redirect()->back()->with('error', 'Gagal menyimpan file foto profil.');
            }
        }

        $dataUpdate = [
            'profile'       => $profilePictureName,
            'name'          => trim($this->request->getPost('name')),
            'no_hp'         => $this->request->getPost('no_hp'),
            'alamat'        => $this->request->getPost('alamat'),
            'agama'         => $this->request->getPost('agama'),
            'jenis_kelamin' => $this->request->getPost('jenis_kelamin'),
        ];

        if (!$officialModel->update($dataUpdate)) {
            return redirect()->back()->with('error', 'Gagal menyimpan data official.');
        }

        return redirect()->to('/official')->with('success', 'Data official berhasil diperbarui.');
    }

    public function delete($id)
    {
        $officialModel = new Official();
        if (!$officialModel->find($id)) {
            return redirect()->to('/official')->with('error', 'Official tidak ditemukan.');
        }

        if (!$officialModel->delete($id)) {
            return redirect()->to('/official')->with('error', 'Gagal menghapus user.');
        }

        return redirect()->to('/official')->with('success', 'Official berhasil dihapus.');
    }
}
