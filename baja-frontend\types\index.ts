export interface User {
  id: number;
  profile?: string;
  name: string;
  email: string;
  no_hp?: string;
  alamat?: string;
  agama?: string;
  role: 'admin' | 'admin-event' | 'ketua-kontingen' | 'user';
  status: '0' | '1';
  created_at: string;
  updated_at: string;
}

export interface Event {
  id: number;
  name: string;
  description?: string;
  start_date: string;
  end_date: string;
  lokasi: string;
  biaya_registrasi: number;
  metode_pembayaran: string;
  status: 'active' | 'completed';
  event_image?: string;
  event_proposal?: string;
  event_pemenang?: string;
  id_user: number;
  user?: User;
  created_at: string;
  updated_at: string;
}

export interface Atlet {
  id: number;
  nik: string;
  name: string;
  no_hp?: string;
  tanggal_lahir: string;
  jenis_kelamin: 'L' | 'P';
  agama?: string;
  alamat?: string;
  umur: number;
  berat_badan: string;
  tinggi_badan: string;
  status_verifikasi: 'pending' | 'verified';
  foto?: string;
  id_user: number;
  id_kontingen: number;
  kontingen?: Kontingen;
  files?: AtletFile[];
  created_at: string;
  updated_at: string;
}

export interface AtletFile {
  id: number;
  file: string;
  file_type: 'rapor' | 'kk_ktp' | 'surat_kesehatan';
  id_atlet: number;
  created_at: string;
  updated_at: string;
}

export interface Kontingen {
  id: number;
  name: string;
  alamat?: string;
  no_hp?: string;
  email?: string;
  id_user: number;
  user?: User;
  created_at: string;
  updated_at: string;
}

export interface Official {
  id: number;
  nik: string;
  name: string;
  no_hp?: string;
  tanggal_lahir: string;
  jenis_kelamin: 'L' | 'P';
  agama?: string;
  alamat?: string;
  jabatan: string;
  id_user: number;
  id_kontingen: number;
  kontingen?: Kontingen;
  created_at: string;
  updated_at: string;
}

export interface Paket {
  id: number;
  name: string;
  images: string;
  description?: string;
  is_popular?: boolean;
  is_featured?: boolean;
  featured_order?: number;
  created_at: string;
  updated_at: string;
}

export interface Gallery {
  id: number;
  images: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

export interface PendaftaranEvent {
  id: number;
  id_event: number;
  id_kontingen: number;
  status: 'pending' | 'approved' | 'rejected';
  event?: Event;
  kontingen?: Kontingen;
  created_at: string;
  updated_at: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  errors?: any[];
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface DashboardStats {
  count_event: number;
  count_kontingen: number;
  count_atlet: number;
  count_official: number;
}

// Admin Management Types
export interface AdminUser extends User {
  created_at: string;
  updated_at: string;
}

export interface AdminEvent extends Event {
  eventUser: {
    id: number;
    name: string;
    email: string;
  };
  created_at: string;
  updated_at: string;
}

export interface AdminPackage extends Paket {
  created_at: string;
  updated_at: string;
}

export interface AdminGallery extends Gallery {
  created_at: string;
  updated_at: string;
}

export interface PaginationData {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface AdminUsersResponse {
  users: AdminUser[];
  pagination: PaginationData;
}

export interface AdminEventsResponse {
  events: AdminEvent[];
  pagination: PaginationData;
}

export interface AdminPackagesResponse {
  packages: AdminPackage[];
  pagination: PaginationData;
}

export interface AdminGalleryResponse {
  gallery: AdminGallery[];
  pagination: PaginationData;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  name: string;
  email: string;
  password: string;
  no_hp?: string;
  alamat?: string;
  agama?: string;
  role?: 'admin' | 'admin-event' | 'ketua-kontingen';
}

export interface AuthResponse {
  user: User;
  token: string;
  event_id?: number;
}
