'use client';

import React, { useState, useEffect } from 'react';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Badge from '@/components/ui/Badge';
import EventDetailModal from '@/components/modals/EventDetailModal';
import { Event } from '@/types';
import { formatDate, formatCurrency, getStatusColor } from '@/lib/utils';
import { uploadService } from '@/lib/upload.service';
import { CalendarDaysIcon, MapPinIcon, CurrencyDollarIcon } from '@heroicons/react/24/outline';
import api from '@/lib/api';

const EventsPage = () => {
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<string>('all');
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);

  useEffect(() => {
    const fetchEvents = async () => {
      try {
        const response = await api.get('/events');
        if (response.data.success && response.data.data && response.data.data.events) {
          setEvents(response.data.data.events);
        } else {
          console.error('Failed to fetch events:', response.data.message);
          setEvents([]);
        }
      } catch (error) {
        console.error('Error fetching events:', error);
        setEvents([]);
      } finally {
        setLoading(false);
      }
    };

    fetchEvents();
  }, []);

  const filteredEvents = events.filter(event => {
    if (filter === 'all') return true;
    return event.status === filter;
  });

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'completed':
        return 'secondary';
      default:
        return 'default';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'active':
        return 'Aktif';
      case 'completed':
        return 'Selesai';
      default:
        return status;
    }
  };

  const handleViewEvent = (event: Event) => {
    setSelectedEvent(event);
    setShowDetailModal(true);
  };

  return (
    <div className="min-h-screen bg-black">
      <Navbar />
      
      <div className="pt-16">
        {/* Header */}
        <div className="bg-gradient-to-r from-black via-gray-900 to-black border-b border-yellow-500/30">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <h1 className="text-3xl font-bold text-white">
              Event Olahraga
            </h1>
            <p className="mt-2 text-gray-300">
              Temukan dan ikuti berbagai event olahraga bela diri
            </p>
          </div>
        </div>

        {/* Filters */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-wrap gap-2">
            <button
              className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                filter === 'all' 
                  ? 'bg-yellow-500 text-black shadow-lg shadow-yellow-500/25' 
                  : 'bg-gray-800 text-white border border-gray-700 hover:bg-gray-700 hover:border-yellow-500/50'
              }`}
              onClick={() => setFilter('all')}
            >
              Semua
            </button>
            <button
              className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                filter === 'active' 
                  ? 'bg-yellow-500 text-black shadow-lg shadow-yellow-500/25' 
                  : 'bg-gray-800 text-white border border-gray-700 hover:bg-gray-700 hover:border-yellow-500/50'
              }`}
              onClick={() => setFilter('active')}
            >
              Aktif
            </button>
            <button
              className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                filter === 'completed' 
                  ? 'bg-yellow-500 text-black shadow-lg shadow-yellow-500/25' 
                  : 'bg-gray-800 text-white border border-gray-700 hover:bg-gray-700 hover:border-yellow-500/50'
              }`}
              onClick={() => setFilter('completed')}
            >
              Selesai
            </button>
          </div>
        </div>

        {/* Events Grid */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12">
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, index) => (
                <div key={index} className="bg-gray-900 rounded-lg border border-gray-800 animate-pulse">
                  <div className="aspect-[3/4] bg-gray-800 rounded-t-lg"></div>
                  <div className="p-6">
                    <div className="h-4 bg-gray-800 rounded mb-2"></div>
                    <div className="h-3 bg-gray-800 rounded mb-4"></div>
                    <div className="space-y-2">
                      <div className="h-3 bg-gray-800 rounded"></div>
                      <div className="h-3 bg-gray-800 rounded"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : filteredEvents.length === 0 ? (
            <div className="text-center py-12">
              <CalendarDaysIcon className="mx-auto h-12 w-12 text-gray-600" />
              <h3 className="mt-2 text-sm font-medium text-white">Tidak ada event</h3>
              <p className="mt-1 text-sm text-gray-400">
                Belum ada event yang tersedia saat ini.
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredEvents.map((event) => (
                <div key={event.id} className="bg-gray-900 rounded-lg border border-gray-800 hover:border-yellow-500/50 hover:shadow-xl hover:shadow-yellow-500/10 transition-all duration-300 group">
                  <div className="relative">
                    <img
                      src={event.event_image ? uploadService.getOptimizedUrl(event.event_image) : '/placeholder-event.jpg'}
                      alt={event.name}
                      className="w-full object-cover rounded-t-lg aspect-[3/4]"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = '/placeholder-event.jpg';
                      }}
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent rounded-t-lg"></div>
                    <div className="absolute top-4 right-4">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        event.status === 'active' 
                          ? 'bg-yellow-500 text-black' 
                          : 'bg-gray-700 text-gray-300'
                      }`}>
                        {getStatusLabel(event.status)}
                      </span>
                    </div>
                  </div>
                  <div className="p-6">
                    <h3 className="text-lg font-semibold text-white mb-2 group-hover:text-yellow-400 transition-colors">
                      {event.name}
                    </h3>
                    <p className="text-gray-400 text-sm mb-4 line-clamp-2">
                      {event.description}
                    </p>
                    
                    <div className="space-y-2 mb-4">
                      <div className="flex items-center text-sm text-gray-400">
                        <CalendarDaysIcon className="h-4 w-4 mr-2 text-yellow-500" />
                        {formatDate(event.start_date)} - {formatDate(event.end_date)}
                      </div>
                      <div className="flex items-center text-sm text-gray-400">
                        <MapPinIcon className="h-4 w-4 mr-2 text-yellow-500" />
                        {event.lokasi}
                      </div>
                      <div className="flex items-center text-sm text-gray-400">
                        <CurrencyDollarIcon className="h-4 w-4 mr-2 text-yellow-500" />
                        {formatCurrency(event.biaya_registrasi)}
                      </div>
                    </div>

                    <div className="flex space-x-2">
                      <button
                        className="flex-1 bg-yellow-500 hover:bg-yellow-400 text-black font-medium py-2 px-4 rounded-md transition-colors duration-200"
                        onClick={() => handleViewEvent(event)}
                      >
                        Lihat Detail
                      </button>
                      {event.status === 'active' && (
                        <button className="bg-gray-800 hover:bg-gray-700 text-white border border-gray-700 hover:border-yellow-500/50 font-medium py-2 px-4 rounded-md transition-all duration-200">
                          Daftar
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      <Footer />

      {/* Event Detail Modal */}
      <EventDetailModal
        isOpen={showDetailModal}
        onClose={() => setShowDetailModal(false)}
        event={selectedEvent}
      />
    </div>
  );
};

export default EventsPage;