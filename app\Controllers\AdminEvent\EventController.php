<?php

namespace App\Controllers\AdminEvent;

use App\Controllers\BaseController;
use App\Models\Event;

class EventController extends BaseController
{
    public function index()
    {
        // Pastikan user sudah login dan ambil ID user
        $userId = session()->get('id');
        if (!$userId) {
            return redirect()->to('/login')->with('error', 'Anda harus login terlebih dahulu.');
        }

        $eventModel = new Event();

        // Periksa status event dan update jika sudah selesai
        $this->checkEventStatus();

        // Ambil semua event
        $data['events'] = $eventModel->findAll();

        // Periksa apakah admin-event sudah memiliki event aktif
        $data['hasActiveEvent'] = $eventModel
            ->where('id_user', $userId)
            ->where('status', 'active')
            ->first() !== null;

        return view('admin-event/event/index', $data);
    }

    /**
     * Periksa event dengan status aktif dan update menjadi completed jika sudah melewati end_date.
     */
    public function checkEventStatus()
    {
        $eventModel = new Event();
        $activeEvents = $eventModel->where('status', 'active')->findAll();
        $currentTime = time();

        foreach ($activeEvents as $event) {
            // Pastikan end_date valid
            if (!empty($event['end_date'])) {
                $endDate = strtotime($event['end_date']);
                if ($currentTime > $endDate) {
                    $eventModel->update($event['id'], ['status' => 'completed']);
                }
            }
        }
    }

    public function create()
    {
        // Tampilkan form pembuatan event
        return view('admin-event/event/create');
    }

    public function store()
    {
        $eventModel = new Event();
        $userId = session()->get('id');

        if (!$userId) {
            return redirect()->to('/login')->with('error', 'Anda harus login terlebih dahulu.');
        }

        // Cek apakah user sudah memiliki event aktif
        $existingEvent = $eventModel
            ->where('id_user', $userId)
            ->where('status', 'active')
            ->first();
        if ($existingEvent) {
            return redirect()->back()->with('error', 'Anda sudah memiliki event aktif. Hapus atau selesaikan event sebelum membuat yang baru.');
        }

        // Aturan validasi input
        $validationRules = [
            'name'                => 'required',
            'description'         => 'required',
            'start_date'          => 'required|valid_date',
            'end_date'            => 'required|valid_date',
            'lokasi'              => 'required',
            'biaya_registrasi'    => 'required|numeric',
            'metode_pembayaran'   => 'required',
            'event_image'         => 'uploaded[event_image]|max_size[event_image,2048]|ext_in[event_image,jpg,jpeg,png]',
            'event_proposal'      => 'uploaded[event_proposal]|max_size[event_proposal,5120]|ext_in[event_proposal,pdf]',
        ];

        if (!$this->validate($validationRules)) {
            return redirect()->back()
                ->withInput()
                ->with('errors', $this->validator->getErrors());
        }

        // Buat nama folder berdasarkan nama event (lowercase, ganti spasi dengan tanda hubung)
        $eventNameSlug = strtolower(str_replace(' ', '-', $this->request->getPost('name')));
        $folderPath = ROOTPATH . 'public/uploads/file-event/' . $eventNameSlug;
        if (!is_dir($folderPath)) {
            mkdir($folderPath, 0755, true);
        }

        // Upload gambar event
        $eventImage = $this->request->getFile('event_image');
        if (!$eventImage->isValid() || $eventImage->hasMoved()) {
            return redirect()->back()->with('error', 'Gagal mengupload gambar event.');
        }
        $eventImageName = $eventImage->getRandomName();
        if (!$eventImage->move($folderPath, $eventImageName)) {
            return redirect()->back()->with('error', 'Gagal menyimpan gambar event.');
        }

        // Upload proposal event
        $eventProposal = $this->request->getFile('event_proposal');
        if (!$eventProposal->isValid() || $eventProposal->hasMoved()) {
            return redirect()->back()->with('error', 'Gagal mengupload proposal event.');
        }
        $eventProposalName = $eventProposal->getRandomName();
        if (!$eventProposal->move($folderPath, $eventProposalName)) {
            return redirect()->back()->with('error', 'Gagal menyimpan proposal event.');
        }

        // Simpan data event ke database
        $data = [
            'name'                => $this->request->getPost('name'),
            'description'         => $this->request->getPost('description'),
            'start_date'          => $this->request->getPost('start_date'),
            'end_date'            => $this->request->getPost('end_date'),
            'lokasi'              => $this->request->getPost('lokasi'),
            'biaya_registrasi'    => $this->request->getPost('biaya_registrasi'),
            'metode_pembayaran'   => $this->request->getPost('metode_pembayaran'),
            'event_image'         => $eventImageName,
            'event_proposal'      => $eventProposalName,
            'id_user'             => $userId,
            'status'              => 'active',
        ];

        if ($eventModel->insert($data)) {
            $newEventId = $eventModel->getInsertID();
            session()->set('event_id', $newEventId);
            return redirect()->to('/event')->with('success', 'Event berhasil ditambahkan. Silahkan login ulang untuk merefresh data.');
        } else {
            return redirect()->back()->with('error', 'Gagal menambahkan event. Periksa kembali data Anda.');
        }
    }

    public function edit($id)
    {
        $eventModel = new Event();
        $event = $eventModel->find($id);
        if (!$event) {
            return redirect()->to('/event')->with('error', 'Event tidak ditemukan.');
        }
        return view('admin-event/event/edit', ['event' => $event]);
    }

    public function update($id)
    {
        // Validasi input untuk update event (file tidak wajib diupload ulang)
        $validationRules = [
            'name'                => 'required',
            'description'         => 'required',
            'start_date'          => 'required|valid_date',
            'end_date'            => 'required|valid_date',
            'lokasi'              => 'required',
            'biaya_registrasi'    => 'required|numeric',
            'metode_pembayaran'   => 'required',
        ];

        if (!$this->validate($validationRules)) {
            return redirect()->back()
                ->withInput()
                ->with('errors', $this->validator->getErrors());
        }

        $eventModel = new Event();
        $updateData = [
            'name'                => $this->request->getPost('name'),
            'description'         => $this->request->getPost('description'),
            'start_date'          => $this->request->getPost('start_date'),
            'end_date'            => $this->request->getPost('end_date'),
            'lokasi'              => $this->request->getPost('lokasi'),
            'biaya_registrasi'    => $this->request->getPost('biaya_registrasi'),
            'metode_pembayaran'   => $this->request->getPost('metode_pembayaran'),
        ];

        if ($eventModel->update($id, $updateData)) {
            return redirect()->to('/event')->with('success', 'Event berhasil diperbarui.');
        } else {
            return redirect()->back()->with('error', 'Gagal memperbarui event. Periksa kembali data Anda.');
        }
    }

    /**
     * Method untuk mengupload file pemenang event setelah event selesai.
     */
    public function upload($id)
    {
        $eventModel = new Event();
        $userId = session()->get('id');

        // Ambil event berdasarkan ID dan pastikan kepemilikan oleh user
        $event = $eventModel
            ->where('id', $id)
            ->where('id_user', $userId)
            ->first();

        if (!$event) {
            return redirect()->to('/event')->with('error', 'Event tidak valid.');
        }

        // Hanya izinkan upload file pemenang jika status event sudah completed
        if ($event['status'] != 'completed') {
            return redirect()->to('/event')->with('error', 'Event belum selesai.');
        }

        // Validasi file pemenang
        $validationRules = [
            'event_pemenang' => [
                'rules'  => 'uploaded[event_pemenang]|max_size[event_pemenang,5120]|ext_in[event_pemenang,pdf]',
                'errors' => [
                    'uploaded' => 'Harap pilih file',
                    'ext_in'   => 'Hanya file PDF yang diperbolehkan',
                    'max_size' => 'Ukuran maksimal 5MB'
                ]
            ]
        ];

        if (!$this->validate($validationRules)) {
            return redirect()->back()
                ->withInput()
                ->with('errors', $this->validator->getErrors());
        }

        // Buat folder berdasarkan nama event (gunakan slug dari nama event)
        $eventNameSlug = strtolower(str_replace(' ', '-', $event['name']));
        $folderPath = ROOTPATH . 'public/uploads/file-event/' . $eventNameSlug;
        if (!is_dir($folderPath)) {
            mkdir($folderPath, 0755, true);
        }

        $eventPemenang = $this->request->getFile('event_pemenang');
        if (!$eventPemenang->isValid() || $eventPemenang->hasMoved()) {
            return redirect()->back()->with('error', 'Gagal mengupload file pemenang.');
        }
        $eventPemenangName = $eventPemenang->getRandomName();
        if (!$eventPemenang->move($folderPath, $eventPemenangName)) {
            return redirect()->back()->with('error', 'Gagal menyimpan file pemenang.');
        }

        // Update data event dengan file pemenang
        $eventModel->update($id, ['event_pemenang' => $eventPemenangName]);
        return redirect()->to('/event')->with('success', 'Pemenang berhasil diupload.');
    }
}
