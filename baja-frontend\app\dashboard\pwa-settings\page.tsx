'use client';

import React, { useState, useEffect } from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { pwaSyncService } from '@/lib/pwa.sync';
import { offlineService } from '@/lib/offline.service';
import {
  WifiIcon,
  CloudArrowUpIcon,
  TrashIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';

const PWASettingsPage = () => {
  const [syncStatus, setSyncStatus] = useState(pwaSyncService.getSyncStatus());
  const [storageInfo, setStorageInfo] = useState(pwaSyncService.getStorageInfo());
  const [pwaSupport, setPwaSupport] = useState(pwaSyncService.checkPWASupport());
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  useEffect(() => {
    const updateStatus = () => {
      setSyncStatus(pwaSyncService.getSyncStatus());
      setStorageInfo(pwaSyncService.getStorageInfo());
      setIsOnline(navigator.onLine);
    };

    // Update status every 5 seconds
    const interval = setInterval(updateStatus, 5000);

    // Listen for online/offline events
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Add sync listener
    pwaSyncService.addSyncListener(updateStatus);

    return () => {
      clearInterval(interval);
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      pwaSyncService.removeSyncListener(updateStatus);
    };
  }, []);

  const handleForceSync = async () => {
    try {
      await pwaSyncService.forcSync();
      setSyncStatus(pwaSyncService.getSyncStatus());
      setStorageInfo(pwaSyncService.getStorageInfo());
    } catch (error) {
      console.error('Force sync failed:', error);
    }
  };

  const handleClearCache = () => {
    if (confirm('Are you sure you want to clear all offline data? This will remove cached data and queued changes.')) {
      pwaSyncService.clearOfflineData();
      setSyncStatus(pwaSyncService.getSyncStatus());
      setStorageInfo(pwaSyncService.getStorageInfo());
    }
  };

  const getStatusIcon = (supported: boolean) => {
    return supported ? (
      <CheckCircleIcon className="h-5 w-5 text-green-400" />
    ) : (
      <XCircleIcon className="h-5 w-5 text-red-400" />
    );
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold text-white">PWA Settings</h1>
          <p className="text-gray-400 mt-2">Manage offline functionality and app settings</p>
        </div>

        {/* Connection Status */}
        <Card className="p-6">
          <div className="flex items-center space-x-3 mb-4">
            <WifiIcon className="h-6 w-6 text-gold-400" />
            <h2 className="text-xl font-semibold text-white">Connection Status</h2>
          </div>
          
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${isOnline ? 'bg-green-400' : 'bg-red-400'}`}></div>
            <span className="text-white font-medium">
              {isOnline ? 'Online' : 'Offline'}
            </span>
          </div>
        </Card>

        {/* Sync Status */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <CloudArrowUpIcon className="h-6 w-6 text-gold-400" />
              <h2 className="text-xl font-semibold text-white">Sync Status</h2>
            </div>
            <Button
              onClick={handleForceSync}
              disabled={!isOnline || syncStatus.inProgress}
              size="sm"
              className="bg-blue-600 hover:bg-blue-700"
            >
              {syncStatus.inProgress ? 'Syncing...' : 'Force Sync'}
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-gray-800 p-4 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <ClockIcon className="h-5 w-5 text-gray-400" />
                <span className="text-gray-300 text-sm">Sync Status</span>
              </div>
              <p className="text-white font-medium">
                {syncStatus.inProgress ? 'In Progress' : 'Idle'}
              </p>
            </div>

            <div className="bg-gray-800 p-4 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <InformationCircleIcon className="h-5 w-5 text-gray-400" />
                <span className="text-gray-300 text-sm">Queue Length</span>
              </div>
              <p className="text-white font-medium">{syncStatus.queueLength} items</p>
            </div>

            <div className="bg-gray-800 p-4 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <ClockIcon className="h-5 w-5 text-gray-400" />
                <span className="text-gray-300 text-sm">Last Sync</span>
              </div>
              <p className="text-white font-medium">
                {syncStatus.lastSync || 'Never'}
              </p>
            </div>
          </div>
        </Card>

        {/* Storage Info */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <InformationCircleIcon className="h-6 w-6 text-gold-400" />
              <h2 className="text-xl font-semibold text-white">Storage Information</h2>
            </div>
            <Button
              onClick={handleClearCache}
              variant="danger"
              size="sm"
            >
              <TrashIcon className="h-4 w-4 mr-2" />
              Clear Cache
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-gray-800 p-4 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <InformationCircleIcon className="h-5 w-5 text-gray-400" />
                <span className="text-gray-300 text-sm">Cached Items</span>
              </div>
              <p className="text-white font-medium">{storageInfo.cacheInfo.totalItems}</p>
            </div>

            <div className="bg-gray-800 p-4 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <InformationCircleIcon className="h-5 w-5 text-gray-400" />
                <span className="text-gray-300 text-sm">Cache Size</span>
              </div>
              <p className="text-white font-medium">{storageInfo.cacheInfo.totalSize}</p>
            </div>
          </div>
        </Card>

        {/* PWA Support */}
        <Card className="p-6">
          <div className="flex items-center space-x-3 mb-4">
            <InformationCircleIcon className="h-6 w-6 text-gold-400" />
            <h2 className="text-xl font-semibold text-white">PWA Feature Support</h2>
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-gray-300">Service Worker</span>
              <div className="flex items-center space-x-2">
                {getStatusIcon(pwaSupport.serviceWorker)}
                <span className="text-white text-sm">
                  {pwaSupport.serviceWorker ? 'Supported' : 'Not Supported'}
                </span>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-gray-300">Background Sync</span>
              <div className="flex items-center space-x-2">
                {getStatusIcon(pwaSupport.backgroundSync)}
                <span className="text-white text-sm">
                  {pwaSupport.backgroundSync ? 'Supported' : 'Not Supported'}
                </span>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-gray-300">Push Notifications</span>
              <div className="flex items-center space-x-2">
                {getStatusIcon(pwaSupport.pushNotifications)}
                <span className="text-white text-sm">
                  {pwaSupport.pushNotifications ? 'Supported' : 'Not Supported'}
                </span>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-gray-300">Install Prompt</span>
              <div className="flex items-center space-x-2">
                {getStatusIcon(pwaSupport.installPrompt)}
                <span className="text-white text-sm">
                  {pwaSupport.installPrompt ? 'Supported' : 'Not Supported'}
                </span>
              </div>
            </div>
          </div>
        </Card>

        {/* Instructions */}
        <Card className="p-6">
          <div className="flex items-center space-x-3 mb-4">
            <InformationCircleIcon className="h-6 w-6 text-gold-400" />
            <h2 className="text-xl font-semibold text-white">How PWA Works</h2>
          </div>

          <div className="space-y-4 text-gray-300">
            <div>
              <h3 className="text-white font-medium mb-2">Offline Functionality</h3>
              <p className="text-sm">
                When you're offline, the app will use cached data and queue any changes you make. 
                These changes will be automatically synced when you're back online.
              </p>
            </div>

            <div>
              <h3 className="text-white font-medium mb-2">Installation</h3>
              <p className="text-sm">
                You can install this app on your device for a native app-like experience. 
                Look for the install prompt or use your browser's install option.
              </p>
            </div>

            <div>
              <h3 className="text-white font-medium mb-2">Data Sync</h3>
              <p className="text-sm">
                The app automatically syncs your data when you come back online. 
                You can also manually force a sync using the button above.
              </p>
            </div>
          </div>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default PWASettingsPage;
