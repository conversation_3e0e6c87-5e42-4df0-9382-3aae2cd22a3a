const http = require('http');

// Test kontingen endpoint without auth (should return 401)
const options = {
  hostname: 'localhost',
  port: 5000,
  path: '/api/v1/kontingen/my',
  method: 'GET',
  headers: {
    'Content-Type': 'application/json'
  }
};

console.log('Testing kontingen endpoint without auth...');

const req = http.request(options, (res) => {
  console.log(`Status: ${res.statusCode}`);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    console.log('Response:', data);
    
    // Test auth endpoint
    testAuth();
  });
});

req.on('error', (e) => {
  console.error(`Error: ${e.message}`);
});

req.end();

function testAuth() {
  console.log('\nTesting auth endpoint...');
  
  const authData = JSON.stringify({
    email: '<EMAIL>',
    password: 'admin123'
  });

  const authOptions = {
    hostname: 'localhost',
    port: 5000,
    path: '/api/v1/auth/login',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(authData)
    }
  };

  const authReq = http.request(authOptions, (res) => {
    console.log(`Auth Status: ${res.statusCode}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      console.log('Auth Response:', data);
    });
  });

  authReq.on('error', (e) => {
    console.error(`Auth Error: ${e.message}`);
  });

  authReq.write(authData);
  authReq.end();
}
