<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreatePendaftaranEventTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 5,
                'unsigned' => true,
                'auto_increment' => true
            ],
            'id_kontingen' => [
                'type' => 'INT',
                'constraint' => 5,
                'unsigned' => true,
            ],
            'id_event' => [
                'type' => 'INT',
                'constraint' => 5,
                'unsigned' => true,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ]
            ]);
        $this->forge->addKey('id', true);
        $this->forge->addForeignKey('id_kontingen', 'kontingen', 'id', 'CASCADE', 'CASCADE');
        $this->forge->addForeignKey('id_event', 'event', 'id', 'CASCADE', 'CASCADE');
        $this->forge->createTable('pendaftaran_event');
    }

    public function down()
    {
        $this->forge->dropTable('pendaftaran_event');
    }
}
