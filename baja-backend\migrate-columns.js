const mysql = require('mysql2/promise');

async function addColumns() {
  let connection;
  
  try {
    console.log('Connecting to database...');
    
    connection = await mysql.createConnection({
      host: '127.0.0.1',
      port: 3306,
      user: 'root',
      password: '',
      database: 'db_baja_app'
    });

    console.log('✅ Database connection successful!');
    
    // Check if columns already exist
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'db_baja_app' 
      AND TABLE_NAME = 'paket'
    `);
    
    const existingColumns = columns.map(col => col.COLUMN_NAME);
    console.log('Existing columns:', existingColumns);
    
    // Add is_popular column if it doesn't exist
    if (!existingColumns.includes('is_popular')) {
      await connection.execute('ALTER TABLE paket ADD COLUMN is_popular BOOLEAN DEFAULT FALSE');
      console.log('✅ Added is_popular column');
    } else {
      console.log('ℹ️ is_popular column already exists');
    }
    
    // Add is_featured column if it doesn't exist
    if (!existingColumns.includes('is_featured')) {
      await connection.execute('ALTER TABLE paket ADD COLUMN is_featured BOOLEAN DEFAULT FALSE');
      console.log('✅ Added is_featured column');
    } else {
      console.log('ℹ️ is_featured column already exists');
    }
    
    // Add featured_order column if it doesn't exist
    if (!existingColumns.includes('featured_order')) {
      await connection.execute('ALTER TABLE paket ADD COLUMN featured_order INT NULL');
      console.log('✅ Added featured_order column');
    } else {
      console.log('ℹ️ featured_order column already exists');
    }
    
    // Show final table structure
    const [finalColumns] = await connection.execute(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'db_baja_app' 
      AND TABLE_NAME = 'paket'
      ORDER BY ORDINAL_POSITION
    `);
    
    console.log('\n📋 Final table structure:');
    finalColumns.forEach(col => {
      console.log(`  ${col.COLUMN_NAME} (${col.DATA_TYPE}) - Nullable: ${col.IS_NULLABLE}, Default: ${col.COLUMN_DEFAULT}`);
    });
    
    console.log('\n✅ Migration completed successfully!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

addColumns();
