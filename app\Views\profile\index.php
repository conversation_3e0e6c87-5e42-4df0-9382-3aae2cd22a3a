<?= $this->extend('layouts/backend/template') ?>
<?= $this->section('content') ?>

<style>
    body {
    background: rgb(99, 39, 120)
}

.form-control:focus {
    box-shadow: none;
    border-color: #BA68C8
}

.profile-button {
    background: rgb(99, 39, 120);
    box-shadow: none;
    border: none
}

.profile-button:hover {
    background: #682773
}

.profile-button:focus {
    background: #682773;
    box-shadow: none
}

.profile-button:active {
    background: #682773;
    box-shadow: none
}

.back:hover {
    color: #682773;
    cursor: pointer
}

.labels {
    font-size: 11px
}

.add-experience:hover {
    background: #BA68C8;
    color: #fff;
    cursor: pointer;
    border: solid 1px #BA68C8
}
</style>

<div class="container rounded bg-white mt-2 mb-2">
        <?php if (session('success')): ?>
            <div class="alert alert-success"><?= session('success') ?></div>
        <?php endif ?>
        
        <?php if (session('error')): ?>
            <div class="alert alert-danger"><?= session('error') ?></div>
        <?php endif ?>
    <form action="<?= base_url('profile/update') ?>" method="POST" enctype="multipart/form-data">
    <?= csrf_field() ?>
        <div class="row">
            <div class="col-md-3 border-right">
                <div class="d-flex flex-column align-items-center text-center p-3 py-5 position-relative">
                    <div class="position-relative">
                        <img id="profile-preview" class="rounded-circle mt-5" width="150px"
                            src="<?= base_url('uploads/file-profile/' . $user['profile']) ?>"
                            onerror="this.src='https://st3.depositphotos.com/15648834/17930/v/600/depositphotos_179308454-stock-illustration-unknown-person-silhouette-glasses-profile.jpg'">

                        <!-- Tombol + untuk upload -->
                        <label for="profile-upload" class="position-absolute"
                            style="bottom: 0; right: 0; background-color: #682773; color: white; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; cursor: pointer;">
                            +
                        </label>

                        <!-- Input file disembunyikan -->
                        <input type="file" id="profile-upload" name="profile" class="d-none" accept="image/*" onchange="previewProfile(event)">
                    </div>

                    <span class="font-weight-bold mt-3"><p>AKUN PENGGUNA</p></span>
                </div>
            </div>
            <div class="col-md-4">
                <div class="p-3 py-5">
                    <div class="d-flex justify-content-between align-items-center experience">
                        <span>Akun Pengguna</span>
                    </div><br>
                    <div class="col-md-12">
                        <label class="labels">Nama</label>
                        <input type="text" class="form-control" name="name" value="<?= $user['name'] ?>">
                    </div><br>
                    <div class="col-md-12">
                        <label class="labels">Email</label>
                        <input type="email" class="form-control" name="email" value="<?= $user['email'] ?>">
                    </div>
                    <div class="col-md-12">
                        <label class="labels">No Handphone</label>
                        <input type="text" class="form-control" name="no_hp" value="<?= $user['no_hp'] ?> ">
                    </div>
                    <div class="col-md-12">
                        <label class="labels">Change Password</label>
                        <input type="password" class="form-control" name="password">
                    </div>
                    <div class="mt-3 text-center">
                            <button class="btn btn-primary profile-button" type="submit">Perbarui</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<?= $this->endSection() ?>