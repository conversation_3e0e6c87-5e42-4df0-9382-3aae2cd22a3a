import { Router } from 'express';
import { body } from 'express-validator';
import rateLimit from 'express-rate-limit';
import {
  register,
  verifyRegistrationOTP,
  login,
  logout,
  getProfile,
  updateProfile,
  changePassword,
  googleRegister,
  googleLogin,
  resendOTP,
  setup2FA,
  verify2FA,
  disable2FA
} from '../controllers/auth.controller';
import { authenticate } from '../middleware/auth.middleware';
import { validate } from '../middleware/validation.middleware';

const router = Router();

// Rate limiting for OTP endpoints
const otpLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 3, // limit each IP to 3 OTP requests per minute
  message: {
    success: false,
    message: 'Too many OTP requests. Please wait before trying again.'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Validation rules
const registerValidation = [
  body('name').notEmpty().withMessage('Name is required'),
  body('email').isEmail().withMessage('Valid email is required'),
  body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters'),
  body('role').optional().isIn(['admin', 'admin-event', 'ketua-kontingen']).withMessage('Invalid role'),
];

const otpValidation = [
  body('email').isEmail().withMessage('Valid email is required'),
  body('otp').isLength({ min: 6, max: 6 }).withMessage('OTP must be 6 digits'),
];

const googleAuthValidation = [
  body('idToken').notEmpty().withMessage('Google ID token is required'),
];

const resendOTPValidation = [
  body('email').isEmail().withMessage('Valid email is required'),
  body('type').isIn(['registration', 'login', 'password_reset']).withMessage('Invalid OTP type'),
];

const setup2FAValidation = [
  // No additional validation needed as user must be authenticated
];

const verify2FAValidation = [
  body('token').isLength({ min: 6, max: 6 }).withMessage('2FA token must be 6 digits'),
];

const disable2FAValidation = [
  body('password').notEmpty().withMessage('Password is required'),
  body('token').optional().isLength({ min: 6, max: 6 }).withMessage('2FA token must be 6 digits'),
];

const loginValidation = [
  body('email').isEmail().withMessage('Valid email is required'),
  body('password').notEmpty().withMessage('Password is required'),
];

const updateProfileValidation = [
  body('name').optional().notEmpty().withMessage('Name cannot be empty'),
  body('no_hp').optional().isMobilePhone('id-ID').withMessage('Invalid phone number'),
];

const changePasswordValidation = [
  body('currentPassword').notEmpty().withMessage('Current password is required'),
  body('newPassword').isLength({ min: 6 }).withMessage('New password must be at least 6 characters'),
];

// Routes
// Authentication
router.post('/register', registerValidation, validate, register);
router.post('/verify-registration', otpLimiter, otpValidation, validate, verifyRegistrationOTP);
router.post('/login', loginValidation, validate, login);
router.post('/logout', logout);

// Google OAuth
router.post('/google/register', googleAuthValidation, validate, googleRegister);
router.post('/google/login', googleAuthValidation, validate, googleLogin);

// OTP
router.post('/resend-otp', resendOTPValidation, validate, resendOTP);

// Profile management
router.get('/profile', authenticate, getProfile);
router.put('/profile', authenticate, updateProfileValidation, validate, updateProfile);
router.put('/change-password', authenticate, changePasswordValidation, validate, changePassword);

// 2FA
router.post('/2fa/setup', authenticate, setup2FA);
router.post('/2fa/verify', authenticate, verify2FAValidation, validate, verify2FA);
router.post('/2fa/disable', authenticate, disable2FAValidation, validate, disable2FA);

export default router;
