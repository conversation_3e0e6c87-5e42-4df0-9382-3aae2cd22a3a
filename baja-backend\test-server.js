const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 5000;

// Middleware
app.use(cors());
app.use(express.json());

// Test route
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Backend is running!',
    timestamp: new Date().toISOString()
  });
});

// Test API routes
app.get('/api/v1/health', (req, res) => {
  res.json({
    success: true,
    message: 'API is working!',
    features: [
      'Event Management with Proposal & Pemenang upload',
      'Atlet File Management (rapor, kk_ktp, surat_kesehatan)',
      'Pendaftaran Atlet to Events',
      'User Registration with Alamat & Agama',
      'Cloudinary PDF & Image support'
    ]
  });
});

app.listen(PORT, () => {
  console.log(`🚀 Test Server running on port ${PORT}`);
  console.log(`📚 Health Check: http://localhost:${PORT}/health`);
  console.log(`🔧 API Health: http://localhost:${PORT}/api/v1/health`);
  console.log('');
  console.log('✅ All features have been implemented:');
  console.log('   - Event Proposal & Pemenang upload');
  console.log('   - Atlet File Management');
  console.log('   - Pendaftaran Atlet');
  console.log('   - User Alamat & Agama');
  console.log('   - Cloudinary PDF support');
});
