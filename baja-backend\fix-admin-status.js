const { Sequelize } = require('sequelize');

// Database configuration
const sequelize = new Sequelize('db_baja_app', 'root', '', {
  host: 'localhost',
  dialect: 'mysql',
  logging: false
});

async function fixAdminStatus() {
  try {
    await sequelize.authenticate();
    console.log('Database connected successfully.');

    // Update all test users status to active
    const [results] = await sequelize.query(
      "UPDATE users SET status = '1' WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>')"
    );

    console.log('Users status updated:', results);

    // Check current test users
    const [users] = await sequelize.query(
      "SELECT id, name, email, role, status FROM users WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>')"
    );

    console.log('Current test users:');
    users.forEach(user => console.log(`- ${user.name} (${user.email}): ${user.role} - status: ${user.status}`));

    await sequelize.close();
    console.log('Database connection closed.');
  } catch (error) {
    console.error('Error:', error);
  }
}

fixAdminStatus();
