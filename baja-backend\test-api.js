const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api/v1';

// Test data
const testUser = {
  name: 'Test Admin',
  email: '<EMAIL>',
  password: 'password123',
  role: 'admin'
};

const testKetua = {
  name: 'Test Ketua <PERSON>',
  email: '<EMAIL>',
  password: 'password123',
  role: 'ketua-kontingen'
};

const testAdminEvent = {
  name: 'Test Admin Event',
  email: '<EMAIL>',
  password: 'password123',
  role: 'admin-event'
};

let adminToken = '';
let ketuaToken = '';
let adminEventToken = '';

// Helper function to make authenticated requests
const makeRequest = async (method, url, data = null, token = '') => {
  try {
    const config = {
      method,
      url: `${BASE_URL}${url}`,
      headers: token ? { Authorization: `Bearer ${token}` } : {},
      data
    };
    
    const response = await axios(config);
    return { success: true, data: response.data };
  } catch (error) {
    return { 
      success: false, 
      error: error.response?.data || error.message,
      status: error.response?.status
    };
  }
};

// Test functions
const testHealthCheck = async () => {
  console.log('\n=== Testing Health Check ===');
  const result = await makeRequest('GET', '/health');
  console.log('Health Check:', result.success ? '✅ PASS' : '❌ FAIL');
  if (!result.success) console.log('Error:', result.error);
};

const testAuth = async () => {
  console.log('\n=== Testing Authentication ===');
  
  // Test registration
  console.log('\n--- Testing Registration ---');
  
  // Register admin
  let result = await makeRequest('POST', '/auth/register', testUser);
  console.log('Admin Registration:', result.success ? '✅ PASS' : '❌ FAIL');
  if (!result.success) console.log('Error:', result.error);
  
  // Register ketua kontingen
  result = await makeRequest('POST', '/auth/register', testKetua);
  console.log('Ketua Registration:', result.success ? '✅ PASS' : '❌ FAIL');
  if (!result.success) console.log('Error:', result.error);
  
  // Register admin event
  result = await makeRequest('POST', '/auth/register', testAdminEvent);
  console.log('Admin Event Registration:', result.success ? '✅ PASS' : '❌ FAIL');
  if (!result.success) console.log('Error:', result.error);
  
  // Test login
  console.log('\n--- Testing Login ---');
  
  // Login admin
  result = await makeRequest('POST', '/auth/login', {
    email: testUser.email,
    password: testUser.password
  });
  console.log('Admin Login:', result.success ? '✅ PASS' : '❌ FAIL');
  if (result.success) {
    adminToken = result.data.data.token;
    console.log('Admin token received');
  } else {
    console.log('Error:', result.error);
  }
  
  // Login ketua
  result = await makeRequest('POST', '/auth/login', {
    email: testKetua.email,
    password: testKetua.password
  });
  console.log('Ketua Login:', result.success ? '✅ PASS' : '❌ FAIL');
  if (result.success) {
    ketuaToken = result.data.data.token;
    console.log('Ketua token received');
  } else {
    console.log('Error:', result.error);
  }
  
  // Login admin event
  result = await makeRequest('POST', '/auth/login', {
    email: testAdminEvent.email,
    password: testAdminEvent.password
  });
  console.log('Admin Event Login:', result.success ? '✅ PASS' : '❌ FAIL');
  if (result.success) {
    adminEventToken = result.data.data.token;
    console.log('Admin Event token received');
  } else {
    console.log('Error:', result.error);
  }
};

const testDashboard = async () => {
  console.log('\n=== Testing Dashboard ===');
  
  // Test admin dashboard
  let result = await makeRequest('GET', '/dashboard/stats', null, adminToken);
  console.log('Admin Dashboard:', result.success ? '✅ PASS' : '❌ FAIL');
  if (!result.success) console.log('Error:', result.error);
  
  // Test ketua dashboard
  result = await makeRequest('GET', '/dashboard/stats', null, ketuaToken);
  console.log('Ketua Dashboard:', result.success ? '✅ PASS' : '❌ FAIL');
  if (!result.success) console.log('Error:', result.error);
  
  // Test admin event dashboard
  result = await makeRequest('GET', '/dashboard/stats', null, adminEventToken);
  console.log('Admin Event Dashboard:', result.success ? '✅ PASS' : '❌ FAIL');
  if (!result.success) console.log('Error:', result.error);
};

const testEvents = async () => {
  console.log('\n=== Testing Events ===');
  
  // Test get all events (public)
  let result = await makeRequest('GET', '/events');
  console.log('Get All Events (Public):', result.success ? '✅ PASS' : '❌ FAIL');
  if (!result.success) console.log('Error:', result.error);
  
  // Test create event (admin)
  const eventData = {
    name: 'Test Championship',
    description: 'Test event description',
    start_date: '2024-06-01',
    end_date: '2024-06-03',
    lokasi: 'Jakarta',
    biaya_registrasi: 500000,
    metode_pembayaran: 'Transfer Bank'
  };
  
  result = await makeRequest('POST', '/events', eventData, adminToken);
  console.log('Create Event (Admin):', result.success ? '✅ PASS' : '❌ FAIL');
  if (!result.success) console.log('Error:', result.error);
  
  // Test create event (admin-event)
  result = await makeRequest('POST', '/events', eventData, adminEventToken);
  console.log('Create Event (Admin Event):', result.success ? '✅ PASS' : '❌ FAIL');
  if (!result.success) console.log('Error:', result.error);
  
  // Test unauthorized create event (ketua)
  result = await makeRequest('POST', '/events', eventData, ketuaToken);
  console.log('Create Event (Unauthorized):', !result.success ? '✅ PASS' : '❌ FAIL');
  if (result.success) console.log('Error: Should have been unauthorized');
};

const testKontingen = async () => {
  console.log('\n=== Testing Kontingen ===');
  
  // Test create kontingen (ketua)
  const kontingenData = {
    name: 'Test Kontingen',
    negara: 'Indonesia',
    provinsi: 'DKI Jakarta',
    kabupaten_kota: 'Jakarta Selatan'
  };
  
  let result = await makeRequest('POST', '/kontingen', kontingenData, ketuaToken);
  console.log('Create Kontingen (Ketua):', result.success ? '✅ PASS' : '❌ FAIL');
  if (!result.success) console.log('Error:', result.error);
  
  // Test get my kontingen
  result = await makeRequest('GET', '/kontingen/my', null, ketuaToken);
  console.log('Get My Kontingen:', result.success ? '✅ PASS' : '❌ FAIL');
  if (!result.success) console.log('Error:', result.error);
  
  // Test get all kontingen
  result = await makeRequest('GET', '/kontingen', null, adminToken);
  console.log('Get All Kontingen:', result.success ? '✅ PASS' : '❌ FAIL');
  if (!result.success) console.log('Error:', result.error);
};

const testAtlet = async () => {
  console.log('\n=== Testing Atlet ===');
  
  // Test create atlet (ketua)
  const atletData = {
    nik: '1234567890123456',
    name: 'Test Atlet',
    no_hp: '081234567892',
    tanggal_lahir: '2000-01-01',
    jenis_kelamin: 'L',
    agama: 'Islam',
    alamat: 'Jakarta Timur',
    umur: 24,
    berat_badan: 70,
    tinggi_badan: 175
  };
  
  let result = await makeRequest('POST', '/atlet', atletData, ketuaToken);
  console.log('Create Atlet (Ketua):', result.success ? '✅ PASS' : '❌ FAIL');
  if (!result.success) console.log('Error:', result.error);
  
  // Test get all atlet
  result = await makeRequest('GET', '/atlet', null, adminToken);
  console.log('Get All Atlet:', result.success ? '✅ PASS' : '❌ FAIL');
  if (!result.success) console.log('Error:', result.error);
};

const testOfficial = async () => {
  console.log('\n=== Testing Official ===');
  
  // Test create official (ketua)
  const officialData = {
    name: 'Test Official',
    no_hp: '081234567893',
    alamat: 'Jakarta Barat',
    agama: 'Islam',
    jenis_kelamin: 'M'
  };
  
  let result = await makeRequest('POST', '/official', officialData, ketuaToken);
  console.log('Create Official (Ketua):', result.success ? '✅ PASS' : '❌ FAIL');
  if (!result.success) console.log('Error:', result.error);
  
  // Test get all official
  result = await makeRequest('GET', '/official', null, adminToken);
  console.log('Get All Official:', result.success ? '✅ PASS' : '❌ FAIL');
  if (!result.success) console.log('Error:', result.error);
};

const testPaket = async () => {
  console.log('\n=== Testing Paket ===');
  
  // Test get active packages (public)
  let result = await makeRequest('GET', '/paket/active');
  console.log('Get Active Packages (Public):', result.success ? '✅ PASS' : '❌ FAIL');
  if (!result.success) console.log('Error:', result.error);
  
  // Test create package (admin)
  const paketData = {
    name: 'Basic Package',
    images: 'basic-package.jpg',
    description: 'Basic features for event management'
  };
  
  result = await makeRequest('POST', '/paket', paketData, adminToken);
  console.log('Create Package (Admin):', result.success ? '✅ PASS' : '❌ FAIL');
  if (!result.success) console.log('Error:', result.error);
  
  // Test unauthorized create package (ketua)
  result = await makeRequest('POST', '/paket', paketData, ketuaToken);
  console.log('Create Package (Unauthorized):', !result.success ? '✅ PASS' : '❌ FAIL');
  if (result.success) console.log('Error: Should have been unauthorized');
};

const testGallery = async () => {
  console.log('\n=== Testing Gallery ===');
  
  // Test get active gallery (public)
  let result = await makeRequest('GET', '/gallery/active');
  console.log('Get Active Gallery (Public):', result.success ? '✅ PASS' : '❌ FAIL');
  if (!result.success) console.log('Error:', result.error);
  
  // Test get all gallery
  result = await makeRequest('GET', '/gallery', null, adminToken);
  console.log('Get All Gallery:', result.success ? '✅ PASS' : '❌ FAIL');
  if (!result.success) console.log('Error:', result.error);
};

// Main test runner
const runTests = async () => {
  console.log('🚀 Starting BAJA Backend API Tests...\n');
  
  try {
    await testHealthCheck();
    await testAuth();
    await testDashboard();
    await testEvents();
    await testKontingen();
    await testAtlet();
    await testOfficial();
    await testPaket();
    await testGallery();
    
    console.log('\n✅ All tests completed!');
    console.log('\nNote: Some tests may fail if database is not properly set up or if there are existing data conflicts.');
    console.log('Make sure to run this test with a clean database for best results.');
    
  } catch (error) {
    console.error('\n❌ Test runner error:', error.message);
  }
};

// Run tests if this file is executed directly
if (require.main === module) {
  runTests();
}

module.exports = { runTests };
