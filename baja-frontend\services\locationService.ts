import { api } from '@/lib/api';

export interface Negara {
  id: number;
  name: string;
}

export interface Provinsi {
  id: number;
  name: string;
  id_negara: number;
}

export interface KabupatenKota {
  id: number;
  name: string;
  id_provinsi: number;
}

class LocationService {
  async getAllNegara(): Promise<Negara[]> {
    try {
      const response = await api.get('/location/negara');
      return response.data.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch countries');
    }
  }

  async getProvinsiByNegara(negaraId: number): Promise<Provinsi[]> {
    try {
      const response = await api.get(`/location/negara/${negaraId}/provinsi`);
      return response.data.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch provinces');
    }
  }

  async getKabupatenKotaByProvinsi(provinsiId: number): Promise<KabupatenKota[]> {
    try {
      const response = await api.get(`/location/provinsi/${provinsiId}/kabupaten-kota`);
      return response.data.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch cities/regencies');
    }
  }

  async getNegaraById(id: number): Promise<Negara | null> {
    try {
      const response = await api.get('/location/negara');
      const negaraList = response.data.data;
      return negaraList.find((negara: Negara) => negara.id === id) || null;
    } catch (error: any) {
      console.error('Error fetching negara by id:', error);
      return null;
    }
  }

  async getProvinsiById(id: number): Promise<Provinsi | null> {
    try {
      // Try Indonesia first (most likely)
      const indonesiaProvinsi = await this.getProvinsiByNegara(102); // Indonesia ID
      const provinsi = indonesiaProvinsi.find((p: Provinsi) => p.id === id);
      if (provinsi) return provinsi;

      // If not found, try other countries
      const allNegara = await this.getAllNegara();
      for (const negara of allNegara) {
        if (negara.id === 102) continue; // Skip Indonesia as we already checked
        const provinsiList = await this.getProvinsiByNegara(negara.id);
        const found = provinsiList.find((p: Provinsi) => p.id === id);
        if (found) return found;
      }
      return null;
    } catch (error: any) {
      console.error('Error fetching provinsi by id:', error);
      return null;
    }
  }

  async getKabupatenKotaById(id: number): Promise<KabupatenKota | null> {
    try {
      // Try Indonesia provinces first (most likely)
      const indonesiaProvinsi = await this.getProvinsiByNegara(102); // Indonesia ID
      for (const provinsi of indonesiaProvinsi) {
        const kabupatenKotaList = await this.getKabupatenKotaByProvinsi(provinsi.id);
        const kabupatenKota = kabupatenKotaList.find((k: KabupatenKota) => k.id === id);
        if (kabupatenKota) return kabupatenKota;
      }

      // If not found, try other countries
      const allNegara = await this.getAllNegara();
      for (const negara of allNegara) {
        if (negara.id === 102) continue; // Skip Indonesia as we already checked
        const provinsiList = await this.getProvinsiByNegara(negara.id);
        for (const provinsi of provinsiList) {
          const kabupatenKotaList = await this.getKabupatenKotaByProvinsi(provinsi.id);
          const found = kabupatenKotaList.find((k: KabupatenKota) => k.id === id);
          if (found) return found;
        }
      }
      return null;
    } catch (error: any) {
      console.error('Error fetching kabupaten/kota by id:', error);
      return null;
    }
  }
}

export const locationService = new LocationService();
