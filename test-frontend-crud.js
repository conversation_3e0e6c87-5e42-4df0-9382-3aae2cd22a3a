const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api/v1';

// Test credentials
const adminCredentials = {
  email: '<EMAIL>',
  password: 'password123'
};

const adminEventCredentials = {
  email: '<EMAIL>',
  password: 'password123'
};

let adminToken = '';
let adminEventToken = '';

const makeRequest = async (method, endpoint, data = null, token = null) => {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
      },
    };

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    return { success: true, data: response.data };
  } catch (error) {
    return { 
      success: false, 
      error: error.response?.data?.message || error.message,
      status: error.response?.status
    };
  }
};

const testFrontendLogin = async () => {
  console.log('\n=== Testing Frontend Login Flow ===');
  
  // Test admin login
  let result = await makeRequest('POST', '/auth/login', adminCredentials);
  console.log('Admin Login:', result.success ? '✅ PASS' : '❌ FAIL');
  if (result.success) {
    adminToken = result.data.data.token;
    console.log('✓ Admin token received');
    console.log('✓ User role:', result.data.data.user.role);
  } else {
    console.log('Error:', result.error);
    return false;
  }
  
  // Test admin-event login
  result = await makeRequest('POST', '/auth/login', adminEventCredentials);
  console.log('Admin-Event Login:', result.success ? '✅ PASS' : '❌ FAIL');
  if (result.success) {
    adminEventToken = result.data.data.token;
    console.log('✓ Admin-Event token received');
    console.log('✓ User role:', result.data.data.user.role);
  } else {
    console.log('Error:', result.error);
    return false;
  }
  
  return true;
};

const testFrontendDashboardAccess = async () => {
  console.log('\n=== Testing Frontend Dashboard Access ===');
  
  // Test admin dashboard events access
  let result = await makeRequest('GET', '/admin/dashboard-events', null, adminToken);
  console.log('Admin Dashboard Events Access:', result.success ? '✅ PASS' : '❌ FAIL');
  if (result.success) {
    console.log(`✓ Admin can see ${result.data.data.events.length} events`);
    console.log('✓ Admin has full access to all events');
  } else {
    console.log('Error:', result.error);
  }
  
  // Test admin-event dashboard events access
  result = await makeRequest('GET', '/admin/dashboard-events', null, adminEventToken);
  console.log('Admin-Event Dashboard Events Access:', result.success ? '✅ PASS' : '❌ FAIL');
  if (result.success) {
    console.log(`✓ Admin-Event can see ${result.data.data.events.length} events`);
    console.log('✓ Admin-Event sees only their own events');
  } else {
    console.log('Error:', result.error);
  }
};

const testFrontendMainEventsPage = async () => {
  console.log('\n=== Testing Frontend Main Events Page ===');
  
  // Test public events access (no auth)
  let result = await makeRequest('GET', '/events');
  console.log('Public Events Access:', result.success ? '✅ PASS' : '❌ FAIL');
  if (result.success) {
    console.log(`✓ Public can see ${result.data.data.events.length} events`);
    console.log('✓ All events are visible to public');
  } else {
    console.log('Error:', result.error);
  }
  
  // Test events access with admin token
  result = await makeRequest('GET', '/events', null, adminToken);
  console.log('Admin Events Access (Main Page):', result.success ? '✅ PASS' : '❌ FAIL');
  if (result.success) {
    console.log(`✓ Admin can see ${result.data.data.events.length} events on main page`);
  } else {
    console.log('Error:', result.error);
  }
  
  // Test events access with admin-event token
  result = await makeRequest('GET', '/events', null, adminEventToken);
  console.log('Admin-Event Events Access (Main Page):', result.success ? '✅ PASS' : '❌ FAIL');
  if (result.success) {
    console.log(`✓ Admin-Event can see ${result.data.data.events.length} events on main page`);
    console.log('✓ Main page shows all events regardless of role');
  } else {
    console.log('Error:', result.error);
  }
};

const testFrontendEventCRUD = async () => {
  console.log('\n=== Testing Frontend Event CRUD Operations ===');
  
  const testEventData = {
    name: 'Frontend Test Event',
    description: 'Event created from frontend test',
    start_date: '2024-07-01',
    end_date: '2024-07-03',
    lokasi: 'Frontend Test Location',
    biaya_registrasi: 500000,
    metode_pembayaran: 'Transfer Bank'
  };
  
  let createdEventId = null;
  
  // Test CREATE event (admin)
  let result = await makeRequest('POST', '/events', testEventData, adminToken);
  console.log('Create Event (Admin):', result.success ? '✅ PASS' : '❌ FAIL');
  if (result.success) {
    createdEventId = result.data.data.id;
    console.log(`✓ Event created with ID: ${createdEventId}`);
  } else {
    console.log('Error:', result.error);
  }
  
  // Test CREATE event (admin-event)
  result = await makeRequest('POST', '/events', {
    ...testEventData,
    name: 'Admin-Event Frontend Test'
  }, adminEventToken);
  console.log('Create Event (Admin-Event):', result.success ? '✅ PASS' : '❌ FAIL');
  if (!result.success) {
    console.log('Error:', result.error);
  }
  
  // Test READ specific event
  if (createdEventId) {
    result = await makeRequest('GET', `/events/${createdEventId}`, null, adminToken);
    console.log('Read Specific Event:', result.success ? '✅ PASS' : '❌ FAIL');
    if (!result.success) {
      console.log('Error:', result.error);
    }
    
    // Test UPDATE event
    const updateData = {
      name: 'Updated Frontend Test Event',
      description: 'Updated from frontend test',
      biaya_registrasi: 600000
    };
    
    result = await makeRequest('PUT', `/events/${createdEventId}`, updateData, adminToken);
    console.log('Update Event:', result.success ? '✅ PASS' : '❌ FAIL');
    if (!result.success) {
      console.log('Error:', result.error);
    }
    
    // Test DELETE event
    result = await makeRequest('DELETE', `/events/${createdEventId}`, null, adminToken);
    console.log('Delete Event:', result.success ? '✅ PASS' : '❌ FAIL');
    if (!result.success) {
      console.log('Error:', result.error);
    }
  }
};

const testFrontendRoleBasedAccess = async () => {
  console.log('\n=== Testing Frontend Role-Based Access ===');
  
  // Test unauthorized access to admin endpoints
  let result = await makeRequest('GET', '/admin/dashboard-events');
  console.log('Unauthorized Dashboard Access:', !result.success ? '✅ PASS' : '❌ FAIL');
  if (!result.success) {
    console.log('✓ Unauthorized access properly blocked');
  }
  
  // Test admin access to admin-only endpoints
  result = await makeRequest('GET', '/admin/events', null, adminToken);
  console.log('Admin-Only Endpoint Access:', result.success ? '✅ PASS' : '❌ FAIL');
  if (result.success) {
    console.log('✓ Admin can access admin-only endpoints');
  } else {
    console.log('Error:', result.error);
  }
  
  // Test admin-event access to admin-only endpoints (should fail)
  result = await makeRequest('GET', '/admin/events', null, adminEventToken);
  console.log('Admin-Event to Admin-Only Access:', !result.success ? '✅ PASS' : '❌ FAIL');
  if (!result.success) {
    console.log('✓ Admin-Event properly blocked from admin-only endpoints');
  } else {
    console.log('❌ Admin-Event should not access admin-only endpoints');
  }
};

// Main test runner
const runFrontendTests = async () => {
  console.log('🎨 Starting BAJA Frontend CRUD Tests...\n');
  console.log('Testing the same API calls that the frontend makes...\n');
  
  try {
    const loginSuccess = await testFrontendLogin();
    if (!loginSuccess) {
      console.log('❌ Login failed, stopping tests');
      return;
    }
    
    await testFrontendDashboardAccess();
    await testFrontendMainEventsPage();
    await testFrontendEventCRUD();
    await testFrontendRoleBasedAccess();
    
    console.log('\n✅ Frontend CRUD Tests Completed!');
    console.log('\n📝 Frontend Test Summary:');
    console.log('✓ Login Flow: Working');
    console.log('✓ Dashboard Access: Working');
    console.log('✓ Main Events Page: Working');
    console.log('✓ Event CRUD Operations: Working');
    console.log('✓ Role-Based Access Control: Working');
    console.log('\n🎯 Frontend should work properly with these API endpoints!');
    
  } catch (error) {
    console.log('\n❌ Frontend test error:', error.message);
  }
};

// Run tests
runFrontendTests();
