import axios, { AxiosResponse } from 'axios';
import Cookies from 'js-cookie';
import { offlineService } from './offline.service';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api/v1';

// Create axios instance
const api = axios.create({
  baseURL: API_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor - no need to add auth token since we're using httpOnly cookies
api.interceptors.request.use(
  (config) => {
    // httpOnly cookies are automatically sent with requests
    // No need to manually add Authorization header
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors and caching
api.interceptors.response.use(
  (response) => {
    // Cache successful GET requests
    if (response.config.method === 'get' && response.status === 200) {
      const endpoint = response.config.url || '';
      offlineService.cacheApiResponse(endpoint, response.data);
    }
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Only redirect to login if we're on a protected route
      // Don't redirect for public API calls (like fetching packages on homepage)
      if (typeof window !== 'undefined') {
        const currentPath = window.location.pathname;
        const isProtectedRoute = currentPath.startsWith('/dashboard') ||
                                currentPath.startsWith('/profile');

        if (isProtectedRoute) {
          window.location.href = '/auth/login';
        }
      }
    }
    return Promise.reject(error);
  }
);

/**
 * Enhanced API service with offline support
 */
class EnhancedApiService {
  /**
   * GET request with offline fallback
   */
  async get<T = any>(endpoint: string, useCache = true): Promise<AxiosResponse<T>> {
    try {
      const response = await api.get<T>(endpoint);
      return response;
    } catch (error) {
      // If offline and cache is enabled, try to get cached data
      if (!offlineService.isOnline() && useCache) {
        const cachedData = offlineService.getCachedApiResponse<T>(endpoint);
        if (cachedData) {
          console.log(`Using cached data for ${endpoint}`);
          return {
            data: cachedData,
            status: 200,
            statusText: 'OK (Cached)',
            headers: {},
            config: {},
          } as AxiosResponse<T>;
        }
      }
      throw error;
    }
  }

  /**
   * POST request with offline queue
   */
  async post<T = any>(endpoint: string, data?: any): Promise<AxiosResponse<T>> {
    try {
      const response = await api.post<T>(endpoint, data);
      return response;
    } catch (error) {
      // If offline, add to sync queue
      if (!offlineService.isOnline()) {
        offlineService.addToSyncQueue({
          method: 'POST',
          url: endpoint,
          data
        });
        
        // Return a mock response for offline
        return {
          data: { success: true, message: 'Queued for sync when online' } as T,
          status: 202,
          statusText: 'Accepted (Queued)',
          headers: {},
          config: {},
        } as AxiosResponse<T>;
      }
      throw error;
    }
  }

  /**
   * PUT request with offline queue
   */
  async put<T = any>(endpoint: string, data?: any): Promise<AxiosResponse<T>> {
    try {
      const response = await api.put<T>(endpoint, data);
      return response;
    } catch (error) {
      // If offline, add to sync queue
      if (!offlineService.isOnline()) {
        offlineService.addToSyncQueue({
          method: 'PUT',
          url: endpoint,
          data
        });
        
        return {
          data: { success: true, message: 'Queued for sync when online' } as T,
          status: 202,
          statusText: 'Accepted (Queued)',
          headers: {},
          config: {},
        } as AxiosResponse<T>;
      }
      throw error;
    }
  }

  /**
   * DELETE request with offline queue
   */
  async delete<T = any>(endpoint: string): Promise<AxiosResponse<T>> {
    try {
      const response = await api.delete<T>(endpoint);
      return response;
    } catch (error) {
      // If offline, add to sync queue
      if (!offlineService.isOnline()) {
        offlineService.addToSyncQueue({
          method: 'DELETE',
          url: endpoint
        });
        
        return {
          data: { success: true, message: 'Queued for sync when online' } as T,
          status: 202,
          statusText: 'Accepted (Queued)',
          headers: {},
          config: {},
        } as AxiosResponse<T>;
      }
      throw error;
    }
  }

  /**
   * Sync queued requests when back online
   */
  async syncQueuedRequests(): Promise<void> {
    if (!offlineService.isOnline()) {
      console.log('Still offline, cannot sync');
      return;
    }

    const queue = offlineService.getSyncQueue();
    console.log(`Syncing ${queue.length} queued requests`);

    for (const item of queue) {
      try {
        switch (item.method) {
          case 'POST':
            await api.post(item.url, item.data);
            break;
          case 'PUT':
            await api.put(item.url, item.data);
            break;
          case 'DELETE':
            await api.delete(item.url);
            break;
        }
        
        // Remove from queue on success
        offlineService.removeFromSyncQueue(item.id);
        console.log(`Synced: ${item.method} ${item.url}`);
      } catch (error) {
        console.error(`Failed to sync: ${item.method} ${item.url}`, error);
        // Keep in queue for retry
      }
    }
  }

  /**
   * Get athletes with offline support
   */
  async getAthletes(userId: string): Promise<any[]> {
    try {
      const response = await this.get(`/atlet?user_id=${userId}`);
      const athletes = response.data.data?.atlet || [];
      offlineService.cacheAthletes(userId, athletes);
      return athletes;
    } catch (error) {
      // Return cached data if offline
      const cached = offlineService.getCachedAthletes(userId);
      if (cached) {
        console.log('Using cached athletes data');
        return cached;
      }
      throw error;
    }
  }

  /**
   * Get events with offline support
   */
  async getEvents(userId?: string): Promise<any[]> {
    try {
      const endpoint = userId ? `/events?user_id=${userId}` : '/events';
      const response = await this.get(endpoint);
      const events = response.data.data?.events || [];
      if (userId) {
        offlineService.cacheEvents(userId, events);
      }
      return events;
    } catch (error) {
      // Return cached data if offline
      if (userId) {
        const cached = offlineService.getCachedEvents(userId);
        if (cached) {
          console.log('Using cached events data');
          return cached;
        }
      }
      throw error;
    }
  }

  /**
   * Get dashboard stats with offline support
   */
  async getDashboardStats(userId: string): Promise<any> {
    try {
      const response = await this.get('/dashboard/stats');
      const stats = response.data.data;
      offlineService.cacheDashboardStats(userId, stats);
      return stats;
    } catch (error) {
      // Return cached data if offline
      const cached = offlineService.getCachedDashboardStats(userId);
      if (cached) {
        console.log('Using cached dashboard stats');
        return cached;
      }
      throw error;
    }
  }
}

export const enhancedApi = new EnhancedApiService();
export { api };
export default api;
