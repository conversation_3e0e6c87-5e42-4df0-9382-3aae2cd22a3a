'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import Badge from '@/components/ui/Badge';
import { kontingenService } from '@/services/kontingenService';
import {
  MagnifyingGlassIcon,
  EyeIcon,
  UsersIcon,
  MapPinIcon,
  PhoneIcon,
  EnvelopeIcon,
} from '@heroicons/react/24/outline';

interface Kontingen {
  id: number;
  name: string;
  alamat?: string;
  no_hp?: string;
  email?: string;
  id_negara: number;
  id_provinsi: number;
  id_kabupaten_kota: number;
  id_user: number;
  created_at: string;
  updated_at: string;
  kontingenUser?: {
    id: number;
    name: string;
    email: string;
  };
  negara?: {
    id: number;
    name: string;
  };
  provinsi?: {
    id: number;
    name: string;
  };
  kabupatenKota?: {
    id: number;
    name: string;
  };
  athleteCount?: number;
  officialCount?: number;
}

const TeamsPage = () => {
  const { user } = useAuth();
  const [teams, setTeams] = useState<Kontingen[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const fetchTeams = async () => {
    try {
      setLoading(true);
      const response = await kontingenService.getAllKontingen(currentPage, 10, searchTerm);
      setTeams(response.kontingen);
      setTotalPages(response.pagination.totalPages);
    } catch (error: any) {
      console.error('Error fetching teams:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTeams();
  }, [currentPage, searchTerm]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchTeams();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID');
  };

  if (user?.role !== 'admin-event') {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold text-white">Access Denied</h1>
          <p className="text-gray-300 mt-2">You don't have permission to access this page.</p>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-white">Teams Management</h1>
            <p className="text-gray-400 mt-1">View and manage registered teams</p>
          </div>
        </div>

        {/* Search */}
        <Card className="p-6">
          <form onSubmit={handleSearch} className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search teams..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Button type="submit" className="bg-gold-500 hover:bg-gold-600 text-black">
              Search
            </Button>
          </form>
        </Card>

        {/* Teams List */}
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <LoadingSpinner size="lg" />
          </div>
        ) : teams.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {teams.map((team) => (
              <Card key={team.id} className="overflow-hidden">
                <div className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h3 className="text-xl font-semibold text-white mb-2">{team.name}</h3>
                      <p className="text-gray-400 text-sm">
                        Registered: {formatDate(team.created_at)}
                      </p>
                    </div>
                  </div>

                  <div className="space-y-3 mb-4">
                    {team.kontingenUser && (
                      <div className="flex items-start space-x-3">
                        <UsersIcon className="h-4 w-4 text-gold-400 mt-1" />
                        <div>
                          <p className="text-white text-sm font-medium">Team Leader</p>
                          <p className="text-gray-300 text-sm">{team.kontingenUser.name}</p>
                          <p className="text-gray-400 text-xs">{team.kontingenUser.email}</p>
                        </div>
                      </div>
                    )}

                    {team.alamat && (
                      <div className="flex items-start space-x-3">
                        <MapPinIcon className="h-4 w-4 text-gold-400 mt-1" />
                        <div>
                          <p className="text-white text-sm font-medium">Address</p>
                          <p className="text-gray-300 text-sm">{team.alamat}</p>
                          <p className="text-gray-400 text-xs">
                            {team.kabupatenKota?.name}, {team.provinsi?.name}, {team.negara?.name}
                          </p>
                        </div>
                      </div>
                    )}

                    {team.no_hp && (
                      <div className="flex items-center space-x-3">
                        <PhoneIcon className="h-4 w-4 text-gold-400" />
                        <div>
                          <p className="text-white text-sm font-medium">Phone</p>
                          <p className="text-gray-300 text-sm">{team.no_hp}</p>
                        </div>
                      </div>
                    )}

                    {team.email && (
                      <div className="flex items-center space-x-3">
                        <EnvelopeIcon className="h-4 w-4 text-gold-400" />
                        <div>
                          <p className="text-white text-sm font-medium">Email</p>
                          <p className="text-gray-300 text-sm">{team.email}</p>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Stats */}
                  <div className="grid grid-cols-2 gap-4 mb-4 p-3 bg-gray-800 rounded-lg">
                    <div className="text-center">
                      <p className="text-gold-400 font-semibold text-lg">
                        {team.athleteCount || 0}
                      </p>
                      <p className="text-gray-400 text-xs">Athletes</p>
                    </div>
                    <div className="text-center">
                      <p className="text-gold-400 font-semibold text-lg">
                        {team.officialCount || 0}
                      </p>
                      <p className="text-gray-400 text-xs">Officials</p>
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1"
                      onClick={() => {
                        // Navigate to team detail page
                        window.location.href = `/dashboard/teams/${team.id}`;
                      }}
                    >
                      <EyeIcon className="h-4 w-4 mr-1" />
                      View Details
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        ) : (
          <Card className="p-8 text-center">
            <UsersIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-white mb-2">No Teams Found</h3>
            <p className="text-gray-400">No teams match your search criteria.</p>
          </Card>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex justify-center space-x-2">
            <Button
              variant="outline"
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            <span className="px-4 py-2 bg-gray-800 text-white rounded-md">
              {currentPage} of {totalPages}
            </span>
            <Button
              variant="outline"
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default TeamsPage;
