# 📧 Panduan Setup Gmail untuk OTP

## ⚠️ PENTING: Gunakan App Password, Bukan Password Biasa!

Gmail tidak mengizinkan aplikasi menggunakan password biasa untuk keamanan. Anda harus membuat **App Password** khusus.

## 🔧 Cara Membuat Gmail App Password:

### 1. **Aktifkan 2-Step Verification**
1. <PERSON><PERSON> [Google Account](https://myaccount.google.com/)
2. Klik **Security** di sidebar kiri
3. <PERSON> bagian "Signing in to Google", klik **2-Step Verification**
4. Ikuti langkah-langkah untuk mengaktifkan 2FA (jika belum aktif)

### 2. **Buat App Password**
1. Setelah 2-Step Verification aktif, kembali ke **Security**
2. <PERSON> bagian "Signing in to Google", klik **App passwords**
3. Pilih **Mail** untuk app type
4. Pilih **Other (custom name)** untuk device
5. <PERSON><PERSON><PERSON> nama: "BAJA Event Organizer"
6. Klik **Generate**
7. **COPY** password 16 karakter yang muncul (contoh: `abcd efgh ijkl mnop`)

### 3. **Update .env File**
```env
# Email Configuration (Gmail)
EMAIL_USER=<EMAIL>
EMAIL_PASS=abcd efgh ijkl mnop  # Ganti dengan App Password yang baru dibuat
```

## 🚀 Testing Email Service

Setelah setup App Password, test dengan:

```bash
cd baja-backend
npm run build
npm start
```

Kemudian test registrasi di frontend untuk melihat apakah OTP terkirim.

## 🔍 Troubleshooting

### **Error: "Invalid login"**
- Pastikan menggunakan App Password, bukan password Gmail biasa
- Pastikan 2-Step Verification sudah aktif
- Coba generate App Password baru

### **Error: "Less secure app access"**
- Gmail sudah tidak mendukung "less secure apps"
- WAJIB menggunakan App Password

### **Email tidak terkirim**
- Cek spam folder
- Pastikan EMAIL_USER dan EMAIL_PASS benar di .env
- Restart server setelah update .env

## 📝 Format App Password

App Password biasanya dalam format: `abcd efgh ijkl mnop`
- 16 karakter
- Dibagi 4 grup dengan spasi
- Bisa ditulis dengan atau tanpa spasi di .env

## ✅ Setelah Setup Berhasil

Anda akan bisa:
- ✅ Registrasi dengan OTP email
- ✅ Login dengan OTP email  
- ✅ Reset password dengan OTP
- ✅ Welcome email otomatis

## 🔐 Keamanan

App Password ini:
- ✅ Lebih aman dari password biasa
- ✅ Bisa di-revoke kapan saja
- ✅ Khusus untuk aplikasi tertentu
- ✅ Tidak memberikan akses penuh ke akun Google

---

**Catatan**: Tanpa App Password yang benar, fitur OTP email tidak akan berfungsi!
