<?php

namespace App\Controllers\KetuaKontingen;

use App\Controllers\BaseController;
use App\Models\User;
use App\Models\Kontingen;
use App\Models\Negara;
use App\Models\Provinsi;
use App\Models\KabupatenKota;

class KontingenController extends BaseController
{
    public function index()
    {
        $userId = session()->get('id');
        if (!$userId) {
            return redirect()->to('/login')->with('error', 'Anda harus login terlebih dahulu.');
        }

        $userModel      = new User();
        $kontingenModel = new Kontingen();
        $negaraModel    = new Negara();

        $data['user']      = $userModel->find($userId);
        $data['kontingen'] = $kontingenModel->where('id_user', $userId)->first();
        $data['negara']    = $negaraModel->findAll();

        return view('ketua-kontingen/kontingen/index', $data);
    }

    public function store()
    {
        $userId = session()->get('id');
        if (!$userId) {
            return redirect()->to('/login')->with('error', 'Anda harus login terlebih dahulu.');
        }

        $validationRules = [
            'name'            => 'required',
            'negara'          => 'required',
            'provinsi'        => 'required',
            'kabupaten_kota'  => 'required',
        ];

        if (!$this->validate($validationRules)) {
            return redirect()->back()
                ->withInput()
                ->with('errors', $this->validator->getErrors());
        }

        $kontingenModel = new Kontingen();
        $data = [
            'name'           => strtoupper(trim($this->request->getPost('name'))),
            'negara'         => $this->request->getPost('negara'),
            'provinsi'       => $this->request->getPost('provinsi'),
            'kabupaten_kota' => $this->request->getPost('kabupaten_kota'),
            'id_user'        => $userId,
        ];

        // Jika sudah ada data kontingen untuk user tersebut, update; jika belum, insert data baru.
        $existingKontingen = $kontingenModel->where('id_user', $userId)->first();
        if ($existingKontingen) {
            if (!$kontingenModel->update($existingKontingen['id'], $data)) {
                return redirect()->back()->with('error', 'Gagal memperbarui data kontingen.');
            }
        } else {
            if (!$kontingenModel->insert($data)) {
                return redirect()->back()->with('error', 'Gagal menyimpan data kontingen.');
            }
        }

        return redirect()->to('/kontingen')->with('success', 'Data kontingen berhasil disimpan.');
    }

    public function getProvinsi($id_negara)
    {
        $provinsiModel = new Provinsi();
        $data = $provinsiModel->getProvinsi($id_negara);
        return $this->response->setJSON($data);
    }

    public function getKabupatenKota($id_provinsi)
    {
        $kabupatenKotaModel = new KabupatenKota();
        $data = $kabupatenKotaModel->getKabupatenKota($id_provinsi);
        return $this->response->setJSON($data);
    }
}
