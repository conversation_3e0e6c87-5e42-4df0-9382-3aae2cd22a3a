import { Response } from 'express';
import { AuthRequest } from '../middleware/auth.middleware';
import Official from '../models/Official';
import Kontingen from '../models/Kontingen';
import { Op } from 'sequelize';
import { officialUpload, deleteFromCloudinary, getOptimizedUrl, extractPublicId } from '../services/cloudinary.service';

export const upload = officialUpload;

export const getAllOfficial = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { page = 1, limit = 10, search = '', kontingen_id = '' } = req.query;
    const offset = (Number(page) - 1) * Number(limit);
    const user = req.user;

    const whereClause: any = {};
    
    // Filter berdasarkan role
    if (user.role === 'ketua-kontingen') {
      const kontingen = await Kontingen.findOne({ where: { id_user: user.id } });
      if (kontingen) {
        whereClause.id_kontingen = kontingen.id;
      } else {
        res.status(404).json({
          success: false,
          message: 'Kontingen not found. Please register your kontingen first.'
        });
        return;
      }
    }

    if (kontingen_id) {
      whereClause.id_kontingen = kontingen_id;
    }

    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { alamat: { [Op.like]: `%${search}%` } }
      ];
    }

    const { count, rows } = await Official.findAndCountAll({
      where: whereClause,
      limit: Number(limit),
      offset,
      order: [['created_at', 'DESC']],
      attributes: ['id', 'profile', 'name', 'no_hp', 'alamat', 'agama', 'jenis_kelamin', 'id_kontingen', 'created_at', 'updated_at'],
      include: [
        {
          model: Kontingen,
          as: 'officialKontingen',
          attributes: ['id', 'name']
        }
      ]
    });

    res.json({
      success: true,
      message: 'Officials retrieved successfully',
      data: {
        official: rows,
        pagination: {
          total: count,
          page: Number(page),
          limit: Number(limit),
          totalPages: Math.ceil(count / Number(limit))
        }
      }
    });
  } catch (error) {
    console.error('Get all official error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const getOfficialById = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const user = req.user;
    
    const whereClause: any = { id };
    
    // Ketua kontingen hanya bisa melihat official dari kontingen mereka
    if (user.role === 'ketua-kontingen') {
      const kontingen = await Kontingen.findOne({ where: { id_user: user.id } });
      if (kontingen) {
        whereClause.id_kontingen = kontingen.id;
      } else {
        res.status(404).json({
          success: false,
          message: 'Kontingen not found'
        });
        return;
      }
    }

    const official = await Official.findOne({
      where: whereClause,
      include: [
        {
          model: Kontingen,
          as: 'officialKontingen',
          attributes: ['id', 'name']
        }
      ]
    });

    if (!official) {
      res.status(404).json({
        success: false,
        message: 'Official not found'
      });
      return;
    }

    res.json({
      success: true,
      message: 'Official retrieved successfully',
      data: official
    });
  } catch (error) {
    console.error('Get official by id error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const createOfficial = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const user = req.user;
    const {
      profile,
      name,
      no_hp,
      alamat,
      agama,
      jenis_kelamin,
      id_kontingen
    } = req.body;

    let kontingenId = id_kontingen;

    // Untuk ketua kontingen, gunakan kontingen mereka sendiri
    if (user.role === 'ketua-kontingen') {
      const kontingen = await Kontingen.findOne({ where: { id_user: user.id } });
      if (!kontingen) {
        res.status(404).json({
          success: false,
          message: 'Kontingen not found. Please register your kontingen first.'
        });
        return;
      }
      kontingenId = kontingen.id;
    }

    const official = await Official.create({
      id_kontingen: kontingenId,
      profile,
      name,
      no_hp,
      alamat,
      agama,
      jenis_kelamin
    });

    res.status(201).json({
      success: true,
      message: 'Official created successfully',
      data: official
    });
  } catch (error) {
    console.error('Create official error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const updateOfficial = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const user = req.user;
    
    const whereClause: any = { id };
    
    // Ketua kontingen hanya bisa update official dari kontingen mereka
    if (user.role === 'ketua-kontingen') {
      const kontingen = await Kontingen.findOne({ where: { id_user: user.id } });
      if (kontingen) {
        whereClause.id_kontingen = kontingen.id;
      } else {
        res.status(404).json({
          success: false,
          message: 'Kontingen not found'
        });
        return;
      }
    }

    const official = await Official.findOne({ where: whereClause });
    if (!official) {
      res.status(404).json({
        success: false,
        message: 'Official not found'
      });
      return;
    }

    await official.update(req.body);

    res.json({
      success: true,
      message: 'Official updated successfully',
      data: official
    });
  } catch (error) {
    console.error('Update official error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const deleteOfficial = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const user = req.user;
    
    const whereClause: any = { id };
    
    // Ketua kontingen hanya bisa delete official dari kontingen mereka
    if (user.role === 'ketua-kontingen') {
      const kontingen = await Kontingen.findOne({ where: { id_user: user.id } });
      if (kontingen) {
        whereClause.id_kontingen = kontingen.id;
      } else {
        res.status(404).json({
          success: false,
          message: 'Kontingen not found'
        });
        return;
      }
    }

    const official = await Official.findOne({ where: whereClause });
    if (!official) {
      res.status(404).json({
        success: false,
        message: 'Official not found'
      });
      return;
    }

    await official.destroy();

    res.json({
      success: true,
      message: 'Official deleted successfully'
    });
  } catch (error) {
    console.error('Delete official error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const uploadOfficialPhoto = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const user = req.user;

    if (!req.file) {
      res.status(400).json({
        success: false,
        message: 'No file uploaded'
      });
      return;
    }

    const whereClause: any = { id };

    // Ketua kontingen hanya bisa upload foto official dari kontingen mereka
    if (user.role === 'ketua-kontingen') {
      const kontingen = await Kontingen.findOne({ where: { id_user: user.id } });
      if (kontingen) {
        whereClause.id_kontingen = kontingen.id;
      } else {
        res.status(404).json({
          success: false,
          message: 'Kontingen not found'
        });
        return;
      }
    }

    const official = await Official.findOne({ where: whereClause });
    if (!official) {
      res.status(404).json({
        success: false,
        message: 'Official not found'
      });
      return;
    }

    // Delete old photo from Cloudinary if exists
    if (official.profile) {
      const publicId = extractPublicId(official.profile);
      await deleteFromCloudinary(publicId);
    }

    // Update official with new photo URL
    await official.update({ profile: req.file.path });

    res.json({
      success: true,
      message: 'Photo uploaded successfully',
      data: {
        id: official.id,
        profile: req.file.path,
        optimizedUrl: getOptimizedUrl(req.file.filename)
      }
    });
  } catch (error) {
    console.error('Upload official photo error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};
