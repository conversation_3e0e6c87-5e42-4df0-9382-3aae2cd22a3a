const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

async function runMigration() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'baja_db',
    multipleStatements: true
  });

  try {
    console.log('Running security features migration...');

    // Step 1: Add columns to users table
    console.log('Adding security columns to users table...');
    await connection.execute(`
      ALTER TABLE users
      ADD COLUMN google_id VARCHAR(255) UNIQUE NULL,
      ADD COLUMN email_verified BOOLEAN DEFAULT FALSE NOT NULL,
      ADD COLUMN two_factor_enabled BOOLEAN DEFAULT FALSE NOT NULL,
      ADD COLUMN two_factor_secret VARCHAR(255) NULL
    `);

    // Step 2: Create OTPs table
    console.log('Creating OTPs table...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS otps (
        id INT AUTO_INCREMENT PRIMARY KEY,
        email VARCHAR(255) NOT NULL,
        otp_code VARCHAR(6) NOT NULL,
        otp_type ENUM('registration', 'login', 'password_reset') NOT NULL,
        expires_at DATETIME NOT NULL,
        is_used BOOLEAN DEFAULT FALSE NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        INDEX idx_email_type (email, otp_type),
        INDEX idx_expires_at (expires_at)
      )
    `);

    // Step 3: Update existing users
    console.log('Updating existing users...');
    await connection.execute(`
      UPDATE users SET email_verified = TRUE WHERE status = '1'
    `);

    console.log('Security features migration completed successfully!');
    console.log('Added:');
    console.log('- Google OAuth support');
    console.log('- Email verification');
    console.log('- Two-factor authentication');
    console.log('- OTP system');

  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  } finally {
    await connection.end();
  }
}

runMigration();
