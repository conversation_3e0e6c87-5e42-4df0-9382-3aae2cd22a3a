'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import { api } from '@/lib/api';
import toast from 'react-hot-toast';
import { 
  UserPlusIcon, 
  CheckCircleIcon, 
  XCircleIcon,
  DocumentArrowUpIcon 
} from '@heroicons/react/24/outline';

interface Athlete {
  id: number;
  nik: string;
  name: string;
  jenis_kelamin: 'M' | 'F';
  umur: number;
  berat_badan: string;
  tinggi_badan: string;
  status_verifikasi: 'pending' | 'verified';
  foto?: string;
}

interface AthleteRegistration {
  id: number;
  jenis_tanding: string;
  kategori_umur?: string;
  kelas_tanding?: string;
  status: 'pending' | 'oncheck' | 'paid';
  bukti_pembayaran?: string;
  atletDetails: {
    id: number;
    atlet: Athlete;
  }[];
}

const AthleteRegistrationPage = () => {
  const params = useParams();
  const router = useRouter();
  const registrationId = params.id as string;

  const [loading, setLoading] = useState(true);
  const [athletes, setAthletes] = useState<Athlete[]>([]);
  const [registrations, setRegistrations] = useState<AthleteRegistration[]>([]);
  const [selectedAthletes, setSelectedAthletes] = useState<number[]>([]);
  const [formData, setFormData] = useState({
    jenis_tanding: '',
    kategori_umur: '',
    kelas_tanding: ''
  });
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    fetchData();
  }, [registrationId]);

  const fetchData = async () => {
    try {
      setLoading(true);
      
      // Fetch available athletes from kontingen
      const athletesResponse = await api.get('/atlet');
      if (athletesResponse.data.success) {
        setAthletes(athletesResponse.data.data.atlet || []);
      }

      // Fetch existing athlete registrations for this event
      const registrationsResponse = await api.get(`/pendaftaran-atlet/event/${registrationId}`);
      if (registrationsResponse.data.success) {
        setRegistrations(registrationsResponse.data.data || []);
      }
    } catch (error: any) {
      console.error('Error fetching data:', error);
      toast.error('Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const handleAthleteSelect = (athleteId: number) => {
    setSelectedAthletes(prev => 
      prev.includes(athleteId) 
        ? prev.filter(id => id !== athleteId)
        : [...prev, athleteId]
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (selectedAthletes.length === 0) {
      toast.error('Please select at least one athlete');
      return;
    }

    if (!formData.jenis_tanding) {
      toast.error('Please fill in competition type');
      return;
    }

    try {
      setSubmitting(true);

      const registrationData = {
        id_pendaftaran_event: parseInt(registrationId),
        jenis_tanding: formData.jenis_tanding,
        kategori_umur: formData.kategori_umur,
        kelas_tanding: formData.kelas_tanding,
        athlete_ids: selectedAthletes
      };

      const response = await api.post('/pendaftaran-atlet', registrationData);
      
      if (response.data.success) {
        toast.success('Athletes registered successfully!');
        setSelectedAthletes([]);
        setFormData({
          jenis_tanding: '',
          kategori_umur: '',
          kelas_tanding: ''
        });
        fetchData(); // Refresh data
      }
    } catch (error: any) {
      console.error('Error registering athletes:', error);
      toast.error(error.response?.data?.message || 'Failed to register athletes');
    } finally {
      setSubmitting(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'verified':
        return <CheckCircleIcon className="h-5 w-5 text-green-400" />;
      case 'pending':
        return <XCircleIcon className="h-5 w-5 text-yellow-400" />;
      default:
        return <XCircleIcon className="h-5 w-5 text-gray-400" />;
    }
  };

  const getRegistrationStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-500/20 text-green-400 border border-green-500/30';
      case 'oncheck':
        return 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30';
      case 'pending':
        return 'bg-red-500/20 text-red-400 border border-red-500/30';
      default:
        return 'bg-gray-500/20 text-gray-400 border border-gray-500/30';
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center h-64">
          <LoadingSpinner size="lg" />
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-white">
              Register <span className="text-gold-400">Athletes</span>
            </h1>
            <p className="text-gray-300 mt-1">Select athletes to register for this event</p>
          </div>
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            Back to Registrations
          </Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Registration Form */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <UserPlusIcon className="h-5 w-5 mr-2" />
                Register New Athletes
              </CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Competition Type *
                  </label>
                  <Input
                    name="jenis_tanding"
                    value={formData.jenis_tanding}
                    onChange={handleChange}
                    placeholder="e.g., Individual, Team"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Age Category
                  </label>
                  <Input
                    name="kategori_umur"
                    value={formData.kategori_umur}
                    onChange={handleChange}
                    placeholder="e.g., U-17, U-20, Senior"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Competition Class
                  </label>
                  <Input
                    name="kelas_tanding"
                    value={formData.kelas_tanding}
                    onChange={handleChange}
                    placeholder="e.g., Lightweight, Heavyweight"
                  />
                </div>

                {/* Athletes Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Select Athletes *
                  </label>
                  <div className="space-y-2 max-h-64 overflow-y-auto">
                    {athletes.map((athlete) => (
                      <div
                        key={athlete.id}
                        className={`
                          p-3 border rounded-lg cursor-pointer transition-colors
                          ${selectedAthletes.includes(athlete.id)
                            ? 'border-gold-500 bg-gold-500/10'
                            : 'border-gray-600 hover:border-gray-500'
                          }
                        `}
                        onClick={() => handleAthleteSelect(athlete.id)}
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="font-medium text-white">{athlete.name}</div>
                            <div className="text-sm text-gray-400">
                              {athlete.jenis_kelamin === 'M' ? 'Male' : 'Female'}, {athlete.umur} years
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            {getStatusBadge(athlete.status_verifikasi)}
                            <input
                              type="checkbox"
                              checked={selectedAthletes.includes(athlete.id)}
                              onChange={() => handleAthleteSelect(athlete.id)}
                              className="h-4 w-4 text-gold-500 focus:ring-gold-500 border-gray-600 bg-gray-800 rounded"
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <Button
                  type="submit"
                  className="w-full"
                  disabled={submitting || selectedAthletes.length === 0}
                  loading={submitting}
                >
                  Register Selected Athletes
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* Existing Registrations */}
          <Card>
            <CardHeader>
              <CardTitle>Registered Athletes</CardTitle>
            </CardHeader>
            <CardContent>
              {registrations.length === 0 ? (
                <div className="text-center py-8">
                  <DocumentArrowUpIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-400">No athletes registered yet</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {registrations.map((registration) => (
                    <div key={registration.id} className="border border-gray-600 rounded-lg p-4">
                      <div className="flex justify-between items-start mb-3">
                        <div>
                          <h4 className="font-medium text-white">{registration.jenis_tanding}</h4>
                          {registration.kategori_umur && (
                            <p className="text-sm text-gray-400">Category: {registration.kategori_umur}</p>
                          )}
                          {registration.kelas_tanding && (
                            <p className="text-sm text-gray-400">Class: {registration.kelas_tanding}</p>
                          )}
                        </div>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRegistrationStatusBadge(registration.status)}`}>
                          {registration.status}
                        </span>
                      </div>
                      
                      <div className="space-y-2">
                        {registration.atletDetails.map((detail) => (
                          <div key={detail.id} className="flex items-center justify-between bg-gray-800 p-2 rounded">
                            <div className="flex items-center">
                              {detail.atlet.foto ? (
                                <img 
                                  src={detail.atlet.foto} 
                                  alt={detail.atlet.name}
                                  className="h-8 w-8 rounded-full mr-3 object-cover"
                                />
                              ) : (
                                <div className="h-8 w-8 bg-gray-600 rounded-full mr-3 flex items-center justify-center">
                                  <span className="text-xs text-gray-300">
                                    {detail.atlet.name.charAt(0)}
                                  </span>
                                </div>
                              )}
                              <div>
                                <div className="text-sm font-medium text-white">{detail.atlet.name}</div>
                                <div className="text-xs text-gray-400">
                                  {detail.atlet.jenis_kelamin === 'M' ? 'Male' : 'Female'}, {detail.atlet.umur} years
                                </div>
                              </div>
                            </div>
                            {getStatusBadge(detail.atlet.status_verifikasi)}
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default AthleteRegistrationPage;
