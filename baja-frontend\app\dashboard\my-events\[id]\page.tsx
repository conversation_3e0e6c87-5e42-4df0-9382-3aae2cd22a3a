'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useParams } from 'next/navigation';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import Badge from '@/components/ui/Badge';
import { api } from '@/lib/api';
import { downloadPdfFromCloudinary, createPdfFilename } from '@/lib/pdf.utils';
import Link from 'next/link';
import {
  ArrowLeftIcon,
  PencilIcon,
  CalendarDaysIcon,
  MapPinIcon,
  CurrencyDollarIcon,
  UsersIcon,
  ClockIcon,
  DocumentTextIcon,
  ArrowDownTrayIcon,
} from '@heroicons/react/24/outline';

interface Event {
  id: number;
  name: string;
  description?: string;
  start_date: string;
  end_date: string;
  lokasi: string;
  biaya_registrasi: number;
  metode_pembayaran: string;
  status: 'active' | 'completed';
  event_image?: string;
  event_proposal?: string;
  event_pemenang?: string;
  id_user: number;
  created_at: string;
  eventUser?: {
    id: number;
    name: string;
    email: string;
  };
}

interface Registration {
  id: number;
  id_event: number;
  id_kontingen: number;
  created_at: string;
  kontingen?: {
    id: number;
    name: string;
  };
}

const EventDetailPage = () => {
  const { user } = useAuth();
  const params = useParams();
  const eventId = params.id as string;
  
  const [event, setEvent] = useState<Event | null>(null);
  const [registrations, setRegistrations] = useState<Registration[]>([]);
  const [loading, setLoading] = useState(true);
  const [registrationsLoading, setRegistrationsLoading] = useState(true);
  const fetchEvent = async () => {
    try {
      setLoading(true);
      const response = await api.get(`/events/${eventId}`);
      
      if (response.data.success) {
        setEvent(response.data.data);
      }
    } catch (error: any) {
      console.error('Error fetching event:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchRegistrations = async () => {
    try {
      setRegistrationsLoading(true);
      const response = await api.get('/pendaftaran', {
        params: { event_id: eventId, limit: 100 }
      });
      
      if (response.data.success) {
        setRegistrations(response.data.data.pendaftaran || []);
      }
    } catch (error: any) {
      console.error('Error fetching registrations:', error);
    } finally {
      setRegistrationsLoading(false);
    }
  };

  useEffect(() => {
    if (eventId) {
      fetchEvent();
      fetchRegistrations();
    }
  }, [eventId]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR'
    }).format(amount);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="success">Active</Badge>;
      case 'completed':
        return <Badge variant="secondary">Completed</Badge>;
      default:
        return <Badge variant="warning">Unknown</Badge>;
    }
  };

  const handleDownloadPdf = async () => {
    if (!event?.event_proposal) return;

    const filename = createPdfFilename(event.name, 'proposal');
    await downloadPdfFromCloudinary(event.event_proposal, filename);
  };

  if (user?.role !== 'admin-event') {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold text-white">Access Denied</h1>
          <p className="text-gray-300 mt-2">You don't have permission to access this page.</p>
        </div>
      </DashboardLayout>
    );
  }

  // Check if admin-event is trying to access event that doesn't belong to them
  if (event && user?.role === 'admin-event' && event.eventUser?.id !== user.id) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold text-white">Access Denied</h1>
          <p className="text-gray-300 mt-2">You can only view events that you have organized.</p>
          <Link href="/dashboard/my-events" className="mt-4 inline-block">
            <Button>Back to My Events</Button>
          </Link>
        </div>
      </DashboardLayout>
    );
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center h-64">
          <LoadingSpinner size="lg" />
        </div>
      </DashboardLayout>
    );
  }

  if (!event) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold text-white">Event Not Found</h1>
          <p className="text-gray-300 mt-2">The event you're looking for doesn't exist.</p>
          <Link href="/dashboard/my-events" className="mt-4 inline-block">
            <Button>Back to Events</Button>
          </Link>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/dashboard/my-events">
              <Button variant="outline" size="sm">
                <ArrowLeftIcon className="h-4 w-4 mr-2" />
                Back to Events
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-white">{event.name}</h1>
              <p className="text-gray-400 mt-1">Event Details</p>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            {getStatusBadge(event.status)}
            <Link href={`/dashboard/my-events/${event.id}/edit`}>
              <Button className="bg-gold-500 hover:bg-gold-600 text-black">
                <PencilIcon className="h-5 w-5 mr-2" />
                Edit Event
              </Button>
            </Link>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Event Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <Card className="p-6">
              <h2 className="text-xl font-semibold text-white mb-4">Event Information</h2>
              
              {event.event_image && (
                <div className="mb-6">
                  <img
                    src={event.event_image}
                    alt={event.name}
                    className="w-full h-64 object-cover rounded-lg"
                  />
                </div>
              )}

              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <CalendarDaysIcon className="h-5 w-5 text-gold-400 mt-1" />
                  <div>
                    <p className="text-white font-medium">Event Period</p>
                    <p className="text-gray-300">{formatDate(event.start_date)} - {formatDate(event.end_date)}</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <MapPinIcon className="h-5 w-5 text-gold-400 mt-1" />
                  <div>
                    <p className="text-white font-medium">Location</p>
                    <p className="text-gray-300">{event.lokasi}</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <CurrencyDollarIcon className="h-5 w-5 text-gold-400 mt-1" />
                  <div>
                    <p className="text-white font-medium">Registration Fee</p>
                    <p className="text-gray-300">{formatCurrency(event.biaya_registrasi)}</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <ClockIcon className="h-5 w-5 text-gold-400 mt-1" />
                  <div>
                    <p className="text-white font-medium">Payment Method</p>
                    <p className="text-gray-300 capitalize">{event.metode_pembayaran}</p>
                  </div>
                </div>

                {event.description && (
                  <div>
                    <p className="text-white font-medium mb-2">Description</p>
                    <p className="text-gray-300">{event.description}</p>
                  </div>
                )}
              </div>
            </Card>

            {/* Event Proposal */}
            {event.event_proposal && (
              <Card className="p-6">
                <h2 className="text-xl font-semibold text-white mb-4">Event Proposal</h2>

                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-gray-800 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <DocumentTextIcon className="h-8 w-8 text-red-400" />
                      <div>
                        <p className="text-white font-medium">Proposal Document</p>
                        <p className="text-gray-400 text-sm">PDF Document</p>
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleDownloadPdf}
                      className="border-green-500/30 text-green-400 hover:bg-green-500/10"
                    >
                      <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                      Download Proposal
                    </Button>
                  </div>


                </div>
              </Card>
            )}
          </div>

          {/* Registrations */}
          <div className="space-y-6">
            <Card className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-white">Registrations</h2>
                <div className="flex items-center space-x-2">
                  <UsersIcon className="h-5 w-5 text-gold-400" />
                  <span className="text-gold-400 font-medium">{registrations.length}</span>
                </div>
              </div>

              {registrationsLoading ? (
                <div className="flex justify-center py-8">
                  <LoadingSpinner size="md" />
                </div>
              ) : registrations.length > 0 ? (
                <div className="space-y-3">
                  {registrations.map((registration) => (
                    <div key={registration.id} className="p-3 bg-gray-800 rounded-lg">
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="text-white font-medium">
                            {registration.kontingen?.name || 'Unknown Team'}
                          </p>
                          <p className="text-gray-400 text-sm">
                            Registered: {new Date(registration.created_at).toLocaleDateString('id-ID')}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <UsersIcon className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                  <p className="text-gray-400">No registrations yet</p>
                </div>
              )}
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default EventDetailPage;
