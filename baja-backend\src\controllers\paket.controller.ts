import { Response } from 'express';
import { AuthRequest } from '../middleware/auth.middleware';
import Paket from '../models/Paket';
import { Op } from 'sequelize';
import { packageUpload, deleteFromCloudinary, getOptimizedUrl, extractPublicId } from '../services/cloudinary.service';

export const upload = packageUpload;

export const getAllPaket = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { page = 1, limit = 10, search = '', status = '' } = req.query;
    const offset = (Number(page) - 1) * Number(limit);

    const whereClause: any = {};
    
    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { description: { [Op.like]: `%${search}%` } }
      ];
    }

    const { count, rows } = await Paket.findAndCountAll({
      where: whereClause,
      limit: Number(limit),
      offset,
      order: [['created_at', 'DESC']]
    });

    res.json({
      success: true,
      message: 'Packages retrieved successfully',
      data: rows,
      pagination: {
        total: count,
        page: Number(page),
        limit: Number(limit),
        totalPages: Math.ceil(count / Number(limit))
      }
    });
  } catch (error) {
    console.error('Get all paket error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const getPaketById = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    
    const paket = await Paket.findByPk(id);

    if (!paket) {
      res.status(404).json({
        success: false,
        message: 'Package not found'
      });
      return;
    }

    res.json({
      success: true,
      message: 'Package retrieved successfully',
      data: paket
    });
  } catch (error) {
    console.error('Get paket by id error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const createPaket = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const user = req.user;

    // Hanya admin yang bisa membuat paket
    if (user.role !== 'admin') {
      res.status(403).json({
        success: false,
        message: 'Access denied. Only admin can create packages.'
      });
      return;
    }

    const {
      name,
      images,
      description
    } = req.body;

    const paket = await Paket.create({
      name,
      images,
      description
    });

    res.status(201).json({
      success: true,
      message: 'Package created successfully',
      data: paket
    });
  } catch (error) {
    console.error('Create paket error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const updatePaket = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const user = req.user;

    // Hanya admin yang bisa update paket
    if (user.role !== 'admin') {
      res.status(403).json({
        success: false,
        message: 'Access denied. Only admin can update packages.'
      });
      return;
    }

    const paket = await Paket.findByPk(id);
    if (!paket) {
      res.status(404).json({
        success: false,
        message: 'Package not found'
      });
      return;
    }

    await paket.update(req.body);

    res.json({
      success: true,
      message: 'Package updated successfully',
      data: paket
    });
  } catch (error) {
    console.error('Update paket error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const deletePaket = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const user = req.user;

    // Hanya admin yang bisa delete paket
    if (user.role !== 'admin') {
      res.status(403).json({
        success: false,
        message: 'Access denied. Only admin can delete packages.'
      });
      return;
    }

    const paket = await Paket.findByPk(id);
    if (!paket) {
      res.status(404).json({
        success: false,
        message: 'Package not found'
      });
      return;
    }

    await paket.destroy();

    res.json({
      success: true,
      message: 'Package deleted successfully'
    });
  } catch (error) {
    console.error('Delete paket error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const changePaketStatus = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const user = req.user;

    // Hanya admin yang bisa mengubah status paket
    if (user.role !== 'admin') {
      res.status(403).json({
        success: false,
        message: 'Access denied. Only admin can change package status.'
      });
      return;
    }

    const paket = await Paket.findByPk(id);
    if (!paket) {
      res.status(404).json({
        success: false,
        message: 'Package not found'
      });
      return;
    }

    res.json({
      success: true,
      message: 'Package status changed successfully',
      data: { id: paket.id }
    });
  } catch (error) {
    console.error('Change paket status error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const setPopularPaket = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const user = req.user;

    // Hanya admin yang bisa mengubah status popular
    if (user.role !== 'admin') {
      res.status(403).json({
        success: false,
        message: 'Access denied. Only admin can set popular package.'
      });
      return;
    }

    const paket = await Paket.findByPk(id);
    if (!paket) {
      res.status(404).json({
        success: false,
        message: 'Package not found'
      });
      return;
    }

    // Reset semua paket menjadi tidak popular
    await Paket.update(
      { is_popular: false },
      { where: {} }
    );

    // Set paket yang dipilih menjadi popular
    await paket.update({ is_popular: true });

    res.json({
      success: true,
      message: 'Package set as popular successfully',
      data: paket
    });
  } catch (error) {
    console.error('Set popular paket error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const unsetPopularPaket = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const user = req.user;

    // Hanya admin yang bisa mengubah status popular
    if (user.role !== 'admin') {
      res.status(403).json({
        success: false,
        message: 'Access denied. Only admin can unset popular package.'
      });
      return;
    }

    const paket = await Paket.findByPk(id);
    if (!paket) {
      res.status(404).json({
        success: false,
        message: 'Package not found'
      });
      return;
    }

    await paket.update({ is_popular: false });

    res.json({
      success: true,
      message: 'Package popular status removed successfully',
      data: paket
    });
  } catch (error) {
    console.error('Unset popular paket error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const getActivePaket = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const paket = await Paket.findAll({
      order: [['name', 'ASC']]
    });

    res.json({
      success: true,
      message: 'Active packages retrieved successfully',
      data: paket
    });
  } catch (error) {
    console.error('Get active paket error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const setFeaturedPaket = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { featured_order } = req.body;
    const user = req.user;

    // Hanya admin yang bisa mengubah status featured
    if (user.role !== 'admin') {
      res.status(403).json({
        success: false,
        message: 'Access denied. Only admin can set featured package.'
      });
      return;
    }

    const paket = await Paket.findByPk(id);
    if (!paket) {
      res.status(404).json({
        success: false,
        message: 'Package not found'
      });
      return;
    }

    // Validasi featured_order (1-2)
    if (featured_order && (featured_order < 1 || featured_order > 2)) {
      res.status(400).json({
        success: false,
        message: 'Featured order must be between 1 and 2'
      });
      return;
    }

    // Cek apakah sudah ada 2 paket featured
    const featuredCount = await Paket.count({
      where: { is_featured: true }
    });

    if (featuredCount >= 2 && !paket.is_featured) {
      res.status(400).json({
        success: false,
        message: 'Maximum 2 packages can be featured. Please remove one first.'
      });
      return;
    }

    // Jika ada featured_order yang sama, reset yang lama
    if (featured_order) {
      await Paket.update(
        { featured_order: null },
        { where: { featured_order: featured_order } }
      );
    }

    await paket.update({
      is_featured: true,
      featured_order: featured_order || null
    });

    res.json({
      success: true,
      message: 'Package set as featured successfully',
      data: paket
    });
  } catch (error) {
    console.error('Set featured paket error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const unsetFeaturedPaket = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const user = req.user;

    // Hanya admin yang bisa mengubah status featured
    if (user.role !== 'admin') {
      res.status(403).json({
        success: false,
        message: 'Access denied. Only admin can unset featured package.'
      });
      return;
    }

    const paket = await Paket.findByPk(id);
    if (!paket) {
      res.status(404).json({
        success: false,
        message: 'Package not found'
      });
      return;
    }

    await paket.update({
      is_featured: false,
      featured_order: null
    });

    res.json({
      success: true,
      message: 'Package featured status removed successfully',
      data: paket
    });
  } catch (error) {
    console.error('Unset featured paket error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const getFeaturedPaket = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const featuredPaket = await Paket.findAll({
      where: { is_featured: true },
      order: [['featured_order', 'ASC'], ['created_at', 'DESC']],
      limit: 2
    });

    res.json({
      success: true,
      message: 'Featured packages retrieved successfully',
      data: featuredPaket
    });
  } catch (error) {
    console.error('Get featured paket error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const getPopularPaket = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const popularPaket = await Paket.findOne({
      where: { is_popular: true }
    });

    res.json({
      success: true,
      message: 'Popular package retrieved successfully',
      data: popularPaket
    });
  } catch (error) {
    console.error('Get popular paket error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Admin-specific package management
export const getAllPaketsAdmin = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { page = 1, limit = 10, search = '' } = req.query;
    const offset = (Number(page) - 1) * Number(limit);

    const whereClause: any = {};

    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { description: { [Op.like]: `%${search}%` } }
      ];
    }

    const { count, rows } = await Paket.findAndCountAll({
      where: whereClause,
      limit: Number(limit),
      offset,
      order: [['created_at', 'DESC']]
    });

    res.json({
      success: true,
      message: 'Packages retrieved successfully',
      data: {
        packages: rows,
        pagination: {
          total: count,
          page: Number(page),
          limit: Number(limit),
          totalPages: Math.ceil(count / Number(limit))
        }
      }
    });
  } catch (error) {
    console.error('Get packages admin error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const uploadPackageImage = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const user = req.user;

    // Hanya admin yang bisa upload gambar package
    if (user.role !== 'admin') {
      res.status(403).json({
        success: false,
        message: 'Access denied. Only admin can upload package images.'
      });
      return;
    }

    const paket = await Paket.findByPk(id);
    if (!paket) {
      res.status(404).json({
        success: false,
        message: 'Package not found'
      });
      return;
    }

    if (!req.file) {
      res.status(400).json({
        success: false,
        message: 'No file uploaded'
      });
      return;
    }

    // Delete old image from Cloudinary if exists
    if (paket.images) {
      const publicId = extractPublicId(paket.images);
      await deleteFromCloudinary(publicId);
    }

    // Update package with new image URL
    await paket.update({
      images: req.file.path // Cloudinary URL
    });

    res.json({
      success: true,
      message: 'Package image uploaded successfully',
      data: {
        filename: req.file.filename,
        url: req.file.path,
        optimizedUrl: getOptimizedUrl(req.file.filename)
      }
    });
  } catch (error) {
    console.error('Upload package image error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};
