<!DOCTYPE html>
<html lang="en">

<head>

    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="">
    <meta name="author" content="">

    <title>SB Admin 2 - Register</title>

    <!-- Custom fonts for this template-->
    <link href="vendor/fontawesome-free/css/all.min.css" rel="stylesheet" type="text/css">
    <link
        href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i"
        rel="stylesheet">

    <!-- Custom styles for this template-->
    <link href="<?= base_url('assets-be/css/sb-admin-2.min.css') ?>" rel="stylesheet">

</head>

<body class="bg-gradient-primary">

    <div class="container">

        <div class="card o-hidden border-0 shadow-lg my-5">
            <div class="card-body p-0">
                <!-- Nested Row within Card Body -->
                <div class="row">
                <div class="col-lg-6 d-none d-lg-block bg-login-image">
                                <img src="<?= base_url('assets-fe/img/bg-login-baja.jpeg') ?>" 
                                    alt="Baja Event Organizer" 
                                    class="img-fluid"
                                    style="max-width: 100%; margin-top: 50px; margin-left: 50px">
                            </div>
                    <div class="col-lg-6">
                        <div class="p-5">
                            <div class="text-center">
                                <h1 class="h4 text-gray-900 mb-4">Create an Account!</h1>
                            </div>
                            <form class="user" action="<?= base_url('create') ?>" method="POST">
                                <div class="form-group">
                                    <input type="username" class="form-control form-control-user" id="exampleInputEmail" name="name"
                                        placeholder="Nama Lengkap" required>
                                    <?php if(isset(session('errors')['name'])): ?>
                                        <small class="text-danger"><?= session('errors')['name'] ?></small>
                                    <?php endif; ?>
                                </div>
                                <div class="form-group">
                                    <input type="email" class="form-control form-control-user" id="exampleInputEmail" name="email"
                                        placeholder="Email" required>
                                    <?php if(isset(session('errors')['email'])): ?>
                                        <small class="text-danger"><?= session('errors')['email'] ?></small>
                                    <?php endif; ?>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-6 mb-3 mb-sm-0">
                                        <input type="password" class="form-control form-control-user" name="password"
                                            id="exampleInputPassword" placeholder="Password" required>
                                        <?php if(isset(session('errors')['password'])): ?>
                                            <small class="text-danger"><?= session('errors')['password'] ?></small>
                                        <?php endif; ?>
                                    </div>
                                    <div class="col-sm-6">
                                        <input type="password" class="form-control form-control-user"
                                            id="exampleRepeatPassword" name="confirm_password" placeholder="Repeat Password" required>
                                        <?php if(isset(session('errors')['confirm_password'])): ?>
                                            <small class="text-danger"><?= session('errors')['confirm_password'] ?></small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-6 mb-3 mb-sm-0">
                                        <input type="number" class="form-control form-control-user" id="exampleInputEmail" name="no_hp" placeholder="No HP (+62)" required>
                                    </div>
                                    <div class="col-sm-6 mt-2">
                                        <select class="form-control" id="exampleInputEmail" name="agama" required>
                                            <option selected disabled>Agama</option>
                                            <option value="Islam">Islam </option>
                                            <option value="Kristen">Kristen</option>
                                            <option value="Hindu">Hindu</option>
                                            <option value="Budha">Budha</option>
                                            <option value="Konghucu">Konghucu</option>
                                        </select>
                                        <?php if(isset(session('errors')['agama'])): ?>
                                            <small class="text-danger"><?= session('errors')['agama'] ?></small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <input type="text" class="form-control form-control-user" id="exampleInputEmail" name="alamat" placeholder="Alamat" required>
                                    <?php if(isset(session('errors')['alamat'])): ?>
                                        <small class="text-danger"><?= session('errors')['alamat'] ?></small>
                                    <?php endif; ?>
                                </div>
                                <div class="form-group mt-2">
                                        <select class="form-control" id="exampleInputEmail" name="role" required>
                                            <option selected disabled>Mendaftar sebagai</option>
                                            <option value="admin-event">Admin Event </option>
                                            <option value="ketua-kontingen">Ketua Kontingen</option>
                                        </select>
                                        <?php if(isset(session('errors')['role'])): ?>
                                            <small class="text-danger"><?= session('errors')['role'] ?></small>
                                        <?php endif; ?>
                                    </div>
                                <button type="submit" href="<?= base_url('login') ?>" class="btn btn-primary btn-user btn-block">
                                    Register Account
                                </button>
                            </form>
                            <hr>
                            <div class="text-center">
                                <a class="small" href="<?= base_url('login') ?>">Already have an account? Login!</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- Bootstrap core JavaScript-->
    <script src="<?= base_url('assets-be/vendor/jquery/jquery.min.js') ?>"></script>
    <script src="<?= base_url('assets-be/vendor/bootstrap/js/bootstrap.bundle.min.js') ?>"></script>

    <!-- Core plugin JavaScript-->
    <script src="<?= base_url('assets-be/vendor/jquery-easing/jquery.easing.min.js') ?>"></script>

    <!-- Custom scripts for all pages-->
    <script src="<?= base_url('assets-be/js/sb-admin-2.min.js') ?>"></script>

</body>

</html>