import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../config/database';

export interface OTPInterface {
  id: number;
  email: string;
  otp_code: string;
  otp_type: 'registration' | 'login' | 'password_reset';
  expires_at: Date;
  is_used: boolean;
  created_at: Date;
  updated_at: Date;
}

interface OTPCreationAttributes extends Optional<OTPInterface, 'id' | 'created_at' | 'updated_at'> {}

class OTP extends Model<OTPInterface, OTPCreationAttributes> implements OTPInterface {
  public id!: number;
  public email!: string;
  public otp_code!: string;
  public otp_type!: 'registration' | 'login' | 'password_reset';
  public expires_at!: Date;
  public is_used!: boolean;
  public readonly created_at!: Date;
  public readonly updated_at!: Date;
}

OTP.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        isEmail: true,
      },
    },
    otp_code: {
      type: DataTypes.STRING(6),
      allowNull: false,
    },
    otp_type: {
      type: DataTypes.ENUM('registration', 'login', 'password_reset'),
      allowNull: false,
    },
    expires_at: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    is_used: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    tableName: 'otps',
    timestamps: true,
    indexes: [
      {
        fields: ['email', 'otp_type'],
      },
      {
        fields: ['expires_at'],
      },
    ],
  }
);

export default OTP;
