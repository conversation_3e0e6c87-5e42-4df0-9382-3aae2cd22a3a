<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\User;
use App\Models\Event;

class AuthController extends BaseController
{
    protected $userModel;

    public function __construct()
    {
        $this->userModel = new User(); 
    }

    public function index()
    {
        return view('auth/login'); 
    }

    // Di AuthController.php
public function login()
{
    $email = $this->request->getPost('email');
    $password = $this->request->getPost('password');

    $user = $this->userModel->asArray()->where('email', $email)->first();

    if ($user) {
        if ($user['status'] == 1) {
            if (password_verify($password, $user['password'])) {
                
                // Jika role adalah admin-event, ambil event_id dari tabel event
                $event_id = null;
                if ($user['role'] == 'admin-event') {
                    $eventModel = new Event();
                    $event = $eventModel->where('id_user', $user['id'])->first();
                    $event_id = $event ? $event['id'] : null;
                }

                $sessionData = [
                    'id' => $user['id'],
                    'email' => $user['email'],
                    'name' => $user['name'],
                    'role' => $user['role'],
                    'event_id' => $event_id, // Tambahkan event_id ke session
                    'logged_in' => true
                ];
                session()->set($sessionData);
                
                return redirect()->to('/dashboard');
            } else {
                return redirect()->back()->with('error', 'Password salah!');
            }
        } else {
            return redirect()->back()->with('error', 'Akun belum aktif.');
        }
    } else {
        return redirect()->back()->with('error', 'Akun tidak ditemukan.');
    }
}

    public function logout()
    {
        session()->destroy();
        return redirect()->to('/login');
    }

    public function register()
    {
        return view('auth/register'); 
    }

    public function create()
    {
        $password = $this->request->getPost('password');
        $confirmPassword = $this->request->getPost('confirm_password');

        if ($password !== $confirmPassword) {
            return redirect()->back()->with('error', 'Password dan konfirmasi password tidak cocok.');
        }

        $userData = [
            'name' => $this->request->getPost('name'),
            'email' => $this->request->getPost('email'),
            'no_hp' => $this->request->getPost('no_hp'),
            'alamat' => $this->request->getPost('alamat'),
            'agama' => $this->request->getPost('agama'),
            'password' => password_hash($this->request->getPost('password'), PASSWORD_DEFAULT),
            'role' => $this->request->getPost('role'),
            'status' => '1',
        ];
        
        if ($this->userModel->save($userData)) {
            return redirect()->to('/login')->with('success', 'Registrasi berhasil! Silakan login.');
        } else {
            return redirect()->back()->with('error', 'Gagal registrasi. Periksa kembali data Anda.');
        }
    }

    public function forgotPassword()
    {
        return view('auth/forgot-password'); 
    }

    public function resetPassword()
    {
        return view('auth/reset-password'); 
    }
}
