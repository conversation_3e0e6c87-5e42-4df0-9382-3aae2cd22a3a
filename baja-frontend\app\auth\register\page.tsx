'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import GoogleAuthButton from '@/components/auth/GoogleAuthButton';
import { RegisterRequest } from '@/types';

const RegisterPage = () => {
  const [loading, setLoading] = useState(false);
  const { register: registerUser } = useAuth();
  const router = useRouter();

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<RegisterRequest & { confirmPassword: string }>();

  const password = watch('password');

  const onSubmit = async (data: RegisterRequest & { confirmPassword: string }) => {
    try {
      setLoading(true);
      const { confirmPassword, ...registerData } = data;
      await registerUser(registerData);
      // Redirect to OTP verification page
      router.push(`/auth/verify-otp?email=${encodeURIComponent(data.email)}&type=registration`);
    } catch (error) {
      // Error handled by AuthContext
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-black flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="text-center">
          <img
            className="mx-auto h-12 w-auto rounded border border-gold-500/30"
            src="/baja.jpeg"
            alt="BAJA Event Organizer"
          />
          <h2 className="mt-6 text-3xl font-bold text-white">
            Daftar <span className="text-gold-400">Akun Baru</span>
          </h2>
          <p className="mt-2 text-sm text-gray-300">
            Atau{' '}
            <Link
              href="/auth/login"
              className="font-medium text-gold-400 hover:text-gold-300 transition-colors duration-300"
            >
              masuk ke akun yang sudah ada
            </Link>
          </p>
        </div>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <Card>
          <CardHeader>
            <CardTitle className="text-center">Registrasi</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              <Input
                label="Nama Lengkap"
                type="text"
                {...register('name', {
                  required: 'Nama lengkap wajib diisi',
                  minLength: {
                    value: 2,
                    message: 'Nama minimal 2 karakter',
                  },
                })}
                error={errors.name?.message}
                placeholder="Masukkan nama lengkap Anda"
              />

              <Input
                label="Email"
                type="email"
                {...register('email', {
                  required: 'Email wajib diisi',
                  pattern: {
                    value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                    message: 'Format email tidak valid',
                  },
                })}
                error={errors.email?.message}
                placeholder="Masukkan email Anda"
              />

              <Input
                label="Nomor HP"
                type="tel"
                {...register('no_hp', {
                  pattern: {
                    value: /^(\+62|62|0)8[1-9][0-9]{6,9}$/,
                    message: 'Format nomor HP tidak valid',
                  },
                })}
                error={errors.no_hp?.message}
                placeholder="Masukkan nomor HP (opsional)"
              />

              <div>
                <label className="block text-sm font-medium text-white mb-1">
                  Alamat
                </label>
                <textarea
                  {...register('alamat')}
                  placeholder="Masukkan alamat lengkap (opsional)"
                  rows={3}
                  className="flex w-full rounded-md border border-gold-500/30 bg-gray-800 text-white px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-gold-500 focus-visible:ring-offset-2 resize-none"
                />
                {errors.alamat && (
                  <p className="text-red-500 text-sm mt-1">{errors.alamat.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-white mb-1">
                  Agama
                </label>
                <select
                  {...register('agama')}
                  className="flex h-10 w-full rounded-md border border-gold-500/30 bg-gray-800 text-white px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-gold-500 focus-visible:ring-offset-2"
                >
                  <option value="">Pilih Agama (opsional)</option>
                  <option value="Islam">Islam</option>
                  <option value="Kristen">Kristen</option>
                  <option value="Katolik">Katolik</option>
                  <option value="Hindu">Hindu</option>
                  <option value="Buddha">Buddha</option>
                  <option value="Konghucu">Konghucu</option>
                  <option value="Lainnya">Lainnya</option>
                </select>
                {errors.agama && (
                  <p className="text-red-500 text-sm mt-1">{errors.agama.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-white mb-1">
                  Role
                </label>
                <select
                  {...register('role', { required: 'Role harus dipilih' })}
                  className="flex h-10 w-full rounded-md border border-gold-500/30 bg-gray-800 text-white px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-gold-500 focus-visible:ring-offset-2"
                >
                  <option value="">Pilih Role</option>
                  <option value="ketua-kontingen">Ketua Kontingen</option>
                  <option value="admin-event">Admin Event</option>
                </select>
                {errors.role && (
                  <p className="text-red-500 text-sm mt-1">{errors.role.message}</p>
                )}
              </div>

              <Input
                label="Password"
                type="password"
                {...register('password', {
                  required: 'Password wajib diisi',
                  minLength: {
                    value: 6,
                    message: 'Password minimal 6 karakter',
                  },
                })}
                error={errors.password?.message}
                placeholder="Masukkan password Anda"
              />

              <Input
                label="Konfirmasi Password"
                type="password"
                {...register('confirmPassword', {
                  required: 'Konfirmasi password wajib diisi',
                  validate: (value) =>
                    value === password || 'Password tidak cocok',
                })}
                error={errors.confirmPassword?.message}
                placeholder="Konfirmasi password Anda"
              />

              <div className="flex items-center">
                <input
                  id="agree-terms"
                  name="agree-terms"
                  type="checkbox"
                  required
                  className="h-4 w-4 text-gold-500 focus:ring-gold-500 border-gray-600 bg-gray-800 rounded"
                />
                <label htmlFor="agree-terms" className="ml-2 block text-sm text-white">
                  Saya setuju dengan{' '}
                  <a href="#" className="text-gold-400 hover:text-gold-300 transition-colors duration-300">
                    syarat dan ketentuan
                  </a>
                </label>
              </div>

              <Button
                type="submit"
                className="w-full"
                loading={loading}
                disabled={loading}
              >
                {loading ? 'Mendaftar...' : 'Daftar'}
              </Button>
            </form>

            <div className="mt-6">
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gold-500/30" />
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-black text-gray-400">Atau</span>
                </div>
              </div>

              <div className="mt-6">
                <GoogleAuthButton
                  mode="register"
                  disabled={loading}
                />
              </div>

              <div className="mt-6 text-center">
                <p className="text-sm text-gray-300">
                  Sudah punya akun?{' '}
                  <Link
                    href="/auth/login"
                    className="font-medium text-gold-400 hover:text-gold-300 transition-colors duration-300"
                  >
                    Masuk sekarang
                  </Link>
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default RegisterPage;
