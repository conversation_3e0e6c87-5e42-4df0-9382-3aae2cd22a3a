<?php

namespace App\Models;

use CodeIgniter\Model;

class Event extends Model
{
    protected $table            = 'event';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;

    protected $allowedFields    = [
        'name',
        'description',
        'start_date',
        'end_date',
        'lokasi',
        'biaya_registrasi',
        'metode_pembayaran',
        'status',
        'event_image',
        'event_proposal',
        'event_pemenang',
        'id_user',
        'created_at',
        'updated_at'
    ];

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField  = 'updated_at';

    protected $skipValidation  = false;
}
