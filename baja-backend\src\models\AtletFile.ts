import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../config/database';
import Atlet from './Atlet';

interface AtletFileInterface {
  id: number;
  file: string;
  file_type?: 'rapor' | 'kk_ktp' | 'surat_kesehatan';
  id_atlet: number;
  created_at: Date;
  updated_at: Date;
}

interface AtletFileCreationAttributes extends Optional<AtletFileInterface, 'id' | 'created_at' | 'updated_at'> {}

class AtletFile extends Model<AtletFileInterface, AtletFileCreationAttributes> implements AtletFileInterface {
  public id!: number;
  public file!: string;
  public file_type?: 'rapor' | 'kk_ktp' | 'surat_kesehatan';
  public id_atlet!: number;
  public readonly created_at!: Date;
  public readonly updated_at!: Date;
}

AtletFile.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    file: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    file_type: {
      type: DataTypes.ENUM('rapor', 'kk_ktp', 'surat_kesehatan'),
      allowNull: true,
    },
    id_atlet: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: Atlet,
        key: 'id',
      },
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    tableName: 'atlet_file',
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  }
);

export default AtletFile;
