import { Router } from 'express';
import {
  getAllNegara,
  getProvinsiByNegara,
  getKabupatenKotaByProvinsi
} from '../controllers/location.controller';

const router = Router();

// Public routes (no authentication required for reading location data)
router.get('/negara', getAllNegara as any);
router.get('/negara/:negaraId/provinsi', getProvinsiByNegara as any);
router.get('/provinsi/:provinsiId/kabupaten-kota', getKabupatenKotaByProvinsi as any);

export default router;
