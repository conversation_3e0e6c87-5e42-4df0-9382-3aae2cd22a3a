import { Router } from 'express';
import { authorize } from '../middleware/auth.middleware';
import {
  createPendaftaranAtlet,
  getPendaftaranAtletByEvent,
  updatePendaftaranAtlet,
  deletePendaftaranAtlet,
  getAllPendaftaranAtlet
} from '../controllers/pendaftaran-atlet.controller';

const router = Router();

// All routes require authentication
router.use(authorize('admin', 'admin-event', 'ketua-kontingen'));

// Get all athlete registrations (with pagination and filtering)
router.get('/', getAllPendaftaranAtlet);

// Get athlete registrations by event registration ID
router.get('/event/:id', getPendaftaranAtletByEvent);

// Create new athlete registration
router.post('/', createPendaftaranAtlet);

// Update athlete registration
router.put('/:id', updatePendaftaranAtlet);

// Delete athlete registration
router.delete('/:id', deletePendaftaranAtlet);

export default router;
