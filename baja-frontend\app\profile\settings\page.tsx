'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import ProfileLayout from '@/components/layout/ProfileLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { api } from '@/lib/api';
import toast from 'react-hot-toast';
import {
  KeyIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';

const ProfileSettingsPage = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPasswordData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      toast.error('New password and confirmation do not match');
      return;
    }

    if (passwordData.newPassword.length < 6) {
      toast.error('Password must be at least 6 characters long');
      return;
    }

    try {
      setLoading(true);
      const response = await api.put('/auth/change-password', {
        currentPassword: passwordData.currentPassword,
        newPassword: passwordData.newPassword,
      });
      
      if (response.data.success) {
        toast.success('Password changed successfully');
        setPasswordData({
          currentPassword: '',
          newPassword: '',
          confirmPassword: '',
        });
      }
    } catch (error: any) {
      console.error('Error changing password:', error);
      toast.error(error.response?.data?.message || 'Failed to change password');
    } finally {
      setLoading(false);
    }
  };

  const getPasswordStrength = (password: string) => {
    if (password.length === 0) return { strength: 0, label: '', color: '' };
    if (password.length < 6) return { strength: 1, label: 'Weak', color: 'text-red-400' };
    if (password.length < 8) return { strength: 2, label: 'Fair', color: 'text-yellow-400' };
    if (password.length >= 8 && /(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(password)) {
      return { strength: 4, label: 'Strong', color: 'text-green-400' };
    }
    return { strength: 3, label: 'Good', color: 'text-blue-400' };
  };

  const passwordStrength = getPasswordStrength(passwordData.newPassword);

  return (
    <ProtectedRoute>
      <ProfileLayout>
        <div className="space-y-6">
          {/* Header */}
          <div>
            <h1 className="text-3xl font-bold text-white">Account Settings</h1>
            <p className="text-gray-400 mt-2">Manage your account security and preferences</p>
          </div>

          {/* Change Password */}
          <Card className="p-6">
            <div className="flex items-center space-x-3 mb-6">
              <KeyIcon className="h-6 w-6 text-gold-400" />
              <h2 className="text-xl font-semibold text-white">Change Password</h2>
            </div>

            <form onSubmit={handlePasswordSubmit} className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Current Password
                </label>
                <input
                  type="password"
                  name="currentPassword"
                  value={passwordData.currentPassword}
                  onChange={handlePasswordChange}
                  required
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-gold-500 focus:border-transparent"
                  placeholder="Enter your current password"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  New Password
                </label>
                <input
                  type="password"
                  name="newPassword"
                  value={passwordData.newPassword}
                  onChange={handlePasswordChange}
                  required
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-gold-500 focus:border-transparent"
                  placeholder="Enter your new password"
                />
                
                {/* Password Strength Indicator */}
                {passwordData.newPassword && (
                  <div className="mt-2">
                    <div className="flex items-center space-x-2">
                      <div className="flex-1 bg-gray-700 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full transition-all duration-300 ${
                            passwordStrength.strength === 1 ? 'bg-red-500 w-1/4' :
                            passwordStrength.strength === 2 ? 'bg-yellow-500 w-2/4' :
                            passwordStrength.strength === 3 ? 'bg-blue-500 w-3/4' :
                            passwordStrength.strength === 4 ? 'bg-green-500 w-full' : 'w-0'
                          }`}
                        />
                      </div>
                      <span className={`text-sm font-medium ${passwordStrength.color}`}>
                        {passwordStrength.label}
                      </span>
                    </div>
                  </div>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Confirm New Password
                </label>
                <input
                  type="password"
                  name="confirmPassword"
                  value={passwordData.confirmPassword}
                  onChange={handlePasswordChange}
                  required
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-gold-500 focus:border-transparent"
                  placeholder="Confirm your new password"
                />
                
                {/* Password Match Indicator */}
                {passwordData.confirmPassword && (
                  <div className="mt-2">
                    {passwordData.newPassword === passwordData.confirmPassword ? (
                      <div className="flex items-center space-x-2 text-green-400">
                        <CheckCircleIcon className="h-4 w-4" />
                        <span className="text-sm">Passwords match</span>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-2 text-red-400">
                        <ExclamationTriangleIcon className="h-4 w-4" />
                        <span className="text-sm">Passwords do not match</span>
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Password Requirements */}
              <div className="bg-blue-500/10 border border-blue-500/30 rounded-md p-4">
                <div className="flex items-start space-x-3">
                  <ShieldCheckIcon className="h-5 w-5 text-blue-400 mt-0.5" />
                  <div>
                    <h3 className="text-sm font-medium text-blue-400 mb-2">
                      Password Requirements
                    </h3>
                    <ul className="text-sm text-blue-300 space-y-1">
                      <li className="flex items-center space-x-2">
                        <div className={`w-2 h-2 rounded-full ${passwordData.newPassword.length >= 6 ? 'bg-green-400' : 'bg-gray-400'}`} />
                        <span>At least 6 characters long</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <div className={`w-2 h-2 rounded-full ${passwordData.newPassword.length >= 8 ? 'bg-green-400' : 'bg-gray-400'}`} />
                        <span>8+ characters recommended</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <div className={`w-2 h-2 rounded-full ${/(?=.*[a-z])(?=.*[A-Z])/.test(passwordData.newPassword) ? 'bg-green-400' : 'bg-gray-400'}`} />
                        <span>Mix of uppercase and lowercase letters</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <div className={`w-2 h-2 rounded-full ${/(?=.*\d)/.test(passwordData.newPassword) ? 'bg-green-400' : 'bg-gray-400'}`} />
                        <span>Include numbers</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>

              <div className="flex justify-end">
                <Button
                  type="submit"
                  disabled={loading || passwordData.newPassword !== passwordData.confirmPassword || passwordData.newPassword.length < 6}
                  className="bg-gold-500 hover:bg-gold-600 text-black font-semibold"
                >
                  {loading ? 'Changing Password...' : 'Change Password'}
                </Button>
              </div>
            </form>
          </Card>

          {/* Account Security Info */}
          <Card className="p-6">
            <div className="flex items-center space-x-3 mb-4">
              <ShieldCheckIcon className="h-6 w-6 text-gold-400" />
              <h2 className="text-xl font-semibold text-white">Account Security</h2>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-gray-800 rounded-lg">
                <div>
                  <h3 className="text-white font-medium">Account Status</h3>
                  <p className="text-gray-400 text-sm">Your account is active and secure</p>
                </div>
                <div className="flex items-center space-x-2 text-green-400">
                  <CheckCircleIcon className="h-5 w-5" />
                  <span className="text-sm font-medium">Active</span>
                </div>
              </div>

              <div className="flex items-center justify-between p-4 bg-gray-800 rounded-lg">
                <div>
                  <h3 className="text-white font-medium">Email Verification</h3>
                  <p className="text-gray-400 text-sm">Your email address is verified</p>
                </div>
                <div className="flex items-center space-x-2 text-green-400">
                  <CheckCircleIcon className="h-5 w-5" />
                  <span className="text-sm font-medium">Verified</span>
                </div>
              </div>

              <div className="flex items-center justify-between p-4 bg-gray-800 rounded-lg">
                <div>
                  <h3 className="text-white font-medium">Role</h3>
                  <p className="text-gray-400 text-sm">Your current role in the system</p>
                </div>
                <div className="text-gold-400 font-medium">
                  {user?.role === 'admin' ? 'Administrator' :
                   user?.role === 'admin-event' ? 'Event Admin' :
                   user?.role === 'ketua-kontingen' ? 'Team Leader' : user?.role}
                </div>
              </div>
            </div>
          </Card>

          {/* Security Tips */}
          <Card className="p-6">
            <div className="flex items-center space-x-3 mb-4">
              <ExclamationTriangleIcon className="h-6 w-6 text-yellow-400" />
              <h2 className="text-xl font-semibold text-white">Security Tips</h2>
            </div>

            <div className="space-y-3 text-gray-300">
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-gold-400 rounded-full mt-2"></div>
                <p className="text-sm">Use a strong, unique password that you don't use elsewhere</p>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-gold-400 rounded-full mt-2"></div>
                <p className="text-sm">Change your password regularly, especially if you suspect it may be compromised</p>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-gold-400 rounded-full mt-2"></div>
                <p className="text-sm">Never share your password with anyone or write it down in an unsecure location</p>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-gold-400 rounded-full mt-2"></div>
                <p className="text-sm">Log out from shared or public computers after use</p>
              </div>
            </div>
          </Card>
        </div>
      </ProfileLayout>
    </ProtectedRoute>
  );
};

export default ProfileSettingsPage;
