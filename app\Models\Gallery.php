<?php
namespace App\Models;

use CodeIgniter\Model;

class Gallery extends Model
{
    protected $table      = 'gallery';
    protected $primaryKey = 'id';

    protected $returnType = 'array';

    protected $allowedFields = [
        'images',
        'description',
    ];

    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    protected $skipValidation = false;
}
