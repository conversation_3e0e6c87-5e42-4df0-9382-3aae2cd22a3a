import { Router } from 'express';
import { authenticate, authorize, optionalAuth } from '../middleware/auth.middleware';
import {
  getAllEvents,
  getEventById,
  createEvent,
  updateEvent,
  deleteEvent,
  uploadEventFile
} from '../controllers/event.controller';
import { flexibleUpload } from '../services/cloudinary.service';

const router = Router();

// Public routes
router.get('/', optionalAuth, getAllEvents);
router.get('/:id', optionalAuth, getEventById);

// Protected routes
router.use(authenticate);

router.post('/', authorize('admin', 'admin-event'), createEvent);
router.put('/:id', authorize('admin', 'admin-event'), updateEvent);
router.delete('/:id', authorize('admin', 'admin-event'), deleteEvent);
router.post('/:id/upload', authorize('admin', 'admin-event'), flexibleUpload.single('file'), uploadEventFile);

export default router;
