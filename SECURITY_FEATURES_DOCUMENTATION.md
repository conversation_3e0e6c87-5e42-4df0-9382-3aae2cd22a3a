# Dokumentasi Fitur Keamanan BAJA Event Organizer

## 🔐 Fitur Keamanan yang Diimplementasikan

### 1. **Google OAuth Authentication**
- **Login/Register dengan Gmail**: Pengguna dapat masuk atau mendaftar menggunakan akun Google mereka
- **Integrasi Google Sign-In API**: Menggunakan Google's JavaScript API untuk autentikasi yang aman
- **Auto-linking Account**: Jika pengguna sudah memiliki akun dengan email yang sama, sistem akan menghubungkan akun Google

### 2. **OTP (One-Time Password) System**
- **Email Verification**: OTP dikirim ke email pengguna untuk verifikasi
- **Multiple OTP Types**: 
  - Registration OTP (untuk pendaftaran)
  - Login OTP (untuk keamanan tambahan saat login)
  - Password Reset OTP (untuk reset password)
- **OTP Expiry**: Kode OTP berlaku selama 10 menit
- **Resend Functionality**: Pengguna dapat meminta OTP baru dengan cooldown 60 detik

### 3. **Two-Factor Authentication (2FA)**
- **TOTP Support**: Menggunakan Time-based One-Time Password
- **QR Code Generation**: Pengguna dapat scan QR code untuk setup 2FA
- **Backup Codes**: Secret key disimpan untuk recovery
- **Enable/Disable**: Pengguna dapat mengaktifkan atau menonaktifkan 2FA

### 4. **Enhanced Password Security**
- **Bcrypt Hashing**: Password di-hash menggunakan bcrypt dengan salt rounds 12
- **Password Validation**: Minimum 6 karakter
- **Password Change**: Fitur untuk mengubah password dengan verifikasi password lama

### 5. **Email Verification System**
- **Welcome Email**: Email selamat datang setelah registrasi berhasil
- **HTML Email Templates**: Email dengan desain yang menarik
- **Email Status Tracking**: Status verifikasi email disimpan di database

## 🏗️ Struktur Backend

### Database Schema Updates
```sql
-- Tambahan kolom di tabel users
ALTER TABLE users 
ADD COLUMN google_id VARCHAR(255) UNIQUE NULL,
ADD COLUMN email_verified BOOLEAN DEFAULT FALSE NOT NULL,
ADD COLUMN two_factor_enabled BOOLEAN DEFAULT FALSE NOT NULL,
ADD COLUMN two_factor_secret VARCHAR(255) NULL;

-- Tabel baru untuk OTP
CREATE TABLE otps (
  id INT AUTO_INCREMENT PRIMARY KEY,
  email VARCHAR(255) NOT NULL,
  otp_code VARCHAR(6) NOT NULL,
  otp_type ENUM('registration', 'login', 'password_reset') NOT NULL,
  expires_at DATETIME NOT NULL,
  is_used BOOLEAN DEFAULT FALSE NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_email_type (email, otp_type),
  INDEX idx_expires_at (expires_at)
);
```

### New API Endpoints
```
POST /api/v1/auth/register              - Registrasi dengan OTP
POST /api/v1/auth/verify-registration   - Verifikasi OTP registrasi
POST /api/v1/auth/google               - Login dengan Google
POST /api/v1/auth/resend-otp           - Kirim ulang OTP
POST /api/v1/auth/2fa/setup            - Setup 2FA
POST /api/v1/auth/2fa/verify           - Verifikasi 2FA
POST /api/v1/auth/2fa/disable          - Nonaktifkan 2FA
```

### Services Implemented
1. **OTPService**: Mengelola pembuatan, verifikasi, dan pengiriman OTP
2. **EmailService**: Mengirim email dengan template HTML
3. **GoogleAuthService**: Menangani autentikasi Google OAuth
4. **SessionMiddleware**: Mengelola session untuk data registrasi sementara

## 🎨 Struktur Frontend

### New Components
1. **OTPInput**: Komponen input 6 digit OTP dengan auto-focus
2. **GoogleLoginButton**: Tombol login Google dengan loading state
3. **VerifyOTPPage**: Halaman verifikasi OTP dengan countdown resend

### Updated Pages
1. **Register Page**: Terintegrasi dengan Google login dan OTP flow
2. **Login Page**: Ditambahkan Google login button
3. **Verify OTP Page**: Halaman baru untuk verifikasi OTP

### Enhanced AuthContext
- `verifyRegistrationOTP()`: Verifikasi OTP registrasi
- `googleLogin()`: Login dengan Google
- `resendOTP()`: Kirim ulang OTP

## 🔧 Konfigurasi Environment

### Backend (.env)
```env
# Email Configuration
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
FRONTEND_URL=http://localhost:3000

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=http://localhost:5000/auth/google/callback
```

### Frontend (.env.local)
```env
# Google OAuth Configuration
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your-google-client-id
```

## 🚀 Cara Setup

### 1. Setup Google OAuth
1. Buka [Google Cloud Console](https://console.cloud.google.com/)
2. Buat project baru atau pilih project yang ada
3. Enable Google+ API dan Google Sign-In API
4. Buat OAuth 2.0 credentials
5. Tambahkan authorized origins: `http://localhost:3000`
6. Copy Client ID dan Client Secret ke environment variables

### 2. Setup Email Service
1. Buat App Password di Gmail:
   - Buka Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate password untuk aplikasi
2. Update EMAIL_USER dan EMAIL_PASS di .env

### 3. Jalankan Migration
```bash
cd baja-backend
node run-security-migration.js
```

### 4. Install Dependencies
```bash
# Backend
cd baja-backend
npm install

# Frontend
cd baja-frontend
npm install
```

### 5. Jalankan Aplikasi
```bash
# Backend (port 5000)
cd baja-backend
npm run dev

# Frontend (port 3000)
cd baja-frontend
npm run dev
```

## 🔒 Teknologi Keamanan yang Digunakan

1. **bcryptjs**: Hashing password dengan salt
2. **jsonwebtoken**: JWT untuk session management
3. **speakeasy**: TOTP untuk 2FA
4. **qrcode**: Generate QR code untuk 2FA setup
5. **google-auth-library**: Verifikasi Google ID token
6. **nodemailer**: Pengiriman email
7. **express-rate-limit**: Rate limiting untuk API
8. **helmet**: Security headers
9. **cors**: Cross-origin resource sharing
10. **express-validator**: Input validation

## 📱 User Flow

### Registrasi dengan Email
1. User mengisi form registrasi
2. Sistem mengirim OTP ke email
3. User memasukkan OTP di halaman verifikasi
4. Akun dibuat dan email welcome dikirim
5. User dapat login

### Login dengan Google
1. User klik "Masuk dengan Google"
2. Google OAuth popup muncul
3. User authorize aplikasi
4. Sistem membuat/link akun otomatis
5. User langsung masuk ke dashboard

### Setup 2FA
1. User masuk ke pengaturan keamanan
2. Klik "Setup 2FA"
3. Scan QR code dengan authenticator app
4. Masukkan kode verifikasi
5. 2FA aktif untuk login selanjutnya

## 🛡️ Keamanan Tambahan

1. **Session Management**: Session data disimpan sementara untuk OTP flow
2. **Rate Limiting**: Pembatasan request per IP
3. **Input Validation**: Validasi semua input dengan express-validator
4. **CORS Protection**: Konfigurasi CORS yang ketat
5. **Security Headers**: Helmet untuk security headers
6. **Cookie Security**: HttpOnly dan Secure cookies
7. **Password Complexity**: Validasi kekuatan password
8. **OTP Cleanup**: Otomatis hapus OTP yang expired

## 📋 Testing

Untuk testing fitur keamanan:

1. **Test Registrasi OTP**:
   - Daftar dengan email valid
   - Cek email untuk OTP
   - Verifikasi dengan kode yang benar/salah

2. **Test Google Login**:
   - Setup Google OAuth credentials
   - Test login dengan akun Google
   - Verify account linking

3. **Test 2FA**:
   - Setup 2FA dengan authenticator app
   - Test login dengan 2FA enabled
   - Test disable 2FA

4. **Test Email Service**:
   - Verify email templates
   - Test different OTP types
   - Check email delivery

## 🔄 Maintenance

1. **OTP Cleanup**: Jalankan cleanup expired OTPs secara berkala
2. **Session Cleanup**: Bersihkan session yang expired
3. **Email Monitoring**: Monitor email delivery rates
4. **Security Updates**: Update dependencies secara berkala
5. **Log Monitoring**: Monitor authentication logs untuk aktivitas mencurigakan

Fitur keamanan ini menggunakan teknologi terbaik yang tersedia secara gratis dan mengikuti best practices untuk keamanan aplikasi web modern.
