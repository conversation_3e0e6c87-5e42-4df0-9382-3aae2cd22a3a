<?= $this->extend('layouts/backend/template') ?>
<?= $this->section('content') ?>

<div id="content">
    <div class="container-fluid">
        <!-- Page Heading -->
        <h1 class="h3 mb-2 text-gray-800">Official</h1>
        <h6 class="m-0 mb-3 font-weight-bold text-primary">
            <a href="<?= base_url('official/create') ?>" class="btn btn-primary">+ Official</a>
        </h6>
        <?php if (session('success')): ?>
                <div class="alert alert-success"><?= session('success') ?></div>
            <?php endif ?>
            
            <?php if (session('error')): ?>
                <div class="alert alert-danger"><?= session('error') ?></div>
            <?php endif ?>

        <!-- DataTales Example -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Data Official</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>Profile</th>
                                <th>Nama Lengkap</th>
                                <th>No HP</th>
                                <th>Alamat</th>
                                <th>Agama</th>
                                <th>Jenis Kelamin</th>
                                <th>Kontingen</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($official)): ?>
                                <?php $no = 1; foreach ($official as $row): ?>
                                    <tr>
                                        <td><?= $no++ ?></td>
                                        <td>
                                            <?php if ($row['profile']): ?>
                                                <img src="<?= base_url("uploads/file-profile/" . $row['profile']) ?>" alt="Profile Picture" style="width: 50px; height: 50px; object-fit: cover; border-radius: 50%;">
                                            <?php else: ?>
                                                Tidak Ada
                                            <?php endif; ?>
                                        </td>
                                        <td><?= $row['name'] ?></td>
                                        <td><?= $row['no_hp'] ?></td>
                                        <td><?= $row['alamat'] ?></td>
                                        <td><?= $row['agama'] ?></td>
                                        <td><?= $row['jenis_kelamin'] == 'M' ? 'Laki-laki' : 'Perempuan' ?></td>
                                        <td><?= $row['kontingen_name'] ?></td>
                                        <td>
                                            <a href="<?= base_url('official/edit/' . $row['id']) ?>" class="btn btn-sm btn-warning"><i class="fa fa-edit"></i></a>
                                            <form action="<?= base_url('official/delete/' . $row['id']) ?>" method="POST" style="display:inline;">
                                                <?= csrf_field() ?>
                                                <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Apakah Anda yakin ingin menghapus data ini?')"><i class="fa fa-trash"></i></button>
                                            </form>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="9" class="text-center">Tidak ada data official.</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <!-- /.container-fluid -->
</div>
<!-- End of Main Content -->

<?= $this->endSection(); ?>