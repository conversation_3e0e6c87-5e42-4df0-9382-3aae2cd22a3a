<?php
namespace App\Models;

use CodeIgniter\Model;

class Paket extends Model
{
    protected $table      = 'paket';
    protected $primaryKey = 'id';

    protected $returnType = 'array';

    protected $allowedFields = [
        'name',
        'images',
        'description',
    ];

    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    
    protected $skipValidation = false;
}
