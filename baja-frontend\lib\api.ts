import axios from 'axios';
import Cookies from 'js-cookie';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api/v1';

// Create axios instance
const api = axios.create({
  baseURL: API_URL,
  timeout: 30000, // Increase timeout
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true, // Include cookies for session
});

// Request interceptor - no need to add auth token since we're using httpOnly cookies
api.interceptors.request.use(
  (config) => {
    // httpOnly cookies are automatically sent with requests
    // No need to manually add Authorization header
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Only redirect to login if we're on a protected route
      // Don't redirect for public API calls (like fetching packages on homepage)
      if (typeof window !== 'undefined') {
        const currentPath = window.location.pathname;
        const isProtectedRoute = currentPath.startsWith('/dashboard') ||
                                currentPath.startsWith('/profile');

        if (isProtectedRoute) {
          window.location.href = '/auth/login';
        }
      }
    }
    return Promise.reject(error);
  }
);

export { api };
export default api;
