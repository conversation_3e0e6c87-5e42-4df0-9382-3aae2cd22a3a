'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';
import { kontingenService, CreateKontingenData } from '@/services/kontingenService';
import { locationService, Negara, Provinsi, KabupatenKota } from '@/services/locationService';

export default function CreateKontingenPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [loadingLocation, setLoadingLocation] = useState(false);
  
  // Location data
  const [negaraList, setNegaraList] = useState<Negara[]>([]);
  const [provinsiList, setProvinsiList] = useState<Provinsi[]>([]);
  const [kabupatenKotaList, setKabupatenKotaList] = useState<KabupatenKota[]>([]);
  
  // Form data
  const [formData, setFormData] = useState<CreateKontingenData>({
    name: '',
    negara: '',
    provinsi: '',
    kabupaten_kota: ''
  });

  // Selected IDs for cascading dropdowns
  const [selectedNegaraId, setSelectedNegaraId] = useState<number | null>(null);
  const [selectedProvinsiId, setSelectedProvinsiId] = useState<number | null>(null);

  useEffect(() => {
    fetchNegara();
  }, []);

  const fetchNegara = async () => {
    try {
      setLoadingLocation(true);
      const data = await locationService.getAllNegara();
      setNegaraList(data);
    } catch (error: any) {
      toast.error(error.message);
    } finally {
      setLoadingLocation(false);
    }
  };

  const fetchProvinsi = async (negaraId: number) => {
    try {
      setLoadingLocation(true);
      const data = await locationService.getProvinsiByNegara(negaraId);
      setProvinsiList(data);
      setKabupatenKotaList([]); // Reset kabupaten/kota when negara changes
      setSelectedProvinsiId(null);
      setFormData(prev => ({ ...prev, provinsi: '', kabupaten_kota: '' }));
    } catch (error: any) {
      toast.error(error.message);
    } finally {
      setLoadingLocation(false);
    }
  };

  const fetchKabupatenKota = async (provinsiId: number) => {
    try {
      setLoadingLocation(true);
      const data = await locationService.getKabupatenKotaByProvinsi(provinsiId);
      setKabupatenKotaList(data);
      setFormData(prev => ({ ...prev, kabupaten_kota: '' }));
    } catch (error: any) {
      toast.error(error.message);
    } finally {
      setLoadingLocation(false);
    }
  };

  const handleNegaraChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const negaraId = parseInt(e.target.value);
    const selectedNegara = negaraList.find(n => n.id === negaraId);
    
    setSelectedNegaraId(negaraId);
    setFormData(prev => ({ ...prev, negara: selectedNegara?.name || '' }));
    
    if (negaraId) {
      fetchProvinsi(negaraId);
    } else {
      setProvinsiList([]);
      setKabupatenKotaList([]);
      setSelectedProvinsiId(null);
      setFormData(prev => ({ ...prev, provinsi: '', kabupaten_kota: '' }));
    }
  };

  const handleProvinsiChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const provinsiId = parseInt(e.target.value);
    const selectedProvinsi = provinsiList.find(p => p.id === provinsiId);
    
    setSelectedProvinsiId(provinsiId);
    setFormData(prev => ({ ...prev, provinsi: selectedProvinsi?.name || '' }));
    
    if (provinsiId) {
      fetchKabupatenKota(provinsiId);
    } else {
      setKabupatenKotaList([]);
      setFormData(prev => ({ ...prev, kabupaten_kota: '' }));
    }
  };

  const handleKabupatenKotaChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const kabupatenKotaId = parseInt(e.target.value);
    const selectedKabupatenKota = kabupatenKotaList.find(k => k.id === kabupatenKotaId);
    
    setFormData(prev => ({ ...prev, kabupaten_kota: selectedKabupatenKota?.name || '' }));
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.negara || !formData.provinsi || !formData.kabupaten_kota) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      setLoading(true);
      await kontingenService.createKontingen(formData);
      toast.success('Kontingen created successfully!');
      router.push('/dashboard/kontingen');
    } catch (error: any) {
      toast.error(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-black text-white p-6">
      <div className="max-w-2xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-yellow-400 mb-2">Create New Kontingen</h1>
          <p className="text-gray-300">Register your kontingen to participate in events</p>
        </div>

        <div className="bg-gray-900 rounded-lg p-6 border border-yellow-400">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Kontingen Name */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-yellow-400 mb-2">
                Kontingen Name *
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-yellow-400"
                placeholder="Enter kontingen name"
                required
              />
            </div>

            {/* Negara */}
            <div>
              <label htmlFor="negara" className="block text-sm font-medium text-yellow-400 mb-2">
                Country *
              </label>
              <select
                id="negara"
                value={selectedNegaraId || ''}
                onChange={handleNegaraChange}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-yellow-400"
                required
                disabled={loadingLocation}
              >
                <option value="">Select Country</option>
                {negaraList.map((negara) => (
                  <option key={negara.id} value={negara.id}>
                    {negara.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Provinsi */}
            <div>
              <label htmlFor="provinsi" className="block text-sm font-medium text-yellow-400 mb-2">
                Province *
              </label>
              <select
                id="provinsi"
                value={selectedProvinsiId || ''}
                onChange={handleProvinsiChange}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-yellow-400"
                required
                disabled={loadingLocation || !selectedNegaraId}
              >
                <option value="">Select Province</option>
                {provinsiList.map((provinsi) => (
                  <option key={provinsi.id} value={provinsi.id}>
                    {provinsi.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Kabupaten/Kota */}
            <div>
              <label htmlFor="kabupaten_kota" className="block text-sm font-medium text-yellow-400 mb-2">
                City/Regency *
              </label>
              <select
                id="kabupaten_kota"
                value={kabupatenKotaList.find(k => k.name === formData.kabupaten_kota)?.id || ''}
                onChange={handleKabupatenKotaChange}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-yellow-400"
                required
                disabled={loadingLocation || !selectedProvinsiId}
              >
                <option value="">Select City/Regency</option>
                {kabupatenKotaList.map((kabupatenKota) => (
                  <option key={kabupatenKota.id} value={kabupatenKota.id}>
                    {kabupatenKota.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Submit Buttons */}
            <div className="flex gap-4 pt-4">
              <button
                type="button"
                onClick={() => router.back()}
                className="flex-1 px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
                disabled={loading}
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading || loadingLocation}
                className="flex-1 px-4 py-2 bg-yellow-600 text-black font-semibold rounded-md hover:bg-yellow-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Creating...' : 'Create Kontingen'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
