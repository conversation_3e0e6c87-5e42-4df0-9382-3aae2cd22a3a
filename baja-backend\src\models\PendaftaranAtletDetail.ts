import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../config/database';
import PendaftaranAtlet from './PendaftaranAtlet';
import Atlet from './Atlet';

interface PendaftaranAtletDetailInterface {
  id: number;
  id_pendaftaran_atlet: number;
  id_atlet: number;
  created_at: Date;
  updated_at: Date;
}

interface PendaftaranAtletDetailCreationAttributes extends Optional<PendaftaranAtletDetailInterface, 'id' | 'created_at' | 'updated_at'> {}

class PendaftaranAtletDetail extends Model<PendaftaranAtletDetailInterface, PendaftaranAtletDetailCreationAttributes> implements PendaftaranAtletDetailInterface {
  public id!: number;
  public id_pendaftaran_atlet!: number;
  public id_atlet!: number;
  public readonly created_at!: Date;
  public readonly updated_at!: Date;
}

PendaftaranAtletDetail.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    id_pendaftaran_atlet: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: PendaftaranAtlet,
        key: 'id',
      },
    },
    id_atlet: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: Atlet,
        key: 'id',
      },
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    tableName: 'pendaftaran_atlet_detail',
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  }
);

export default PendaftaranAtletDetail;
