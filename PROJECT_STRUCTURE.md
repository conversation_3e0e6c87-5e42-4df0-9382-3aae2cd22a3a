# BAJA Event Organizer - Project Structure

Dokumentasi lengkap struktur project BAJA Event Organizer yang terdiri dari Backend (Express.js) dan Frontend (Next.js).

## Overview

Project ini adalah sistem manajemen event olahraga bela diri yang terdiri dari:
- **Backend**: REST API menggunakan Express.js + TypeScript + MySQL
- **Frontend**: Web application menggunakan Next.js + React + TypeScript + Tailwind CSS

## Project Structure

```
baja_apps/
├── baja-backend/           # Backend API (Express.js)
│   ├── src/
│   │   ├── config/         # Database & app configuration
│   │   ├── controllers/    # Route controllers
│   │   ├── middleware/     # Custom middleware
│   │   ├── models/         # Database models (Sequelize)
│   │   ├── routes/         # API routes
│   │   ├── services/       # Business logic services
│   │   ├── types/          # TypeScript type definitions
│   │   ├── utils/          # Utility functions
│   │   └── server.ts       # Main server file
│   ├── uploads/            # File uploads directory
│   ├── .env.example        # Environment variables template
│   ├── package.json        # Dependencies & scripts
│   ├── tsconfig.json       # TypeScript configuration
│   └── README.md           # Backend documentation
│
├── baja-frontend/          # Frontend App (Next.js)
│   ├── app/                # Next.js App Router
│   │   ├── auth/           # Authentication pages
│   │   ├── dashboard/      # Dashboard pages
│   │   ├── events/         # Event pages
│   │   ├── gallery/        # Gallery page
│   │   ├── packages/       # Packages page
│   │   ├── profile/        # Profile page
│   │   ├── globals.css     # Global styles
│   │   ├── layout.tsx      # Root layout
│   │   └── page.tsx        # Home page
│   ├── components/         # Reusable components
│   │   ├── auth/           # Auth components
│   │   ├── layout/         # Layout components
│   │   └── ui/             # UI components
│   ├── contexts/           # React contexts
│   ├── hooks/              # Custom hooks
│   ├── lib/                # Utility libraries
│   ├── services/           # API services
│   ├── types/              # TypeScript types
│   ├── utils/              # Utility functions
│   ├── .env.local.example  # Environment variables template
│   ├── next.config.js      # Next.js configuration
│   ├── package.json        # Dependencies & scripts
│   ├── tailwind.config.js  # Tailwind CSS configuration
│   ├── tsconfig.json       # TypeScript configuration
│   └── README.md           # Frontend documentation
│
└── PROJECT_STRUCTURE.md   # This file
```

## Backend Structure (baja-backend/)

### Core Files
- `src/server.ts` - Main server entry point
- `src/config/database.ts` - Database connection configuration
- `package.json` - Dependencies and scripts
- `tsconfig.json` - TypeScript configuration
- `.env.example` - Environment variables template

### Source Code (`src/`)

#### Controllers (`src/controllers/`)
- `auth.controller.ts` - Authentication logic (login, register, profile)
- `dashboard.controller.ts` - Dashboard statistics
- `user.controller.ts` - User management (admin only)
- `event.controller.ts` - Event management
- `atlet.controller.ts` - Athlete management
- `kontingen.controller.ts` - Team management
- `official.controller.ts` - Official management
- `paket.controller.ts` - Package management
- `gallery.controller.ts` - Gallery management

#### Models (`src/models/`)
- `User.ts` - User model (admin, admin-event, ketua-kontingen)
- `Event.ts` - Event model
- `Atlet.ts` - Athlete model
- `Kontingen.ts` - Team model
- `Official.ts` - Official model
- `Paket.ts` - Package model
- `Gallery.ts` - Gallery model

#### Routes (`src/routes/`)
- `auth.routes.ts` - Authentication endpoints
- `dashboard.routes.ts` - Dashboard endpoints
- `user.routes.ts` - User management endpoints
- `event.routes.ts` - Event endpoints
- `atlet.routes.ts` - Athlete endpoints
- `kontingen.routes.ts` - Team endpoints
- `official.routes.ts` - Official endpoints
- `paket.routes.ts` - Package endpoints
- `gallery.routes.ts` - Gallery endpoints

#### Middleware (`src/middleware/`)
- `auth.middleware.ts` - Authentication & authorization
- `error.middleware.ts` - Global error handling
- `validation.middleware.ts` - Request validation
- `notFound.middleware.ts` - 404 handler

#### Types (`src/types/`)
- `index.ts` - TypeScript interfaces and types

## Frontend Structure (baja-frontend/)

### Core Files
- `app/layout.tsx` - Root layout with providers
- `app/page.tsx` - Home page
- `next.config.js` - Next.js configuration
- `tailwind.config.js` - Tailwind CSS configuration
- `package.json` - Dependencies and scripts
- `tsconfig.json` - TypeScript configuration

### App Router (`app/`)

#### Public Pages
- `page.tsx` - Landing page
- `auth/login/page.tsx` - Login page
- `auth/register/page.tsx` - Registration page
- `events/page.tsx` - Events listing
- `gallery/page.tsx` - Photo gallery
- `packages/page.tsx` - Subscription packages

#### Protected Pages
- `dashboard/page.tsx` - Main dashboard (role-based)
- `profile/page.tsx` - User profile management

### Components (`components/`)

#### UI Components (`components/ui/`)
- `Button.tsx` - Reusable button component
- `Input.tsx` - Form input component
- `Card.tsx` - Card container component
- `Modal.tsx` - Modal dialog component
- `Table.tsx` - Data table component
- `Badge.tsx` - Status badge component

#### Layout Components (`components/layout/`)
- `Navbar.tsx` - Navigation bar
- `Footer.tsx` - Footer component
- `DashboardLayout.tsx` - Dashboard layout with sidebar

#### Auth Components (`components/auth/`)
- `ProtectedRoute.tsx` - Route protection wrapper

### Contexts (`contexts/`)
- `AuthContext.tsx` - Authentication state management

### Library (`lib/`)
- `api.ts` - Axios instance with interceptors
- `auth.ts` - Authentication service functions
- `utils.ts` - Utility functions

### Types (`types/`)
- `index.ts` - TypeScript interfaces matching backend

## Key Features by Role

### Admin
- **Full System Access**
  - User management (CRUD)
  - Event management (CRUD)
  - Package management (CRUD)
  - Gallery management (CRUD)
  - Global dashboard statistics

### Admin Event
- **Event-Specific Management**
  - Create and manage own events
  - View registered athletes and teams
  - Event-specific dashboard
  - Athlete verification

### Ketua Kontingen (Team Leader)
- **Team Management**
  - Manage team information
  - Register athletes and officials
  - Register team for events
  - Team-specific dashboard

## Database Schema

### Users Table
- Multi-role system (admin, admin-event, ketua-kontingen)
- Profile information and contact details
- Status management (active/inactive)

### Events Table
- Event details (name, description, dates, location)
- Registration fees and payment methods
- Status tracking (draft, published, ongoing, completed)
- File attachments (images, proposals)

### Athletes Table
- Personal information (NIK, name, contact)
- Physical attributes (weight, height, age)
- Verification status
- Team association

### Teams (Kontingen) Table
- Team information and contact details
- Association with team leader (user)

### Officials Table
- Official information and roles
- Team association

### Packages Table
- Subscription packages with features
- Pricing and status

### Gallery Table
- Image gallery with descriptions
- Active/inactive status

## API Architecture

### Authentication
- JWT-based authentication
- Role-based authorization
- Token refresh mechanism
- Secure password hashing

### Data Flow
1. Frontend makes API requests with JWT token
2. Backend validates token and permissions
3. Database operations through Sequelize ORM
4. Structured JSON responses
5. Error handling and validation

### Security Features
- Rate limiting
- CORS protection
- Helmet security headers
- Input validation
- File upload restrictions

## Development Workflow

### Backend Development
1. Start MySQL server
2. Configure `.env` file
3. Run `npm run dev` for development
4. API available at `http://localhost:5000`

### Frontend Development
1. Configure `.env.local` file
2. Run `npm run dev` for development
3. App available at `http://localhost:3000`

### Database Setup
1. Create MySQL database `db_baja`
2. Sequelize will auto-create tables
3. Seed initial data if needed

## Deployment

### Backend Deployment
- Build: `npm run build`
- Start: `npm start`
- Environment: Production `.env` configuration

### Frontend Deployment
- Build: `npm run build`
- Deploy to Vercel/Netlify or custom server
- Environment: Production environment variables

## Technology Stack Summary

### Backend
- **Runtime**: Node.js
- **Framework**: Express.js
- **Language**: TypeScript
- **Database**: MySQL
- **ORM**: Sequelize
- **Authentication**: JWT + Bcrypt
- **Validation**: Joi + Express Validator
- **File Upload**: Multer
- **Security**: Helmet, CORS, Rate Limiting

### Frontend
- **Framework**: Next.js 14 (App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Headless UI + Custom
- **Icons**: Heroicons
- **Forms**: React Hook Form
- **HTTP Client**: Axios
- **State Management**: React Context + Zustand
- **Notifications**: React Hot Toast
- **Animation**: Framer Motion

This structure provides a scalable, maintainable, and feature-rich application for managing martial arts events with proper separation of concerns and role-based access control.
