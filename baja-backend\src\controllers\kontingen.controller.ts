import { Response } from 'express';
import { AuthRequest } from '../middleware/auth.middleware';
import Kontingen from '../models/Kontingen';
import User from '../models/User';
import Atlet from '../models/Atlet';
import Official from '../models/Official';
import Event from '../models/Event';
import PendaftaranEvent from '../models/PendaftaranEvent';
import { Op } from 'sequelize';

export const getAllKontingen = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { page = 1, limit = 10, search = '' } = req.query;
    const offset = (Number(page) - 1) * Number(limit);
    const user = req.user;

    const whereClause: any = {};
    
    // Filter berdasarkan role
    if (user.role === 'ketua-kontingen') {
      whereClause.id_user = user.id;
    } else if (user.role === 'admin-event') {
      // Admin event hanya melihat kontingen yang mendaftar ke event mereka
      const userEvents = await Event.findAll({
        where: { id_user: user.id },
        attributes: ['id']
      });

      if (userEvents.length === 0) {
        res.json({
          success: true,
          message: 'Kontingen retrieved successfully',
          data: {
            kontingen: [],
            pagination: {
              total: 0,
              page: Number(page),
              limit: Number(limit),
              totalPages: 0
            }
          }
        });
        return;
      }

      const eventIds = userEvents.map(event => event.id);

      // Cari kontingen yang mendaftar ke event admin-event ini
      const registrations = await PendaftaranEvent.findAll({
        where: { id_event: { [Op.in]: eventIds } },
        attributes: ['id_kontingen']
      });

      const kontingenIds = [...new Set(registrations.map(reg => reg.id_kontingen))];

      if (kontingenIds.length === 0) {
        res.json({
          success: true,
          message: 'Kontingen retrieved successfully',
          data: {
            kontingen: [],
            pagination: {
              total: 0,
              page: Number(page),
              limit: Number(limit),
              totalPages: 0
            }
          }
        });
        return;
      }

      whereClause.id = { [Op.in]: kontingenIds };
    }

    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { alamat: { [Op.like]: `%${search}%` } }
      ];
    }

    const { count, rows } = await Kontingen.findAndCountAll({
      where: whereClause,
      limit: Number(limit),
      offset,
      order: [['created_at', 'DESC']],
      include: [
        {
          model: User,
          as: 'kontingenUser',
          attributes: ['id', 'name', 'email', 'no_hp']
        }
      ]
    });

    // Include counts for each kontingen
    const kontingenWithCounts = await Promise.all(
      rows.map(async (kontingen) => {
        const atletCount = await Atlet.count({ where: { id_kontingen: kontingen.id } });
        const officialCount = await Official.count({ where: { id_kontingen: kontingen.id } });
        
        return {
          ...kontingen.toJSON(),
          atletCount,
          officialCount
        };
      })
    );

    res.json({
      success: true,
      message: 'Kontingen retrieved successfully',
      data: {
        kontingen: kontingenWithCounts,
        pagination: {
          total: count,
          page: Number(page),
          limit: Number(limit),
          totalPages: Math.ceil(count / Number(limit))
        }
      }
    });
  } catch (error) {
    console.error('Get all kontingen error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const getKontingenById = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const user = req.user;
    
    const whereClause: any = { id };
    
    // Ketua kontingen hanya bisa melihat kontingen mereka sendiri
    if (user.role === 'ketua-kontingen') {
      whereClause.id_user = user.id;
    }

    const kontingen = await Kontingen.findOne({
      where: whereClause,
      include: [
        {
          model: User,
          as: 'kontingenUser',
          attributes: ['id', 'name', 'email', 'no_hp']
        }
      ]
    });

    if (!kontingen) {
      res.status(404).json({
        success: false,
        message: 'Kontingen not found'
      });
      return;
    }

    // Get atlet and official counts
    const atletCount = await Atlet.count({ where: { id_kontingen: kontingen.id } });
    const officialCount = await Official.count({ where: { id_kontingen: kontingen.id } });

    res.json({
      success: true,
      message: 'Kontingen retrieved successfully',
      data: {
        ...kontingen.toJSON(),
        atletCount,
        officialCount
      }
    });
  } catch (error) {
    console.error('Get kontingen by id error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const createKontingen = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const user = req.user;
    const {
      name,
      negara,
      provinsi,
      kabupaten_kota
    } = req.body;

    // Check if user already has a kontingen (for ketua-kontingen role)
    if (user.role === 'ketua-kontingen') {
      const existingKontingen = await Kontingen.findOne({ where: { id_user: user.id } });
      if (existingKontingen) {
        res.status(400).json({
          success: false,
          message: 'You already have a kontingen registered'
        });
        return;
      }
    }

    const kontingen = await Kontingen.create({
      id_user: user.role === 'ketua-kontingen' ? user.id : req.body.id_user,
      name,
      negara,
      provinsi,
      kabupaten_kota
    });

    res.status(201).json({
      success: true,
      message: 'Kontingen created successfully',
      data: kontingen
    });
  } catch (error) {
    console.error('Create kontingen error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const updateKontingen = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const user = req.user;
    
    const whereClause: any = { id };
    
    // Ketua kontingen hanya bisa update kontingen mereka sendiri
    if (user.role === 'ketua-kontingen') {
      whereClause.id_user = user.id;
    }

    const kontingen = await Kontingen.findOne({ where: whereClause });
    if (!kontingen) {
      res.status(404).json({
        success: false,
        message: 'Kontingen not found'
      });
      return;
    }

    await kontingen.update(req.body);

    res.json({
      success: true,
      message: 'Kontingen updated successfully',
      data: kontingen
    });
  } catch (error) {
    console.error('Update kontingen error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const deleteKontingen = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const user = req.user;
    
    const whereClause: any = { id };
    
    // Ketua kontingen hanya bisa delete kontingen mereka sendiri
    if (user.role === 'ketua-kontingen') {
      whereClause.id_user = user.id;
    }

    const kontingen = await Kontingen.findOne({ where: whereClause });
    if (!kontingen) {
      res.status(404).json({
        success: false,
        message: 'Kontingen not found'
      });
      return;
    }

    // Check if kontingen has athletes or officials
    const atletCount = await Atlet.count({ where: { id_kontingen: kontingen.id } });
    const officialCount = await Official.count({ where: { id_kontingen: kontingen.id } });

    if (atletCount > 0 || officialCount > 0) {
      res.status(400).json({
        success: false,
        message: 'Cannot delete kontingen that has athletes or officials. Please remove them first.'
      });
      return;
    }

    await kontingen.destroy();

    res.json({
      success: true,
      message: 'Kontingen deleted successfully'
    });
  } catch (error) {
    console.error('Delete kontingen error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const getMyKontingen = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const user = req.user;

    if (user.role !== 'ketua-kontingen') {
      res.status(403).json({
        success: false,
        message: 'Access denied. Only ketua-kontingen can access this endpoint.'
      });
      return;
    }

    const kontingen = await Kontingen.findOne({
      where: { id_user: user.id },
      include: [
        {
          model: User,
          as: 'kontingenUser',
          attributes: ['id', 'name', 'email', 'no_hp']
        }
      ]
    });

    if (!kontingen) {
      res.status(404).json({
        success: false,
        message: 'Kontingen not found. Please register your kontingen first.'
      });
      return;
    }

    // Get atlet and official counts
    const atletCount = await Atlet.count({ where: { id_kontingen: kontingen.id } });
    const officialCount = await Official.count({ where: { id_kontingen: kontingen.id } });

    res.json({
      success: true,
      message: 'Kontingen retrieved successfully',
      data: {
        ...kontingen.toJSON(),
        atletCount,
        officialCount
      }
    });
  } catch (error) {
    console.error('Get my kontingen error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};
