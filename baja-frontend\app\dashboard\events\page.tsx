'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card, CardContent } from '@/components/ui/Card';
import Spinner from '@/components/ui/Spinner';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import CardSkeleton from '@/components/ui/CardSkeleton';
import EventModal from '@/components/modals/EventModal';
import EventDetailModal from '@/components/modals/EventDetailModal';
import DeleteConfirmModal from '@/components/modals/DeleteConfirmModal';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  CalendarDaysIcon,
  MapPinIcon,
  CurrencyDollarIcon,
} from '@heroicons/react/24/outline';
import { adminService } from '@/lib/admin';
import { AdminEvent, AdminEventsResponse } from '@/types';
import { uploadService } from '@/lib/upload.service';
import { api } from '@/lib/api';
import toast from 'react-hot-toast';

const EventsPage = () => {
  const { user } = useAuth();
  const [events, setEvents] = useState<AdminEvent[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedStatus, setSelectedStatus] = useState('');

  // Modal states
  const [showEventModal, setShowEventModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<AdminEvent | null>(null);
  const [modalMode, setModalMode] = useState<'create' | 'edit'>('create');
  const [deleteLoading, setDeleteLoading] = useState(false);

  const fetchEvents = async () => {
    try {
      setLoading(true);
      const response = await adminService.getEvents({
        page: currentPage,
        limit: 10,
        search: searchTerm,
        status: selectedStatus
      });
      setEvents(response.events);
      setTotalPages(response.pagination.totalPages);
    } catch (error: any) {
      toast.error(error.message || 'Failed to fetch events');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchEvents();
  }, [currentPage, searchTerm, selectedStatus]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchEvents();
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-500/20 text-green-400 border border-green-500/30';
      case 'inactive':
        return 'bg-red-500/20 text-red-400 border border-red-500/30';
      case 'completed':
        return 'bg-gold-500/20 text-gold-400 border border-gold-500/30';
      default:
        return 'bg-gray-500/20 text-gray-400 border border-gray-500/30';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // CRUD Handlers
  const handleCreateEvent = () => {
    setSelectedEvent(null);
    setModalMode('create');
    setShowEventModal(true);
  };

  const handleEditEvent = (event: AdminEvent) => {
    setSelectedEvent(event);
    setModalMode('edit');
    setShowEventModal(true);
  };

  const handleViewEvent = (event: AdminEvent) => {
    setSelectedEvent(event);
    setShowDetailModal(true);
  };

  const handleDeleteEvent = (event: AdminEvent) => {
    setSelectedEvent(event);
    setShowDeleteModal(true);
  };

  const handleSaveEvent = async (eventData: Partial<AdminEvent>, imageFile?: File, proposalFile?: File, pemenangFile?: File): Promise<void> => {
    let savedEvent;
    if (modalMode === 'create') {
      savedEvent = await adminService.createEvent(eventData);
    } else if (selectedEvent) {
      savedEvent = await adminService.updateEvent(selectedEvent.id, eventData);
    }

    // Upload files if provided and event was saved
    if (savedEvent?.id) {
      try {
        // Upload image
        if (imageFile) {
          await uploadService.uploadEventImage(savedEvent.id.toString(), imageFile);
        }

        // Upload proposal
        if (proposalFile) {
          await uploadService.uploadEventProposal(savedEvent.id.toString(), proposalFile);
        }

        // Upload pemenang document
        if (pemenangFile) {
          await uploadService.uploadEventPemenang(savedEvent.id.toString(), pemenangFile);
        }
      } catch (uploadError: any) {
        toast.error('Event saved but file upload failed: ' + uploadError.message);
      }
    }

    fetchEvents(); // Refresh the list
  };

  const handleConfirmDelete = async () => {
    if (!selectedEvent) return;

    setDeleteLoading(true);
    try {
      await adminService.deleteEvent(selectedEvent.id);
      toast.success('Event deleted successfully!');
      setShowDeleteModal(false);
      setSelectedEvent(null);
      fetchEvents(); // Refresh the list
    } catch (error: any) {
      toast.error(error.message || 'Failed to delete event');
    } finally {
      setDeleteLoading(false);
    }
  };

  if (user?.role !== 'admin' && user?.role !== 'admin-event') {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold text-white">Access Denied</h1>
          <p className="text-gray-300 mt-2">You don't have permission to access this page.</p>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-white">
              Event <span className="text-gold-400">Management</span>
            </h1>
            <p className="text-gray-300 mt-2">
              {user?.role === 'admin'
                ? 'Manage all events in the system'
                : 'Manage your organized events'
              }
            </p>
          </div>
          {user?.role === 'admin' && (
            <Button
              className="flex items-center space-x-2 bg-gold-500 hover:bg-gold-600 text-black font-semibold px-6 py-3 rounded-lg transition-all duration-200 shadow-lg hover:shadow-gold-500/25"
              onClick={handleCreateEvent}
            >
              <PlusIcon className="h-5 w-5" />
              <span>Add New Event</span>
            </Button>
          )}
        </div>

        {/* Filters */}
        <Card className="bg-gray-900/50 border-gold-500/20 backdrop-blur-sm">
          <CardContent className="p-6">
            <div className="flex items-center mb-4">
              <MagnifyingGlassIcon className="h-5 w-5 text-gold-400 mr-2" />
              <h2 className="text-lg font-semibold text-white">Search & Filter Events</h2>
            </div>
            <form onSubmit={handleSearch} className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <Input
                  type="text"
                  placeholder="Cari events berdasarkan nama, deskripsi, atau lokasi..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full bg-gray-800/50 border-gold-500/30 text-white placeholder-gray-400 focus:border-gold-500 focus:ring-gold-500/20"
                />
              </div>
              <div className="w-full md:w-48">
                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="w-full px-4 py-2 border border-gold-500/30 bg-gray-800/50 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-gold-500/50 focus:border-gold-500 transition-all duration-200"
                >
                  <option value="">All Status</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                  <option value="completed">Completed</option>
                </select>
              </div>
              <Button
                type="submit"
                className="flex items-center space-x-2 bg-gold-500 hover:bg-gold-600 text-black font-medium px-6 py-2 transition-all duration-200"
              >
                <MagnifyingGlassIcon className="h-4 w-4" />
                <span>Search</span>
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Events Grid */}
        {loading ? (
          <CardSkeleton
            count={6}
            gridCols="grid-cols-1 lg:grid-cols-2 xl:grid-cols-3"
          />
        ) : events.length === 0 ? (
          <Card className="bg-gray-900/30 border-gold-500/20">
            <CardContent className="text-center py-16">
              <div className="bg-gray-800/50 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6">
                <CalendarDaysIcon className="h-12 w-12 text-gold-400" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">No Events Found</h3>
              <p className="text-gray-400 mb-6 max-w-md mx-auto">
                {searchTerm || selectedStatus
                  ? 'No events match your search criteria. Try adjusting your filters or search terms.'
                  : user?.role === 'admin'
                    ? 'No events have been created yet. Start by adding your first event.'
                    : 'You haven\'t organized any events yet. Contact an administrator to create events.'
                }
              </p>
              {user?.role === 'admin' && !searchTerm && !selectedStatus && (
                <Button
                  onClick={handleCreateEvent}
                  className="bg-gold-500 hover:bg-gold-600 text-black font-semibold px-6 py-3 rounded-lg transition-all duration-200"
                >
                  <PlusIcon className="h-5 w-5 mr-2" />
                  Create Your First Event
                </Button>
              )}
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {events.map((event) => (
              <Card key={event.id} className="bg-gray-900/50 border-gold-500/20 hover:border-gold-500/40 hover:shadow-xl hover:shadow-gold-500/10 transition-all duration-300 group backdrop-blur-sm">
                <CardContent className="p-0">
                  {/* Event Image */}
                  <div className="h-64 bg-gradient-to-br from-gray-800 to-gray-900 relative overflow-hidden aspect-[3/4]">
                    {event.event_image ? (
                      <img
                        src={uploadService.getOptimizedUrl(event.event_image)}
                        alt={event.name}
                        className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = '/placeholder-event.jpg';
                        }}
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-800 to-gray-900">
                        <CalendarDaysIcon className="h-16 w-16 text-gold-400/50" />
                      </div>
                    )}
                    {/* Status Badge Overlay */}
                    <div className="absolute top-3 right-3">
                      <span className={`inline-flex px-3 py-1 text-xs font-semibold rounded-full backdrop-blur-sm ${getStatusBadgeColor(event.status)}`}>
                        {event.status.charAt(0).toUpperCase() + event.status.slice(1)}
                      </span>
                    </div>
                  </div>

                  <div className="p-6">
                    <div className="mb-4">
                      <h3 className="text-xl font-bold text-white mb-2 line-clamp-1 group-hover:text-gold-400 transition-colors duration-200">
                        {event.name}
                      </h3>
                      <p className="text-gray-300 text-sm leading-relaxed line-clamp-2">
                        {event.description || 'No description available'}
                      </p>
                    </div>

                    <div className="space-y-3 mb-6">
                      <div className="flex items-center text-sm text-gray-300 bg-gray-800/30 rounded-lg p-3">
                        <CalendarDaysIcon className="h-5 w-5 mr-3 text-gold-400 flex-shrink-0" />
                        <div>
                          <div className="font-medium text-white">Event Period</div>
                          <div className="text-xs">{formatDate(event.start_date)} - {formatDate(event.end_date)}</div>
                        </div>
                      </div>
                      <div className="flex items-center text-sm text-gray-300 bg-gray-800/30 rounded-lg p-3">
                        <MapPinIcon className="h-5 w-5 mr-3 text-gold-400 flex-shrink-0" />
                        <div>
                          <div className="font-medium text-white">Location</div>
                          <div className="text-xs line-clamp-1">{event.lokasi}</div>
                        </div>
                      </div>
                      <div className="flex items-center text-sm text-gray-300 bg-gray-800/30 rounded-lg p-3">
                        <CurrencyDollarIcon className="h-5 w-5 mr-3 text-gold-400 flex-shrink-0" />
                        <div>
                          <div className="font-medium text-white">Registration Fee</div>
                          <div className="text-xs font-semibold text-gold-400">{formatCurrency(event.biaya_registrasi)}</div>
                        </div>
                      </div>
                    </div>

                    <div className="border-t border-gold-500/20 pt-4">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center space-x-2">
                          <div className="w-8 h-8 bg-gold-500 rounded-full flex items-center justify-center">
                            <span className="text-black font-semibold text-sm">
                              {event.eventUser.name.charAt(0).toUpperCase()}
                            </span>
                          </div>
                          <div>
                            <div className="text-xs text-gray-400">Organized by</div>
                            <div className="text-sm font-medium text-white">{event.eventUser.name}</div>
                          </div>
                        </div>
                      </div>

                      <div className="flex justify-between space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewEvent(event)}
                          className="flex-1 border-gold-500/30 text-gold-400 hover:bg-gold-500/10 hover:border-gold-500/50"
                          title="View Details"
                        >
                          <EyeIcon className="h-4 w-4 mr-1" />
                          <span className="hidden sm:inline">View</span>
                        </Button>
                        {(user?.role === 'admin' || (user?.role === 'admin-event' && event.eventUser.id === user.id)) && (
                          <>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEditEvent(event)}
                              className="flex-1 border-blue-500/30 text-blue-400 hover:bg-blue-500/10 hover:border-blue-500/50"
                              title="Edit Event"
                            >
                              <PencilIcon className="h-4 w-4 mr-1" />
                              <span className="hidden sm:inline">Edit</span>
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className="flex-1 border-red-500/30 text-red-400 hover:bg-red-500/10 hover:border-red-500/50"
                              onClick={() => handleDeleteEvent(event)}
                              title="Delete Event"
                            >
                              <TrashIcon className="h-4 w-4 mr-1" />
                              <span className="hidden sm:inline">Delete</span>
                            </Button>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <Card className="bg-gray-900/30 border-gold-500/20">
            <CardContent className="p-4">
              <div className="flex justify-between items-center">
                <div className="text-sm text-gray-300">
                  Showing page <span className="font-semibold text-gold-400">{currentPage}</span> of{' '}
                  <span className="font-semibold text-gold-400">{totalPages}</span>
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                    className="border-gold-500/30 text-gold-400 hover:bg-gold-500/10 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Previous
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages}
                    className="border-gold-500/30 text-gold-400 hover:bg-gold-500/10 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Next
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Modals */}
        <EventModal
          isOpen={showEventModal}
          onClose={() => setShowEventModal(false)}
          onSave={handleSaveEvent}
          event={selectedEvent}
          mode={modalMode}
        />

        <EventDetailModal
          isOpen={showDetailModal}
          onClose={() => setShowDetailModal(false)}
          event={selectedEvent}
        />

        <DeleteConfirmModal
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          onConfirm={handleConfirmDelete}
          title="Delete Event"
          message="Are you sure you want to delete this event? This will also remove all associated registrations and data."
          itemName={selectedEvent?.name}
          loading={deleteLoading}
        />
      </div>
    </DashboardLayout>
  );
};

export default EventsPage;
