const http = require('http');

// Test if backend is running
const options = {
  hostname: 'localhost',
  port: 5000,
  path: '/api/v1/health',
  method: 'GET'
};

const req = http.request(options, (res) => {
  console.log(`Backend status: ${res.statusCode}`);
  console.log(`Headers: ${JSON.stringify(res.headers)}`);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    console.log('Response:', data);
  });
});

req.on('error', (e) => {
  console.error(`Backend connection error: ${e.message}`);
});

req.end();

// Test frontend
const frontendOptions = {
  hostname: 'localhost',
  port: 3000,
  path: '/',
  method: 'GET'
};

const frontendReq = http.request(frontendOptions, (res) => {
  console.log(`Frontend status: ${res.statusCode}`);
});

frontendReq.on('error', (e) => {
  console.error(`Frontend connection error: ${e.message}`);
});

frontendReq.end();
