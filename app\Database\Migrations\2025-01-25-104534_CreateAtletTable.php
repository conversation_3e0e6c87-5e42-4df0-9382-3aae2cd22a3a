<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateAtletTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 5,
                'unsigned' => true,
                'auto_increment' => true
            ],
            'nik' => [
                'type' => 'VARCHAR',
                'constraint' => '255'
            ], 
            'name' => [
                'type' => 'VARCHAR',
                'constraint' => '255'
            ],
            'no_hp' => [
                'type' => 'VARCHAR',
                'constraint' => '255'
            ],
            'tanggal_lahir' => [
                'type' => 'DATE',
            ],
            'jenis_kelamin' => [
                'type' => 'ENUM',
                'constraint' => ['M', 'F'],
            ],
            'agama' => [
                'type' => 'VARCHAR',
                'constraint' => '255'
            ],
            'alamat' => [
                'type' => 'VARCHAR',
                'constraint' => '255'
            ],
            'umur' => [
                'type' => 'VARCHAR',
                'constraint' => '255'
            ],
            'berat_badan' => [
                'type' => 'VARCHAR',
                'constraint' => '255'
            ],
            'tinggi_badan' => [
                'type' => 'VARCHAR',
                'constraint' => '255'
            ],
            'status_verifikasi' => [
                'type' => 'ENUM',
                'constraint' => ['pending', 'verified'],
                'default' => 'pending'
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'id_user' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
            ],
            'id_kontingen' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true, 
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->addForeignKey('id_user', 'users', 'id', 'CASCADE', 'CASCADE');
        $this->forge->addForeignKey('id_kontingen', 'kontingen', 'id', 'CASCADE', 'CASCADE');
        $this->forge->createTable('atlet');
    }

    public function down()
    {
        $this->forge->dropTable('atlet');
    }
}
