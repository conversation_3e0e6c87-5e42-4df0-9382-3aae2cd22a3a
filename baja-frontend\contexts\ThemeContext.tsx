'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';

type Theme = 'dark' | 'light';

interface ThemeContextType {
  theme: Theme;
  toggleTheme: () => void;
  setTheme: (theme: Theme) => void;
}

export const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [theme, setThemeState] = useState<Theme>('dark');
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    // Get theme from localStorage or default to dark
    const savedTheme = localStorage.getItem('theme') as Theme;
    const initialTheme = (savedTheme && (savedTheme === 'dark' || savedTheme === 'light')) ? savedTheme : 'dark';

    setThemeState(initialTheme);
    localStorage.setItem('theme', initialTheme);

    // Apply initial theme immediately
    const root = document.documentElement;
    const body = document.body;

    root.classList.remove('light', 'dark');
    body.classList.remove('light', 'dark');
    root.classList.add(initialTheme);
    body.classList.add(initialTheme);

    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted) {
      // Apply theme to document
      const root = document.documentElement;
      const body = document.body;

      // Remove existing theme classes
      root.classList.remove('light', 'dark');
      body.classList.remove('light', 'dark');

      // Add new theme class
      root.classList.add(theme);
      body.classList.add(theme);

      // Save to localStorage
      localStorage.setItem('theme', theme);

      console.log('Theme applied:', theme, 'to', root.className); // Debug log
    }
  }, [theme, mounted]);

  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme);
  };

  const toggleTheme = () => {
    setThemeState(prev => prev === 'dark' ? 'light' : 'dark');
  };

  const value: ThemeContextType = {
    theme,
    toggleTheme,
    setTheme,
  };

  // Prevent hydration mismatch
  if (!mounted) {
    return <>{children}</>;
  }

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}
