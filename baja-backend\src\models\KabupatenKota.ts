import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../config/database';
import <PERSON><PERSON><PERSON> from './Provinsi';

interface KabupatenKotaInterface {
  id: number;
  name: string;
  id_provinsi: number;
}

interface KabupatenKotaCreationAttributes extends Optional<KabupatenKotaInterface, 'id'> {}

class KabupatenKota extends Model<KabupatenKotaInterface, KabupatenKotaCreationAttributes> implements KabupatenKotaInterface {
  public id!: number;
  public name!: string;
  public id_provinsi!: number;
}

KabupatenKota.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    id_provinsi: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: Provinsi,
        key: 'id',
      },
    },
  },
  {
    sequelize,
    tableName: 'kabupaten_kota',
    timestamps: false,
  }
);

export default KabupatenKota;
