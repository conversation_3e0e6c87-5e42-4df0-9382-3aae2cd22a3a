<?= $this->extend('layouts/backend/template') ?>
<?= $this->section('content') ?>

<div id="content">
<div class="container-fluid">

                    <!-- Page Heading -->
                    <h1 class="h3 mb-2 text-gray-800">Users</h1>
                    <h6 class="m-0 mb-3 font-weight-bold text-primary"><a href="<?= base_url('users/create') ?>" class="btn btn-primary">+ Users</a></h6>

                    <?php if (session('success')): ?>
                        <div class="alert alert-success"><?= session('success') ?></div>
                    <?php endif ?>
                    
                    <?php if (session('error')): ?>
                        <div class="alert alert-danger"><?= session('error') ?></div>
                    <?php endif ?>
                    <!-- DataTales Example -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Data Users</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>No</th>
                                            <th>Profile</th>
                                            <th>Nama Lengkap</th>
                                            <th>Email</th>
                                            <th>No HP</th>
                                            <th>Agama</th>
                                            <th>Alamat</th>
                                            <th>Role</th>
                                            <th>Status</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $no = 1; foreach ($users as $row) : ?>
                                            <tr>
                                                <td><?= $no++ ?></td>
                                                <td><img src="<?= base_url('profile/'.$row['profile']) ?>" width="100px"></td>
                                                <td><?= $row['name'] ?></td>
                                                <td><?= $row['email'] ?></td>
                                                <td><?= $row['no_hp'] ?></td>
                                                <td><?= $row['agama'] ?></td>
                                                <td><?= $row['alamat'] ?></td>
                                                <td><?= $row['role'] ?></td>
                                                <td><?= $row['status'] ?></td>
                                                <td>
                                                    <a href="<?= base_url('users/edit/'.$row['id']) ?>" class="btn btn-primary btn-sm"><i class="fa fa-edit"></i></a>
                                                    <a href="<?= base_url('users/edit-password/'.$row['id']) ?>" class="btn btn-warning btn-sm"><i class="fa fa-key"></i></a>
                                                    <a href="<?= base_url('users/delete/'.$row['id']) ?>" class="btn btn-danger btn-sm"><i class="fa fa-trash"></i></a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

<?= $this->endSection(); ?>