import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../config/database';
import { Kontingen as KontingenInterface } from '../types';
import User from './User';

interface KontingenCreationAttributes extends Optional<KontingenInterface, 'id' | 'created_at' | 'updated_at'> {}

class Kontingen extends Model<KontingenInterface, KontingenCreationAttributes> implements KontingenInterface {
  public id!: number;
  public name!: string;
  public negara!: string;
  public provinsi!: string;
  public kabupaten_kota!: string;
  public id_user!: number;
  public readonly created_at!: Date;
  public readonly updated_at!: Date;
}

Kontingen.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    negara: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    provinsi: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    kabupaten_kota: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    id_user: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: User,
        key: 'id',
      },
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    tableName: 'kontingen',
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  }
);

// Associations will be set up in models/index.ts

export default Kontingen;
