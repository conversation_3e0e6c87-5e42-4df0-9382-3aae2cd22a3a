import { Router } from 'express';
import { authenticate, authorize } from '../middleware/auth.middleware';
import {
  getAllUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser,
  updateUserStatus
} from '../controllers/admin/user.controller';
import { getAllEventsAdmin, getDashboardEvents } from '../controllers/event.controller';
import { getAllPaketsAdmin } from '../controllers/paket.controller';
import { getAllGalleryAdmin } from '../controllers/gallery.controller';

const router = Router();

// Apply auth middleware to all admin routes
router.use(authenticate);

// User management routes (admin only)
router.get('/users', authorize('admin'), getAllUsers);
router.get('/users/:id', authorize('admin'), getUserById);
router.post('/users', authorize('admin'), createUser);
router.put('/users/:id', authorize('admin'), updateUser);
router.delete('/users/:id', authorize('admin'), deleteUser);
router.patch('/users/:id/status', authorize('admin'), updateUserStatus);

// Event management routes
router.get('/events', authorize('admin'), getAllEventsAdmin); // Admin only - all events
router.get('/dashboard-events', authorize('admin', 'admin-event'), getDashboardEvents); // Dashboard events with filtering

// Package management routes
router.get('/packages', getAllPaketsAdmin);

// Gallery management routes
router.get('/gallery', getAllGalleryAdmin);

export default router;
