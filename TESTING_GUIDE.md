# BAJA Event Organizer - Testing Guide

## 🚀 Quick Start

### Prerequisites
- Backend running on `http://localhost:5000`
- Frontend running on `http://localhost:3000`
- MySQL database with test data

### Test Accounts
```
Admin Account:
- Email: <EMAIL>
- Password: password123
- Role: admin

Admin Event Account:
- Email: <EMAIL>
- Password: password123
- Role: admin-event
```

## 🧪 Backend API Testing

### Automated Tests
Run the automated test scripts:

```bash
# Test basic endpoints
node test-simple.js

# Test CRUD operations
node test-crud-events.js

# Test frontend API calls
node test-frontend-crud.js
```

### Manual API Testing
Use tools like Postman or curl to test:

1. **Authentication**
   ```bash
   POST /api/v1/auth/login
   {
     "email": "<EMAIL>",
     "password": "password123"
   }
   ```

2. **Events Endpoints**
   ```bash
   GET /api/v1/events                    # Public - all events
   GET /api/v1/admin/dashboard-events    # Dashboard - role-based filtering
   GET /api/v1/admin/events             # Admin only - all events
   ```

## 🎨 Frontend Testing

### 1. Main Events Page Testing
**URL:** `http://localhost:3000/events`

**Expected Behavior:**
- ✅ Shows ALL events regardless of user role
- ✅ No authentication required
- ✅ Beautiful black & gold styling
- ✅ Event cards with images, details, and actions
- ✅ Filter and search functionality

**Test Steps:**
1. Visit `/events` without login → Should show all events
2. Login as admin → Should still show all events
3. Login as admin-event → Should still show all events

### 2. Dashboard Events Page Testing
**URL:** `http://localhost:3000/dashboard/events`

**Expected Behavior:**
- ✅ Requires authentication
- ✅ Role-based event filtering:
  - **Admin**: Sees ALL events in system
  - **Admin-Event**: Sees only THEIR events
- ✅ Enhanced UI with improved styling
- ✅ CRUD operations based on permissions

**Test Steps:**
1. Visit without login → Should redirect to login
2. Login as admin:
   - Should see all events
   - Should have "Add New Event" button
   - Should be able to edit/delete any event
3. Login as admin-event:
   - Should see only their own events
   - Should NOT have "Add New Event" button
   - Should only edit/delete their own events

### 3. CRUD Operations Testing

#### Create Event
**Who can test:** Admin only
1. Login as admin
2. Go to `/dashboard/events`
3. Click "Add New Event" button
4. Fill form and submit
5. Verify event appears in list

#### Read Events
**Who can test:** Everyone
1. Visit `/events` → Should show all events
2. Visit `/dashboard/events` as admin → Should show all events
3. Visit `/dashboard/events` as admin-event → Should show filtered events

#### Update Event
**Who can test:** Admin (any event), Admin-Event (own events only)
1. Go to event card
2. Click "Edit" button
3. Modify details and save
4. Verify changes are reflected

#### Delete Event
**Who can test:** Admin (any event), Admin-Event (own events only)
1. Go to event card
2. Click "Delete" button
3. Confirm deletion
4. Verify event is removed

### 4. UI/UX Testing

#### Styling Verification
- ✅ Black background with gold accents
- ✅ Gradient buttons and hover effects
- ✅ Card shadows and animations
- ✅ Responsive design
- ✅ Loading states and skeletons
- ✅ Toast notifications

#### Interactive Elements
- ✅ Hover effects on cards and buttons
- ✅ Smooth transitions and animations
- ✅ Modal dialogs for create/edit/delete
- ✅ Form validation and error handling
- ✅ Search and filter functionality
- ✅ Pagination controls

### 5. Role-Based Access Testing

#### Admin Role
- ✅ Can access `/dashboard/events`
- ✅ Sees all events in dashboard
- ✅ Can create new events
- ✅ Can edit any event
- ✅ Can delete any event
- ✅ Has "Add New Event" button

#### Admin-Event Role
- ✅ Can access `/dashboard/events`
- ✅ Sees only their own events in dashboard
- ✅ Cannot create new events (no button)
- ✅ Can edit only their own events
- ✅ Can delete only their own events
- ✅ Edit/Delete buttons only appear for owned events

#### Public/Unauthenticated
- ✅ Can access `/events`
- ✅ Sees all events
- ✅ Cannot access `/dashboard/events`
- ✅ Redirected to login when accessing protected routes

## 🐛 Troubleshooting

### Common Issues

1. **Frontend shows HTML without styling**
   - Check if Tailwind CSS is loading
   - Verify `npm run dev` is running properly
   - Check browser console for errors

2. **API calls failing**
   - Verify backend is running on port 5000
   - Check CORS settings
   - Verify authentication tokens

3. **Role-based filtering not working**
   - Check user role in JWT token
   - Verify backend endpoint logic
   - Check frontend API calls

### Debug Commands
```bash
# Check if servers are running
netstat -ano | findstr :3000
netstat -ano | findstr :5000

# Test API endpoints
node test-simple.js
node test-frontend-crud.js

# Check frontend build
cd baja-frontend && npm run build
```

## ✅ Test Checklist

### Backend Tests
- [ ] Authentication endpoints working
- [ ] Main events endpoint shows all events
- [ ] Dashboard events endpoint filters by role
- [ ] CRUD operations work correctly
- [ ] Role-based access control enforced

### Frontend Tests
- [ ] Main events page shows all events
- [ ] Dashboard events page filters by role
- [ ] Styling and animations working
- [ ] CRUD operations functional
- [ ] Role-based UI elements correct
- [ ] Responsive design working
- [ ] Error handling and loading states

### Integration Tests
- [ ] Login flow works end-to-end
- [ ] Event creation works from frontend
- [ ] Event editing works from frontend
- [ ] Event deletion works from frontend
- [ ] Role switching shows different data
- [ ] Unauthorized access properly blocked

## 📊 Expected Test Results

### API Test Results
```
✅ Main Events Endpoint: 200 OK, shows 10+ events
✅ Dashboard Events (Admin): 200 OK, shows all events
✅ Dashboard Events (Admin-Event): 200 OK, shows filtered events
✅ CRUD Operations: All working correctly
✅ Role-Based Access: Properly enforced
```

### Frontend Test Results
```
✅ Styling: Black & gold theme applied
✅ Responsiveness: Works on all screen sizes
✅ Animations: Smooth transitions and hover effects
✅ CRUD: All operations working through UI
✅ Role-Based UI: Correct buttons and access levels
✅ Error Handling: Proper feedback and validation
```

## 🎯 My Events Page Testing (Admin-Event Role)

### URL: `http://localhost:3000/dashboard/my-events`

**Expected Behavior:**
- ✅ Only accessible by admin-event role
- ✅ Shows only events organized by the logged-in admin-event
- ✅ Enhanced UI with improved styling
- ✅ PDF proposal preview and download functionality

**Test Steps:**
1. Login as admin-event (`<EMAIL>` / `password123`)
2. Navigate to `/dashboard/my-events`
3. Should see only events organized by this user (6 events in test data)
4. Click on event card to view details
5. Test PDF proposal functionality if available

### Event Detail Page Testing

**URL:** `http://localhost:3000/dashboard/my-events/[id]`

**Expected Behavior:**
- ✅ Only shows events owned by the admin-event
- ✅ Access denied for other users' events
- ✅ PDF proposal preview in iframe
- ✅ Direct PDF download functionality
- ✅ Enhanced UI with better styling

**Test Steps:**
1. From my-events page, click "View Details" on any event
2. Should see complete event information
3. If event has proposal, should see PDF section with:
   - Preview button (opens PDF in iframe)
   - Download button (downloads PDF file)
4. Try accessing another user's event ID - should be denied

### PDF Proposal Functionality

**Features:**
- ✅ PDF preview in embedded iframe
- ✅ Direct download with proper filename
- ✅ Cloudinary URL handling
- ✅ Error handling for missing proposals

**Test Steps:**
1. Find event with proposal document
2. Click "Preview" button - should open PDF in iframe
3. Click "Download" button - should download PDF file
4. Verify filename is meaningful (e.g., "EventName_proposal.pdf")

## 🔄 Comparison: Events vs My Events

### Main Events Page (`/events`)
- **Who can access:** Everyone (public)
- **What it shows:** ALL events (10 events)
- **Filtering:** None - shows all events regardless of user role
- **Purpose:** Public event listing

### Dashboard Events (`/dashboard/events`)
- **Who can access:** Admin and Admin-Event
- **What it shows:**
  - Admin: ALL events (10 events)
  - Admin-Event: Only their events (6 events)
- **Filtering:** Role-based filtering
- **Purpose:** Event management dashboard

### My Events (`/dashboard/my-events`)
- **Who can access:** Admin-Event only
- **What it shows:** Only events organized by the admin-event (6 events)
- **Filtering:** Strict user-based filtering
- **Purpose:** Personal event management for admin-event

## 🧪 Automated Testing

### Run My Events Tests
```bash
# Test my events filtering and PDF functionality
node test-my-events-functionality.js
```

**Expected Results:**
```
✅ Admin-Event Login: Working
✅ Dashboard Events Filtering: Working (only own events)
✅ Main Events Page: Working (shows all events)
✅ Event Detail Access Control: Working
✅ PDF Proposal Functionality: Available
```
