/**
 * PWA Background Sync Service
 * Handles syncing data when the app comes back online
 */

import { enhancedApi } from './api.enhanced';
import { offlineService } from './offline.service';

class PWASyncService {
  private syncInProgress = false;
  private syncListeners: (() => void)[] = [];

  constructor() {
    // Listen for online events
    if (typeof window !== 'undefined') {
      window.addEventListener('online', this.handleOnline.bind(this));
      
      // Also sync when the app becomes visible again
      document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
    }
  }

  /**
   * Handle when app comes back online
   */
  private async handleOnline(): Promise<void> {
    console.log('App is back online, starting sync...');
    await this.syncAll();
  }

  /**
   * Handle when app becomes visible
   */
  private async handleVisibilityChange(): Promise<void> {
    if (!document.hidden && navigator.onLine && !this.syncInProgress) {
      console.log('App became visible and online, checking for sync...');
      await this.syncAll();
    }
  }

  /**
   * Sync all queued data
   */
  async syncAll(): Promise<void> {
    if (this.syncInProgress || !navigator.onLine) {
      return;
    }

    this.syncInProgress = true;
    
    try {
      // Sync queued API requests
      await enhancedApi.syncQueuedRequests();
      
      // Refresh cached data
      await this.refreshCachedData();
      
      // Notify listeners
      this.notifyListeners();
      
      console.log('Sync completed successfully');
    } catch (error) {
      console.error('Sync failed:', error);
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * Refresh cached data with fresh data from server
   */
  private async refreshCachedData(): Promise<void> {
    try {
      // Get user info from auth context or cookies
      const userStr = localStorage.getItem('user');
      if (!userStr) return;
      
      const user = JSON.parse(userStr);
      const userId = user.id;

      // Refresh athletes data
      try {
        await enhancedApi.getAthletes(userId);
      } catch (error) {
        console.warn('Failed to refresh athletes data:', error);
      }

      // Refresh events data
      try {
        await enhancedApi.getEvents(userId);
      } catch (error) {
        console.warn('Failed to refresh events data:', error);
      }

      // Refresh dashboard stats
      try {
        await enhancedApi.getDashboardStats(userId);
      } catch (error) {
        console.warn('Failed to refresh dashboard stats:', error);
      }

    } catch (error) {
      console.error('Error refreshing cached data:', error);
    }
  }

  /**
   * Add sync listener
   */
  addSyncListener(callback: () => void): void {
    this.syncListeners.push(callback);
  }

  /**
   * Remove sync listener
   */
  removeSyncListener(callback: () => void): void {
    this.syncListeners = this.syncListeners.filter(listener => listener !== callback);
  }

  /**
   * Notify all listeners
   */
  private notifyListeners(): void {
    this.syncListeners.forEach(listener => {
      try {
        listener();
      } catch (error) {
        console.error('Error in sync listener:', error);
      }
    });
  }

  /**
   * Force sync now
   */
  async forcSync(): Promise<void> {
    if (!navigator.onLine) {
      throw new Error('Cannot sync while offline');
    }
    
    await this.syncAll();
  }

  /**
   * Get sync status
   */
  getSyncStatus(): {
    inProgress: boolean;
    queueLength: number;
    lastSync: string | null;
  } {
    const queue = offlineService.getSyncQueue();
    const lastSync = localStorage.getItem('last_sync');
    
    return {
      inProgress: this.syncInProgress,
      queueLength: queue.length,
      lastSync: lastSync ? new Date(parseInt(lastSync)).toLocaleString() : null
    };
  }

  /**
   * Clear all offline data
   */
  clearOfflineData(): void {
    offlineService.clearCache();
    offlineService.clearSyncQueue();
    localStorage.removeItem('last_sync');
  }

  /**
   * Register for background sync (if supported)
   */
  async registerBackgroundSync(): Promise<void> {
    if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
      try {
        const registration = await navigator.serviceWorker.ready;
        await registration.sync.register('background-sync');
        console.log('Background sync registered');
      } catch (error) {
        console.error('Background sync registration failed:', error);
      }
    }
  }

  /**
   * Check if PWA features are supported
   */
  checkPWASupport(): {
    serviceWorker: boolean;
    backgroundSync: boolean;
    pushNotifications: boolean;
    installPrompt: boolean;
  } {
    return {
      serviceWorker: 'serviceWorker' in navigator,
      backgroundSync: 'serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype,
      pushNotifications: 'PushManager' in window,
      installPrompt: 'BeforeInstallPromptEvent' in window
    };
  }

  /**
   * Get offline storage info
   */
  getStorageInfo(): {
    cacheInfo: { totalItems: number; totalSize: string };
    queueLength: number;
    isOnline: boolean;
  } {
    return {
      cacheInfo: offlineService.getCacheInfo(),
      queueLength: offlineService.getSyncQueue().length,
      isOnline: navigator.onLine
    };
  }
}

// Create singleton instance
export const pwaSyncService = new PWASyncService();

// Auto-register background sync when service loads
if (typeof window !== 'undefined') {
  pwaSyncService.registerBackgroundSync().catch(console.error);
}
