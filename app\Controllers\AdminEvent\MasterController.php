<?php

namespace App\Controllers\AdminEvent;

use App\Controllers\BaseController;
use App\Models\Atlet as AtletModel;
use App\Models\PendaftaranAtlet;
use App\Models\PendaftaranEvent;
use App\Models\Kontingen;
use App\Models\Event;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class MasterController extends BaseController
{
    public function index()
    {
        // Ambil ID event dari session admin
        $eventId = session('event_id');

        // Pastikan event_id ada, jika tidak tampilkan data kosong
        if (!$eventId) {
            return view('admin-event/master/index', ['atlets' => []]);
        }
        
        // Periksa status event
        $eventModel = new Event();
        $currentEvent = $eventModel->find($eventId);
        if (!$currentEvent || $currentEvent['status'] !== 'active') {
            // Jika event sudah selesai atau tidak aktif, hapus event_id dari session
            session()->remove('event_id');
            return view('admin-event/master/index', ['atlets' => []]);
        }

        $pendaftaranEventModel = new PendaftaranEvent();
        $atletModel = new AtletModel();

        // Ambil semua ID kontingen yang terdaftar di event ini
        $kontingenIds = $pendaftaranEventModel
            ->where('id_event', $eventId)
            ->select('id_kontingen')
            ->findColumn('id_kontingen');

        // Jika tidak ada kontingen yang terdaftar, tampilkan data kosong
        if (empty($kontingenIds)) {
            return view('admin-event/master/index', ['atlets' => []]);
        }

        // Ambil data atlet beserta pendaftaran untuk event saat ini saja
        $data['atlets'] = $atletModel
            ->select('
                pendaftaran_atlet.id as pendaftaran_atlet_id,
                atlet.*,
                kontingen.name as kontingen_name,
                pendaftaran_atlet.jenis_tanding,
                pendaftaran_atlet.kategori_umur,
                pendaftaran_atlet.kelas_tanding,
                pendaftaran_atlet.bukti_pembayaran,
                pendaftaran_atlet.status
            ')
            ->join('kontingen', 'kontingen.id = atlet.id_kontingen')
            ->join('pendaftaran_atlet_detail', 'pendaftaran_atlet_detail.id_atlet = atlet.id')
            ->join('pendaftaran_atlet', 'pendaftaran_atlet.id = pendaftaran_atlet_detail.id_pendaftaran_atlet')
            ->where('pendaftaran_atlet.id_pendaftaran_event', $eventId) // Filter untuk event saat ini
            ->whereIn('atlet.id_kontingen', $kontingenIds)
            ->findAll();

        return view('admin-event/master/index', $data);
    }


    public function konfirmasi($id)
    {
        $pendaftaranAtlet = new PendaftaranAtlet();
        $pendaftaran = $pendaftaranAtlet->find($id);
        
        if (!$pendaftaran) {
            return redirect()->back()->with('error', 'Data tidak ditemukan');
        }

        // Update status pembayaran
        $pendaftaranAtlet->update($id, [
            'status' => 'paid',
            'updated_at' => date('Y-m-d H:i:s')
        ]);

        return redirect()->back()->with('success', 'Pembayaran dikonfirmasi');
    }

    public function export()
    {
        $eventId = session('event_id');
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set header Excel sesuai view
        $headers = [
            'NO', 'INPA ID', 'NAME', 'GENDER', 'BIRTH DATE', 
            'PARTICIPANT NAME', 'MATCH CATEGORY', 'WEIGHT', 
            'HEIGHT', 'CATEGORY', 'MATCH CATEGORY', 'CLASS', 
            'PICTURE', 'DELEGATION', 'PICTURE'
        ];

        $column = 'A';
        foreach ($headers as $header) {
            $sheet->setCellValue($column++ . '1', $header);
        }

        // Ambil data atlet dengan filter event saat ini
        $atletModel = new AtletModel(); // Pastikan namespace benar
        $pendaftaranEventModel = new PendaftaranEvent();
        
        // Ambil kontingenIds yang hanya terkait dengan event saat ini
        $kontingenIds = $pendaftaranEventModel
            ->where('id_event', $eventId)
            ->select('id_kontingen')
            ->findColumn('id_kontingen');

        // Jika tidak ada kontingen yang terdaftar pada event saat ini, set array kosong
        if (empty($kontingenIds)) {
            $atlets = [];
        } else {
            $atlets = $atletModel
                ->select('
                    atlet.id,
                    atlet.name,
                    atlet.jenis_kelamin,
                    atlet.tanggal_lahir,
                    atlet.berat_badan,
                    atlet.tinggi_badan,
                    kontingen.name as kontingen_name,
                    pendaftaran_atlet.jenis_tanding,
                    pendaftaran_atlet.kategori_umur,
                    pendaftaran_atlet.kelas_tanding
                ')
                ->join('kontingen', 'kontingen.id = atlet.id_kontingen')
                ->join('pendaftaran_atlet_detail', 'pendaftaran_atlet_detail.id_atlet = atlet.id')
                ->join('pendaftaran_atlet', 'pendaftaran_atlet.id = pendaftaran_atlet_detail.id_pendaftaran_atlet')
                ->where('pendaftaran_atlet.id_pendaftaran_event', $eventId) // Filter untuk event saat ini
                ->whereIn('atlet.id_kontingen', $kontingenIds)
                ->findAll();
        }

        // Isi data ke Excel
        $rowIndex = 2;
        foreach ($atlets as $key => $atlet) {
            $rowData = [
                $key + 1,
                '',
                $atlet['name'],
                $atlet['jenis_kelamin'],
                $atlet['tanggal_lahir'],
                $atlet['kontingen_name'], // Participant Name
                $atlet['kategori_umur'],  // Match Category 1
                $atlet['berat_badan'],
                $atlet['tinggi_badan'],
                $atlet['jenis_tanding'],   // Category
                $atlet['kategori_umur'],   // Match Category 2
                $atlet['kelas_tanding'],
                '', // Picture 1
                '', // Delegation
                ''  // Picture 2
            ];

            $sheet->fromArray($rowData, null, 'A' . $rowIndex);
            $rowIndex++;
        }

        // Konfigurasi download file Excel
        $writer = new Xlsx($spreadsheet);
        $fileName = 'DATA_MASTER_' . date('YmdHis') . '.xlsx';
        
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $fileName . '"');
        header('Cache-Control: max-age=0');
        
        $writer->save('php://output');
        exit();
    }
}
