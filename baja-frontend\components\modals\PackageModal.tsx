'use client';

import React, { useState, useEffect } from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import ImageUpload from '@/components/ui/ImageUpload';
import LoadingOverlay from '@/components/ui/LoadingOverlay';
import { AdminPackage } from '@/types';
import { uploadService } from '@/lib/upload.service';
import toast from 'react-hot-toast';

interface PackageModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (packageData: Partial<AdminPackage>, imageFile?: File) => Promise<void>;
  package?: AdminPackage | null;
  mode: 'create' | 'edit';
}

const PackageModal: React.FC<PackageModalProps> = ({
  isOpen,
  onClose,
  onSave,
  package: pkg,
  mode
}) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    images: ''
  });
  const [loading, setLoading] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  useEffect(() => {
    if (mode === 'edit' && pkg) {
      setFormData({
        name: pkg.name || '',
        description: pkg.description || '',
        images: pkg.images || ''
      });
    } else {
      setFormData({
        name: '',
        description: '',
        images: ''
      });
    }
    setSelectedFile(null);
  }, [mode, pkg, isOpen]);

  const handleImagesSelected = (files: File[]) => {
    if (files.length > 0) {
      setSelectedFile(files[0]); // Take the first file since we only allow one
    } else {
      setSelectedFile(null);
    }
    // Don't show success message here, just store the file for later upload
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      toast.error('Please fill in the package name');
      return;
    }

    setLoading(true);
    try {
      // Pass the selected file to the parent component
      await onSave(formData, selectedFile || undefined);

      toast.success(`Package ${mode === 'create' ? 'created' : 'updated'} successfully!`);
      onClose();
    } catch (error: any) {
      toast.error(error.message || `Failed to ${mode} package`);
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto border border-gold-500/30">
        <div className="flex justify-between items-center p-6 border-b border-gold-500/20">
          <h2 className="text-xl font-semibold text-white">
            {mode === 'create' ? 'Create New Package' : 'Edit Package'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gold-400 transition-colors"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-white mb-1">
              Package Name *
            </label>
            <Input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleChange}
              placeholder="Enter package name"
              required
              className="bg-gray-800 border-gray-600 text-white focus:border-gold-500 focus:ring-gold-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-white mb-1">
              Description
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleChange}
              placeholder="Enter package description"
              rows={4}
              className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gold-500 focus:border-gold-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-white mb-1">
              Package Image
            </label>
            <ImageUpload
              onImagesSelected={handleImagesSelected}
              currentImages={formData.images ? [formData.images] : []}
              maxImages={1}
              placeholder="Select a package image (will be uploaded when package is saved)"
              maxSize={5}
              acceptedFormats={['image/jpeg', 'image/jpg', 'image/png']}
              showPreview={true}
              selectButtonText="Choose Image"
              disabled={loading}
              theme="dark"
            />
            {formData.images && !selectedFile && (
              <p className="text-sm text-green-400 mt-1">
                ✓ Current image uploaded
              </p>
            )}
            {selectedFile && (
              <p className="text-sm text-gold-400 mt-1">
                📷 Image selected: {selectedFile.name} (will be uploaded when package is saved)
              </p>
            )}
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={loading}
              loading={loading}
            >
              {mode === 'create' ? 'Create Package' : 'Update Package'}
            </Button>
          </div>
        </form>
      </div>

      <LoadingOverlay
        isLoading={loading}
        message={mode === 'create' ? 'Creating package...' : 'Updating package...'}
      />
    </div>
  );
};

export default PackageModal;
