import { ReactThreeFiber } from '@react-three/fiber'
import * as THREE from 'three'

declare global {
  namespace JSX {
    interface IntrinsicElements {
      // Basic Three.js objects
      group: ReactThreeFiber.Object3DNode<THREE.Group, typeof THREE.Group>
      mesh: ReactThreeFiber.Object3DNode<THREE.Mesh, typeof THREE.Mesh>
      scene: ReactThreeFiber.Object3DNode<THREE.Scene, typeof THREE.Scene>
      
      // Geometries
      boxGeometry: ReactThreeFiber.Node<THREE.BoxGeometry, typeof THREE.BoxGeometry>
      sphereGeometry: ReactThreeFiber.Node<THREE.SphereGeometry, typeof THREE.SphereGeometry>
      planeGeometry: ReactThreeFiber.Node<THREE.PlaneGeometry, typeof THREE.PlaneGeometry>
      cylinderGeometry: ReactThreeFiber.Node<THREE.CylinderGeometry, typeof THREE.CylinderGeometry>
      bufferGeometry: ReactThreeFiber.Node<THREE.BufferGeometry, typeof THREE.BufferGeometry>
      
      // Materials
      meshBasicMaterial: ReactThreeFiber.MaterialNode<THREE.MeshBasicMaterial, typeof THREE.MeshBasicMaterial>
      meshStandardMaterial: ReactThreeFiber.MaterialNode<THREE.MeshStandardMaterial, typeof THREE.MeshStandardMaterial>
      meshPhysicalMaterial: ReactThreeFiber.MaterialNode<THREE.MeshPhysicalMaterial, typeof THREE.MeshPhysicalMaterial>
      shaderMaterial: ReactThreeFiber.MaterialNode<THREE.ShaderMaterial, typeof THREE.ShaderMaterial>
      
      // Lights
      ambientLight: ReactThreeFiber.LightNode<THREE.AmbientLight, typeof THREE.AmbientLight>
      directionalLight: ReactThreeFiber.LightNode<THREE.DirectionalLight, typeof THREE.DirectionalLight>
      pointLight: ReactThreeFiber.LightNode<THREE.PointLight, typeof THREE.PointLight>
      spotLight: ReactThreeFiber.LightNode<THREE.SpotLight, typeof THREE.SpotLight>
      
      // Cameras
      perspectiveCamera: ReactThreeFiber.CameraNode<THREE.PerspectiveCamera, typeof THREE.PerspectiveCamera>
      orthographicCamera: ReactThreeFiber.CameraNode<THREE.OrthographicCamera, typeof THREE.OrthographicCamera>
      
      // Helpers
      color: ReactThreeFiber.Node<THREE.Color, typeof THREE.Color>
      fog: ReactThreeFiber.Node<THREE.Fog, typeof THREE.Fog>
      fogExp2: ReactThreeFiber.Node<THREE.FogExp2, typeof THREE.FogExp2>
      
      // Textures
      texture: ReactThreeFiber.Node<THREE.Texture, typeof THREE.Texture>
      canvasTexture: ReactThreeFiber.Node<THREE.CanvasTexture, typeof THREE.CanvasTexture>
      dataTexture: ReactThreeFiber.Node<THREE.DataTexture, typeof THREE.DataTexture>
      
      // Controls and other common elements
      primitive: ReactThreeFiber.Primitive
    }
  }
}

// Extend ReactThreeFiber namespace for custom properties
declare module '@react-three/fiber' {
  interface ThreeElements {
    // Custom shader material properties
    extendedMaterial: ReactThreeFiber.MaterialNode<THREE.ShaderMaterial, typeof THREE.ShaderMaterial>
  }
}

export {}
