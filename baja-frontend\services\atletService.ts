import { api } from '@/lib/api';

export interface Atlet {
  id: number;
  nik: string;
  name: string;
  tanggal_lahir: string;
  jenis_kelamin: 'M' | 'F';
  alamat?: string;
  no_hp?: string;
  agama?: string;
  umur?: number;
  berat_badan?: number;
  tinggi_badan?: number;
  foto?: string;
  status_verifikasi: 'pending' | 'verified' | 'rejected';
  id_kontingen: number;
  id_user: number;
  atletKontingen?: {
    id: number;
    name: string;
  };
  created_at: string;
  updated_at: string;
}

export interface CreateAtletData {
  nik: string;
  name: string;
  tanggal_lahir: string;
  jenis_kelamin: 'M' | 'F';
  alamat?: string;
  no_hp?: string;
  agama?: string;
  umur?: number;
  berat_badan?: number;
  tinggi_badan?: number;
  id_kontingen?: number;
}

export interface UpdateAtletData {
  nik?: string;
  name?: string;
  tanggal_lahir?: string;
  jenis_kelamin?: 'M' | 'F';
  alamat?: string;
  no_hp?: string;
  agama?: string;
  umur?: number;
  berat_badan?: number;
  tinggi_badan?: number;
}

class AtletService {
  async getAllAtlet(page = 1, limit = 10, search = '', kontingen_id = ''): Promise<{
    atlet: Atlet[];
    pagination: {
      total: number;
      page: number;
      limit: number;
      totalPages: number;
    };
  }> {
    try {
      const response = await api.get('/atlet', {
        params: { page, limit, search, kontingen_id }
      });
      return response.data.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch athletes');
    }
  }

  async getAtletById(id: number): Promise<Atlet> {
    try {
      const response = await api.get(`/atlet/${id}`);
      return response.data.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch athlete');
    }
  }

  async createAtlet(data: CreateAtletData): Promise<Atlet> {
    try {
      const response = await api.post('/atlet', data);
      return response.data.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to create athlete');
    }
  }

  async updateAtlet(id: number, data: UpdateAtletData): Promise<Atlet> {
    try {
      const response = await api.put(`/atlet/${id}`, data);
      return response.data.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to update athlete');
    }
  }

  async deleteAtlet(id: number): Promise<void> {
    try {
      await api.delete(`/atlet/${id}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to delete athlete');
    }
  }

  async uploadAtletPhoto(id: number, file: File): Promise<{ foto: string; optimizedUrl: string }> {
    try {
      const formData = new FormData();
      formData.append('photo', file);

      const response = await api.post(`/atlet/${id}/upload`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to upload photo');
    }
  }

  async verifyAtlet(id: number, status: 'verified' | 'rejected'): Promise<void> {
    try {
      await api.patch(`/atlet/${id}/verify`, { status_verifikasi: status });
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to verify athlete');
    }
  }
}

export const atletService = new AtletService();
