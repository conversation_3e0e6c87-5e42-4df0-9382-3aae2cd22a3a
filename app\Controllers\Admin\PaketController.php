<?php
namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\Paket;

class PaketController extends BaseController
{
    public function index()
    {
        $paketModel = new Paket();
        $data['paket'] = $paketModel->findAll();
        return view('admin/paket/index', $data);
    }

    public function create()
    {
        return view('admin/paket/create');
    }

    public function store()
    {
        // Aturan validasi untuk create
        $validationRules = [
            'name'        => 'required|min_length[3]',
            'images'      => 'uploaded[images]|max_size[images,2048]|is_image[images]',
            'description' => 'required|min_length[10]',
        ];

        if (!$this->validate($validationRules)) {
            return redirect()->back()
                ->withInput()
                ->with('errors', $this->validator->getErrors());
        }

        $imageFile = $this->request->getFile('images');
        if ($imageFile->isValid() && !$imageFile->hasMoved()) {
            $newName = $imageFile->getRandomName();
            // Pastikan direktori diakhiri dengan slash
            if (!$imageFile->move(ROOTPATH . 'public/uploads/file-paket/', $newName)) {
                return redirect()->back()
                    ->withInput()
                    ->with('error', 'Gagal mengunggah gambar.');
            }

            $paketModel = new Paket();
            $insertData = [
                'name'        => $this->request->getPost('name'),
                'images'      => $newName,
                'description' => $this->request->getPost('description'),
            ];

            if ($paketModel->insert($insertData) === false) {
                return redirect()->back()
                    ->withInput()
                    ->with('errors', $paketModel->errors());
            }

            return redirect()->to('/paket')
                ->with('success', 'Paket berhasil ditambahkan.');
        }

        return redirect()->back()
            ->withInput()
            ->with('error', 'Gagal mengunggah gambar.');
    }

    public function edit($id)
    {
        $paketModel = new Paket();
        $data['paket'] = $paketModel->find($id);
        if (!$data['paket']) {
            return redirect()->to('/paket')
                ->with('error', 'Paket tidak ditemukan.');
        }
        return view('admin/paket/edit', $data);
    }

    public function update($id)
    {
        $paketModel = new Paket();
        $existingPaket = $paketModel->find($id);
        if (!$existingPaket) {
            return redirect()->to('/paket')
                ->with('error', 'Paket tidak ditemukan.');
        }

        // Aturan validasi untuk update (field gambar opsional)
        $validationRules = [
            'name'        => 'required|min_length[3]',
            'description' => 'required|min_length[10]',
        ];

        $imageFile = $this->request->getFile('images');
        $uploaded = $imageFile && $imageFile->isValid() && !$imageFile->hasMoved();
        if ($uploaded) {
            $validationRules['images'] = 'max_size[images,2048]|is_image[images,jpg,png,jpeg]';
        }

        if (!$this->validate($validationRules)) {
            return redirect()->back()
                ->withInput()
                ->with('errors', $this->validator->getErrors());
        }

        $updateData = [
            'name'        => $this->request->getPost('name'),
            'description' => $this->request->getPost('description'),
        ];

        // Jika file gambar diupload, proses upload dan hapus file lama
        if ($uploaded) {
            $newName = $imageFile->getRandomName();
            if (!$imageFile->move(ROOTPATH . 'public/uploads/file-paket/', $newName)) {
                return redirect()->back()
                    ->withInput()
                    ->with('error', 'Gagal mengunggah gambar.');
            }
            // Hapus file gambar lama jika ada
            $oldImage = $existingPaket['images'];
            $oldImagePath = ROOTPATH . 'public/uploads/file-paket/' . $oldImage;
            if ($oldImage && file_exists($oldImagePath)) {
                @unlink($oldImagePath);
            }
            $updateData['images'] = $newName;
        }

        if ($paketModel->update($id, $updateData) === false) {
            return redirect()->back()
                ->withInput()
                ->with('errors', $paketModel->errors());
        }

        return redirect()->to('/paket')
            ->with('success', 'Paket berhasil diubah.');
    }

    public function delete($id)
    {
        $paketModel = new Paket();
        $paketData = $paketModel->find($id);
        if (!$paketData) {
            return redirect()->to('/paket')
                ->with('error', 'Paket tidak ditemukan.');
        }

        // Hapus file gambar jika ada
        $image = $paketData['images'];
        $imagePath = ROOTPATH . 'public/uploads/file-paket/' . $image;
        if ($image && file_exists($imagePath)) {
            @unlink($imagePath);
        }

        if (!$paketModel->delete($id)) {
            return redirect()->to('/paket')
                ->with('error', 'Gagal menghapus paket.');
        }

        return redirect()->to('/paket')
            ->with('success', 'Paket berhasil dihapus.');
    }
}
