'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import toast from 'react-hot-toast';

interface GoogleAuthButtonProps {
  mode: 'login' | 'register';
  onSuccess?: () => void;
  disabled?: boolean;
}

export default function GoogleAuthButton({ mode, onSuccess }: GoogleAuthButtonProps) {
  const [loading, setLoading] = useState(false);
  const [googleReady, setGoogleReady] = useState(false);
  const { googleLogin } = useAuth();
  const router = useRouter();

  useEffect(() => {
    initializeGoogleAuth();
  }, [mode]);

  const initializeGoogleAuth = async () => {
    try {
      // Check if Google Client ID is configured
      if (!process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID ||
          process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID === 'your-google-client-id') {
        return;
      }

      // Load Google API if not already loaded
      if (!window.google) {
        await loadGoogleAPI();
      }

      // Initialize Google Sign-In
      if (window.google) {
        window.google.accounts.id.initialize({
          client_id: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
          callback: handleCredentialResponse,
          auto_select: false,
          cancel_on_tap_outside: true,
        });

        // Render the Google button directly
        const buttonContainer = document.getElementById(`google-auth-button-${mode}`);
        if (buttonContainer) {
          buttonContainer.innerHTML = '';
          window.google.accounts.id.renderButton(buttonContainer, {
            theme: 'outline',
            size: 'large',
            width: '100%',
            text: mode === 'register' ? 'signup_with' : 'signin_with',
            shape: 'rectangular',
          });
          setGoogleReady(true);
        }
      }

    } catch (error) {
      console.error('Google auth initialization error:', error);
      setGoogleReady(false);
    }
  };

  const handleCredentialResponse = async (response: any) => {
    try {
      setLoading(true);
      console.log(`🔐 Google credential received for ${mode}:`, response);
      
      if (!response.credential) {
        throw new Error('No credential received from Google');
      }
      
      if (mode === 'login') {
        // Direct login
        await googleLogin(response.credential);
        toast.success('Login dengan Google berhasil!');
        onSuccess?.();
      } else {
        // Registration - redirect to role selection
        // Decode the JWT to get user info for display
        const payload = JSON.parse(atob(response.credential.split('.')[1]));
        
        // Store Google data temporarily for role selection
        sessionStorage.setItem('pendingGoogleAuth', JSON.stringify({
          idToken: response.credential,
          name: payload.name,
          email: payload.email,
          picture: payload.picture
        }));
        
        // Redirect to role selection page
        router.push('/auth/google-role-selection');
      }
    } catch (error: any) {
      console.error(`❌ Google ${mode} error:`, error);
      const errorMessage = error.response?.data?.message || error.message || `${mode === 'login' ? 'Login' : 'Registrasi'} dengan Google gagal`;
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const loadGoogleAPI = (): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (document.getElementById('google-api-script')) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.id = 'google-api-script';
      script.src = 'https://accounts.google.com/gsi/client';
      script.async = true;
      script.defer = true;
      script.onload = () => resolve();
      script.onerror = () => reject(new Error('Failed to load Google API'));
      document.head.appendChild(script);
    });
  };



  return (
    <div className="w-full">
      {/* Google Auth Button Container - This will be populated by Google's renderButton */}
      <div id={`google-auth-button-${mode}`} className="w-full"></div>

      {/* Fallback message if Google is not available */}
      {!googleReady && (
        <div className="w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg bg-gray-100 text-gray-500">
          {loading ? (
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-gray-500 mr-3"></div>
              Memuat Google Sign-In...
            </div>
          ) : (
            <div className="flex items-center">
              <svg className="w-5 h-5 mr-3 opacity-50" viewBox="0 0 24 24">
                <path
                  fill="#4285F4"
                  d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                />
                <path
                  fill="#34A853"
                  d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                />
                <path
                  fill="#FBBC05"
                  d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                />
                <path
                  fill="#EA4335"
                  d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                />
              </svg>
              Google Sign-In tidak tersedia
            </div>
          )}
        </div>
      )}
    </div>
  );
}
