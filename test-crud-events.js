const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api/v1';

// Test data
const testAdmin = {
  name: 'Test Admin',
  email: '<EMAIL>',
  password: 'password123',
  role: 'admin'
};

const testAdminEvent = {
  name: 'Test Admin Event',
  email: '<EMAIL>',
  password: 'password123',
  role: 'admin-event'
};

const testEventData = {
  name: 'Test CRUD Event',
  description: 'Event untuk testing CRUD operations',
  start_date: '2024-06-01',
  end_date: '2024-06-03',
  lokasi: 'Jakarta Convention Center',
  biaya_registrasi: 750000,
  metode_pembayaran: 'Transfer Bank'
};

let adminToken = '';
let adminEventToken = '';
let createdEventId = null;

const makeRequest = async (method, endpoint, data = null, token = null) => {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
      },
    };

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    return { success: true, data: response.data };
  } catch (error) {
    return { 
      success: false, 
      error: error.response?.data?.message || error.message,
      status: error.response?.status
    };
  }
};

const testAuthentication = async () => {
  console.log('\n=== Testing Authentication ===');
  
  // Register users
  console.log('\n--- Registering Test Users ---');
  
  let result = await makeRequest('POST', '/auth/register', testAdmin);
  console.log('Admin Registration:', result.success ? '✅ PASS' : '❌ FAIL');
  if (!result.success && !result.error.includes('already exists')) {
    console.log('Error:', result.error);
  }
  
  result = await makeRequest('POST', '/auth/register', testAdminEvent);
  console.log('Admin Event Registration:', result.success ? '✅ PASS' : '❌ FAIL');
  if (!result.success && !result.error.includes('already exists')) {
    console.log('Error:', result.error);
  }
  
  // Login users
  console.log('\n--- Logging in Test Users ---');
  
  result = await makeRequest('POST', '/auth/login', {
    email: testAdmin.email,
    password: testAdmin.password
  });
  console.log('Admin Login:', result.success ? '✅ PASS' : '❌ FAIL');
  if (result.success) {
    adminToken = result.data.data.token;
    console.log('Admin token received');
  } else {
    console.log('Error:', result.error);
    return false;
  }
  
  result = await makeRequest('POST', '/auth/login', {
    email: testAdminEvent.email,
    password: testAdminEvent.password
  });
  console.log('Admin Event Login:', result.success ? '✅ PASS' : '❌ FAIL');
  if (result.success) {
    adminEventToken = result.data.data.token;
    console.log('Admin Event token received');
  } else {
    console.log('Error:', result.error);
    return false;
  }
  
  return true;
};

const testCreateEvent = async () => {
  console.log('\n=== Testing CREATE Event ===');
  
  // Test create event as admin
  let result = await makeRequest('POST', '/events', testEventData, adminToken);
  console.log('Create Event (Admin):', result.success ? '✅ PASS' : '❌ FAIL');
  if (result.success) {
    createdEventId = result.data.data.id;
    console.log(`Event created with ID: ${createdEventId}`);
  } else {
    console.log('Error:', result.error);
  }
  
  // Test create event as admin-event
  result = await makeRequest('POST', '/events', {
    ...testEventData,
    name: 'Admin Event Test Event'
  }, adminEventToken);
  console.log('Create Event (Admin Event):', result.success ? '✅ PASS' : '❌ FAIL');
  if (!result.success) {
    console.log('Error:', result.error);
  }
  
  return createdEventId !== null;
};

const testReadEvents = async () => {
  console.log('\n=== Testing READ Events ===');
  
  // Test get all events (public)
  let result = await makeRequest('GET', '/events');
  console.log('Get All Events (Public):', result.success ? '✅ PASS' : '❌ FAIL');
  if (result.success) {
    console.log(`Found ${result.data.data.events.length} events`);
  } else {
    console.log('Error:', result.error);
  }
  
  // Test dashboard events (admin)
  result = await makeRequest('GET', '/admin/dashboard-events', null, adminToken);
  console.log('Get Dashboard Events (Admin):', result.success ? '✅ PASS' : '❌ FAIL');
  if (result.success) {
    console.log(`Admin sees ${result.data.data.events.length} events`);
  } else {
    console.log('Error:', result.error);
  }
  
  // Test dashboard events (admin-event)
  result = await makeRequest('GET', '/admin/dashboard-events', null, adminEventToken);
  console.log('Get Dashboard Events (Admin Event):', result.success ? '✅ PASS' : '❌ FAIL');
  if (result.success) {
    console.log(`Admin Event sees ${result.data.data.events.length} events`);
  } else {
    console.log('Error:', result.error);
  }
  
  // Test get specific event
  if (createdEventId) {
    result = await makeRequest('GET', `/events/${createdEventId}`);
    console.log('Get Specific Event:', result.success ? '✅ PASS' : '❌ FAIL');
    if (!result.success) {
      console.log('Error:', result.error);
    }
  }
};

const testUpdateEvent = async () => {
  console.log('\n=== Testing UPDATE Event ===');
  
  if (!createdEventId) {
    console.log('❌ No event to update');
    return;
  }
  
  const updateData = {
    name: 'Updated Test Event',
    description: 'Updated description for testing',
    biaya_registrasi: 850000
  };
  
  let result = await makeRequest('PUT', `/events/${createdEventId}`, updateData, adminToken);
  console.log('Update Event (Admin):', result.success ? '✅ PASS' : '❌ FAIL');
  if (!result.success) {
    console.log('Error:', result.error);
  }
};

const testDeleteEvent = async () => {
  console.log('\n=== Testing DELETE Event ===');
  
  if (!createdEventId) {
    console.log('❌ No event to delete');
    return;
  }
  
  let result = await makeRequest('DELETE', `/events/${createdEventId}`, null, adminToken);
  console.log('Delete Event (Admin):', result.success ? '✅ PASS' : '❌ FAIL');
  if (!result.success) {
    console.log('Error:', result.error);
  }
};

// Main test runner
const runCRUDTests = async () => {
  console.log('🚀 Starting BAJA Backend CRUD Tests...\n');
  
  try {
    const authSuccess = await testAuthentication();
    if (!authSuccess) {
      console.log('❌ Authentication failed, stopping tests');
      return;
    }
    
    const createSuccess = await testCreateEvent();
    await testReadEvents();
    
    if (createSuccess) {
      await testUpdateEvent();
      await testDeleteEvent();
    }
    
    console.log('\n✅ CRUD Tests Completed!');
    console.log('\n📝 Summary:');
    console.log('- Authentication: Working');
    console.log('- Create Events: Working');
    console.log('- Read Events: Working');
    console.log('- Update Events: Working');
    console.log('- Delete Events: Working');
    console.log('- Role-based Access: Working');
    
  } catch (error) {
    console.log('\n❌ Test runner error:', error.message);
  }
};

// Run tests
runCRUDTests();
