import api from './api';
import Cookies from 'js-cookie';
import { LoginRequest, RegisterRequest, AuthResponse, User, ApiResponse } from '@/types';

export const authService = {
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    const response = await api.post<ApiResponse<AuthResponse>>('/auth/login', credentials);

    if (response.data.success && response.data.data) {
      // Token is already set as httpOnly cookie by the server
      // No need to set client-side cookie
      return response.data.data;
    }

    throw new Error(response.data.message || 'Login failed');
  },

  async register(userData: RegisterRequest): Promise<{ email: string }> {
    const response = await api.post<ApiResponse<{ email: string }>>('/auth/register', userData);

    if (response.data.success && response.data.data) {
      return response.data.data;
    }

    throw new Error(response.data.message || 'Registration failed');
  },

  async verifyRegistrationOTP(email: string, otp: string): Promise<User> {
    try {
      console.log('🔍 Verifying OTP:', { email, otp });

      const response = await api.post<ApiResponse<User>>('/auth/verify-registration', {
        email,
        otp: otp.toString()
      });

      console.log('📡 OTP verification response:', response.data);

      if (response.data.success && response.data.data) {
        return response.data.data;
      }

      throw new Error(response.data.message || 'OTP verification failed');
    } catch (error: any) {
      console.error('❌ OTP verification error:', error);

      if (error.response) {
        console.error('❌ Response data:', error.response.data);
        console.error('❌ Response status:', error.response.status);
        throw new Error(error.response.data?.message || 'OTP verification failed');
      } else if (error.request) {
        console.error('❌ Network error:', error.request);
        throw new Error('Network error. Please check your connection.');
      } else {
        throw new Error(error.message || 'OTP verification failed');
      }
    }
  },

  async googleLogin(idToken: string): Promise<AuthResponse> {
    const response = await api.post<ApiResponse<AuthResponse>>('/auth/google/login', { idToken });

    if (response.data.success && response.data.data) {
      // Token is already set as httpOnly cookie by the server
      // No need to set client-side cookie
      return response.data.data;
    }

    throw new Error(response.data.message || 'Google login failed');
  },

  async googleRegister(idToken: string, role: string): Promise<AuthResponse> {
    const response = await api.post<ApiResponse<AuthResponse>>('/auth/google/register', { idToken, role });

    if (response.data.success && response.data.data) {
      // Token is already set as httpOnly cookie by the server
      // No need to set client-side cookie
      return response.data.data;
    }

    throw new Error(response.data.message || 'Google registration failed');
  },

  async resendOTP(email: string, type: 'registration' | 'login' | 'password_reset'): Promise<void> {
    const response = await api.post<ApiResponse<{ email: string }>>('/auth/resend-otp', { email, type });

    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to resend OTP');
    }
  },

  async logout(): Promise<void> {
    try {
      await api.post('/auth/logout');
    } catch (error) {
      console.error('Logout error:', error);
    }
    // Server will clear the httpOnly cookie
    // No need to remove client-side cookie since we're not using it
  },

  async getProfile(): Promise<User> {
    const response = await api.get<ApiResponse<User>>('/auth/profile');
    
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    
    throw new Error(response.data.message || 'Failed to get profile');
  },

  async updateProfile(userData: Partial<User>): Promise<User> {
    const response = await api.put<ApiResponse<User>>('/auth/profile', userData);
    
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    
    throw new Error(response.data.message || 'Failed to update profile');
  },

  isAuthenticated(): boolean {
    // Since we're using httpOnly cookies, we can't check the token directly
    // We'll need to make a request to check authentication status
    // For now, we'll rely on the AuthContext to manage this state
    return true; // This will be managed by AuthContext
  },

  getToken(): string | undefined {
    // Token is httpOnly, not accessible from client-side
    return undefined;
  },
};
