<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateKontingenTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true
            ],
            'name' => [
                'type' => 'VARCHAR',
                'constraint' => '255',
            ],
            'negara' => [
                'type' => 'VARCHAR',
                'constraint' => '255',
            ],
            'provinsi' => [
                'type' => 'VARCHAR',
                'constraint' => '255',
            ],
            'kabupaten_kota' => [
                'type' => 'VARCHAR',    
                'constraint' => '255',
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'id_user' => [
                'type' => 'INT',
                'constraint' => 5,
                'unsigned' => true, 
            ],
            ]); 
        $this->forge->addKey('id', true);
        $this->forge->addForeignKey('id_user', 'users', 'id', 'CASCADE', 'CASCADE');
        $this->forge->createTable('kontingen');
    }

    public function down()
    {
        $this->forge->dropTable('kontingen');
    }
}
