<?= $this->extend('layouts/backend/template') ?>
<?= $this->section('content') ?>

<div id="content">
    <div class="container-fluid">

        <!-- Page Heading -->
        <h1 class="h3 mb-2 text-gray-800">Edit Event</h1>

        <!-- Form Tambah Users -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Form Edit Event</h6>
            </div>
            <div class="card-body">
                <form action="<?= base_url('event/update') ?>" method="post" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="event_image">Gambar (JPG, PNG)</label>
                        <input type="file" class="form-control" id="event_image" name="event_image">
                        <?php if(isset(session('errors')['event_image'])): ?>
                            <small class="text-danger"><?= session('errors')['event_image'] ?></small>
                        <?php endif; ?>
                    </div>
                    <div class="form-group">
                        <label for="name">Nama Event</label>
                        <input type="text" class="form-control" id="name" name="name" value="<?= $event['name'] ?>" required>
                        <?php if(isset(session('errors')['name'])): ?>
                            <small class="text-danger"><?= session('errors')['name'] ?></small>
                        <?php endif; ?>
                    </div>
                    <div class="form-group">
                        <label for="description">Deskripsi Event</label>
                        <textarea class="form-control" id="description" name="description" rows="3" value="<?= $event['description'] ?>" required></textarea>
                        <?php if(isset(session('errors')['description'])): ?>
                            <small class="text-danger"><?= session('errors')['description'] ?></small>
                        <?php endif; ?>
                    </div>
                    <div class="form-group">
                        <label for="start_date">Tanggal Mulai</label>
                        <input type="date" class="form-control" id="start_date" name="start_date" value="<?= $event['start_date'] ?>" required>
                        <?php if(isset(session('errors')['start_date'])): ?>
                            <small class="text-danger"><?= session('errors')['start_date'] ?></small>
                        <?php endif; ?>
                    </div>
                    <div class="form-group">
                        <label for="end_date">Tanggal Selesai</label>
                        <input type="date" class="form-control" id="end_date" name="end_date" value="<?= $event['end_date'] ?>" required>
                        <?php if(isset(session('errors')['end_date'])): ?>
                            <small class="text-danger"><?= session('errors')['end_date'] ?></small>
                        <?php endif; ?>
                    </div>
                    <div class="form-group">
                        <label for="lokasi">Lokasi Event</label>
                        <textarea class="form-control" id="lokasi" name="lokasi" rows="3" value="<?= $event['lokasi'] ?>" required></textarea>
                        <?php if(isset(session('errors')['lokasi'])): ?>
                            <small class="text-danger"><?= session('errors')['lokasi'] ?></small>
                        <?php endif; ?>
                    </div>
                    <div class="form-group">
                        <label for="event_proposal">Proposal Event (MAX 2 MB PDF)</label>
                        <input type="file" class="form-control" id="event_proposal" name="event_proposal">
                        <?php if(isset(session('errors')['event_proposal'])): ?>
                            <small class="text-danger"><?= session('errors')['event_proposal'] ?></small>
                        <?php endif; ?>
                    </div>
                    <div class="form-group">
                        <label for="biaya_registrasi">Biaya Registrasi</label>
                        <input type="number" class="form-control" id="biaya_registrasi" name="biaya_registrasi" value="<?= $event['biaya_registrasi'] ?>" required>
                        <?php if(isset(session('errors')['biaya_registrasi'])): ?>
                            <small class="text-danger"><?= session('errors')['biaya_registrasi'] ?></small>
                        <?php endif; ?>
                    </div>
                    <div class="form-group">
                        <label for="metode_pembayaran">Metode Pembayaran</label>
                        <input type="text" class="form-control" id="metode_pembayaran" name="metode_pembayaran" value="<?= $event['metode_pembayaran'] ?>" required>
                        <?php if(isset(session('errors')['metode_pembayaran'])): ?>
                            <small class="text-danger"><?= session('errors')['metode_pembayaran'] ?></small>
                        <?php endif; ?>
                    </div>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                    <a href="<?= base_url('event') ?>" class="btn btn-secondary">Batal</a>
                </form>
            </div>
        </div>

    </div>
    <!-- /.container-fluid -->

</div>
<!-- End of Main Content -->

<?= $this->endSection(); ?>
