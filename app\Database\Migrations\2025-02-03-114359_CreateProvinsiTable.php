<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateProvinsiTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true
            ],
            'provinsi' => [
                'type' => 'VARCHAR',
                'constraint' => 255
            ],
            'id_negara' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
            ]
            ]);

            $this->forge->addKey('id', true);
            $this->forge->addForeignKey('id_negara', 'negara', 'id', 'CASCADE', 'CASCADE');
            $this->forge->createTable('provinsi');
    }

    public function down()
    {
        $this->forge->dropTable('provinsi');
    }
}
