<?= $this->extend('layouts/backend/template') ?>
<?= $this->section('content') ?>

<div id="content">
    <div class="container-fluid">

        <!-- Page Heading -->
        <h1 class="h3 mb-2 text-gray-800">Pendaftaran Atlet</h1>

        <!-- Form Pendaftaran Atlet -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Form Pendaftaran Atlet</h6>
            </div>
            <div class="card-body">
                <form action="<?= base_url('pendaftaran-atlet/store') ?>" method="post" enctype="multipart/form-data">
                    <!-- Hidden Input untuk id_pendaftaran_event -->
                    <input type="hidden" name="id_pendaftaran_event" value="<?= isset($id_pendaftaran_event) ? $id_pendaftaran_event : '' ?>">

                    <!-- Field Jenis <PERSON> -->
                    <div class="form-group">
                        <label for="jenis_tanding">Jen<PERSON></label>
                        <select class="form-control" id="jenis_tanding" name="jenis_tanding" required>
                            <option value="" selected disabled>-- Pilih Jenis Tanding --</option>
                            <option value="TANDING">Tanding</option>
                            <option value="TUNGGAL">Tunggal</option>
                            <option value="TANGAN KOSONG">Tunggal Tangan Kosong</option>
                            <option value="SENJATA">Tunggal Senjata</option>
                            <option value="GANDA TANGAN KOSONG">Ganda Tangan Kosong</option>
                            <option value="GANDA SENJATA">Ganda Senjata</option>
                            <option value="REGU A 1-6">Regu A 1-6</option>
                            <option value="REGU B 7-12">Regu B 7-12</option>
                            <option value="SOLO">Solo Kreatif</option>
                        </select>
                        <?php if(isset(session('errors')['jenis_tanding'])): ?>
                            <small class="text-danger"><?= session('errors')['jenis_tanding'] ?></small>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Field Kategori Umur -->
                    <div class="form-group">
                        <label for="kategori_umur">Kategori Umur</label>
                        <select class="form-control" id="kategori_umur" name="kategori_umur" required>
                            <option value="" selected disabled>-- Pilih Kategori Umur --</option>
                            <option value="MACAN">Usia Dini 1</option>
                            <option value="PRE TEEN">Usia Dini 2</option>
                            <option value="PRE JUNIOR">Pra Remaja</option>
                            <option value="JUNIOR">Remaja</option>
                            <option value="SENIOR">Dewasa</option>
                            <option value="SINGA">TNI/POLRI</option>
                        </select>
                        <?php if(isset(session('errors')['kategori_umur'])): ?>
                            <small class="text-danger"><?= session('errors')['kategori_umur'] ?></small>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Field Jenis Kelas Tanding -->
                    <div class="form-group">
                        <label for="kelas_tanding">Jenis Kelas Tanding</label>
                        <select class="form-control" id="kelas_tanding" name="kelas_tanding" required>
                            <option value="" selected disabled>-- Pilih Kelas Tanding --</option>
                            <!-- Opsi-opsi kelas tanding -->
                            <option value="<A">Laki-laki Under A</option>
                            <option value="A">Laki-laki A</option>
                            <option value="B">Laki-laki B</option>
                            <option value="C">Laki-laki C</option>
                            <option value="D">Laki-laki D</option>
                            <option value="E">Laki-laki E</option>
                            <option value="F">Laki-laki F</option>
                            <option value="G">Laki-laki G</option>
                            <option value="H">Laki-laki H</option>
                            <option value="I">Laki-laki I</option>
                            <option value="J">Laki-laki J</option>
                            <option value="K">Laki-laki K</option>
                            <option value="L">Laki-laki L</option>
                            <option value="M">Laki-laki M</option>
                            <option value="N">Laki-laki N</option>
                            <option value="O">Laki-laki O</option>
                            <option value="P">Laki-laki P</option>
                            <option value="Q">Laki-laki Q</option>
                            <option value="R">Laki-laki R</option>
                            <option value="S">Laki-laki S</option>
                            <option value="OPEN">Laki-laki OPEN</option>
                            <option value="OPEN 1">Laki-laki OPEN 1</option>
                            <option value="OPEN 2">Laki-laki OPEN 2</option>
                            <option value="<A">Perempuan Under A</option>
                            <option value="A">Perempuan A</option>
                            <option value="B">Perempuan B</option>
                            <option value="C">Perempuan C</option>
                            <option value="D">Perempuan D</option>
                            <option value="E">Perempuan E</option>
                            <option value="F">Perempuan F</option>
                            <option value="G">Perempuan G</option>
                            <option value="H">Perempuan H</option>
                            <option value="I">Perempuan I</option>
                            <option value="J">Perempuan J</option>
                            <option value="K">Perempuan K</option>
                            <option value="L">Perempuan L</option>
                            <option value="M">Perempuan M</option>
                            <option value="N">Perempuan N</option>
                            <option value="O">Perempuan O</option>
                            <option value="P">Perempuan P</option>
                            <option value="Q">Perempuan Q</option>
                            <option value="R">Perempuan R</option>
                            <option value="S">Perempuan S</option>
                            <option value="OPEN">Perempuan OPEN</option>
                            <option value="OPEN 1">Perempuan OPEN 1</option>
                            <option value="OPEN 2">Perempuan OPEN 2</option>
                        </select>
                        <?php if(isset(session('errors')['kelas_tanding'])): ?>
                            <small class="text-danger"><?= session('errors')['kelas_tanding'] ?></small>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Field Pilih Atlet -->
                    <div class="form-group">
                        <label for="id_atlet_select">Pilih Atlet</label>
                        <select class="form-control" id="id_atlet_select">
                            <option value="" selected disabled>-- Pilih Atlet --</option>
                            <?php if (!empty($atlet)): ?>
                                <?php foreach ($atlet as $row): ?>
                                    <option value="<?= $row['id'] ?>" data-name="<?= $row['name'] ?>">
                                        <?= $row['name'] ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <option value="" disabled>Tidak ada atlet tersedia.</option>
                            <?php endif; ?>
                        </select>
                        <?php if(isset(session('errors')['name'])): ?>
                            <small class="text-danger"><?= session('errors')['name'] ?></small>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Tempat menampilkan atlet yang dipilih -->
                    <div class="form-group">
                        <label>Atlet yang Dipilih:</label>
                        <div id="selected-atlet" class="selected-tags"></div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">Simpan</button>
                    <a href="<?= base_url('pendaftaran-atlet') ?>" class="btn btn-secondary">Batal</a>
                </form>
            </div>
        </div>

    </div>
    <!-- /.container-fluid -->
</div>
<!-- End of Main Content -->

<!-- CSS untuk tampilan _tag_ atlet yang dipilih -->
<style>
.selected-tags {
    margin-top: 10px;
}
.tag {
    display: inline-block;
    background-color: #f0f0f0;
    padding: 5px 10px;
    margin-right: 5px;
    margin-bottom: 5px;
    border-radius: 3px;
    border: 1px solid #ccc;
}
.remove-tag {
    cursor: pointer;
    margin-left: 5px;
    color: red;
    font-weight: bold;
}
</style>

<!-- JavaScript untuk menambahkan dan menghapus _tag_ secara interaktif -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectedAtletContainer = document.getElementById('selected-atlet');
    const selectElement = document.getElementById('id_atlet_select');
    
    // Array untuk menyimpan data atlet yang dipilih
    let selectedAtlet = [];
    
    // Saat terjadi perubahan pada dropdown
    selectElement.addEventListener('change', function() {
        const selectedOption = selectElement.options[selectElement.selectedIndex];
        const id = selectedOption.value;
        const name = selectedOption.getAttribute('data-name');
        
        // Cek apakah atlet sudah dipilih
        if (selectedAtlet.some(item => item.id === id)) {
            alert('Atlet ini sudah dipilih.');
            selectElement.value = "";
            return;
        }
        
        // Tambahkan atlet ke array pilihan
        selectedAtlet.push({id: id, name: name});
        updateSelectedTags();
        
        // Reset dropdown ke nilai default
        selectElement.value = "";
    });
    
    // Fungsi untuk memperbarui tampilan _tag_ atlet yang dipilih
    function updateSelectedTags() {
        // Bersihkan kontainer tampilan
        selectedAtletContainer.innerHTML = "";
        
        // Hapus semua _hidden input_ yang sebelumnya sudah dibuat
        document.querySelectorAll('input[name="id_atlet[]"]').forEach(el => el.remove());
        
        // Untuk setiap atlet yang dipilih, tampilkan _tag_ dan buat _hidden input_ untuk submit
        selectedAtlet.forEach(function(atlet) {
            // Buat elemen _tag_
            const tag = document.createElement('div');
            tag.className = "tag";
            tag.innerHTML = atlet.name + ' <span class="remove-tag" data-id="'+ atlet.id +'">&times;</span>';
            selectedAtletContainer.appendChild(tag);
            
            // Buat hidden input agar data tersubmit sebagai array
            const hiddenInput = document.createElement('input');
            hiddenInput.type = "hidden";
            hiddenInput.name = "id_atlet[]";
            hiddenInput.value = atlet.id;
            // Tempelkan hidden input ke dalam form
            selectElement.closest('form').appendChild(hiddenInput);
        });
    }
    
    // Menggunakan _event delegation_ untuk meng-handle klik tombol hapus pada _tag_
    selectedAtletContainer.addEventListener('click', function(e) {
        if(e.target && e.target.matches('.remove-tag')) {
            const idToRemove = e.target.getAttribute('data-id');
            selectedAtlet = selectedAtlet.filter(item => item.id !== idToRemove);
            updateSelectedTags();
        }
    });

    // Fungsi untuk cek dan update status disable/enable
    function updateFields() {
        var selectedVal = $('#jenis_tanding').val();
        if (selectedVal !== 'TANDING') {
            $('#kelas_tanding').prop('disabled', true);
        } else {
            $('#kelas_tanding').prop('disabled', false);
        }
    }
    
    // Panggil saat halaman pertama kali dimuat (jika sudah ada nilai default)
    updateFields();
    
    // Setiap kali dropdown jenis tanding berubah, perbarui status field terkait
    $('#jenis_tanding').change(function(){
        updateFields();
    });

});
</script>

<?= $this->endSection(); ?>
