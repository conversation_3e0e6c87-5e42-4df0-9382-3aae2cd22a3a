'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import Badge from '@/components/ui/Badge';
import EventDetailModal from '@/components/modals/EventDetailModal';
import { api } from '@/lib/api';
import Link from 'next/link';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  CalendarDaysIcon,
  MapPinIcon,
  CurrencyDollarIcon,
  UsersIcon,
} from '@heroicons/react/24/outline';

interface Event {
  id: number;
  name: string;
  description?: string;
  start_date: string;
  end_date: string;
  lokasi: string;
  biaya_registrasi: number;
  metode_pembayaran: string;
  status: 'active' | 'completed';
  event_image?: string;
  event_proposal?: string;
  event_pemenang?: string;
  created_at: string;
  updated_at: string;
  registrationCount?: number;
  eventUser?: {
    id: number;
    name: string;
    email: string;
  };
}

const MyEventsPage = () => {
  const { user } = useAuth();
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedStatus, setSelectedStatus] = useState('');
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);

  const fetchEvents = async () => {
    try {
      setLoading(true);
      const response = await api.get('/admin/dashboard-events', {
        params: {
          page: currentPage,
          limit: 10,
          search: searchTerm,
          status: selectedStatus
        }
      });

      if (response.data.success) {
        setEvents(response.data.data.events || []);
        setTotalPages(response.data.data.pagination?.totalPages || 1);
      }
    } catch (error: any) {
      console.error('Error fetching events:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchEvents();
  }, [currentPage, searchTerm, selectedStatus]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchEvents();
  };

  const handleViewEvent = (event: Event) => {
    setSelectedEvent(event);
    setShowDetailModal(true);
  };

  const handleDeleteEvent = async (eventId: number) => {
    if (!confirm('Are you sure you want to delete this event?')) return;

    try {
      await api.delete(`/events/${eventId}`);
      fetchEvents(); // Refresh the list
      alert('Event deleted successfully!');
    } catch (error: any) {
      console.error('Error deleting event:', error);
      alert('Failed to delete event');
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="success">Active</Badge>;
      case 'completed':
        return <Badge variant="secondary">Completed</Badge>;
      default:
        return <Badge variant="warning">Unknown</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID');
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR'
    }).format(amount);
  };

  if (user?.role !== 'admin-event') {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold text-white">Access Denied</h1>
          <p className="text-gray-300 mt-2">You don't have permission to access this page.</p>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-white">
              My <span className="text-gold-400">Events</span>
            </h1>
            <p className="text-gray-400 mt-2">
              Manage events that you have organized
            </p>
          </div>
          <Link href="/dashboard/my-events/create">
            <Button className="bg-gold-500 hover:bg-gold-600 text-black font-semibold px-6 py-3 rounded-lg transition-all duration-200 shadow-lg hover:shadow-gold-500/25">
              <PlusIcon className="h-5 w-5 mr-2" />
              Create New Event
            </Button>
          </Link>
        </div>

        {/* Filters */}
        <Card className="bg-gray-900/50 border-gold-500/20 backdrop-blur-sm">
          <div className="p-6">
            <div className="flex items-center mb-4">
              <MagnifyingGlassIcon className="h-5 w-5 text-gold-400 mr-2" />
              <h2 className="text-lg font-semibold text-white">Search & Filter My Events</h2>
            </div>
            <form onSubmit={handleSearch} className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <Input
                  type="text"
                  placeholder="Search your events by name, description, or location..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full bg-gray-800/50 border-gold-500/30 text-white placeholder-gray-400 focus:border-gold-500 focus:ring-gold-500/20"
                />
              </div>
              <div className="w-full md:w-48">
                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="w-full px-4 py-2 border border-gold-500/30 bg-gray-800/50 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-gold-500/50 focus:border-gold-500 transition-all duration-200"
                >
                  <option value="">All Status</option>
                  <option value="active">Active</option>
                  <option value="completed">Completed</option>
                </select>
              </div>
              <Button
                type="submit"
                className="bg-gold-500 hover:bg-gold-600 text-black font-medium px-6 py-2 transition-all duration-200"
              >
                <MagnifyingGlassIcon className="h-4 w-4 mr-2" />
                Search
              </Button>
            </form>
          </div>
        </Card>

        {/* Events List */}
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <LoadingSpinner size="lg" />
          </div>
        ) : events.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {events.map((event) => (
              <Card key={event.id} className="bg-gray-900/50 border-gold-500/20 hover:border-gold-500/40 hover:shadow-xl hover:shadow-gold-500/10 transition-all duration-300 group backdrop-blur-sm overflow-hidden">
                {event.event_image ? (
                  <div className="aspect-[3/4] bg-gradient-to-br from-gray-800 to-gray-900 relative overflow-hidden">
                    <img
                      src={event.event_image}
                      alt={event.name}
                      className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                    />
                    {/* Status Badge Overlay */}
                    <div className="absolute top-3 right-3">
                      {getStatusBadge(event.status)}
                    </div>
                  </div>
                ) : (
                  <div className="aspect-[3/4] bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center relative">
                    <CalendarDaysIcon className="h-16 w-16 text-gold-400/50" />
                    <div className="absolute top-3 right-3">
                      {getStatusBadge(event.status)}
                    </div>
                  </div>
                )}
                <div className="p-6">
                  <div className="mb-4">
                    <h3 className="text-xl font-bold text-white mb-2 line-clamp-1 group-hover:text-gold-400 transition-colors duration-200">
                      {event.name}
                    </h3>
                    {event.description && (
                      <p className="text-gray-300 text-sm leading-relaxed line-clamp-2">
                        {event.description}
                      </p>
                    )}
                  </div>

                  <div className="space-y-3 mb-6">
                    <div className="flex items-center text-sm text-gray-300 bg-gray-800/30 rounded-lg p-3">
                      <CalendarDaysIcon className="h-5 w-5 mr-3 text-gold-400 flex-shrink-0" />
                      <div>
                        <div className="font-medium text-white">Event Period</div>
                        <div className="text-xs">{formatDate(event.start_date)} - {formatDate(event.end_date)}</div>
                      </div>
                    </div>
                    <div className="flex items-center text-sm text-gray-300 bg-gray-800/30 rounded-lg p-3">
                      <MapPinIcon className="h-5 w-5 mr-3 text-gold-400 flex-shrink-0" />
                      <div>
                        <div className="font-medium text-white">Location</div>
                        <div className="text-xs line-clamp-1">{event.lokasi}</div>
                      </div>
                    </div>
                    <div className="flex items-center text-sm text-gray-300 bg-gray-800/30 rounded-lg p-3">
                      <CurrencyDollarIcon className="h-5 w-5 mr-3 text-gold-400 flex-shrink-0" />
                      <div>
                        <div className="font-medium text-white">Registration Fee</div>
                        <div className="text-xs font-semibold text-gold-400">{formatCurrency(event.biaya_registrasi)}</div>
                      </div>
                    </div>
                    {event.registrationCount !== undefined && (
                      <div className="flex items-center text-sm text-gray-300 bg-gray-800/30 rounded-lg p-3">
                        <UsersIcon className="h-5 w-5 mr-3 text-gold-400 flex-shrink-0" />
                        <div>
                          <div className="font-medium text-white">Registrations</div>
                          <div className="text-xs font-semibold text-green-400">{event.registrationCount} teams</div>
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="border-t border-gold-500/20 pt-4">
                    <div className="flex justify-between space-x-2">
                      <Link href={`/dashboard/my-events/${event.id}`} className="flex-1">
                        <Button
                          variant="outline"
                          size="sm"
                          className="w-full border-gold-500/30 text-gold-400 hover:bg-gold-500/10 hover:border-gold-500/50"
                        >
                          <EyeIcon className="h-4 w-4 mr-1" />
                          <span className="hidden sm:inline">View Details</span>
                          <span className="sm:hidden">View</span>
                        </Button>
                      </Link>
                      <Link href={`/dashboard/my-events/${event.id}/edit`} className="flex-1">
                        <Button
                          variant="outline"
                          size="sm"
                          className="w-full border-blue-500/30 text-blue-400 hover:bg-blue-500/10 hover:border-blue-500/50"
                        >
                          <PencilIcon className="h-4 w-4 mr-1" />
                          <span className="hidden sm:inline">Edit</span>
                        </Button>
                      </Link>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteEvent(event.id)}
                        className="flex-1 border-red-500/30 text-red-400 hover:bg-red-500/10 hover:border-red-500/50"
                      >
                        <TrashIcon className="h-4 w-4 mr-1" />
                        <span className="hidden sm:inline">Delete</span>
                      </Button>
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        ) : (
          <Card className="bg-gray-900/30 border-gold-500/20">
            <div className="p-16 text-center">
              <div className="bg-gray-800/50 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6">
                <CalendarDaysIcon className="h-12 w-12 text-gold-400" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">No Events Found</h3>
              <p className="text-gray-400 mb-6 max-w-md mx-auto">
                {searchTerm || selectedStatus
                  ? 'No events match your search criteria. Try adjusting your filters or search terms.'
                  : 'You haven\'t created any events yet. Start by creating your first event to manage competitions and tournaments.'
                }
              </p>
              {!searchTerm && !selectedStatus && (
                <Link href="/dashboard/my-events/create">
                  <Button className="bg-gold-500 hover:bg-gold-600 text-black font-semibold px-6 py-3 rounded-lg transition-all duration-200">
                    <PlusIcon className="h-5 w-5 mr-2" />
                    Create Your First Event
                  </Button>
                </Link>
              )}
            </div>
          </Card>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <Card className="bg-gray-900/30 border-gold-500/20">
            <div className="p-4">
              <div className="flex justify-between items-center">
                <div className="text-sm text-gray-300">
                  Showing page <span className="font-semibold text-gold-400">{currentPage}</span> of{' '}
                  <span className="font-semibold text-gold-400">{totalPages}</span>
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                    className="border-gold-500/30 text-gold-400 hover:bg-gold-500/10 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Previous
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages}
                    className="border-gold-500/30 text-gold-400 hover:bg-gold-500/10 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Next
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        )}

        {/* Event Detail Modal */}
        <EventDetailModal
          isOpen={showDetailModal}
          onClose={() => setShowDetailModal(false)}
          event={selectedEvent}
        />
      </div>
    </DashboardLayout>
  );
};

export default MyEventsPage;
