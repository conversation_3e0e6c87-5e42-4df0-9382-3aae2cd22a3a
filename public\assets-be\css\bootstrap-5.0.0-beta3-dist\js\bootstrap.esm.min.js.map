{"version": 3, "sources": ["../../js/src/util/index.js", "../../js/src/dom/data.js", "../../js/src/dom/event-handler.js", "../../js/src/base-component.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/dom/manipulator.js", "../../js/src/dom/selector-engine.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../js/src/dropdown.js", "../../js/src/modal.js", "../../js/src/util/scrollbar.js", "../../js/src/offcanvas.js", "../../js/src/util/sanitizer.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/toast.js"], "names": ["MAX_UID", "MILLISECONDS_MULTIPLIER", "TRANSITION_END", "toType", "obj", "toString", "call", "match", "toLowerCase", "getUID", "prefix", "Math", "floor", "random", "document", "getElementById", "getSelector", "element", "selector", "getAttribute", "hrefAttr", "includes", "startsWith", "split", "trim", "getSelectorFromElement", "querySelector", "getElementFromSelector", "getTransitionDurationFromElement", "transitionDuration", "transitionDelay", "window", "getComputedStyle", "floatTransitionDuration", "Number", "parseFloat", "floatTransitionDelay", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "nodeType", "emulateTransitionEnd", "duration", "called", "emulatedDuration", "addEventListener", "listener", "removeEventListener", "setTimeout", "typeCheckConfig", "componentName", "config", "configTypes", "Object", "keys", "for<PERSON>ach", "property", "expectedTypes", "value", "valueType", "RegExp", "test", "TypeError", "toUpperCase", "isVisible", "style", "parentNode", "elementStyle", "parentNodeStyle", "display", "visibility", "isDisabled", "Node", "ELEMENT_NODE", "classList", "contains", "disabled", "hasAttribute", "findShadowRoot", "documentElement", "attachShadow", "getRootNode", "root", "ShadowRoot", "noop", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "body", "onDOMContentLoaded", "callback", "readyState", "isRTL", "dir", "defineJQueryPlugin", "name", "plugin", "$", "JQUERY_NO_CONFLICT", "fn", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "elementMap", "Map", "Data", "set", "key", "instance", "has", "instanceMap", "get", "size", "console", "error", "Array", "from", "remove", "delete", "namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "nativeEvents", "Set", "getUidEvent", "uid", "getEvent", "bootstrapHandler", "handler", "event", "<PERSON><PERSON><PERSON><PERSON>", "oneOff", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "dom<PERSON><PERSON>s", "querySelectorAll", "target", "this", "i", "length", "<PERSON><PERSON><PERSON><PERSON>", "events", "delegationSelector", "uidEventList", "len", "<PERSON><PERSON><PERSON><PERSON>", "normalizeParams", "originalTypeEvent", "delegationFn", "delegation", "typeEvent", "replace", "custom", "add<PERSON><PERSON><PERSON>", "handlers", "previousFn", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "removeNamespacedHandlers", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "on", "one", "inNamespace", "isNamespace", "elementEvent", "slice", "keyHandlers", "trigger", "args", "isNative", "jQueryEvent", "bubbles", "nativeDispatch", "defaultPrevented", "evt", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "createEvent", "initEvent", "CustomEvent", "cancelable", "defineProperty", "preventDefault", "VERSION", "BaseComponent", "constructor", "_element", "DATA_KEY", "dispose", "[object Object]", "NAME", "EVENT_KEY", "DATA_API_KEY", "SELECTOR_DISMISS", "EVENT_CLOSE", "EVENT_CLOSED", "EVENT_CLICK_DATA_API", "CLASS_NAME_ALERT", "CLASS_NAME_FADE", "CLASS_NAME_SHOW", "<PERSON><PERSON>", "close", "rootElement", "_getRootElement", "customEvent", "_triggerCloseEvent", "_removeElement", "closest", "_destroyElement", "<PERSON><PERSON><PERSON><PERSON>", "each", "data", "alertInstance", "handle<PERSON><PERSON><PERSON>", "CLASS_NAME_ACTIVE", "SELECTOR_DATA_TOGGLE", "<PERSON><PERSON>", "toggle", "setAttribute", "normalizeData", "val", "normalizeDataKey", "chr", "button", "Manipulator", "setDataAttribute", "removeDataAttribute", "removeAttribute", "getDataAttributes", "attributes", "dataset", "filter", "pureKey", "char<PERSON>t", "getDataAttribute", "offset", "rect", "getBoundingClientRect", "top", "scrollTop", "left", "scrollLeft", "position", "offsetTop", "offsetLeft", "NODE_TEXT", "SelectorEngine", "find", "concat", "Element", "prototype", "findOne", "children", "child", "matches", "parents", "ancestor", "push", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "ARROW_LEFT_KEY", "ARROW_RIGHT_KEY", "TOUCHEVENT_COMPAT_WAIT", "SWIPE_THRESHOLD", "<PERSON><PERSON><PERSON>", "interval", "keyboard", "slide", "pause", "wrap", "touch", "DefaultType", "ORDER_NEXT", "ORDER_PREV", "DIRECTION_LEFT", "DIRECTION_RIGHT", "EVENT_SLIDE", "EVENT_SLID", "EVENT_KEYDOWN", "EVENT_MOUSEENTER", "EVENT_MOUSELEAVE", "EVENT_TOUCHSTART", "EVENT_TOUCHMOVE", "EVENT_TOUCHEND", "EVENT_POINTERDOWN", "EVENT_POINTERUP", "EVENT_DRAG_START", "EVENT_LOAD_DATA_API", "CLASS_NAME_CAROUSEL", "CLASS_NAME_SLIDE", "CLASS_NAME_END", "CLASS_NAME_START", "CLASS_NAME_NEXT", "CLASS_NAME_PREV", "CLASS_NAME_POINTER_EVENT", "SELECTOR_ACTIVE", "SELECTOR_ACTIVE_ITEM", "SELECTOR_ITEM", "SELECTOR_ITEM_IMG", "SELECTOR_NEXT_PREV", "SELECTOR_INDICATORS", "SELECTOR_INDICATOR", "SELECTOR_DATA_SLIDE", "SELECTOR_DATA_RIDE", "POINTER_TYPE_TOUCH", "POINTER_TYPE_PEN", "Carousel", "super", "_items", "_interval", "_activeElement", "_isPaused", "_isSliding", "touchTimeout", "touchStartX", "touchDeltaX", "_config", "_getConfig", "_indicatorsElement", "_touchSupported", "navigator", "maxTouchPoints", "_pointerEvent", "PointerEvent", "_addEventListeners", "_slide", "nextWhenVisible", "hidden", "cycle", "clearInterval", "_updateInterval", "setInterval", "visibilityState", "bind", "to", "index", "activeIndex", "_getItemIndex", "order", "_handleSwipe", "absDeltax", "abs", "direction", "_keydown", "_addTouchEventListeners", "start", "pointerType", "touches", "clientX", "move", "end", "clearTimeout", "itemImg", "e", "add", "tagName", "indexOf", "_getItemByOrder", "activeElement", "isNext", "isPrev", "lastItemIndex", "itemIndex", "_triggerSlideEvent", "relatedTarget", "eventDirectionName", "targetIndex", "fromIndex", "_setActiveIndicatorElement", "activeIndicator", "indicators", "parseInt", "elementInterval", "defaultInterval", "directionOrOrder", "_directionToOrder", "activeElementIndex", "nextElement", "nextElementIndex", "isCycling", "directionalClassName", "orderClassName", "_orderToDirection", "action", "ride", "carouselInterface", "slideIndex", "dataApiClickHandler", "carousels", "parent", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_HIDE", "EVENT_HIDDEN", "CLASS_NAME_COLLAPSE", "CLASS_NAME_COLLAPSING", "CLASS_NAME_COLLAPSED", "WIDTH", "HEIGHT", "SELECTOR_ACTIVES", "Collapse", "_isTransitioning", "_triggerArray", "id", "toggleList", "elem", "filterElement", "foundElem", "_selector", "_parent", "_getParent", "_addAriaAndCollapsedClass", "hide", "show", "actives", "activesData", "container", "tempActiveData", "elemActive", "collapseInterface", "dimension", "_getDimension", "setTransitioning", "scrollSize", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isTransitioning", "j<PERSON>y", "selected", "trigger<PERSON><PERSON>y", "isOpen", "triggerData", "ESCAPE_KEY", "SPACE_KEY", "TAB_KEY", "ARROW_UP_KEY", "ARROW_DOWN_KEY", "RIGHT_MOUSE_BUTTON", "REGEXP_KEYDOWN", "EVENT_CLICK", "EVENT_KEYDOWN_DATA_API", "EVENT_KEYUP_DATA_API", "CLASS_NAME_DISABLED", "CLASS_NAME_DROPUP", "CLASS_NAME_DROPEND", "CLASS_NAME_DROPSTART", "CLASS_NAME_NAVBAR", "SELECTOR_MENU", "SELECTOR_NAVBAR_NAV", "SELECTOR_VISIBLE_ITEMS", "PLACEMENT_TOP", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "boundary", "reference", "popperConfig", "Dropdown", "_popper", "_menu", "_getMenuElement", "_inNavbar", "_detectNavbar", "isActive", "clearMenus", "getParentFromElement", "<PERSON><PERSON>", "referenceElement", "_getPopperConfig", "isDisplayStatic", "modifiers", "modifier", "enabled", "createPopper", "focus", "destroy", "update", "_getPlacement", "parentDropdown", "isEnd", "getPropertyValue", "_getOffset", "map", "popperData", "defaultBsPopperConfig", "placement", "options", "dropdownInterface", "toggles", "context", "clickEvent", "dropdownMenu", "some", "<PERSON><PERSON><PERSON>", "stopPropagation", "click", "items", "dataApiKeydownHandler", "backdrop", "EVENT_HIDE_PREVENTED", "EVENT_FOCUSIN", "EVENT_RESIZE", "EVENT_CLICK_DISMISS", "EVENT_KEYDOWN_DISMISS", "EVENT_MOUSEUP_DISMISS", "EVENT_MOUSEDOWN_DISMISS", "CLASS_NAME_SCROLLBAR_MEASURER", "CLASS_NAME_BACKDROP", "CLASS_NAME_OPEN", "CLASS_NAME_STATIC", "SELECTOR_DIALOG", "SELECTOR_MODAL_BODY", "SELECTOR_DATA_DISMISS", "SELECTOR_FIXED_CONTENT", "SELECTOR_STICKY_CONTENT", "Modal", "_dialog", "_backdrop", "_isShown", "_isBodyOverflowing", "_ignoreBackdropClick", "_scrollbarWidth", "_isAnimated", "showEvent", "_checkScrollbar", "_setScrollbar", "_adjustDialog", "_setEscapeEvent", "_setResizeEvent", "_showBackdrop", "_showElement", "isAnimated", "_hideModal", "htmlElement", "handleUpdate", "modalBody", "append<PERSON><PERSON><PERSON>", "_enforceFocus", "transitionComplete", "_triggerBackdropTransition", "_resetAdjustments", "_resetScrollbar", "_removeBackdrop", "createElement", "className", "currentTarget", "backdropTransitionDuration", "callback<PERSON><PERSON><PERSON>", "isModalOverflowing", "scrollHeight", "clientHeight", "overflowY", "modalTransitionDuration", "paddingLeft", "paddingRight", "round", "right", "innerWidth", "_getScrollbarWidth", "_setElementAttributes", "calculatedValue", "styleProp", "clientWidth", "actualValue", "_resetElementAttributes", "scrollDiv", "scrollbarWidth", "width", "getWidth", "documentWidth", "overflow", "reset", "removeProperty", "scroll", "CLASS_NAME_BACKDROP_BODY", "CLASS_NAME_TOGGLING", "OPEN_SELECTOR", "ACTIVE_SELECTOR", "<PERSON><PERSON><PERSON>", "scrollBarHide", "_enforceFocusOnElement", "blur", "undefined", "allReadyOpen", "el", "uriAttrs", "ARIA_ATTRIBUTE_PATTERN", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "allowedAttribute", "attr", "allowedAttributeList", "attrName", "nodeName", "nodeValue", "regExp", "attrRegex", "DefaultAllowlist", "*", "a", "area", "b", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "img", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "sanitizeHtml", "unsafeHtml", "allowList", "sanitizeFn", "createdDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "allow<PERSON><PERSON><PERSON><PERSON>", "elements", "el<PERSON>ame", "attributeList", "allowedAttributes", "innerHTML", "CLASS_PREFIX", "BSCLS_PREFIX_REGEX", "DISALLOWED_ATTRIBUTES", "animation", "template", "title", "delay", "html", "fallbackPlacements", "customClass", "sanitize", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "HIDE", "HIDDEN", "SHOW", "SHOWN", "INSERTED", "CLICK", "FOCUSIN", "FOCUSOUT", "MOUSEENTER", "MOUSELEAVE", "CLASS_NAME_MODAL", "HOVER_STATE_SHOW", "HOVER_STATE_OUT", "SELECTOR_TOOLTIP_INNER", "TRIGGER_HOVER", "TRIGGER_FOCUS", "TRIGGER_CLICK", "TRIGGER_MANUAL", "<PERSON><PERSON><PERSON>", "_isEnabled", "_timeout", "_hoverState", "_activeTrigger", "tip", "_setListeners", "enable", "disable", "toggle<PERSON>nabled", "_initializeOnDelegatedTarget", "_isWithActiveTrigger", "_enter", "_leave", "getTipElement", "_hideModalHandler", "Error", "isWithContent", "shadowRoot", "isInTheDom", "ownerDocument", "tipId", "<PERSON><PERSON><PERSON><PERSON>", "attachment", "_getAttachment", "_addAttachmentClass", "_get<PERSON><PERSON><PERSON>", "complete", "prevHoverState", "_cleanTipClass", "getTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "textContent", "updateAttachment", "dataKey", "_getDelegateConfig", "altBoundary", "phase", "_handlePopperPlacementChange", "onFirstUpdate", "eventIn", "eventOut", "_fixTitle", "originalTitleType", "dataAttributes", "dataAttr", "tabClass", "token", "tClass", "state", "popper", "SELECTOR_TITLE", "SELECTOR_CONTENT", "Popover", "_getContent", "method", "EVENT_ACTIVATE", "EVENT_SCROLL", "CLASS_NAME_DROPDOWN_ITEM", "SELECTOR_DATA_SPY", "SELECTOR_NAV_LIST_GROUP", "SELECTOR_NAV_LINKS", "SELECTOR_NAV_ITEMS", "SELECTOR_LIST_ITEMS", "SELECTOR_DROPDOWN", "SELECTOR_DROPDOWN_TOGGLE", "METHOD_OFFSET", "METHOD_POSITION", "ScrollSpy", "_scrollElement", "_offsets", "_targets", "_activeTarget", "_scrollHeight", "_process", "refresh", "autoMethod", "offsetMethod", "offsetBase", "_getScrollTop", "_getScrollHeight", "targetSelector", "targetBCR", "height", "item", "sort", "pageYOffset", "max", "_getOffsetHeight", "innerHeight", "maxScroll", "_activate", "_clear", "queries", "link", "join", "listGroup", "navItem", "node", "spy", "CLASS_NAME_DROPDOWN_MENU", "SELECTOR_ACTIVE_UL", "SELECTOR_DROPDOWN_ACTIVE_CHILD", "Tab", "listElement", "itemSelector", "hideEvent", "active", "_transitionComplete", "dropdown<PERSON><PERSON>d", "dropdown", "CLASS_NAME_HIDE", "CLASS_NAME_SHOWING", "autohide", "Toast", "_clearTimeout"], "mappings": ";;;;;sCAOA,MAAMA,QAAU,IACVC,wBAA0B,IAC1BC,eAAiB,gBAGjBC,OAASC,GACTA,MAAAA,EACM,GAAEA,EAGL,GAAGC,SAASC,KAAKF,GAAKG,MAAM,eAAe,GAAGC,cASjDC,OAASC,IACb,GACEA,GAAUC,KAAKC,MArBH,IAqBSD,KAAKE,gBACnBC,SAASC,eAAeL,IAEjC,OAAOA,GAGHM,YAAcC,IAClB,IAAIC,EAAWD,EAAQE,aAAa,kBAEpC,IAAKD,GAAyB,MAAbA,EAAkB,CACjC,IAAIE,EAAWH,EAAQE,aAAa,QAMpC,IAAKC,IAAcA,EAASC,SAAS,OAASD,EAASE,WAAW,KAChE,OAAO,KAILF,EAASC,SAAS,OAASD,EAASE,WAAW,OACjDF,EAAW,IAAMA,EAASG,MAAM,KAAK,IAGvCL,EAAWE,GAAyB,MAAbA,EAAmBA,EAASI,OAAS,KAG9D,OAAON,GAGHO,uBAAyBR,IAC7B,MAAMC,EAAWF,YAAYC,GAE7B,OAAIC,GACKJ,SAASY,cAAcR,GAAYA,EAGrC,MAGHS,uBAAyBV,IAC7B,MAAMC,EAAWF,YAAYC,GAE7B,OAAOC,EAAWJ,SAASY,cAAcR,GAAY,MAGjDU,iCAAmCX,IACvC,IAAKA,EACH,OAAO,EAIT,IAAIY,mBAAEA,EAAFC,gBAAsBA,GAAoBC,OAAOC,iBAAiBf,GAEtE,MAAMgB,EAA0BC,OAAOC,WAAWN,GAC5CO,EAAuBF,OAAOC,WAAWL,GAG/C,OAAKG,GAA4BG,GAKjCP,EAAqBA,EAAmBN,MAAM,KAAK,GACnDO,EAAkBA,EAAgBP,MAAM,KAAK,GArFf,KAuFtBW,OAAOC,WAAWN,GAAsBK,OAAOC,WAAWL,KAPzD,GAULO,qBAAuBpB,IAC3BA,EAAQqB,cAAc,IAAIC,MAAMrC,kBAG5BsC,UAAYpC,IAAQA,EAAI,IAAMA,GAAKqC,SAEnCC,qBAAuB,CAACzB,EAAS0B,KACrC,IAAIC,GAAS,EACb,MACMC,EAAmBF,EADD,EAQxB1B,EAAQ6B,iBAAiB5C,gBALzB,SAAS6C,IACPH,GAAS,EACT3B,EAAQ+B,oBAAoB9C,eAAgB6C,MAI9CE,WAAW,KACJL,GACHP,qBAAqBpB,IAEtB4B,IAGCK,gBAAkB,CAACC,EAAeC,EAAQC,KAC9CC,OAAOC,KAAKF,GAAaG,QAAQC,IAC/B,MAAMC,EAAgBL,EAAYI,GAC5BE,EAAQP,EAAOK,GACfG,EAAYD,GAASnB,UAAUmB,GAAS,UAjH5CvD,OADSA,EAkHsDuD,GAhHzD,GAAEvD,EAGL,GAAGC,SAASC,KAAKF,GAAKG,MAAM,eAAe,GAAGC,cALxCJ,IAAAA,EAoHX,IAAK,IAAIyD,OAAOH,GAAeI,KAAKF,GAClC,MAAM,IAAIG,UACLZ,EAAca,cAAhB,KACA,WAAUP,qBAA4BG,MACtC,sBAAqBF,UAMxBO,UAAYhD,IAChB,IAAKA,EACH,OAAO,EAGT,GAAIA,EAAQiD,OAASjD,EAAQkD,YAAclD,EAAQkD,WAAWD,MAAO,CACnE,MAAME,EAAepC,iBAAiBf,GAChCoD,EAAkBrC,iBAAiBf,EAAQkD,YAEjD,MAAgC,SAAzBC,EAAaE,SACU,SAA5BD,EAAgBC,SACY,WAA5BF,EAAaG,WAGjB,OAAO,GAGHC,WAAavD,IACZA,GAAWA,EAAQwB,WAAagC,KAAKC,gBAItCzD,EAAQ0D,UAAUC,SAAS,mBAIC,IAArB3D,EAAQ4D,SACV5D,EAAQ4D,SAGV5D,EAAQ6D,aAAa,aAAoD,UAArC7D,EAAQE,aAAa,aAG5D4D,eAAiB9D,IACrB,IAAKH,SAASkE,gBAAgBC,aAC5B,OAAO,KAIT,GAAmC,mBAAxBhE,EAAQiE,YAA4B,CAC7C,MAAMC,EAAOlE,EAAQiE,cACrB,OAAOC,aAAgBC,WAAaD,EAAO,KAG7C,OAAIlE,aAAmBmE,WACdnE,EAIJA,EAAQkD,WAINY,eAAe9D,EAAQkD,YAHrB,MAMLkB,KAAO,IAAM,aAEbC,OAASrE,GAAWA,EAAQsE,aAE5BC,UAAY,KAChB,MAAMC,OAAEA,GAAW1D,OAEnB,OAAI0D,IAAW3E,SAAS4E,KAAKZ,aAAa,qBACjCW,EAGF,MAGHE,mBAAqBC,IACG,YAAxB9E,SAAS+E,WACX/E,SAASgC,iBAAiB,mBAAoB8C,GAE9CA,KAIEE,MAAQ,IAAuC,QAAjChF,SAASkE,gBAAgBe,IAEvCC,mBAAqB,CAACC,EAAMC,KAVPN,IAAAA,EAAAA,EAWN,KACjB,MAAMO,EAAIX,YAEV,GAAIW,EAAG,CACL,MAAMC,EAAqBD,EAAEE,GAAGJ,GAChCE,EAAEE,GAAGJ,GAAQC,EAAOI,gBACpBH,EAAEE,GAAGJ,GAAMM,YAAcL,EACzBC,EAAEE,GAAGJ,GAAMO,WAAa,KACtBL,EAAEE,GAAGJ,GAAQG,EACNF,EAAOI,mBAnBQ,YAAxBxF,SAAS+E,WACX/E,SAASgC,iBAAiB,mBAAoB8C,GAE9CA,KCvMEa,WAAa,IAAIC,IAEvB,IAAAC,KAAe,CACbC,IAAI3F,EAAS4F,EAAKC,GACXL,WAAWM,IAAI9F,IAClBwF,WAAWG,IAAI3F,EAAS,IAAIyF,KAG9B,MAAMM,EAAcP,WAAWQ,IAAIhG,GAI9B+F,EAAYD,IAAIF,IAA6B,IAArBG,EAAYE,KAMzCF,EAAYJ,IAAIC,EAAKC,GAJnBK,QAAQC,MAAO,+EAA8EC,MAAMC,KAAKN,EAAYzD,QAAQ,QAOhI0D,IAAG,CAAChG,EAAS4F,IACPJ,WAAWM,IAAI9F,IACVwF,WAAWQ,IAAIhG,GAASgG,IAAIJ,IAG9B,KAGTU,OAAOtG,EAAS4F,GACd,IAAKJ,WAAWM,IAAI9F,GAClB,OAGF,MAAM+F,EAAcP,WAAWQ,IAAIhG,GAEnC+F,EAAYQ,OAAOX,GAGM,IAArBG,EAAYE,MACdT,WAAWe,OAAOvG,KCtCxB,MAAMwG,eAAiB,qBACjBC,eAAiB,OACjBC,cAAgB,SAChBC,cAAgB,GACtB,IAAIC,SAAW,EACf,MAAMC,aAAe,CACnBC,WAAY,YACZC,WAAY,YAERC,aAAe,IAAIC,IAAI,CAC3B,QACA,WACA,UACA,YACA,cACA,aACA,iBACA,YACA,WACA,YACA,cACA,YACA,UACA,WACA,QACA,oBACA,aACA,YACA,WACA,cACA,cACA,cACA,YACA,eACA,gBACA,eACA,gBACA,aACA,QACA,OACA,SACA,QACA,SACA,SACA,UACA,WACA,OACA,SACA,eACA,SACA,OACA,mBACA,mBACA,QACA,QACA,WASF,SAASC,YAAYlH,EAASmH,GAC5B,OAAQA,GAAQ,GAAEA,MAAQP,cAAiB5G,EAAQ4G,UAAYA,WAGjE,SAASQ,SAASpH,GAChB,MAAMmH,EAAMD,YAAYlH,GAKxB,OAHAA,EAAQ4G,SAAWO,EACnBR,cAAcQ,GAAOR,cAAcQ,IAAQ,GAEpCR,cAAcQ,GAGvB,SAASE,iBAAiBrH,EAASoF,GACjC,OAAO,SAASkC,EAAQC,GAOtB,OANAA,EAAMC,eAAiBxH,EAEnBsH,EAAQG,QACVC,aAAaC,IAAI3H,EAASuH,EAAMK,KAAMxC,GAGjCA,EAAGyC,MAAM7H,EAAS,CAACuH,KAI9B,SAASO,2BAA2B9H,EAASC,EAAUmF,GACrD,OAAO,SAASkC,EAAQC,GACtB,MAAMQ,EAAc/H,EAAQgI,iBAAiB/H,GAE7C,IAAK,IAAIgI,OAAEA,GAAWV,EAAOU,GAAUA,IAAWC,KAAMD,EAASA,EAAO/E,WACtE,IAAK,IAAIiF,EAAIJ,EAAYK,OAAQD,KAC/B,GAAIJ,EAAYI,KAAOF,EAQrB,OAPAV,EAAMC,eAAiBS,EAEnBX,EAAQG,QAEVC,aAAaC,IAAI3H,EAASuH,EAAMK,KAAMxC,GAGjCA,EAAGyC,MAAMI,EAAQ,CAACV,IAM/B,OAAO,MAIX,SAASc,YAAYC,EAAQhB,EAASiB,EAAqB,MACzD,MAAMC,EAAenG,OAAOC,KAAKgG,GAEjC,IAAK,IAAIH,EAAI,EAAGM,EAAMD,EAAaJ,OAAQD,EAAIM,EAAKN,IAAK,CACvD,MAAMZ,EAAQe,EAAOE,EAAaL,IAElC,GAAIZ,EAAMmB,kBAAoBpB,GAAWC,EAAMgB,qBAAuBA,EACpE,OAAOhB,EAIX,OAAO,KAGT,SAASoB,gBAAgBC,EAAmBtB,EAASuB,GACnD,MAAMC,EAAgC,iBAAZxB,EACpBoB,EAAkBI,EAAaD,EAAevB,EAGpD,IAAIyB,EAAYH,EAAkBI,QAAQvC,eAAgB,IAC1D,MAAMwC,EAASpC,aAAakC,GAY5B,OAVIE,IACFF,EAAYE,GAGGjC,aAAalB,IAAIiD,KAGhCA,EAAYH,GAGP,CAACE,EAAYJ,EAAiBK,GAGvC,SAASG,WAAWlJ,EAAS4I,EAAmBtB,EAASuB,EAAcpB,GACrE,GAAiC,iBAAtBmB,IAAmC5I,EAC5C,OAGGsH,IACHA,EAAUuB,EACVA,EAAe,MAGjB,MAAOC,EAAYJ,EAAiBK,GAAaJ,gBAAgBC,EAAmBtB,EAASuB,GACvFP,EAASlB,SAASpH,GAClBmJ,EAAWb,EAAOS,KAAeT,EAAOS,GAAa,IACrDK,EAAaf,YAAYc,EAAUT,EAAiBI,EAAaxB,EAAU,MAEjF,GAAI8B,EAGF,YAFAA,EAAW3B,OAAS2B,EAAW3B,QAAUA,GAK3C,MAAMN,EAAMD,YAAYwB,EAAiBE,EAAkBI,QAAQxC,eAAgB,KAC7EpB,EAAK0D,EACThB,2BAA2B9H,EAASsH,EAASuB,GAC7CxB,iBAAiBrH,EAASsH,GAE5BlC,EAAGmD,mBAAqBO,EAAaxB,EAAU,KAC/ClC,EAAGsD,gBAAkBA,EACrBtD,EAAGqC,OAASA,EACZrC,EAAGwB,SAAWO,EACdgC,EAAShC,GAAO/B,EAEhBpF,EAAQ6B,iBAAiBkH,EAAW3D,EAAI0D,GAG1C,SAASO,cAAcrJ,EAASsI,EAAQS,EAAWzB,EAASiB,GAC1D,MAAMnD,EAAKiD,YAAYC,EAAOS,GAAYzB,EAASiB,GAE9CnD,IAILpF,EAAQ+B,oBAAoBgH,EAAW3D,EAAIkE,QAAQf,WAC5CD,EAAOS,GAAW3D,EAAGwB,WAG9B,SAAS2C,yBAAyBvJ,EAASsI,EAAQS,EAAWS,GAC5D,MAAMC,EAAoBnB,EAAOS,IAAc,GAE/C1G,OAAOC,KAAKmH,GAAmBlH,QAAQmH,IACrC,GAAIA,EAAWtJ,SAASoJ,GAAY,CAClC,MAAMjC,EAAQkC,EAAkBC,GAEhCL,cAAcrJ,EAASsI,EAAQS,EAAWxB,EAAMmB,gBAAiBnB,EAAMgB,uBAK7E,MAAMb,aAAe,CACnBiC,GAAG3J,EAASuH,EAAOD,EAASuB,GAC1BK,WAAWlJ,EAASuH,EAAOD,EAASuB,GAAc,IAGpDe,IAAI5J,EAASuH,EAAOD,EAASuB,GAC3BK,WAAWlJ,EAASuH,EAAOD,EAASuB,GAAc,IAGpDlB,IAAI3H,EAAS4I,EAAmBtB,EAASuB,GACvC,GAAiC,iBAAtBD,IAAmC5I,EAC5C,OAGF,MAAO8I,EAAYJ,EAAiBK,GAAaJ,gBAAgBC,EAAmBtB,EAASuB,GACvFgB,EAAcd,IAAcH,EAC5BN,EAASlB,SAASpH,GAClB8J,EAAclB,EAAkBvI,WAAW,KAEjD,QAA+B,IAApBqI,EAAiC,CAE1C,IAAKJ,IAAWA,EAAOS,GACrB,OAIF,YADAM,cAAcrJ,EAASsI,EAAQS,EAAWL,EAAiBI,EAAaxB,EAAU,MAIhFwC,GACFzH,OAAOC,KAAKgG,GAAQ/F,QAAQwH,IAC1BR,yBAAyBvJ,EAASsI,EAAQyB,EAAcnB,EAAkBoB,MAAM,MAIpF,MAAMP,EAAoBnB,EAAOS,IAAc,GAC/C1G,OAAOC,KAAKmH,GAAmBlH,QAAQ0H,IACrC,MAAMP,EAAaO,EAAYjB,QAAQtC,cAAe,IAEtD,IAAKmD,GAAejB,EAAkBxI,SAASsJ,GAAa,CAC1D,MAAMnC,EAAQkC,EAAkBQ,GAEhCZ,cAAcrJ,EAASsI,EAAQS,EAAWxB,EAAMmB,gBAAiBnB,EAAMgB,wBAK7E2B,QAAQlK,EAASuH,EAAO4C,GACtB,GAAqB,iBAAV5C,IAAuBvH,EAChC,OAAO,KAGT,MAAMkF,EAAIX,YACJwE,EAAYxB,EAAMyB,QAAQvC,eAAgB,IAC1CoD,EAActC,IAAUwB,EACxBqB,EAAWpD,aAAalB,IAAIiD,GAElC,IAAIsB,EACAC,GAAU,EACVC,GAAiB,EACjBC,GAAmB,EACnBC,EAAM,KA4CV,OA1CIZ,GAAe3E,IACjBmF,EAAcnF,EAAE5D,MAAMiG,EAAO4C,GAE7BjF,EAAElF,GAASkK,QAAQG,GACnBC,GAAWD,EAAYK,uBACvBH,GAAkBF,EAAYM,gCAC9BH,EAAmBH,EAAYO,sBAG7BR,GACFK,EAAM5K,SAASgL,YAAY,cAC3BJ,EAAIK,UAAU/B,EAAWuB,GAAS,IAElCG,EAAM,IAAIM,YAAYxD,EAAO,CAC3B+C,QAAAA,EACAU,YAAY,SAKI,IAATb,GACT9H,OAAOC,KAAK6H,GAAM5H,QAAQqD,IACxBvD,OAAO4I,eAAeR,EAAK7E,EAAK,CAC9BI,IAAG,IACMmE,EAAKvE,OAMhB4E,GACFC,EAAIS,iBAGFX,GACFvK,EAAQqB,cAAcoJ,GAGpBA,EAAID,uBAA2C,IAAhBH,GACjCA,EAAYa,iBAGPT,ICvTLU,QAAU,cAEhB,MAAMC,cACJC,YAAYrL,IACVA,EAA6B,iBAAZA,EAAuBH,SAASY,cAAcT,GAAWA,KAM1EkI,KAAKoD,SAAWtL,EAChB0F,KAAKC,IAAIuC,KAAKoD,SAAUpD,KAAKmD,YAAYE,SAAUrD,OAGrDsD,UACE9F,KAAKY,OAAO4B,KAAKoD,SAAUpD,KAAKmD,YAAYE,UAC5CrD,KAAKoD,SAAW,KAKAG,mBAACzL,GACjB,OAAO0F,KAAKM,IAAIhG,EAASkI,KAAKqD,UAGdJ,qBAChB,OAAOA,SClBX,MAAMO,OAAO,QACPH,WAAW,WACXI,YAAa,YACbC,eAAe,YAEfC,iBAAmB,4BAEnBC,YAAe,iBACfC,aAAgB,kBAChBC,uBAAwB,0BAExBC,iBAAmB,QACnBC,kBAAkB,OAClBC,kBAAkB,OAQxB,MAAMC,cAAchB,cAGCG,sBACjB,MAxBa,WA6Bfc,MAAMrM,GACJ,MAAMsM,EAActM,EAAUkI,KAAKqE,gBAAgBvM,GAAWkI,KAAKoD,SAC7DkB,EAActE,KAAKuE,mBAAmBH,GAExB,OAAhBE,GAAwBA,EAAYhC,kBAIxCtC,KAAKwE,eAAeJ,GAKtBC,gBAAgBvM,GACd,OAAOU,uBAAuBV,IAAYA,EAAQ2M,QAAS,UAG7DF,mBAAmBzM,GACjB,OAAO0H,aAAawC,QAAQlK,EAAS8L,aAGvCY,eAAe1M,GAGb,GAFAA,EAAQ0D,UAAU4C,OAvCE,SAyCftG,EAAQ0D,UAAUC,SA1CH,QA4ClB,YADAuE,KAAK0E,gBAAgB5M,GAIvB,MAAMY,EAAqBD,iCAAiCX,GAE5D0H,aAAakC,IAAI5J,EAAS,gBAAiB,IAAMkI,KAAK0E,gBAAgB5M,IACtEyB,qBAAqBzB,EAASY,GAGhCgM,gBAAgB5M,GACVA,EAAQkD,YACVlD,EAAQkD,WAAW2J,YAAY7M,GAGjC0H,aAAawC,QAAQlK,EAAS+L,cAKVN,uBAACtJ,GACrB,OAAO+F,KAAK4E,MAAK,WACf,IAAIC,EAAOrH,KAAKM,IAAIkC,KA5ET,YA8EN6E,IACHA,EAAO,IAAIX,MAAMlE,OAGJ,UAAX/F,GACF4K,EAAK5K,GAAQ+F,SAKCuD,qBAACuB,GACnB,OAAO,SAAUzF,GACXA,GACFA,EAAM2D,iBAGR8B,EAAcX,MAAMnE,QAW1BR,aAAaiC,GAAG9J,SAAUmM,uBAAsBH,iBAAkBO,MAAMa,cAAc,IAAIb,QAS1FrH,mBAAmB2G,OAAMU,OCxHzB,MAAMV,OAAO,SACPH,WAAW,YACXI,YAAa,aACbC,eAAe,YAEfsB,oBAAoB,SAEpBC,uBAAuB,4BAEvBnB,uBAAwB,2BAQ9B,MAAMoB,eAAehC,cAGAG,sBACjB,OAAOA,WAKT8B,SAEEnF,KAAKoD,SAASgC,aAAa,eAAgBpF,KAAKoD,SAAS5H,UAAU2J,OAvB7C,WA4BF5B,uBAACtJ,GACrB,OAAO+F,KAAK4E,MAAK,WACf,IAAIC,EAAOrH,KAAKM,IAAIkC,KAAMqD,YAErBwB,IACHA,EAAO,IAAIK,OAAOlF,OAGL,WAAX/F,GACF4K,EAAK5K,SCrDb,SAASoL,cAAcC,GACrB,MAAY,SAARA,GAIQ,UAARA,IAIAA,IAAQvM,OAAOuM,GAAKpO,WACf6B,OAAOuM,GAGJ,KAARA,GAAsB,SAARA,EACT,KAGFA,GAGT,SAASC,iBAAiB7H,GACxB,OAAOA,EAAIoD,QAAQ,SAAU0E,GAAQ,IAAGA,EAAInO,eD4C9CmI,aAAaiC,GAAG9J,SAAUmM,uBAAsBmB,uBAAsB5F,IACpEA,EAAM2D,iBAEN,MAAMyC,EAASpG,EAAMU,OAAO0E,QAAQQ,wBAEpC,IAAIJ,EAAOrH,KAAKM,IAAI2H,EAAQpC,YACvBwB,IACHA,EAAO,IAAIK,OAAOO,IAGpBZ,EAAKM,WAUPtI,mBAAmB2G,OAAM0B,QC7DzB,MAAMQ,YAAc,CAClBC,iBAAiB7N,EAAS4F,EAAKlD,GAC7B1C,EAAQsN,aAAc,WAAUG,iBAAiB7H,GAAQlD,IAG3DoL,oBAAoB9N,EAAS4F,GAC3B5F,EAAQ+N,gBAAiB,WAAUN,iBAAiB7H,KAGtDoI,kBAAkBhO,GAChB,IAAKA,EACH,MAAO,GAGT,MAAMiO,EAAa,GAUnB,OARA5L,OAAOC,KAAKtC,EAAQkO,SACjBC,OAAOvI,GAAOA,EAAIvF,WAAW,OAC7BkC,QAAQqD,IACP,IAAIwI,EAAUxI,EAAIoD,QAAQ,MAAO,IACjCoF,EAAUA,EAAQC,OAAO,GAAG9O,cAAgB6O,EAAQpE,MAAM,EAAGoE,EAAQhG,QACrE6F,EAAWG,GAAWb,cAAcvN,EAAQkO,QAAQtI,MAGjDqI,GAGTK,iBAAgB,CAACtO,EAAS4F,IACjB2H,cAAcvN,EAAQE,aAAc,WAAUuN,iBAAiB7H,KAGxE2I,OAAOvO,GACL,MAAMwO,EAAOxO,EAAQyO,wBAErB,MAAO,CACLC,IAAKF,EAAKE,IAAM7O,SAAS4E,KAAKkK,UAC9BC,KAAMJ,EAAKI,KAAO/O,SAAS4E,KAAKoK,aAIpCC,SAAS9O,IACA,CACL0O,IAAK1O,EAAQ+O,UACbH,KAAM5O,EAAQgP,cC7DdC,UAAY,EAEZC,eAAiB,CACrBC,KAAI,CAAClP,EAAUD,EAAUH,SAASkE,kBACzB,GAAGqL,UAAUC,QAAQC,UAAUtH,iBAAiB3I,KAAKW,EAASC,IAGvEsP,QAAO,CAACtP,EAAUD,EAAUH,SAASkE,kBAC5BsL,QAAQC,UAAU7O,cAAcpB,KAAKW,EAASC,GAGvDuP,SAAQ,CAACxP,EAASC,IACT,GAAGmP,UAAUpP,EAAQwP,UACzBrB,OAAOsB,GAASA,EAAMC,QAAQzP,IAGnC0P,QAAQ3P,EAASC,GACf,MAAM0P,EAAU,GAEhB,IAAIC,EAAW5P,EAAQkD,WAEvB,KAAO0M,GAAYA,EAASpO,WAAagC,KAAKC,cArBhC,IAqBgDmM,EAASpO,UACjEoO,EAASF,QAAQzP,IACnB0P,EAAQE,KAAKD,GAGfA,EAAWA,EAAS1M,WAGtB,OAAOyM,GAGTG,KAAK9P,EAASC,GACZ,IAAI8P,EAAW/P,EAAQgQ,uBAEvB,KAAOD,GAAU,CACf,GAAIA,EAASL,QAAQzP,GACnB,MAAO,CAAC8P,GAGVA,EAAWA,EAASC,uBAGtB,MAAO,IAGTC,KAAKjQ,EAASC,GACZ,IAAIgQ,EAAOjQ,EAAQkQ,mBAEnB,KAAOD,GAAM,CACX,GAAIA,EAAKP,QAAQzP,GACf,MAAO,CAACgQ,GAGVA,EAAOA,EAAKC,mBAGd,MAAO,KCxCLxE,OAAO,WACPH,WAAW,cACXI,YAAa,eACbC,eAAe,YAEfuE,eAAiB,YACjBC,gBAAkB,aAClBC,uBAAyB,IACzBC,gBAAkB,GAElBC,UAAU,CACdC,SAAU,IACVC,UAAU,EACVC,OAAO,EACPC,MAAO,QACPC,MAAM,EACNC,OAAO,GAGHC,cAAc,CAClBN,SAAU,mBACVC,SAAU,UACVC,MAAO,mBACPC,MAAO,mBACPC,KAAM,UACNC,MAAO,WAGHE,WAAa,OACbC,WAAa,OACbC,eAAiB,OACjBC,gBAAkB,QAElBC,YAAe,oBACfC,WAAc,mBACdC,cAAiB,sBACjBC,iBAAoB,yBACpBC,iBAAoB,yBACpBC,iBAAoB,yBACpBC,gBAAmB,wBACnBC,eAAkB,uBAClBC,kBAAqB,0BACrBC,gBAAmB,wBACnBC,iBAAoB,wBACpBC,sBAAuB,4BACvB9F,uBAAwB,6BAExB+F,oBAAsB,WACtB7E,oBAAoB,SACpB8E,iBAAmB,QACnBC,eAAiB,oBACjBC,iBAAmB,sBACnBC,gBAAkB,qBAClBC,gBAAkB,qBAClBC,yBAA2B,gBAE3BC,kBAAkB,UAClBC,qBAAuB,wBACvBC,cAAgB,iBAChBC,kBAAoB,qBACpBC,mBAAqB,2CACrBC,oBAAsB,uBACtBC,mBAAqB,mBACrBC,oBAAsB,sCACtBC,mBAAqB,4BAErBC,mBAAqB,QACrBC,iBAAmB,MAOzB,MAAMC,iBAAiB7H,cACrBC,YAAYrL,EAASmC,GACnB+Q,MAAMlT,GAENkI,KAAKiL,OAAS,KACdjL,KAAKkL,UAAY,KACjBlL,KAAKmL,eAAiB,KACtBnL,KAAKoL,WAAY,EACjBpL,KAAKqL,YAAa,EAClBrL,KAAKsL,aAAe,KACpBtL,KAAKuL,YAAc,EACnBvL,KAAKwL,YAAc,EAEnBxL,KAAKyL,QAAUzL,KAAK0L,WAAWzR,GAC/B+F,KAAK2L,mBAAqB3E,eAAeK,QAAQoD,oBAAqBzK,KAAKoD,UAC3EpD,KAAK4L,gBAAkB,iBAAkBjU,SAASkE,iBAAmBgQ,UAAUC,eAAiB,EAChG9L,KAAK+L,cAAgB3K,QAAQxI,OAAOoT,cAEpChM,KAAKiM,qBAKW5D,qBAChB,OAAOA,UAGUhF,sBACjB,OAAOA,WAKT0E,OACO/H,KAAKqL,YACRrL,KAAKkM,OAAOrD,YAIhBsD,mBAGOxU,SAASyU,QAAUtR,UAAUkF,KAAKoD,WACrCpD,KAAK+H,OAITH,OACO5H,KAAKqL,YACRrL,KAAKkM,OAAOpD,YAIhBL,MAAMpJ,GACCA,IACHW,KAAKoL,WAAY,GAGfpE,eAAeK,QAAQmD,mBAAoBxK,KAAKoD,YAClDlK,qBAAqB8G,KAAKoD,UAC1BpD,KAAKqM,OAAM,IAGbC,cAActM,KAAKkL,WACnBlL,KAAKkL,UAAY,KAGnBmB,MAAMhN,GACCA,IACHW,KAAKoL,WAAY,GAGfpL,KAAKkL,YACPoB,cAActM,KAAKkL,WACnBlL,KAAKkL,UAAY,MAGflL,KAAKyL,SAAWzL,KAAKyL,QAAQnD,WAAatI,KAAKoL,YACjDpL,KAAKuM,kBAELvM,KAAKkL,UAAYsB,aACd7U,SAAS8U,gBAAkBzM,KAAKmM,gBAAkBnM,KAAK+H,MAAM2E,KAAK1M,MACnEA,KAAKyL,QAAQnD,WAKnBqE,GAAGC,GACD5M,KAAKmL,eAAiBnE,eAAeK,QAAQgD,qBAAsBrK,KAAKoD,UACxE,MAAMyJ,EAAc7M,KAAK8M,cAAc9M,KAAKmL,gBAE5C,GAAIyB,EAAQ5M,KAAKiL,OAAO/K,OAAS,GAAK0M,EAAQ,EAC5C,OAGF,GAAI5M,KAAKqL,WAEP,YADA7L,aAAakC,IAAI1B,KAAKoD,SAAU8F,WAAY,IAAMlJ,KAAK2M,GAAGC,IAI5D,GAAIC,IAAgBD,EAGlB,OAFA5M,KAAKyI,aACLzI,KAAKqM,QAIP,MAAMU,EAAQH,EAAQC,EACpBhE,WACAC,WAEF9I,KAAKkM,OAAOa,EAAO/M,KAAKiL,OAAO2B,IAGjCtJ,UACE9D,aAAaC,IAAIO,KAAKoD,SAAUK,aAEhCzD,KAAKiL,OAAS,KACdjL,KAAKyL,QAAU,KACfzL,KAAKkL,UAAY,KACjBlL,KAAKoL,UAAY,KACjBpL,KAAKqL,WAAa,KAClBrL,KAAKmL,eAAiB,KACtBnL,KAAK2L,mBAAqB,KAE1BX,MAAM1H,UAKRoI,WAAWzR,GAMT,OALAA,EAAS,IACJoO,aACApO,GAELF,gBAAgByJ,OAAMvJ,EAAQ2O,eACvB3O,EAGT+S,eACE,MAAMC,EAAYzV,KAAK0V,IAAIlN,KAAKwL,aAEhC,GAAIyB,GA/MgB,GAgNlB,OAGF,MAAME,EAAYF,EAAYjN,KAAKwL,YAEnCxL,KAAKwL,YAAc,EAEd2B,GAILnN,KAAKkM,OAAOiB,EAAY,EAAInE,gBAAkBD,gBAGhDkD,qBACMjM,KAAKyL,QAAQlD,UACf/I,aAAaiC,GAAGzB,KAAKoD,SAAU+F,cAAe9J,GAASW,KAAKoN,SAAS/N,IAG5C,UAAvBW,KAAKyL,QAAQhD,QACfjJ,aAAaiC,GAAGzB,KAAKoD,SAAUgG,iBAAkB/J,GAASW,KAAKyI,MAAMpJ,IACrEG,aAAaiC,GAAGzB,KAAKoD,SAAUiG,iBAAkBhK,GAASW,KAAKqM,MAAMhN,KAGnEW,KAAKyL,QAAQ9C,OAAS3I,KAAK4L,iBAC7B5L,KAAKqN,0BAITA,0BACE,MAAMC,EAAQjO,KACRW,KAAK+L,eApLU,QAoLQ1M,EAAMkO,aArLZ,UAqLgDlO,EAAMkO,YAE/DvN,KAAK+L,gBACf/L,KAAKuL,YAAclM,EAAMmO,QAAQ,GAAGC,SAFpCzN,KAAKuL,YAAclM,EAAMoO,SAMvBC,EAAOrO,IAEXW,KAAKwL,YAAcnM,EAAMmO,SAAWnO,EAAMmO,QAAQtN,OAAS,EACzD,EACAb,EAAMmO,QAAQ,GAAGC,QAAUzN,KAAKuL,aAG9BoC,EAAMtO,KACNW,KAAK+L,eAnMU,QAmMQ1M,EAAMkO,aApMZ,UAoMgDlO,EAAMkO,cACzEvN,KAAKwL,YAAcnM,EAAMoO,QAAUzN,KAAKuL,aAG1CvL,KAAKgN,eACsB,UAAvBhN,KAAKyL,QAAQhD,QASfzI,KAAKyI,QACDzI,KAAKsL,cACPsC,aAAa5N,KAAKsL,cAGpBtL,KAAKsL,aAAexR,WAAWuF,GAASW,KAAKqM,MAAMhN,GAlR5B,IAkR6DW,KAAKyL,QAAQnD,YAIrGtB,eAAeC,KAAKsD,kBAAmBvK,KAAKoD,UAAU/I,QAAQwT,IAC5DrO,aAAaiC,GAAGoM,EAASlE,iBAAkBmE,GAAKA,EAAE9K,oBAGhDhD,KAAK+L,eACPvM,aAAaiC,GAAGzB,KAAKoD,SAAUqG,kBAAmBpK,GAASiO,EAAMjO,IACjEG,aAAaiC,GAAGzB,KAAKoD,SAAUsG,gBAAiBrK,GAASsO,EAAItO,IAE7DW,KAAKoD,SAAS5H,UAAUuS,IA/OG,mBAiP3BvO,aAAaiC,GAAGzB,KAAKoD,SAAUkG,iBAAkBjK,GAASiO,EAAMjO,IAChEG,aAAaiC,GAAGzB,KAAKoD,SAAUmG,gBAAiBlK,GAASqO,EAAKrO,IAC9DG,aAAaiC,GAAGzB,KAAKoD,SAAUoG,eAAgBnK,GAASsO,EAAItO,KAIhE+N,SAAS/N,GACH,kBAAkB1E,KAAK0E,EAAMU,OAAOiO,WAzSrB,cA6Sf3O,EAAM3B,KACR2B,EAAM2D,iBACNhD,KAAKkM,OAAOnD,iBA9SM,eA+ST1J,EAAM3B,MACf2B,EAAM2D,iBACNhD,KAAKkM,OAAOlD,mBAIhB8D,cAAchV,GAKZ,OAJAkI,KAAKiL,OAASnT,GAAWA,EAAQkD,WAC/BgM,eAAeC,KAAKqD,cAAexS,EAAQkD,YAC3C,GAEKgF,KAAKiL,OAAOgD,QAAQnW,GAG7BoW,gBAAgBnB,EAAOoB,GACrB,MAAMC,EAASrB,IAAUlE,WACnBwF,EAAStB,IAAUjE,WACnB+D,EAAc7M,KAAK8M,cAAcqB,GACjCG,EAAgBtO,KAAKiL,OAAO/K,OAAS,EAG3C,IAFuBmO,GAA0B,IAAhBxB,GAAuBuB,GAAUvB,IAAgByB,KAE5DtO,KAAKyL,QAAQ/C,KACjC,OAAOyF,EAGT,MACMI,GAAa1B,GADLwB,GAAU,EAAI,IACcrO,KAAKiL,OAAO/K,OAEtD,OAAsB,IAAfqO,EACLvO,KAAKiL,OAAOjL,KAAKiL,OAAO/K,OAAS,GACjCF,KAAKiL,OAAOsD,GAGhBC,mBAAmBC,EAAeC,GAChC,MAAMC,EAAc3O,KAAK8M,cAAc2B,GACjCG,EAAY5O,KAAK8M,cAAc9F,eAAeK,QAAQgD,qBAAsBrK,KAAKoD,WAEvF,OAAO5D,aAAawC,QAAQhC,KAAKoD,SAAU6F,YAAa,CACtDwF,cAAAA,EACAtB,UAAWuB,EACXvQ,KAAMyQ,EACNjC,GAAIgC,IAIRE,2BAA2B/W,GACzB,GAAIkI,KAAK2L,mBAAoB,CAC3B,MAAMmD,EAAkB9H,eAAeK,QA5SrB,UA4S8CrH,KAAK2L,oBAErEmD,EAAgBtT,UAAU4C,OAtTN,UAuTpB0Q,EAAgBjJ,gBAAgB,gBAEhC,MAAMkJ,EAAa/H,eAAeC,KA3Sb,mBA2SsCjH,KAAK2L,oBAEhE,IAAK,IAAI1L,EAAI,EAAGA,EAAI8O,EAAW7O,OAAQD,IACrC,GAAIlH,OAAOiW,SAASD,EAAW9O,GAAGjI,aAAa,oBAAqB,MAAQgI,KAAK8M,cAAchV,GAAU,CACvGiX,EAAW9O,GAAGzE,UAAUuS,IA7TR,UA8ThBgB,EAAW9O,GAAGmF,aAAa,eAAgB,QAC3C,QAMRmH,kBACE,MAAMzU,EAAUkI,KAAKmL,gBAAkBnE,eAAeK,QAAQgD,qBAAsBrK,KAAKoD,UAEzF,IAAKtL,EACH,OAGF,MAAMmX,EAAkBlW,OAAOiW,SAASlX,EAAQE,aAAa,oBAAqB,IAE9EiX,GACFjP,KAAKyL,QAAQyD,gBAAkBlP,KAAKyL,QAAQyD,iBAAmBlP,KAAKyL,QAAQnD,SAC5EtI,KAAKyL,QAAQnD,SAAW2G,GAExBjP,KAAKyL,QAAQnD,SAAWtI,KAAKyL,QAAQyD,iBAAmBlP,KAAKyL,QAAQnD,SAIzE4D,OAAOiD,EAAkBrX,GACvB,MAAMiV,EAAQ/M,KAAKoP,kBAAkBD,GAC/BhB,EAAgBnH,eAAeK,QAAQgD,qBAAsBrK,KAAKoD,UAClEiM,EAAqBrP,KAAK8M,cAAcqB,GACxCmB,EAAcxX,GAAWkI,KAAKkO,gBAAgBnB,EAAOoB,GAErDoB,EAAmBvP,KAAK8M,cAAcwC,GACtCE,EAAYpO,QAAQpB,KAAKkL,WAEzBkD,EAASrB,IAAUlE,WACnB4G,EAAuBrB,EAASpE,iBAAmBD,eACnD2F,EAAiBtB,EAASnE,gBAAkBC,gBAC5CwE,EAAqB1O,KAAK2P,kBAAkB5C,GAElD,GAAIuC,GAAeA,EAAY9T,UAAUC,SApWnB,UAqWpBuE,KAAKqL,YAAa,OAKpB,IADmBrL,KAAKwO,mBAAmBc,EAAaZ,GACzCpM,kBAIV6L,GAAkBmB,EAAvB,CAcA,GATAtP,KAAKqL,YAAa,EAEdmE,GACFxP,KAAKyI,QAGPzI,KAAK6O,2BAA2BS,GAChCtP,KAAKmL,eAAiBmE,EAElBtP,KAAKoD,SAAS5H,UAAUC,SA3XP,SA2XmC,CACtD6T,EAAY9T,UAAUuS,IAAI2B,GAE1BvT,OAAOmT,GAEPnB,EAAc3S,UAAUuS,IAAI0B,GAC5BH,EAAY9T,UAAUuS,IAAI0B,GAE1B,MAAM/W,EAAqBD,iCAAiC0V,GAE5D3O,aAAakC,IAAIyM,EAAe,gBAAiB,KAC/CmB,EAAY9T,UAAU4C,OAAOqR,EAAsBC,GACnDJ,EAAY9T,UAAUuS,IAxYJ,UA0YlBI,EAAc3S,UAAU4C,OA1YN,SA0YgCsR,EAAgBD,GAElEzP,KAAKqL,YAAa,EAElBvR,WAAW,KACT0F,aAAawC,QAAQhC,KAAKoD,SAAU8F,WAAY,CAC9CuF,cAAea,EACfnC,UAAWuB,EACXvQ,KAAMkR,EACN1C,GAAI4C,KAEL,KAGLhW,qBAAqB4U,EAAezV,QAEpCyV,EAAc3S,UAAU4C,OA1ZJ,UA2ZpBkR,EAAY9T,UAAUuS,IA3ZF,UA6ZpB/N,KAAKqL,YAAa,EAClB7L,aAAawC,QAAQhC,KAAKoD,SAAU8F,WAAY,CAC9CuF,cAAea,EACfnC,UAAWuB,EACXvQ,KAAMkR,EACN1C,GAAI4C,IAIJC,GACFxP,KAAKqM,SAIT+C,kBAAkBjC,GAChB,MAAK,CAACnE,gBAAiBD,gBAAgB7Q,SAASiV,GAI5CxQ,QACKwQ,IAAcnE,gBAAkBF,WAAaD,WAG/CsE,IAAcnE,gBAAkBH,WAAaC,WAP3CqE,EAUXwC,kBAAkB5C,GAChB,MAAK,CAAClE,WAAYC,YAAY5Q,SAAS6U,GAInCpQ,QACKoQ,IAAUlE,WAAaE,eAAiBC,gBAG1C+D,IAAUlE,WAAaG,gBAAkBD,eAPvCgE,EAYaxJ,yBAACzL,EAASmC,GAChC,IAAI4K,EAAOrH,KAAKM,IAAIhG,EAASuL,YACzBoI,EAAU,IACTpD,aACA3C,YAAYI,kBAAkBhO,IAGb,iBAAXmC,IACTwR,EAAU,IACLA,KACAxR,IAIP,MAAM2V,EAA2B,iBAAX3V,EAAsBA,EAASwR,EAAQjD,MAM7D,GAJK3D,IACHA,EAAO,IAAIkG,SAASjT,EAAS2T,IAGT,iBAAXxR,EACT4K,EAAK8H,GAAG1S,QACH,GAAsB,iBAAX2V,EAAqB,CACrC,QAA4B,IAAjB/K,EAAK+K,GACd,MAAM,IAAIhV,UAAW,oBAAmBgV,MAG1C/K,EAAK+K,UACInE,EAAQnD,UAAYmD,EAAQoE,OACrChL,EAAK4D,QACL5D,EAAKwH,SAIa9I,uBAACtJ,GACrB,OAAO+F,KAAK4E,MAAK,WACfmG,SAAS+E,kBAAkB9P,KAAM/F,MAIXsJ,2BAAClE,GACzB,MAAMU,EAASvH,uBAAuBwH,MAEtC,IAAKD,IAAWA,EAAOvE,UAAUC,SAjfT,YAkftB,OAGF,MAAMxB,EAAS,IACVyL,YAAYI,kBAAkB/F,MAC9B2F,YAAYI,kBAAkB9F,OAE7B+P,EAAa/P,KAAKhI,aAAa,oBAEjC+X,IACF9V,EAAOqO,UAAW,GAGpByC,SAAS+E,kBAAkB/P,EAAQ9F,GAE/B8V,GACFvS,KAAKM,IAAIiC,EAAQsD,YAAUsJ,GAAGoD,GAGhC1Q,EAAM2D,kBAUVxD,aAAaiC,GAAG9J,SAAUmM,uBAAsB6G,oBAAqBI,SAASiF,qBAE9ExQ,aAAaiC,GAAG7I,OAAQgR,sBAAqB,KAC3C,MAAMqG,EAAYjJ,eAAeC,KAAK2D,oBAEtC,IAAK,IAAI3K,EAAI,EAAGM,EAAM0P,EAAU/P,OAAQD,EAAIM,EAAKN,IAC/C8K,SAAS+E,kBAAkBG,EAAUhQ,GAAIzC,KAAKM,IAAImS,EAAUhQ,GAAIoD,eAWpExG,mBAAmB2G,OAAMuH,UChlBzB,MAAMvH,OAAO,WACPH,WAAW,cACXI,YAAa,eACbC,eAAe,YAEf2E,UAAU,CACdlD,QAAQ,EACR+K,OAAQ,IAGJtH,cAAc,CAClBzD,OAAQ,UACR+K,OAAQ,oBAGJC,aAAc,mBACdC,cAAe,oBACfC,aAAc,mBACdC,eAAgB,qBAChBxM,uBAAwB,6BAExBG,kBAAkB,OAClBsM,oBAAsB,WACtBC,sBAAwB,aACxBC,qBAAuB,YAEvBC,MAAQ,QACRC,OAAS,SAETC,iBAAmB,qBACnB3L,uBAAuB,8BAQ7B,MAAM4L,iBAAiB3N,cACrBC,YAAYrL,EAASmC,GACnB+Q,MAAMlT,GAENkI,KAAK8Q,kBAAmB,EACxB9Q,KAAKyL,QAAUzL,KAAK0L,WAAWzR,GAC/B+F,KAAK+Q,cAAgB/J,eAAeC,KACjC,GAAEhC,iCAA+BjF,KAAKoD,SAAS4N,QAC7C/L,2CAAyCjF,KAAKoD,SAAS4N,QAG5D,MAAMC,EAAajK,eAAeC,KAAKhC,wBAEvC,IAAK,IAAIhF,EAAI,EAAGM,EAAM0Q,EAAW/Q,OAAQD,EAAIM,EAAKN,IAAK,CACrD,MAAMiR,EAAOD,EAAWhR,GAClBlI,EAAWO,uBAAuB4Y,GAClCC,EAAgBnK,eAAeC,KAAKlP,GACvCkO,OAAOmL,GAAaA,IAAcpR,KAAKoD,UAEzB,OAAbrL,GAAqBoZ,EAAcjR,SACrCF,KAAKqR,UAAYtZ,EACjBiI,KAAK+Q,cAAcpJ,KAAKuJ,IAI5BlR,KAAKsR,QAAUtR,KAAKyL,QAAQyE,OAASlQ,KAAKuR,aAAe,KAEpDvR,KAAKyL,QAAQyE,QAChBlQ,KAAKwR,0BAA0BxR,KAAKoD,SAAUpD,KAAK+Q,eAGjD/Q,KAAKyL,QAAQtG,QACfnF,KAAKmF,SAMSkD,qBAChB,OAAOA,UAGUhF,sBACjB,OAAOA,WAKT8B,SACMnF,KAAKoD,SAAS5H,UAAUC,SAlER,QAmElBuE,KAAKyR,OAELzR,KAAK0R,OAITA,OACE,GAAI1R,KAAK8Q,kBAAoB9Q,KAAKoD,SAAS5H,UAAUC,SA1EjC,QA2ElB,OAGF,IAAIkW,EACAC,EAEA5R,KAAKsR,UACPK,EAAU3K,eAAeC,KAAK2J,iBAAkB5Q,KAAKsR,SAClDrL,OAAOiL,GAC6B,iBAAxBlR,KAAKyL,QAAQyE,OACfgB,EAAKlZ,aAAa,oBAAsBgI,KAAKyL,QAAQyE,OAGvDgB,EAAK1V,UAAUC,SAvFJ,aA0FC,IAAnBkW,EAAQzR,SACVyR,EAAU,OAId,MAAME,EAAY7K,eAAeK,QAAQrH,KAAKqR,WAC9C,GAAIM,EAAS,CACX,MAAMG,EAAiBH,EAAQ1K,KAAKiK,GAAQW,IAAcX,GAG1D,GAFAU,EAAcE,EAAiBtU,KAAKM,IAAIgU,EAAgBzO,YAAY,KAEhEuO,GAAeA,EAAYd,iBAC7B,OAKJ,GADmBtR,aAAawC,QAAQhC,KAAKoD,SAAU+M,cACxC7N,iBACb,OAGEqP,GACFA,EAAQtX,QAAQ0X,IACVF,IAAcE,GAChBlB,SAASmB,kBAAkBD,EAAY,QAGpCH,GACHpU,KAAKC,IAAIsU,EAAY1O,WAAU,QAKrC,MAAM4O,EAAYjS,KAAKkS,gBAEvBlS,KAAKoD,SAAS5H,UAAU4C,OA5HA,YA6HxB4B,KAAKoD,SAAS5H,UAAUuS,IA5HE,cA8H1B/N,KAAKoD,SAASrI,MAAMkX,GAAa,EAE7BjS,KAAK+Q,cAAc7Q,QACrBF,KAAK+Q,cAAc1W,QAAQvC,IACzBA,EAAQ0D,UAAU4C,OAjIG,aAkIrBtG,EAAQsN,aAAa,iBAAiB,KAI1CpF,KAAKmS,kBAAiB,GAEtB,MAYMC,EAAc,UADSH,EAAU,GAAGpX,cAAgBoX,EAAUnQ,MAAM,IAEpEpJ,EAAqBD,iCAAiCuH,KAAKoD,UAEjE5D,aAAakC,IAAI1B,KAAKoD,SAAU,gBAff,KACfpD,KAAKoD,SAAS5H,UAAU4C,OA1IA,cA2IxB4B,KAAKoD,SAAS5H,UAAUuS,IA5IF,WADJ,QA+IlB/N,KAAKoD,SAASrI,MAAMkX,GAAa,GAEjCjS,KAAKmS,kBAAiB,GAEtB3S,aAAawC,QAAQhC,KAAKoD,SAAUgN,iBAStC7W,qBAAqByG,KAAKoD,SAAU1K,GACpCsH,KAAKoD,SAASrI,MAAMkX,GAAgBjS,KAAKoD,SAASgP,GAAhB,KAGpCX,OACE,GAAIzR,KAAK8Q,mBAAqB9Q,KAAKoD,SAAS5H,UAAUC,SAjKlC,QAkKlB,OAIF,GADmB+D,aAAawC,QAAQhC,KAAKoD,SAAUiN,cACxC/N,iBACb,OAGF,MAAM2P,EAAYjS,KAAKkS,gBAEvBlS,KAAKoD,SAASrI,MAAMkX,GAAgBjS,KAAKoD,SAASmD,wBAAwB0L,GAAxC,KAElC9V,OAAO6D,KAAKoD,UAEZpD,KAAKoD,SAAS5H,UAAUuS,IA9KE,cA+K1B/N,KAAKoD,SAAS5H,UAAU4C,OAhLA,WADJ,QAmLpB,MAAMiU,EAAqBrS,KAAK+Q,cAAc7Q,OAC9C,GAAImS,EAAqB,EACvB,IAAK,IAAIpS,EAAI,EAAGA,EAAIoS,EAAoBpS,IAAK,CAC3C,MAAM+B,EAAUhC,KAAK+Q,cAAc9Q,GAC7BiR,EAAO1Y,uBAAuBwJ,GAEhCkP,IAASA,EAAK1V,UAAUC,SAzLZ,UA0LduG,EAAQxG,UAAUuS,IAvLC,aAwLnB/L,EAAQoD,aAAa,iBAAiB,IAK5CpF,KAAKmS,kBAAiB,GAStBnS,KAAKoD,SAASrI,MAAMkX,GAAa,GACjC,MAAMvZ,EAAqBD,iCAAiCuH,KAAKoD,UAEjE5D,aAAakC,IAAI1B,KAAKoD,SAAU,gBAVf,KACfpD,KAAKmS,kBAAiB,GACtBnS,KAAKoD,SAAS5H,UAAU4C,OAlMA,cAmMxB4B,KAAKoD,SAAS5H,UAAUuS,IApMF,YAqMtBvO,aAAawC,QAAQhC,KAAKoD,SAAUkN,kBAOtC/W,qBAAqByG,KAAKoD,SAAU1K,GAGtCyZ,iBAAiBG,GACftS,KAAK8Q,iBAAmBwB,EAG1BhP,UACE0H,MAAM1H,UACNtD,KAAKyL,QAAU,KACfzL,KAAKsR,QAAU,KACftR,KAAK+Q,cAAgB,KACrB/Q,KAAK8Q,iBAAmB,KAK1BpF,WAAWzR,GAOT,OANAA,EAAS,IACJoO,aACApO,IAEEkL,OAAS/D,QAAQnH,EAAOkL,QAC/BpL,gBAAgByJ,OAAMvJ,EAAQ2O,eACvB3O,EAGTiY,gBACE,OAAOlS,KAAKoD,SAAS5H,UAAUC,SAASiV,OAASA,MAAQC,OAG3DY,aACE,IAAIrB,OAAEA,GAAWlQ,KAAKyL,QAElBpS,UAAU6W,QAEiB,IAAlBA,EAAOqC,aAA+C,IAAdrC,EAAO,KACxDA,EAASA,EAAO,IAGlBA,EAASlJ,eAAeK,QAAQ6I,GAGlC,MAAMnY,EAAY,GAAEkN,0CAAwCiL,MAY5D,OAVAlJ,eAAeC,KAAKlP,EAAUmY,GAC3B7V,QAAQvC,IACP,MAAM0a,EAAWha,uBAAuBV,GAExCkI,KAAKwR,0BACHgB,EACA,CAAC1a,MAIAoY,EAGTsB,0BAA0B1Z,EAAS2a,GACjC,IAAK3a,IAAY2a,EAAavS,OAC5B,OAGF,MAAMwS,EAAS5a,EAAQ0D,UAAUC,SA5Qb,QA8QpBgX,EAAapY,QAAQ6W,IACfwB,EACFxB,EAAK1V,UAAU4C,OA7QM,aA+QrB8S,EAAK1V,UAAUuS,IA/QM,aAkRvBmD,EAAK9L,aAAa,gBAAiBsN,KAMfnP,yBAACzL,EAASmC,GAChC,IAAI4K,EAAOrH,KAAKM,IAAIhG,EAASuL,YAC7B,MAAMoI,EAAU,IACXpD,aACA3C,YAAYI,kBAAkBhO,MACX,iBAAXmC,GAAuBA,EAASA,EAAS,IAWtD,IARK4K,GAAQ4G,EAAQtG,QAA4B,iBAAXlL,GAAuB,YAAYU,KAAKV,KAC5EwR,EAAQtG,QAAS,GAGdN,IACHA,EAAO,IAAIgM,SAAS/Y,EAAS2T,IAGT,iBAAXxR,EAAqB,CAC9B,QAA4B,IAAjB4K,EAAK5K,GACd,MAAM,IAAIW,UAAW,oBAAmBX,MAG1C4K,EAAK5K,MAIasJ,uBAACtJ,GACrB,OAAO+F,KAAK4E,MAAK,WACfiM,SAASmB,kBAAkBhS,KAAM/F,OAWvCuF,aAAaiC,GAAG9J,SAAUmM,uBAAsBmB,wBAAsB,SAAU5F,IAEjD,MAAzBA,EAAMU,OAAOiO,SAAoB3O,EAAMC,gBAAmD,MAAjCD,EAAMC,eAAe0O,UAChF3O,EAAM2D,iBAGR,MAAM2P,EAAcjN,YAAYI,kBAAkB9F,MAC5CjI,EAAWO,uBAAuB0H,MACfgH,eAAeC,KAAKlP,GAE5BsC,QAAQvC,IACvB,MAAM+M,EAAOrH,KAAKM,IAAIhG,EAASuL,YAC/B,IAAIpJ,EACA4K,GAEmB,OAAjBA,EAAKyM,SAAkD,iBAAvBqB,EAAYzC,SAC9CrL,EAAK4G,QAAQyE,OAASyC,EAAYzC,OAClCrL,EAAKyM,QAAUzM,EAAK0M,cAGtBtX,EAAS,UAETA,EAAS0Y,EAGX9B,SAASmB,kBAAkBla,EAASmC,QAWxC4C,mBAAmB2G,OAAMqN,UCzXzB,MAAMrN,OAAO,WACPH,WAAW,cACXI,YAAa,eACbC,eAAe,YAEfkP,aAAa,SACbC,UAAY,QACZC,QAAU,MACVC,aAAe,UACfC,eAAiB,YACjBC,mBAAqB,EAErBC,eAAiB,IAAIxY,OAAQ,4BAE7B2V,aAAc,mBACdC,eAAgB,qBAChBH,aAAc,mBACdC,cAAe,oBACf+C,YAAe,oBACfrP,uBAAwB,6BACxBsP,uBAA0B,+BAC1BC,qBAAwB,6BAExBC,oBAAsB,WACtBrP,kBAAkB,OAClBsP,kBAAoB,SACpBC,mBAAqB,UACrBC,qBAAuB,YACvBC,kBAAoB,SAEpBzO,uBAAuB,8BACvB0O,cAAgB,iBAChBC,oBAAsB,cACtBC,uBAAyB,8DAEzBC,cAAgBnX,QAAU,UAAY,YACtCoX,iBAAmBpX,QAAU,YAAc,UAC3CqX,iBAAmBrX,QAAU,aAAe,eAC5CsX,oBAAsBtX,QAAU,eAAiB,aACjDuX,gBAAkBvX,QAAU,aAAe,cAC3CwX,eAAiBxX,QAAU,cAAgB,aAE3C0L,UAAU,CACdhC,OAAQ,CAAC,EAAG,GACZ+N,SAAU,kBACVC,UAAW,SACXlZ,QAAS,UACTmZ,aAAc,MAGV1L,cAAc,CAClBvC,OAAQ,0BACR+N,SAAU,mBACVC,UAAW,0BACXlZ,QAAS,SACTmZ,aAAc,0BAShB,MAAMC,iBAAiBrR,cACrBC,YAAYrL,EAASmC,GACnB+Q,MAAMlT,GAENkI,KAAKwU,QAAU,KACfxU,KAAKyL,QAAUzL,KAAK0L,WAAWzR,GAC/B+F,KAAKyU,MAAQzU,KAAK0U,kBAClB1U,KAAK2U,UAAY3U,KAAK4U,gBAEtB5U,KAAKiM,qBAKW5D,qBAChB,OAAOA,UAGaO,yBACpB,OAAOA,cAGUvF,sBACjB,OAAOA,WAKT8B,SACE,GAAInF,KAAKoD,SAAS1H,UAAYsE,KAAKoD,SAAS5H,UAAUC,SAtE9B,YAuEtB,OAGF,MAAMoZ,EAAW7U,KAAKoD,SAAS5H,UAAUC,SAzErB,QA2EpB8Y,SAASO,aAELD,GAIJ7U,KAAK0R,OAGPA,OACE,GAAI1R,KAAKoD,SAAS1H,UAAYsE,KAAKoD,SAAS5H,UAAUC,SAtF9B,aAsF+DuE,KAAKyU,MAAMjZ,UAAUC,SArFxF,QAsFlB,OAGF,MAAMyU,EAASqE,SAASQ,qBAAqB/U,KAAKoD,UAC5CqL,EAAgB,CACpBA,cAAezO,KAAKoD,UAKtB,IAFkB5D,aAAawC,QAAQhC,KAAKoD,SAAU+M,aAAY1B,GAEpDnM,iBAAd,CAKA,GAAItC,KAAK2U,UACPjP,YAAYC,iBAAiB3F,KAAKyU,MAAO,SAAU,YAC9C,CACL,QAAsB,IAAXO,OACT,MAAM,IAAIpa,UAAU,gEAGtB,IAAIqa,EAAmBjV,KAAKoD,SAEG,WAA3BpD,KAAKyL,QAAQ4I,UACfY,EAAmB/E,EACV7W,UAAU2G,KAAKyL,QAAQ4I,YAChCY,EAAmBjV,KAAKyL,QAAQ4I,eAGa,IAAlCrU,KAAKyL,QAAQ4I,UAAU9B,SAChC0C,EAAmBjV,KAAKyL,QAAQ4I,UAAU,KAED,iBAA3BrU,KAAKyL,QAAQ4I,YAC7BY,EAAmBjV,KAAKyL,QAAQ4I,WAGlC,MAAMC,EAAetU,KAAKkV,mBACpBC,EAAkBb,EAAac,UAAUnO,KAAKoO,GAA8B,gBAAlBA,EAASvY,OAA+C,IAArBuY,EAASC,SAE5GtV,KAAKwU,QAAUQ,OAAOO,aAAaN,EAAkBjV,KAAKyU,MAAOH,GAE7Da,GACFzP,YAAYC,iBAAiB3F,KAAKyU,MAAO,SAAU,UAQnD,iBAAkB9c,SAASkE,kBAC5BqU,EAAOzL,QAlIc,gBAmItB,GAAGyC,UAAUvP,SAAS4E,KAAK+K,UACxBjN,QAAQ6W,GAAQ1R,aAAaiC,GAAGyP,EAAM,YAAa,MVAzC,gBUGflR,KAAKoD,SAASoS,QACdxV,KAAKoD,SAASgC,aAAa,iBAAiB,GAE5CpF,KAAKyU,MAAMjZ,UAAU2J,OAlJD,QAmJpBnF,KAAKoD,SAAS5H,UAAU2J,OAnJJ,QAoJpB3F,aAAawC,QAAQhC,KAAKoD,SAAUgN,cAAa3B,IAGnDgD,OACE,GAAIzR,KAAKoD,SAAS1H,UAAYsE,KAAKoD,SAAS5H,UAAUC,SAzJ9B,cAyJgEuE,KAAKyU,MAAMjZ,UAAUC,SAxJzF,QAyJlB,OAGF,MAAMgT,EAAgB,CACpBA,cAAezO,KAAKoD,UAGJ5D,aAAawC,QAAQhC,KAAKoD,SAAUiN,aAAY5B,GAEpDnM,mBAIVtC,KAAKwU,SACPxU,KAAKwU,QAAQiB,UAGfzV,KAAKyU,MAAMjZ,UAAU2J,OA1KD,QA2KpBnF,KAAKoD,SAAS5H,UAAU2J,OA3KJ,QA4KpBO,YAAYE,oBAAoB5F,KAAKyU,MAAO,UAC5CjV,aAAawC,QAAQhC,KAAKoD,SAAUkN,eAAc7B,IAGpDnL,UACE9D,aAAaC,IAAIO,KAAKoD,SAAUK,aAChCzD,KAAKyU,MAAQ,KAETzU,KAAKwU,UACPxU,KAAKwU,QAAQiB,UACbzV,KAAKwU,QAAU,MAGjBxJ,MAAM1H,UAGRoS,SACE1V,KAAK2U,UAAY3U,KAAK4U,gBAClB5U,KAAKwU,SACPxU,KAAKwU,QAAQkB,SAMjBzJ,qBACEzM,aAAaiC,GAAGzB,KAAKoD,SAAU+P,YAAa9T,IAC1CA,EAAM2D,iBACNhD,KAAKmF,WAITuG,WAAWzR,GAST,GARAA,EAAS,IACJ+F,KAAKmD,YAAYkF,WACjB3C,YAAYI,kBAAkB9F,KAAKoD,aACnCnJ,GAGLF,gBAAgByJ,OAAMvJ,EAAQ+F,KAAKmD,YAAYyF,aAEf,iBAArB3O,EAAOoa,YAA2Bhb,UAAUY,EAAOoa,YACV,mBAA3Cpa,EAAOoa,UAAU9N,sBAGxB,MAAM,IAAI3L,UAAa4I,OAAK3I,cAAP,kGAGvB,OAAOZ,EAGTya,kBACE,OAAO1N,eAAee,KAAK/H,KAAKoD,SAAUuQ,eAAe,GAG3DgC,gBACE,MAAMC,EAAiB5V,KAAKoD,SAASpI,WAErC,GAAI4a,EAAepa,UAAUC,SApON,WAqOrB,OAAOyY,gBAGT,GAAI0B,EAAepa,UAAUC,SAvOJ,aAwOvB,OAAO0Y,eAIT,MAAM0B,EAAkF,QAA1Ehd,iBAAiBmH,KAAKyU,OAAOqB,iBAAiB,iBAAiBzd,OAE7E,OAAIud,EAAepa,UAAUC,SAhPP,UAiPboa,EAAQ9B,iBAAmBD,cAG7B+B,EAAQ5B,oBAAsBD,iBAGvCY,gBACE,OAA0D,OAAnD5U,KAAKoD,SAASqB,QAAS,WAGhCsR,aACE,MAAM1P,OAAEA,GAAWrG,KAAKyL,QAExB,MAAsB,iBAAXpF,EACFA,EAAOjO,MAAM,KAAK4d,IAAI1Q,GAAOvM,OAAOiW,SAAS1J,EAAK,KAGrC,mBAAXe,EACF4P,GAAc5P,EAAO4P,EAAYjW,KAAKoD,UAGxCiD,EAGT6O,mBACE,MAAMgB,EAAwB,CAC5BC,UAAWnW,KAAK2V,gBAChBP,UAAW,CAAC,CACVtY,KAAM,kBACNsZ,QAAS,CACPhC,SAAUpU,KAAKyL,QAAQ2I,WAG3B,CACEtX,KAAM,SACNsZ,QAAS,CACP/P,OAAQrG,KAAK+V,iBAanB,MAP6B,WAAzB/V,KAAKyL,QAAQtQ,UACf+a,EAAsBd,UAAY,CAAC,CACjCtY,KAAM,cACNwY,SAAS,KAIN,IACFY,KACsC,mBAA9BlW,KAAKyL,QAAQ6I,aAA8BtU,KAAKyL,QAAQ6I,aAAa4B,GAAyBlW,KAAKyL,QAAQ6I,cAMlG/Q,yBAACzL,EAASmC,GAChC,IAAI4K,EAAOrH,KAAKM,IAAIhG,EAASuL,YAO7B,GAJKwB,IACHA,EAAO,IAAI0P,SAASzc,EAHY,iBAAXmC,EAAsBA,EAAS,OAMhC,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjB4K,EAAK5K,GACd,MAAM,IAAIW,UAAW,oBAAmBX,MAG1C4K,EAAK5K,MAIasJ,uBAACtJ,GACrB,OAAO+F,KAAK4E,MAAK,WACf2P,SAAS8B,kBAAkBrW,KAAM/F,MAIpBsJ,kBAAClE,GAChB,GAAIA,EAAO,CACT,GAlVqB,IAkVjBA,EAAMoG,QAAiD,UAAfpG,EAAMK,MArVxC,QAqV4DL,EAAM3B,IAC1E,OAGF,GAAI,8BAA8B/C,KAAK0E,EAAMU,OAAOiO,SAClD,OAIJ,MAAMsI,EAAUtP,eAAeC,KAAKhC,wBAEpC,IAAK,IAAIhF,EAAI,EAAGM,EAAM+V,EAAQpW,OAAQD,EAAIM,EAAKN,IAAK,CAClD,MAAMsW,EAAU/Y,KAAKM,IAAIwY,EAAQrW,GAAIoD,YAC/BoL,EAAgB,CACpBA,cAAe6H,EAAQrW,IAOzB,GAJIZ,GAAwB,UAAfA,EAAMK,OACjB+O,EAAc+H,WAAanX,IAGxBkX,EACH,SAGF,MAAME,EAAeF,EAAQ9B,MAC7B,GAAK6B,EAAQrW,GAAGzE,UAAUC,SA9VR,QA8VlB,CAIA,GAAI4D,EAAO,CAET,GAAI,CAACkX,EAAQnT,UAAUsT,KAAK5e,GAAWuH,EAAMsX,eAAeze,SAASJ,IACnE,SAIF,GAAmB,UAAfuH,EAAMK,MA1XF,QA0XsBL,EAAM3B,KAAmB+Y,EAAahb,SAAS4D,EAAMU,QACjF,SAIcP,aAAawC,QAAQsU,EAAQrW,GAAIoQ,aAAY5B,GACjDnM,mBAMV,iBAAkB3K,SAASkE,iBAC7B,GAAGqL,UAAUvP,SAAS4E,KAAK+K,UACxBjN,QAAQ6W,GAAQ1R,aAAaC,IAAIyR,EAAM,YAAa,MV3O5C,gBU8OboF,EAAQrW,GAAGmF,aAAa,gBAAiB,SAErCmR,EAAQ/B,SACV+B,EAAQ/B,QAAQiB,UAGlBgB,EAAajb,UAAU4C,OAhYL,QAiYlBkY,EAAQrW,GAAGzE,UAAU4C,OAjYH,QAkYlBsH,YAAYE,oBAAoB6Q,EAAc,UAC9CjX,aAAawC,QAAQsU,EAAQrW,GAAIqQ,eAAc7B,MAIxBlL,4BAACzL,GAC1B,OAAOU,uBAAuBV,IAAYA,EAAQkD,WAGxBuI,6BAAClE,GAQ3B,GAAI,kBAAkB1E,KAAK0E,EAAMU,OAAOiO,SAra1B,UAsaZ3O,EAAM3B,KAvaO,WAuae2B,EAAM3B,MAnajB,cAoaf2B,EAAM3B,KAraO,YAqamB2B,EAAM3B,KACtC2B,EAAMU,OAAO0E,QAAQkP,iBACtBT,eAAevY,KAAK0E,EAAM3B,KAC3B,OAMF,GAHA2B,EAAM2D,iBACN3D,EAAMuX,kBAEF5W,KAAKtE,UAAYsE,KAAKxE,UAAUC,SA/ZZ,YAgatB,OAGF,MAAMyU,EAASqE,SAASQ,qBAAqB/U,MACvC6U,EAAW7U,KAAKxE,UAAUC,SAnaZ,QAqapB,GAxbe,WAwbX4D,EAAM3B,IAIR,OAHesC,KAAKwH,QAAQvC,wBAAwBjF,KAAOgH,eAAeY,KAAK5H,KAAMiF,wBAAsB,IACpGuQ,aACPjB,SAASO,aAIX,IAAKD,IA5bY,YA4bCxV,EAAM3B,KA3bL,cA2b6B2B,EAAM3B,KAGpD,YAFesC,KAAKwH,QAAQvC,wBAAwBjF,KAAOgH,eAAeY,KAAK5H,KAAMiF,wBAAsB,IACpG4R,QAIT,IAAKhC,GApcS,UAocGxV,EAAM3B,IAErB,YADA6W,SAASO,aAIX,MAAMgC,EAAQ9P,eAAeC,KAAK4M,uBAAwB3D,GAAQjK,OAAOnL,WAEzE,IAAKgc,EAAM5W,OACT,OAGF,IAAI0M,EAAQkK,EAAM7I,QAAQ5O,EAAMU,QA7cf,YAgdbV,EAAM3B,KAAwBkP,EAAQ,GACxCA,IAhdiB,cAodfvN,EAAM3B,KAA0BkP,EAAQkK,EAAM5W,OAAS,GACzD0M,IAIFA,GAAmB,IAAXA,EAAe,EAAIA,EAE3BkK,EAAMlK,GAAO4I,SAUjBhW,aAAaiC,GAAG9J,SAAUyb,uBAAwBnO,uBAAsBsP,SAASwC,uBACjFvX,aAAaiC,GAAG9J,SAAUyb,uBAAwBO,cAAeY,SAASwC,uBAC1EvX,aAAaiC,GAAG9J,SAAUmM,uBAAsByQ,SAASO,YACzDtV,aAAaiC,GAAG9J,SAAU0b,qBAAsBkB,SAASO,YACzDtV,aAAaiC,GAAG9J,SAAUmM,uBAAsBmB,wBAAsB,SAAU5F,GAC9EA,EAAM2D,iBACNuR,SAAS8B,kBAAkBrW,SAU7BnD,mBAAmB2G,OAAM+Q,UC/fzB,MAAM/Q,OAAO,QACPH,WAAW,WACXI,YAAa,YACbC,eAAe,YACfkP,aAAa,SAEbvK,UAAU,CACd2O,UAAU,EACVzO,UAAU,EACViN,OAAO,GAGH5M,cAAc,CAClBoO,SAAU,mBACVzO,SAAU,UACViN,MAAO,WAGHnF,aAAc,gBACd4G,qBAAwB,yBACxB3G,eAAgB,kBAChBH,aAAc,gBACdC,cAAe,iBACf8G,gBAAiB,mBACjBC,aAAgB,kBAChBC,sBAAuB,yBACvBC,sBAAyB,2BACzBC,sBAAyB,2BACzBC,wBAA2B,6BAC3BzT,uBAAwB,0BAExB0T,8BAAgC,0BAChCC,oBAAsB,iBACtBC,gBAAkB,aAClB1T,kBAAkB,OAClBC,kBAAkB,OAClB0T,kBAAoB,eAEpBC,gBAAkB,gBAClBC,oBAAsB,cACtB5S,uBAAuB,2BACvB6S,wBAAwB,4BACxBC,yBAAyB,oDACzBC,0BAA0B,cAQhC,MAAMC,cAAc/U,cAClBC,YAAYrL,EAASmC,GACnB+Q,MAAMlT,GAENkI,KAAKyL,QAAUzL,KAAK0L,WAAWzR,GAC/B+F,KAAKkY,QAAUlR,eAAeK,QAlBV,gBAkBmCrH,KAAKoD,UAC5DpD,KAAKmY,UAAY,KACjBnY,KAAKoY,UAAW,EAChBpY,KAAKqY,oBAAqB,EAC1BrY,KAAKsY,sBAAuB,EAC5BtY,KAAK8Q,kBAAmB,EACxB9Q,KAAKuY,gBAAkB,EAKPlQ,qBAChB,OAAOA,UAGUhF,sBACjB,MAvEa,WA4Ef8B,OAAOsJ,GACL,OAAOzO,KAAKoY,SAAWpY,KAAKyR,OAASzR,KAAK0R,KAAKjD,GAGjDiD,KAAKjD,GACH,GAAIzO,KAAKoY,UAAYpY,KAAK8Q,iBACxB,OAGE9Q,KAAKwY,gBACPxY,KAAK8Q,kBAAmB,GAG1B,MAAM2H,EAAYjZ,aAAawC,QAAQhC,KAAKoD,SAAU+M,aAAY,CAChE1B,cAAAA,IAGEzO,KAAKoY,UAAYK,EAAUnW,mBAI/BtC,KAAKoY,UAAW,EAEhBpY,KAAK0Y,kBACL1Y,KAAK2Y,gBAEL3Y,KAAK4Y,gBAEL5Y,KAAK6Y,kBACL7Y,KAAK8Y,kBAELtZ,aAAaiC,GAAGzB,KAAKoD,SAAUgU,sBAAqBU,wBAAuBzY,GAASW,KAAKyR,KAAKpS,IAE9FG,aAAaiC,GAAGzB,KAAKkY,QAASX,wBAAyB,KACrD/X,aAAakC,IAAI1B,KAAKoD,SAAUkU,sBAAuBjY,IACjDA,EAAMU,SAAWC,KAAKoD,WACxBpD,KAAKsY,sBAAuB,OAKlCtY,KAAK+Y,cAAc,IAAM/Y,KAAKgZ,aAAavK,KAG7CgD,KAAKpS,GAKH,GAJIA,GACFA,EAAM2D,kBAGHhD,KAAKoY,UAAYpY,KAAK8Q,iBACzB,OAKF,GAFkBtR,aAAawC,QAAQhC,KAAKoD,SAAUiN,cAExC/N,iBACZ,OAGFtC,KAAKoY,UAAW,EAChB,MAAMa,EAAajZ,KAAKwY,cAgBxB,GAdIS,IACFjZ,KAAK8Q,kBAAmB,GAG1B9Q,KAAK6Y,kBACL7Y,KAAK8Y,kBAELtZ,aAAaC,IAAI9H,SAAUuf,iBAE3BlX,KAAKoD,SAAS5H,UAAU4C,OAjHJ,QAmHpBoB,aAAaC,IAAIO,KAAKoD,SAAUgU,uBAChC5X,aAAaC,IAAIO,KAAKkY,QAASX,yBAE3B0B,EAAY,CACd,MAAMvgB,EAAqBD,iCAAiCuH,KAAKoD,UAEjE5D,aAAakC,IAAI1B,KAAKoD,SAAU,gBAAiB/D,GAASW,KAAKkZ,WAAW7Z,IAC1E9F,qBAAqByG,KAAKoD,SAAU1K,QAEpCsH,KAAKkZ,aAIT5V,UACE,CAAC1K,OAAQoH,KAAKoD,SAAUpD,KAAKkY,SAC1B7d,QAAQ8e,GAAe3Z,aAAaC,IAAI0Z,EAnK5B,cAqKfnO,MAAM1H,UAON9D,aAAaC,IAAI9H,SAAUuf,iBAE3BlX,KAAKyL,QAAU,KACfzL,KAAKkY,QAAU,KACflY,KAAKmY,UAAY,KACjBnY,KAAKoY,SAAW,KAChBpY,KAAKqY,mBAAqB,KAC1BrY,KAAKsY,qBAAuB,KAC5BtY,KAAK8Q,iBAAmB,KACxB9Q,KAAKuY,gBAAkB,KAGzBa,eACEpZ,KAAK4Y,gBAKPlN,WAAWzR,GAMT,OALAA,EAAS,IACJoO,aACApO,GAELF,gBAAgByJ,OAAMvJ,EAAQ2O,eACvB3O,EAGT+e,aAAavK,GACX,MAAMwK,EAAajZ,KAAKwY,cAClBa,EAAYrS,eAAeK,QApKT,cAoKsCrH,KAAKkY,SAE9DlY,KAAKoD,SAASpI,YAAcgF,KAAKoD,SAASpI,WAAW1B,WAAagC,KAAKC,cAE1E5D,SAAS4E,KAAK+c,YAAYtZ,KAAKoD,UAGjCpD,KAAKoD,SAASrI,MAAMI,QAAU,QAC9B6E,KAAKoD,SAASyC,gBAAgB,eAC9B7F,KAAKoD,SAASgC,aAAa,cAAc,GACzCpF,KAAKoD,SAASgC,aAAa,OAAQ,UACnCpF,KAAKoD,SAASqD,UAAY,EAEtB4S,IACFA,EAAU5S,UAAY,GAGpBwS,GACF9c,OAAO6D,KAAKoD,UAGdpD,KAAKoD,SAAS5H,UAAUuS,IA7LJ,QA+LhB/N,KAAKyL,QAAQ+J,OACfxV,KAAKuZ,gBAGP,MAAMC,EAAqB,KACrBxZ,KAAKyL,QAAQ+J,OACfxV,KAAKoD,SAASoS,QAGhBxV,KAAK8Q,kBAAmB,EACxBtR,aAAawC,QAAQhC,KAAKoD,SAAUgN,cAAa,CAC/C3B,cAAAA,KAIJ,GAAIwK,EAAY,CACd,MAAMvgB,EAAqBD,iCAAiCuH,KAAKkY,SAEjE1Y,aAAakC,IAAI1B,KAAKkY,QAAS,gBAAiBsB,GAChDjgB,qBAAqByG,KAAKkY,QAASxf,QAEnC8gB,IAIJD,gBACE/Z,aAAaC,IAAI9H,SAAUuf,iBAC3B1X,aAAaiC,GAAG9J,SAAUuf,gBAAe7X,IACnC1H,WAAa0H,EAAMU,QACnBC,KAAKoD,WAAa/D,EAAMU,QACvBC,KAAKoD,SAAS3H,SAAS4D,EAAMU,SAChCC,KAAKoD,SAASoS,UAKpBqD,kBACM7Y,KAAKoY,SACP5Y,aAAaiC,GAAGzB,KAAKoD,SAAUiU,sBAAuBhY,IAChDW,KAAKyL,QAAQlD,UArQN,WAqQkBlJ,EAAM3B,KACjC2B,EAAM2D,iBACNhD,KAAKyR,QACKzR,KAAKyL,QAAQlD,UAxQd,WAwQ0BlJ,EAAM3B,KACzCsC,KAAKyZ,+BAITja,aAAaC,IAAIO,KAAKoD,SAAUiU,uBAIpCyB,kBACM9Y,KAAKoY,SACP5Y,aAAaiC,GAAG7I,OAAQue,aAAc,IAAMnX,KAAK4Y,iBAEjDpZ,aAAaC,IAAI7G,OAAQue,cAI7B+B,aACElZ,KAAKoD,SAASrI,MAAMI,QAAU,OAC9B6E,KAAKoD,SAASgC,aAAa,eAAe,GAC1CpF,KAAKoD,SAASyC,gBAAgB,cAC9B7F,KAAKoD,SAASyC,gBAAgB,QAC9B7F,KAAK8Q,kBAAmB,EACxB9Q,KAAK+Y,cAAc,KACjBphB,SAAS4E,KAAKf,UAAU4C,OAnQN,cAoQlB4B,KAAK0Z,oBACL1Z,KAAK2Z,kBACLna,aAAawC,QAAQhC,KAAKoD,SAAUkN,kBAIxCsJ,kBACE5Z,KAAKmY,UAAUnd,WAAW2J,YAAY3E,KAAKmY,WAC3CnY,KAAKmY,UAAY,KAGnBY,cAActc,GACZ,MAAMwc,EAAajZ,KAAKwY,cACxB,GAAIxY,KAAKoY,UAAYpY,KAAKyL,QAAQuL,SAAU,CAiC1C,GAhCAhX,KAAKmY,UAAYxgB,SAASkiB,cAAc,OACxC7Z,KAAKmY,UAAU2B,UApRO,iBAsRlBb,GACFjZ,KAAKmY,UAAU3c,UAAUuS,IArRT,QAwRlBpW,SAAS4E,KAAK+c,YAAYtZ,KAAKmY,WAE/B3Y,aAAaiC,GAAGzB,KAAKoD,SAAUgU,sBAAqB/X,IAC9CW,KAAKsY,qBACPtY,KAAKsY,sBAAuB,EAI1BjZ,EAAMU,SAAWV,EAAM0a,gBAIG,WAA1B/Z,KAAKyL,QAAQuL,SACfhX,KAAKyZ,6BAELzZ,KAAKyR,UAILwH,GACF9c,OAAO6D,KAAKmY,WAGdnY,KAAKmY,UAAU3c,UAAUuS,IA9SP,SAgTbkL,EAEH,YADAxc,IAIF,MAAMud,EAA6BvhB,iCAAiCuH,KAAKmY,WAEzE3Y,aAAakC,IAAI1B,KAAKmY,UAAW,gBAAiB1b,GAClDlD,qBAAqByG,KAAKmY,UAAW6B,QAChC,IAAKha,KAAKoY,UAAYpY,KAAKmY,UAAW,CAC3CnY,KAAKmY,UAAU3c,UAAU4C,OA1TP,QA4TlB,MAAM6b,EAAiB,KACrBja,KAAK4Z,kBACLnd,KAGF,GAAIwc,EAAY,CACd,MAAMe,EAA6BvhB,iCAAiCuH,KAAKmY,WACzE3Y,aAAakC,IAAI1B,KAAKmY,UAAW,gBAAiB8B,GAClD1gB,qBAAqByG,KAAKmY,UAAW6B,QAErCC,SAGFxd,IAIJ+b,cACE,OAAOxY,KAAKoD,SAAS5H,UAAUC,SA/UX,QAkVtBge,6BAEE,GADkBja,aAAawC,QAAQhC,KAAKoD,SAAU6T,sBACxC3U,iBACZ,OAGF,MAAM4X,EAAqBla,KAAKoD,SAAS+W,aAAexiB,SAASkE,gBAAgBue,aAE5EF,IACHla,KAAKoD,SAASrI,MAAMsf,UAAY,UAGlCra,KAAKoD,SAAS5H,UAAUuS,IA5VF,gBA6VtB,MAAMuM,EAA0B7hB,iCAAiCuH,KAAKkY,SACtE1Y,aAAaC,IAAIO,KAAKoD,SAAU,iBAChC5D,aAAakC,IAAI1B,KAAKoD,SAAU,gBAAiB,KAC/CpD,KAAKoD,SAAS5H,UAAU4C,OAhWJ,gBAiWf8b,IACH1a,aAAakC,IAAI1B,KAAKoD,SAAU,gBAAiB,KAC/CpD,KAAKoD,SAASrI,MAAMsf,UAAY,KAElC9gB,qBAAqByG,KAAKoD,SAAUkX,MAGxC/gB,qBAAqByG,KAAKoD,SAAUkX,GACpCta,KAAKoD,SAASoS,QAOhBoD,gBACE,MAAMsB,EAAqBla,KAAKoD,SAAS+W,aAAexiB,SAASkE,gBAAgBue,eAE3Epa,KAAKqY,oBAAsB6B,IAAuBvd,SAAaqD,KAAKqY,qBAAuB6B,GAAsBvd,WACrHqD,KAAKoD,SAASrI,MAAMwf,YAAiBva,KAAKuY,gBAAP,OAGhCvY,KAAKqY,qBAAuB6B,IAAuBvd,UAAcqD,KAAKqY,oBAAsB6B,GAAsBvd,WACrHqD,KAAKoD,SAASrI,MAAMyf,aAAkBxa,KAAKuY,gBAAP,MAIxCmB,oBACE1Z,KAAKoD,SAASrI,MAAMwf,YAAc,GAClCva,KAAKoD,SAASrI,MAAMyf,aAAe,GAGrC9B,kBACE,MAAMpS,EAAO3O,SAAS4E,KAAKgK,wBAC3BvG,KAAKqY,mBAAqB7gB,KAAKijB,MAAMnU,EAAKI,KAAOJ,EAAKoU,OAAS9hB,OAAO+hB,WACtE3a,KAAKuY,gBAAkBvY,KAAK4a,qBAG9BjC,gBACM3Y,KAAKqY,qBACPrY,KAAK6a,sBAAsB9C,yBAAwB,eAAgB+C,GAAmBA,EAAkB9a,KAAKuY,iBAC7GvY,KAAK6a,sBAnYqB,cAmY0B,cAAeC,GAAmBA,EAAkB9a,KAAKuY,iBAC7GvY,KAAK6a,sBAAsB,OAAQ,eAAgBC,GAAmBA,EAAkB9a,KAAKuY,kBAG/F5gB,SAAS4E,KAAKf,UAAUuS,IAjZJ,cAoZtB8M,sBAAsB9iB,EAAUgjB,EAAWte,GACzCuK,eAAeC,KAAKlP,GACjBsC,QAAQvC,IACP,GAAIA,IAAYH,SAAS4E,MAAQ3D,OAAO+hB,WAAa7iB,EAAQkjB,YAAchb,KAAKuY,gBAC9E,OAGF,MAAM0C,EAAcnjB,EAAQiD,MAAMggB,GAC5BD,EAAkBliB,OAAOC,iBAAiBf,GAASijB,GACzDrV,YAAYC,iBAAiB7N,EAASijB,EAAWE,GACjDnjB,EAAQiD,MAAMggB,GAAate,EAAS1D,OAAOC,WAAW8hB,IAAoB,OAIhFnB,kBACE3Z,KAAKkb,wBAAwBnD,yBAAwB,gBACrD/X,KAAKkb,wBA1ZuB,cA0Z0B,eACtDlb,KAAKkb,wBAAwB,OAAQ,gBAGvCA,wBAAwBnjB,EAAUgjB,GAChC/T,eAAeC,KAAKlP,GAAUsC,QAAQvC,IACpC,MAAM0C,EAAQkL,YAAYU,iBAAiBtO,EAASijB,QAC/B,IAAVvgB,GAAyB1C,IAAYH,SAAS4E,KACvDzE,EAAQiD,MAAMggB,GAAa,IAE3BrV,YAAYE,oBAAoB9N,EAASijB,GACzCjjB,EAAQiD,MAAMggB,GAAavgB,KAKjCogB,qBACE,MAAMO,EAAYxjB,SAASkiB,cAAc,OACzCsB,EAAUrB,UAxbwB,0BAyblCniB,SAAS4E,KAAK+c,YAAY6B,GAC1B,MAAMC,EAAiBD,EAAU5U,wBAAwB8U,MAAQF,EAAUH,YAE3E,OADArjB,SAAS4E,KAAKoI,YAAYwW,GACnBC,EAKa7X,uBAACtJ,EAAQwU,GAC7B,OAAOzO,KAAK4E,MAAK,WACf,IAAIC,EAAOrH,KAAKM,IAAIkC,KAjeT,YAkeX,MAAMyL,EAAU,IACXpD,aACA3C,YAAYI,kBAAkB9F,SACX,iBAAX/F,GAAuBA,EAASA,EAAS,IAOtD,GAJK4K,IACHA,EAAO,IAAIoT,MAAMjY,KAAMyL,IAGH,iBAAXxR,EAAqB,CAC9B,QAA4B,IAAjB4K,EAAK5K,GACd,MAAM,IAAIW,UAAW,oBAAmBX,MAG1C4K,EAAK5K,GAAQwU,QAYrBjP,aAAaiC,GAAG9J,SAAUmM,uBAAsBmB,wBAAsB,SAAU5F,GAC9E,MAAMU,EAASvH,uBAAuBwH,MAEjB,MAAjBA,KAAKgO,SAAoC,SAAjBhO,KAAKgO,SAC/B3O,EAAM2D,iBAGRxD,aAAakC,IAAI3B,EAAQoQ,aAAYsI,IAC/BA,EAAUnW,kBAKd9C,aAAakC,IAAI3B,EAAQuQ,eAAc,KACjCxV,UAAUkF,OACZA,KAAKwV,YAKX,IAAI3Q,EAAOrH,KAAKM,IAAIiC,EAjhBL,YAkhBf,IAAK8E,EAAM,CACT,MAAM5K,EAAS,IACVyL,YAAYI,kBAAkB/F,MAC9B2F,YAAYI,kBAAkB9F,OAGnC6E,EAAO,IAAIoT,MAAMlY,EAAQ9F,GAG3B4K,EAAKM,OAAOnF,SAUdnD,mBAAmB2G,OAAMyU,OCzjBzB,MAAMF,uBAAyB,uCACzBC,wBAA0B,cAE1BsD,SAAW,KAEf,MAAMC,EAAgB5jB,SAASkE,gBAAgBmf,YAC/C,OAAOxjB,KAAK0V,IAAItU,OAAO+hB,WAAaY,IAGhC9J,KAAO,CAAC4J,EAAQC,cACpB3jB,SAAS4E,KAAKxB,MAAMygB,SAAW,SAC/BX,sBAAsB9C,uBAAwB,eAAgB+C,GAAmBA,EAAkBO,GACnGR,sBAX8B,cAWiB,cAAeC,GAAmBA,EAAkBO,GACnGR,sBAAsB,OAAQ,eAAgBC,GAAmBA,EAAkBO,IAG/ER,sBAAwB,CAAC9iB,EAAUgjB,EAAWte,KAClD,MAAM2e,EAAiBE,WACvBtU,eAAeC,KAAKlP,GACjBsC,QAAQvC,IACP,GAAIA,IAAYH,SAAS4E,MAAQ3D,OAAO+hB,WAAa7iB,EAAQkjB,YAAcI,EACzE,OAGF,MAAMH,EAAcnjB,EAAQiD,MAAMggB,GAC5BD,EAAkBliB,OAAOC,iBAAiBf,GAASijB,GACzDrV,YAAYC,iBAAiB7N,EAASijB,EAAWE,GACjDnjB,EAAQiD,MAAMggB,GAAate,EAAS1D,OAAOC,WAAW8hB,IAAoB,QAI1EW,MAAQ,KACZ9jB,SAAS4E,KAAKxB,MAAMygB,SAAW,OAC/BN,wBAAwBnD,uBAAwB,gBAChDmD,wBAjC8B,cAiCmB,eACjDA,wBAAwB,OAAQ,iBAG5BA,wBAA0B,CAACnjB,EAAUgjB,KACzC/T,eAAeC,KAAKlP,GAAUsC,QAAQvC,IACpC,MAAM0C,EAAQkL,YAAYU,iBAAiBtO,EAASijB,QAC/B,IAAVvgB,GAAyB1C,IAAYH,SAAS4E,KACvDzE,EAAQiD,MAAM2gB,eAAeX,IAE7BrV,YAAYE,oBAAoB9N,EAASijB,GACzCjjB,EAAQiD,MAAMggB,GAAavgB,MC1B3BgJ,OAAO,YACPH,WAAW,eACXI,YAAa,gBACbC,eAAe,YACfkG,sBAAuB,6BACvBgJ,WAAa,SAEbvK,UAAU,CACd2O,UAAU,EACVzO,UAAU,EACVoT,QAAQ,GAGJ/S,cAAc,CAClBoO,SAAU,UACVzO,SAAU,UACVoT,OAAQ,WAGJC,yBAA2B,qBAC3B3X,kBAAkB,OAClB4X,oBAAsB,qBACtBC,cAAgB,kBAChBC,gBAAmB,uCAEnB5L,aAAc,oBACdC,cAAe,qBACfC,aAAc,oBACdC,eAAgB,sBAChB4G,cAAiB,uBACjBpT,uBAAwB,8BACxBsT,sBAAuB,6BAEvBU,wBAAwB,gCACxB7S,uBAAuB,+BAQ7B,MAAM+W,kBAAkB9Y,cACtBC,YAAYrL,EAASmC,GACnB+Q,MAAMlT,GAENkI,KAAKyL,QAAUzL,KAAK0L,WAAWzR,GAC/B+F,KAAKoY,UAAW,EAChBpY,KAAKiM,qBAKW5D,qBAChB,OAAOA,UAGUhF,sBACjB,OAAOA,WAKT8B,OAAOsJ,GACL,OAAOzO,KAAKoY,SAAWpY,KAAKyR,OAASzR,KAAK0R,KAAKjD,GAGjDiD,KAAKjD,GACCzO,KAAKoY,UAIS5Y,aAAawC,QAAQhC,KAAKoD,SAAU+M,aAAY,CAAE1B,cAAAA,IAEtDnM,mBAIdtC,KAAKoY,UAAW,EAChBpY,KAAKoD,SAASrI,MAAMK,WAAa,UAE7B4E,KAAKyL,QAAQuL,UACfrf,SAAS4E,KAAKf,UAAUuS,IA/DG,sBAkExB/N,KAAKyL,QAAQkQ,QAChBM,OAGFjc,KAAKoD,SAAS5H,UAAUuS,IAAI8N,qBAC5B7b,KAAKoD,SAASyC,gBAAgB,eAC9B7F,KAAKoD,SAASgC,aAAa,cAAc,GACzCpF,KAAKoD,SAASgC,aAAa,OAAQ,UACnCpF,KAAKoD,SAAS5H,UAAUuS,IAzEJ,QAiFpBjU,WANyB,KACvBkG,KAAKoD,SAAS5H,UAAU4C,OAAOyd,qBAC/Brc,aAAawC,QAAQhC,KAAKoD,SAAUgN,cAAa,CAAE3B,cAAAA,IACnDzO,KAAKkc,uBAAuBlc,KAAKoD,WAGN3K,iCAAiCuH,KAAKoD,YAGrEqO,OACOzR,KAAKoY,WAIQ5Y,aAAawC,QAAQhC,KAAKoD,SAAUiN,cAExC/N,mBAIdtC,KAAKoD,SAAS5H,UAAUuS,IAAI8N,qBAC5Brc,aAAaC,IAAI9H,SAAUuf,eAC3BlX,KAAKoD,SAAS+Y,OACdnc,KAAKoY,UAAW,EAChBpY,KAAKoD,SAAS5H,UAAU4C,OAnGJ,QAuHpBtE,WAlByB,KACvBkG,KAAKoD,SAASgC,aAAa,eAAe,GAC1CpF,KAAKoD,SAASyC,gBAAgB,cAC9B7F,KAAKoD,SAASyC,gBAAgB,QAC9B7F,KAAKoD,SAASrI,MAAMK,WAAa,SAE7B4E,KAAKyL,QAAQuL,UACfrf,SAAS4E,KAAKf,UAAU4C,OA7GC,sBAgHtB4B,KAAKyL,QAAQkQ,SDtHtBhkB,SAAS4E,KAAKxB,MAAMygB,SAAW,OAC/BN,wBAAwBnD,uBAAwB,gBAChDmD,wBAjC8B,cAiCmB,eACjDA,wBAAwB,OAAQ,iBCuH5B1b,aAAawC,QAAQhC,KAAKoD,SAAUkN,gBACpCtQ,KAAKoD,SAAS5H,UAAU4C,OAAOyd,sBAGJpjB,iCAAiCuH,KAAKoD,aAKrEsI,WAAWzR,GAOT,OANAA,EAAS,IACJoO,aACA3C,YAAYI,kBAAkB9F,KAAKoD,aAChB,iBAAXnJ,EAAsBA,EAAS,IAE5CF,gBAAgByJ,OAAMvJ,EAAQ2O,eACvB3O,EAGTiiB,uBAAuBpkB,GACrB0H,aAAaC,IAAI9H,SAAUuf,eAC3B1X,aAAaiC,GAAG9J,SAAUuf,cAAe7X,IACnC1H,WAAa0H,EAAMU,QACrBjI,IAAYuH,EAAMU,QACjBjI,EAAQ2D,SAAS4D,EAAMU,SACxBjI,EAAQ0d,UAGZ1d,EAAQ0d,QAGVvJ,qBACEzM,aAAaiC,GAAGzB,KAAKoD,SAAUgU,sBAAqBU,wBAAuB,IAAM9X,KAAKyR,QAEtFjS,aAAaiC,GAAG9J,SAAU,UAAW0H,IAC/BW,KAAKyL,QAAQlD,UArKJ,WAqKgBlJ,EAAM3B,KACjCsC,KAAKyR,SAITjS,aAAaiC,GAAG9J,SAAUmM,uBAAsBzE,IAC9C,MAAMU,EAASiH,eAAeK,QAAQ/O,uBAAuB+G,EAAMU,SAC9DC,KAAKoD,SAAS3H,SAAS4D,EAAMU,SAAWA,IAAWC,KAAKoD,UAC3DpD,KAAKyR,SAOWlO,uBAACtJ,GACrB,OAAO+F,KAAK4E,MAAK,WACf,MAAMC,EAAOrH,KAAKM,IAAIkC,KAAMqD,aAAa,IAAI2Y,UAAUhc,KAAwB,iBAAX/F,EAAsBA,EAAS,IAEnG,GAAsB,iBAAXA,EAAX,CAIA,QAAqBmiB,IAAjBvX,EAAK5K,IAAyBA,EAAO9B,WAAW,MAAmB,gBAAX8B,EAC1D,MAAM,IAAIW,UAAW,oBAAmBX,MAG1C4K,EAAK5K,GAAQ+F,WAWnBR,aAAaiC,GAAG9J,SAAUmM,uBAAsBmB,wBAAsB,SAAU5F,GAC9E,MAAMU,EAASvH,uBAAuBwH,MAMtC,GAJI,CAAC,IAAK,QAAQ9H,SAAS8H,KAAKgO,UAC9B3O,EAAM2D,iBAGJ3H,WAAW2E,MACb,OAGFR,aAAakC,IAAI3B,EAAQuQ,eAAc,KAEjCxV,UAAUkF,OACZA,KAAKwV,UAKT,MAAM6G,EAAerV,eAAeK,QAAQ0U,iBACxCM,GAAgBA,IAAiBtc,IAIxBvC,KAAKM,IAAIiC,EAAQsD,aAAa,IAAI2Y,UAAUjc,IAEpDoF,OAAOnF,SAGdR,aAAaiC,GAAG7I,OAAQgR,sBAAqB,KAC3C5C,eAAeC,KAAK6U,eAAezhB,QAAQiiB,IAAO9e,KAAKM,IAAIwe,EAAIjZ,aAAa,IAAI2Y,UAAUM,IAAK5K,UASjG7U,mBAAmB2G,OAAMwY,WC7QzB,MAAMO,SAAW,IAAIxd,IAAI,CACvB,aACA,OACA,OACA,WACA,WACA,SACA,MACA,eAGIyd,uBAAyB,iBAOzBC,iBAAmB,6DAOnBC,iBAAmB,qIAEnBC,iBAAmB,CAACC,EAAMC,KAC9B,MAAMC,EAAWF,EAAKG,SAAS1lB,cAE/B,GAAIwlB,EAAqB3kB,SAAS4kB,GAChC,OAAIP,SAAS3e,IAAIkf,IACR1b,QAAQqb,iBAAiB9hB,KAAKiiB,EAAKI,YAAcN,iBAAiB/hB,KAAKiiB,EAAKI,YAMvF,MAAMC,EAASJ,EAAqB5W,OAAOiX,GAAaA,aAAqBxiB,QAG7E,IAAK,IAAIuF,EAAI,EAAGM,EAAM0c,EAAO/c,OAAQD,EAAIM,EAAKN,IAC5C,GAAIgd,EAAOhd,GAAGtF,KAAKmiB,GACjB,OAAO,EAIX,OAAO,GAGIK,iBAAmB,CAE9BC,IAAK,CAAC,QAAS,MAAO,KAAM,OAAQ,OAAQZ,wBAC5Ca,EAAG,CAAC,SAAU,OAAQ,QAAS,OAC/BC,KAAM,GACNC,EAAG,GACHC,GAAI,GACJC,IAAK,GACLC,KAAM,GACNC,IAAK,GACLC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJle,EAAG,GACHme,IAAK,CAAC,MAAO,SAAU,MAAO,QAAS,QAAS,UAChDC,GAAI,GACJC,GAAI,GACJC,EAAG,GACHC,IAAK,GACLC,EAAG,GACHC,MAAO,GACPC,KAAM,GACNC,IAAK,GACLC,IAAK,GACLC,OAAQ,GACRC,EAAG,GACHC,GAAI,IAGC,SAASC,aAAaC,EAAYC,EAAWC,GAClD,IAAKF,EAAWhf,OACd,OAAOgf,EAGT,GAAIE,GAAoC,mBAAfA,EACvB,OAAOA,EAAWF,GAGpB,MACMG,GADY,IAAIzmB,OAAO0mB,WACKC,gBAAgBL,EAAY,aACxDM,EAAgBrlB,OAAOC,KAAK+kB,GAC5BM,EAAW,GAAGvY,UAAUmY,EAAgB9iB,KAAKuD,iBAAiB,MAEpE,IAAK,IAAIG,EAAI,EAAGM,EAAMkf,EAASvf,OAAQD,EAAIM,EAAKN,IAAK,CACnD,MAAMqc,EAAKmD,EAASxf,GACdyf,EAASpD,EAAGS,SAAS1lB,cAE3B,IAAKmoB,EAActnB,SAASwnB,GAAS,CACnCpD,EAAGthB,WAAW2J,YAAY2X,GAE1B,SAGF,MAAMqD,EAAgB,GAAGzY,UAAUoV,EAAGvW,YAChC6Z,EAAoB,GAAG1Y,OAAOiY,EAAU,MAAQ,GAAIA,EAAUO,IAAW,IAE/EC,EAActlB,QAAQuiB,IACfD,iBAAiBC,EAAMgD,IAC1BtD,EAAGzW,gBAAgB+W,EAAKG,YAK9B,OAAOsC,EAAgB9iB,KAAKsjB,UCzF9B,MAAMrc,OAAO,UACPH,WAAW,aACXI,YAAa,cACbqc,eAAe,aACfC,qBAAqB,IAAIrlB,OAAQ,wBAA6B,KAC9DslB,sBAAwB,IAAIjhB,IAAI,CAAC,WAAY,YAAa,eAE1D6J,cAAc,CAClBqX,UAAW,UACXC,SAAU,SACVC,MAAO,4BACPne,QAAS,SACToe,MAAO,kBACPC,KAAM,UACNtoB,SAAU,mBACVoe,UAAW,oBACX9P,OAAQ,0BACRwL,UAAW,2BACXyO,mBAAoB,QACpBlM,SAAU,mBACVmM,YAAa,oBACbC,SAAU,UACVpB,WAAY,kBACZD,UAAW,SACX7K,aAAc,0BAGVmM,cAAgB,CACpBC,KAAM,OACNC,IAAK,MACLC,MAAOjkB,QAAU,OAAS,QAC1BkkB,OAAQ,SACRC,KAAMnkB,QAAU,QAAU,QAGtB0L,UAAU,CACd4X,WAAW,EACXC,SAAU,+GAIVle,QAAS,cACTme,MAAO,GACPC,MAAO,EACPC,MAAM,EACNtoB,UAAU,EACVoe,UAAW,MACX9P,OAAQ,CAAC,EAAG,GACZwL,WAAW,EACXyO,mBAAoB,CAAC,MAAO,QAAS,SAAU,QAC/ClM,SAAU,kBACVmM,YAAa,GACbC,UAAU,EACVpB,WAAY,KACZD,UAAWhC,iBACX7I,aAAc,MAGVlb,QAAQ,CACZ2nB,KAAO,kBACPC,OAAS,oBACTC,KAAO,kBACPC,MAAQ,mBACRC,SAAW,sBACXC,MAAQ,mBACRC,QAAU,qBACVC,SAAW,sBACXC,WAAa,wBACbC,WAAa,yBAGTxd,kBAAkB,OAClByd,iBAAmB,QACnBxd,kBAAkB,OAElByd,iBAAmB,OACnBC,gBAAkB,MAElBC,uBAAyB,iBAEzBC,cAAgB,QAChBC,cAAgB,QAChBC,cAAgB,QAChBC,eAAiB,SAQvB,MAAMC,gBAAgB/e,cACpBC,YAAYrL,EAASmC,GACnB,QAAsB,IAAX+a,OACT,MAAM,IAAIpa,UAAU,+DAGtBoQ,MAAMlT,GAGNkI,KAAKkiB,YAAa,EAClBliB,KAAKmiB,SAAW,EAChBniB,KAAKoiB,YAAc,GACnBpiB,KAAKqiB,eAAiB,GACtBriB,KAAKwU,QAAU,KAGfxU,KAAK/F,OAAS+F,KAAK0L,WAAWzR,GAC9B+F,KAAKsiB,IAAM,KAEXtiB,KAAKuiB,gBAKWla,qBAChB,OAAOA,UAGM7E,kBACb,OAAOA,OAGUH,sBACjB,OAAOA,WAGOjK,mBACd,OAAOA,QAGWqK,uBAClB,OAAOA,YAGamF,yBACpB,OAAOA,cAKT4Z,SACExiB,KAAKkiB,YAAa,EAGpBO,UACEziB,KAAKkiB,YAAa,EAGpBQ,gBACE1iB,KAAKkiB,YAAcliB,KAAKkiB,WAG1B/c,OAAO9F,GACL,GAAKW,KAAKkiB,WAIV,GAAI7iB,EAAO,CACT,MAAMkX,EAAUvW,KAAK2iB,6BAA6BtjB,GAElDkX,EAAQ8L,eAAexL,OAASN,EAAQ8L,eAAexL,MAEnDN,EAAQqM,uBACVrM,EAAQsM,OAAO,KAAMtM,GAErBA,EAAQuM,OAAO,KAAMvM,OAElB,CACL,GAAIvW,KAAK+iB,gBAAgBvnB,UAAUC,SAhGjB,QAkGhB,YADAuE,KAAK8iB,OAAO,KAAM9iB,MAIpBA,KAAK6iB,OAAO,KAAM7iB,OAItBsD,UACEsK,aAAa5N,KAAKmiB,UAElB3iB,aAAaC,IAAIO,KAAKoD,SAAUpD,KAAKmD,YAAYM,WACjDjE,aAAaC,IAAIO,KAAKoD,SAASqB,QAAS,UAAwB,gBAAiBzE,KAAKgjB,mBAElFhjB,KAAKsiB,KAAOtiB,KAAKsiB,IAAItnB,YACvBgF,KAAKsiB,IAAItnB,WAAW2J,YAAY3E,KAAKsiB,KAGvCtiB,KAAKkiB,WAAa,KAClBliB,KAAKmiB,SAAW,KAChBniB,KAAKoiB,YAAc,KACnBpiB,KAAKqiB,eAAiB,KAClBriB,KAAKwU,SACPxU,KAAKwU,QAAQiB,UAGfzV,KAAKwU,QAAU,KACfxU,KAAK/F,OAAS,KACd+F,KAAKsiB,IAAM,KACXtX,MAAM1H,UAGRoO,OACE,GAAoC,SAAhC1R,KAAKoD,SAASrI,MAAMI,QACtB,MAAM,IAAI8nB,MAAM,uCAGlB,IAAMjjB,KAAKkjB,kBAAmBljB,KAAKkiB,WACjC,OAGF,MAAMzJ,EAAYjZ,aAAawC,QAAQhC,KAAKoD,SAAUpD,KAAKmD,YAAY/J,MAAM6nB,MACvEkC,EAAavnB,eAAeoE,KAAKoD,UACjCggB,EAA4B,OAAfD,EACjBnjB,KAAKoD,SAASigB,cAAcxnB,gBAAgBJ,SAASuE,KAAKoD,UAC1D+f,EAAW1nB,SAASuE,KAAKoD,UAE3B,GAAIqV,EAAUnW,mBAAqB8gB,EACjC,OAGF,MAAMd,EAAMtiB,KAAK+iB,gBACXO,EAAQhsB,OAAO0I,KAAKmD,YAAYK,MAEtC8e,EAAIld,aAAa,KAAMke,GACvBtjB,KAAKoD,SAASgC,aAAa,mBAAoBke,GAE/CtjB,KAAKujB,aAEDvjB,KAAK/F,OAAOgmB,WACdqC,EAAI9mB,UAAUuS,IA/JI,QAkKpB,MAAMoI,EAA6C,mBAA1BnW,KAAK/F,OAAOkc,UACnCnW,KAAK/F,OAAOkc,UAAUhf,KAAK6I,KAAMsiB,EAAKtiB,KAAKoD,UAC3CpD,KAAK/F,OAAOkc,UAERqN,EAAaxjB,KAAKyjB,eAAetN,GACvCnW,KAAK0jB,oBAAoBF,GAEzB,MAAM3R,EAAY7R,KAAK2jB,gBACvBnmB,KAAKC,IAAI6kB,EAAKtiB,KAAKmD,YAAYE,SAAUrD,MAEpCA,KAAKoD,SAASigB,cAAcxnB,gBAAgBJ,SAASuE,KAAKsiB,OAC7DzQ,EAAUyH,YAAYgJ,GACtB9iB,aAAawC,QAAQhC,KAAKoD,SAAUpD,KAAKmD,YAAY/J,MAAM+nB,WAGzDnhB,KAAKwU,QACPxU,KAAKwU,QAAQkB,SAEb1V,KAAKwU,QAAUQ,OAAOO,aAAavV,KAAKoD,SAAUkf,EAAKtiB,KAAKkV,iBAAiBsO,IAG/ElB,EAAI9mB,UAAUuS,IArLM,QAuLpB,MAAMwS,EAAiD,mBAA5BvgB,KAAK/F,OAAOsmB,YAA6BvgB,KAAK/F,OAAOsmB,cAAgBvgB,KAAK/F,OAAOsmB,YACxGA,GACF+B,EAAI9mB,UAAUuS,OAAOwS,EAAYnoB,MAAM,MAOrC,iBAAkBT,SAASkE,iBAC7B,GAAGqL,UAAUvP,SAAS4E,KAAK+K,UAAUjN,QAAQvC,IAC3C0H,aAAaiC,GAAG3J,EAAS,af7Gd,iBeiHf,MAAM8rB,EAAW,KACf,MAAMC,EAAiB7jB,KAAKoiB,YAE5BpiB,KAAKoiB,YAAc,KACnB5iB,aAAawC,QAAQhC,KAAKoD,SAAUpD,KAAKmD,YAAY/J,MAAM8nB,OAvMzC,QAyMd2C,GACF7jB,KAAK8iB,OAAO,KAAM9iB,OAItB,GAAIA,KAAKsiB,IAAI9mB,UAAUC,SAnNH,QAmN8B,CAChD,MAAM/C,EAAqBD,iCAAiCuH,KAAKsiB,KACjE9iB,aAAakC,IAAI1B,KAAKsiB,IAAK,gBAAiBsB,GAC5CrqB,qBAAqByG,KAAKsiB,IAAK5pB,QAE/BkrB,IAIJnS,OACE,IAAKzR,KAAKwU,QACR,OAGF,MAAM8N,EAAMtiB,KAAK+iB,gBACXa,EAAW,KACX5jB,KAAK4iB,yBA/NU,SAmOf5iB,KAAKoiB,aAAoCE,EAAItnB,YAC/CsnB,EAAItnB,WAAW2J,YAAY2d,GAG7BtiB,KAAK8jB,iBACL9jB,KAAKoD,SAASyC,gBAAgB,oBAC9BrG,aAAawC,QAAQhC,KAAKoD,SAAUpD,KAAKmD,YAAY/J,MAAM4nB,QAEvDhhB,KAAKwU,UACPxU,KAAKwU,QAAQiB,UACbzV,KAAKwU,QAAU,QAKnB,IADkBhV,aAAawC,QAAQhC,KAAKoD,SAAUpD,KAAKmD,YAAY/J,MAAM2nB,MAC/Dze,iBAAd,CAiBA,GAbAggB,EAAI9mB,UAAU4C,OAxPM,QA4PhB,iBAAkBzG,SAASkE,iBAC7B,GAAGqL,UAAUvP,SAAS4E,KAAK+K,UACxBjN,QAAQvC,GAAW0H,aAAaC,IAAI3H,EAAS,YAAaoE,OAG/D8D,KAAKqiB,eAAL,OAAqC,EACrCriB,KAAKqiB,eAAL,OAAqC,EACrCriB,KAAKqiB,eAAL,OAAqC,EAEjCriB,KAAKsiB,IAAI9mB,UAAUC,SAvQH,QAuQ8B,CAChD,MAAM/C,EAAqBD,iCAAiC6pB,GAE5D9iB,aAAakC,IAAI4gB,EAAK,gBAAiBsB,GACvCrqB,qBAAqB+oB,EAAK5pB,QAE1BkrB,IAGF5jB,KAAKoiB,YAAc,IAGrB1M,SACuB,OAAjB1V,KAAKwU,SACPxU,KAAKwU,QAAQkB,SAMjBwN,gBACE,OAAO9hB,QAAQpB,KAAK+jB,YAGtBhB,gBACE,GAAI/iB,KAAKsiB,IACP,OAAOtiB,KAAKsiB,IAGd,MAAMxqB,EAAUH,SAASkiB,cAAc,OAIvC,OAHA/hB,EAAQ+nB,UAAY7f,KAAK/F,OAAOimB,SAEhClgB,KAAKsiB,IAAMxqB,EAAQwP,SAAS,GACrBtH,KAAKsiB,IAGdiB,aACE,MAAMjB,EAAMtiB,KAAK+iB,gBACjB/iB,KAAKgkB,kBAAkBhd,eAAeK,QAtSX,iBAsS2Cib,GAAMtiB,KAAK+jB,YACjFzB,EAAI9mB,UAAU4C,OA9SM,OAEA,QA+StB4lB,kBAAkBlsB,EAASmsB,GACzB,GAAgB,OAAZnsB,EAIJ,MAAuB,iBAAZmsB,GAAwB5qB,UAAU4qB,IACvCA,EAAQ1R,SACV0R,EAAUA,EAAQ,SAIhBjkB,KAAK/F,OAAOomB,KACV4D,EAAQjpB,aAAelD,IACzBA,EAAQ+nB,UAAY,GACpB/nB,EAAQwhB,YAAY2K,IAGtBnsB,EAAQosB,YAAcD,EAAQC,mBAM9BlkB,KAAK/F,OAAOomB,MACVrgB,KAAK/F,OAAOumB,WACdyD,EAAUhF,aAAagF,EAASjkB,KAAK/F,OAAOklB,UAAWnf,KAAK/F,OAAOmlB,aAGrEtnB,EAAQ+nB,UAAYoE,GAEpBnsB,EAAQosB,YAAcD,GAI1BF,WACE,IAAI5D,EAAQngB,KAAKoD,SAASpL,aAAa,0BAQvC,OANKmoB,IACHA,EAAqC,mBAAtBngB,KAAK/F,OAAOkmB,MACzBngB,KAAK/F,OAAOkmB,MAAMhpB,KAAK6I,KAAKoD,UAC5BpD,KAAK/F,OAAOkmB,OAGTA,EAGTgE,iBAAiBX,GACf,MAAmB,UAAfA,EACK,MAGU,SAAfA,EACK,QAGFA,EAKTb,6BAA6BtjB,EAAOkX,GAClC,MAAM6N,EAAUpkB,KAAKmD,YAAYE,SAQjC,OAPAkT,EAAUA,GAAW/Y,KAAKM,IAAIuB,EAAMC,eAAgB8kB,MAGlD7N,EAAU,IAAIvW,KAAKmD,YAAY9D,EAAMC,eAAgBU,KAAKqkB,sBAC1D7mB,KAAKC,IAAI4B,EAAMC,eAAgB8kB,EAAS7N,IAGnCA,EAGTR,aACE,MAAM1P,OAAEA,GAAWrG,KAAK/F,OAExB,MAAsB,iBAAXoM,EACFA,EAAOjO,MAAM,KAAK4d,IAAI1Q,GAAOvM,OAAOiW,SAAS1J,EAAK,KAGrC,mBAAXe,EACF4P,GAAc5P,EAAO4P,EAAYjW,KAAKoD,UAGxCiD,EAGT6O,iBAAiBsO,GACf,MAAMtN,EAAwB,CAC5BC,UAAWqN,EACXpO,UAAW,CACT,CACEtY,KAAM,OACNsZ,QAAS,CACPkO,aAAa,EACbhE,mBAAoBtgB,KAAK/F,OAAOqmB,qBAGpC,CACExjB,KAAM,SACNsZ,QAAS,CACP/P,OAAQrG,KAAK+V,eAGjB,CACEjZ,KAAM,kBACNsZ,QAAS,CACPhC,SAAUpU,KAAK/F,OAAOma,WAG1B,CACEtX,KAAM,QACNsZ,QAAS,CACPte,QAAU,IAAGkI,KAAKmD,YAAYK,eAGlC,CACE1G,KAAM,WACNwY,SAAS,EACTiP,MAAO,aACPrnB,GAAI2H,GAAQ7E,KAAKwkB,6BAA6B3f,KAGlD4f,cAAe5f,IACTA,EAAKuR,QAAQD,YAActR,EAAKsR,WAClCnW,KAAKwkB,6BAA6B3f,KAKxC,MAAO,IACFqR,KACqC,mBAA7BlW,KAAK/F,OAAOqa,aAA8BtU,KAAK/F,OAAOqa,aAAa4B,GAAyBlW,KAAK/F,OAAOqa,cAIvHoP,oBAAoBF,GAClBxjB,KAAK+iB,gBAAgBvnB,UAAUuS,IAAK,cAAkB/N,KAAKmkB,iBAAiBX,IAG9EG,gBACE,OAA8B,IAA1B3jB,KAAK/F,OAAO4X,UACPla,SAAS4E,KAGdlD,UAAU2G,KAAK/F,OAAO4X,WACjB7R,KAAK/F,OAAO4X,UAGd7K,eAAeK,QAAQrH,KAAK/F,OAAO4X,WAG5C4R,eAAetN,GACb,OAAOsK,cAActK,EAAUtb,eAGjC0nB,gBACmBviB,KAAK/F,OAAO+H,QAAQ5J,MAAM,KAElCiC,QAAQ2H,IACf,GAAgB,UAAZA,EACFxC,aAAaiC,GAAGzB,KAAKoD,SAAUpD,KAAKmD,YAAY/J,MAAMgoB,MAAOphB,KAAK/F,OAAOlC,SAAUsH,GAASW,KAAKmF,OAAO9F,SACnG,GAtcU,WAscN2C,EAA4B,CACrC,MAAM0iB,EA1cQ,UA0cE1iB,EACdhC,KAAKmD,YAAY/J,MAAMmoB,WACvBvhB,KAAKmD,YAAY/J,MAAMioB,QACnBsD,EA7cQ,UA6cG3iB,EACfhC,KAAKmD,YAAY/J,MAAMooB,WACvBxhB,KAAKmD,YAAY/J,MAAMkoB,SAEzB9hB,aAAaiC,GAAGzB,KAAKoD,SAAUshB,EAAS1kB,KAAK/F,OAAOlC,SAAUsH,GAASW,KAAK6iB,OAAOxjB,IACnFG,aAAaiC,GAAGzB,KAAKoD,SAAUuhB,EAAU3kB,KAAK/F,OAAOlC,SAAUsH,GAASW,KAAK8iB,OAAOzjB,OAIxFW,KAAKgjB,kBAAoB,KACnBhjB,KAAKoD,UACPpD,KAAKyR,QAITjS,aAAaiC,GAAGzB,KAAKoD,SAASqB,QAAS,UAAwB,gBAAiBzE,KAAKgjB,mBAEjFhjB,KAAK/F,OAAOlC,SACdiI,KAAK/F,OAAS,IACT+F,KAAK/F,OACR+H,QAAS,SACTjK,SAAU,IAGZiI,KAAK4kB,YAITA,YACE,MAAMzE,EAAQngB,KAAKoD,SAASpL,aAAa,SACnC6sB,SAA2B7kB,KAAKoD,SAASpL,aAAa,2BAExDmoB,GAA+B,WAAtB0E,KACX7kB,KAAKoD,SAASgC,aAAa,yBAA0B+a,GAAS,KAC1DA,GAAUngB,KAAKoD,SAASpL,aAAa,eAAkBgI,KAAKoD,SAAS8gB,aACvElkB,KAAKoD,SAASgC,aAAa,aAAc+a,GAG3CngB,KAAKoD,SAASgC,aAAa,QAAS,KAIxCyd,OAAOxjB,EAAOkX,GACZA,EAAUvW,KAAK2iB,6BAA6BtjB,EAAOkX,GAE/ClX,IACFkX,EAAQ8L,eACS,YAAfhjB,EAAMK,KA3fQ,QADA,UA6fZ,GAGF6W,EAAQwM,gBAAgBvnB,UAAUC,SAvgBlB,SAEC,SAqgB8C8a,EAAQ6L,YACzE7L,EAAQ6L,YAtgBW,QA0gBrBxU,aAAa2I,EAAQ4L,UAErB5L,EAAQ6L,YA5gBa,OA8gBhB7L,EAAQtc,OAAOmmB,OAAU7J,EAAQtc,OAAOmmB,MAAM1O,KAKnD6E,EAAQ4L,SAAWroB,WAAW,KAnhBT,SAohBfyc,EAAQ6L,aACV7L,EAAQ7E,QAET6E,EAAQtc,OAAOmmB,MAAM1O,MARtB6E,EAAQ7E,QAWZoR,OAAOzjB,EAAOkX,GACZA,EAAUvW,KAAK2iB,6BAA6BtjB,EAAOkX,GAE/ClX,IACFkX,EAAQ8L,eACS,aAAfhjB,EAAMK,KAzhBQ,QADA,SA2hBZ6W,EAAQnT,SAAS3H,SAAS4D,EAAMoP,gBAGlC8H,EAAQqM,yBAIZhV,aAAa2I,EAAQ4L,UAErB5L,EAAQ6L,YAxiBY,MA0iBf7L,EAAQtc,OAAOmmB,OAAU7J,EAAQtc,OAAOmmB,MAAM3O,KAKnD8E,EAAQ4L,SAAWroB,WAAW,KA/iBV,QAgjBdyc,EAAQ6L,aACV7L,EAAQ9E,QAET8E,EAAQtc,OAAOmmB,MAAM3O,MARtB8E,EAAQ9E,QAWZmR,uBACE,IAAK,MAAM5gB,KAAWhC,KAAKqiB,eACzB,GAAIriB,KAAKqiB,eAAergB,GACtB,OAAO,EAIX,OAAO,EAGT0J,WAAWzR,GACT,MAAM6qB,EAAiBpf,YAAYI,kBAAkB9F,KAAKoD,UAuC1D,OArCAjJ,OAAOC,KAAK0qB,GAAgBzqB,QAAQ0qB,IAC9B/E,sBAAsBpiB,IAAImnB,WACrBD,EAAeC,KAItB9qB,GAAsC,iBAArBA,EAAO4X,WAA0B5X,EAAO4X,UAAUU,SACrEtY,EAAO4X,UAAY5X,EAAO4X,UAAU,IASV,iBAN5B5X,EAAS,IACJ+F,KAAKmD,YAAYkF,WACjByc,KACmB,iBAAX7qB,GAAuBA,EAASA,EAAS,KAGpCmmB,QAChBnmB,EAAOmmB,MAAQ,CACb1O,KAAMzX,EAAOmmB,MACb3O,KAAMxX,EAAOmmB,QAIW,iBAAjBnmB,EAAOkmB,QAChBlmB,EAAOkmB,MAAQlmB,EAAOkmB,MAAMjpB,YAGA,iBAAnB+C,EAAOgqB,UAChBhqB,EAAOgqB,QAAUhqB,EAAOgqB,QAAQ/sB,YAGlC6C,gBAAgByJ,OAAMvJ,EAAQ+F,KAAKmD,YAAYyF,aAE3C3O,EAAOumB,WACTvmB,EAAOimB,SAAWjB,aAAahlB,EAAOimB,SAAUjmB,EAAOklB,UAAWllB,EAAOmlB,aAGpEnlB,EAGToqB,qBACE,MAAMpqB,EAAS,GAEf,GAAI+F,KAAK/F,OACP,IAAK,MAAMyD,KAAOsC,KAAK/F,OACjB+F,KAAKmD,YAAYkF,QAAQ3K,KAASsC,KAAK/F,OAAOyD,KAChDzD,EAAOyD,GAAOsC,KAAK/F,OAAOyD,IAKhC,OAAOzD,EAGT6pB,iBACE,MAAMxB,EAAMtiB,KAAK+iB,gBACXiC,EAAW1C,EAAItqB,aAAa,SAASZ,MAAM2oB,sBAChC,OAAbiF,GAAqBA,EAAS9kB,OAAS,GACzC8kB,EAAShP,IAAIiP,GAASA,EAAM5sB,QACzBgC,QAAQ6qB,GAAU5C,EAAI9mB,UAAU4C,OAAO8mB,IAI9CV,6BAA6BvO,GAC3B,MAAMkP,MAAEA,GAAUlP,EAEbkP,IAILnlB,KAAKsiB,IAAM6C,EAAM1F,SAAS2F,OAC1BplB,KAAK8jB,iBACL9jB,KAAK0jB,oBAAoB1jB,KAAKyjB,eAAe0B,EAAMhP,aAK/B5S,uBAACtJ,GACrB,OAAO+F,KAAK4E,MAAK,WACf,IAAIC,EAAOrH,KAAKM,IAAIkC,KAAMqD,YAC1B,MAAMoI,EAA4B,iBAAXxR,GAAuBA,EAE9C,IAAK4K,IAAQ,eAAelK,KAAKV,MAI5B4K,IACHA,EAAO,IAAIod,QAAQjiB,KAAMyL,IAGL,iBAAXxR,GAAqB,CAC9B,QAA4B,IAAjB4K,EAAK5K,GACd,MAAM,IAAIW,UAAW,oBAAmBX,MAG1C4K,EAAK5K,UAab4C,mBAAmB2G,OAAMye,SC7wBzB,MAAMze,OAAO,UACPH,WAAW,aACXI,YAAa,cACbqc,aAAe,aACfC,mBAAqB,IAAIrlB,OAAQ,wBAA6B,KAE9D2N,UAAU,IACX4Z,QAAQ5Z,QACX8N,UAAW,QACX9P,OAAQ,CAAC,EAAG,GACZrE,QAAS,QACTiiB,QAAS,GACT/D,SAAU,+IAONtX,cAAc,IACfqZ,QAAQrZ,YACXqb,QAAS,6BAGL7qB,QAAQ,CACZ2nB,KAAO,kBACPC,OAAS,oBACTC,KAAO,kBACPC,MAAQ,mBACRC,SAAW,sBACXC,MAAQ,mBACRC,QAAU,qBACVC,SAAW,sBACXC,WAAa,wBACbC,WAAa,yBAGTxd,kBAAkB,OAClBC,kBAAkB,OAElBohB,eAAiB,kBACjBC,iBAAmB,gBAQzB,MAAMC,gBAAgBtD,QAGF5Z,qBAChB,OAAOA,UAGM7E,kBACb,OAAOA,OAGUH,sBACjB,OAAOA,WAGOjK,mBACd,OAAOA,QAGWqK,uBAClB,OAAOA,YAGamF,yBACpB,OAAOA,cAKTsa,gBACE,OAAOljB,KAAK+jB,YAAc/jB,KAAKwlB,cAGjCjC,aACE,MAAMjB,EAAMtiB,KAAK+iB,gBAGjB/iB,KAAKgkB,kBAAkBhd,eAAeK,QAAQge,eAAgB/C,GAAMtiB,KAAK+jB,YACzE,IAAIE,EAAUjkB,KAAKwlB,cACI,mBAAZvB,IACTA,EAAUA,EAAQ9sB,KAAK6I,KAAKoD,WAG9BpD,KAAKgkB,kBAAkBhd,eAAeK,QAnDjB,gBAmD2Cib,GAAM2B,GAEtE3B,EAAI9mB,UAAU4C,OAzDM,OACA,QA6DtBslB,oBAAoBF,GAClBxjB,KAAK+iB,gBAAgBvnB,UAAUuS,IAAK,cAAkB/N,KAAKmkB,iBAAiBX,IAG9EgC,cACE,OAAOxlB,KAAKoD,SAASpL,aAAa,oBAAsBgI,KAAK/F,OAAOgqB,QAGtEH,iBACE,MAAMxB,EAAMtiB,KAAK+iB,gBACXiC,EAAW1C,EAAItqB,aAAa,SAASZ,MAAM2oB,oBAChC,OAAbiF,GAAqBA,EAAS9kB,OAAS,GACzC8kB,EAAShP,IAAIiP,GAASA,EAAM5sB,QACzBgC,QAAQ6qB,GAAU5C,EAAI9mB,UAAU4C,OAAO8mB,IAMxB3hB,uBAACtJ,GACrB,OAAO+F,KAAK4E,MAAK,WACf,IAAIC,EAAOrH,KAAKM,IAAIkC,KAAMqD,YAC1B,MAAMoI,EAA4B,iBAAXxR,EAAsBA,EAAS,KAEtD,IAAK4K,IAAQ,eAAelK,KAAKV,MAI5B4K,IACHA,EAAO,IAAI0gB,QAAQvlB,KAAMyL,GACzBjO,KAAKC,IAAIuC,KAAMqD,WAAUwB,IAGL,iBAAX5K,GAAqB,CAC9B,QAA4B,IAAjB4K,EAAK5K,GACd,MAAM,IAAIW,UAAW,oBAAmBX,MAG1C4K,EAAK5K,UAab4C,mBAAmB2G,OAAM+hB,SC9IzB,MAAM/hB,OAAO,YACPH,WAAW,eACXI,YAAa,gBACbC,eAAe,YAEf2E,UAAU,CACdhC,OAAQ,GACRof,OAAQ,OACR1lB,OAAQ,IAGJ6I,cAAc,CAClBvC,OAAQ,SACRof,OAAQ,SACR1lB,OAAQ,oBAGJ2lB,eAAkB,wBAClBC,aAAgB,sBAChB/b,oBAAuB,6BAEvBgc,yBAA2B,gBAC3B5gB,oBAAoB,SAEpB6gB,kBAAoB,yBACpBC,0BAA0B,oBAC1BC,mBAAqB,YACrBC,mBAAqB,YACrBC,oBAAsB,mBACtBC,oBAAoB,YACpBC,2BAA2B,mBAE3BC,cAAgB,SAChBC,gBAAkB,WAQxB,MAAMC,kBAAkBpjB,cACtBC,YAAYrL,EAASmC,GACnB+Q,MAAMlT,GACNkI,KAAKumB,eAA2C,SAA1BvmB,KAAKoD,SAAS4K,QAAqBpV,OAASoH,KAAKoD,SACvEpD,KAAKyL,QAAUzL,KAAK0L,WAAWzR,GAC/B+F,KAAKqR,UAAa,GAAErR,KAAKyL,QAAQ1L,qBAAiCC,KAAKyL,QAAQ1L,4BAAkCC,KAAKyL,QAAQ1L,wBAC9HC,KAAKwmB,SAAW,GAChBxmB,KAAKymB,SAAW,GAChBzmB,KAAK0mB,cAAgB,KACrB1mB,KAAK2mB,cAAgB,EAErBnnB,aAAaiC,GAAGzB,KAAKumB,eAAgBZ,aAAc,IAAM3lB,KAAK4mB,YAE9D5mB,KAAK6mB,UACL7mB,KAAK4mB,WAKWve,qBAChB,OAAOA,UAGUhF,sBACjB,OAAOA,WAKTwjB,UACE,MAAMC,EAAa9mB,KAAKumB,iBAAmBvmB,KAAKumB,eAAe3tB,OAvC7C,SACE,WA0CdmuB,EAAuC,SAAxB/mB,KAAKyL,QAAQga,OAChCqB,EACA9mB,KAAKyL,QAAQga,OAETuB,EA9Cc,aA8CDD,EACjB/mB,KAAKinB,gBACL,EAEFjnB,KAAKwmB,SAAW,GAChBxmB,KAAKymB,SAAW,GAChBzmB,KAAK2mB,cAAgB3mB,KAAKknB,mBAEVlgB,eAAeC,KAAKjH,KAAKqR,WAEjC2E,IAAIle,IACV,MAAMqvB,EAAiB7uB,uBAAuBR,GACxCiI,EAASonB,EAAiBngB,eAAeK,QAAQ8f,GAAkB,KAEzE,GAAIpnB,EAAQ,CACV,MAAMqnB,EAAYrnB,EAAOwG,wBACzB,GAAI6gB,EAAU/L,OAAS+L,EAAUC,OAC/B,MAAO,CACL3hB,YAAYqhB,GAAchnB,GAAQyG,IAAMwgB,EACxCG,GAKN,OAAO,OAENlhB,OAAOqhB,GAAQA,GACfC,KAAK,CAAClK,EAAGE,IAAMF,EAAE,GAAKE,EAAE,IACxBljB,QAAQitB,IACPtnB,KAAKwmB,SAAS7e,KAAK2f,EAAK,IACxBtnB,KAAKymB,SAAS9e,KAAK2f,EAAK,MAI9BhkB,UACE0H,MAAM1H,UACN9D,aAAaC,IAAIO,KAAKumB,eAAgB9iB,aAEtCzD,KAAKumB,eAAiB,KACtBvmB,KAAKyL,QAAU,KACfzL,KAAKqR,UAAY,KACjBrR,KAAKwmB,SAAW,KAChBxmB,KAAKymB,SAAW,KAChBzmB,KAAK0mB,cAAgB,KACrB1mB,KAAK2mB,cAAgB,KAKvBjb,WAAWzR,GAMT,GAA6B,iBAL7BA,EAAS,IACJoO,aACmB,iBAAXpO,GAAuBA,EAASA,EAAS,KAGpC8F,QAAuB1G,UAAUY,EAAO8F,QAAS,CACjE,IAAIiR,GAAEA,GAAO/W,EAAO8F,OACfiR,IACHA,EAAK1Z,OAAOkM,QACZvJ,EAAO8F,OAAOiR,GAAKA,GAGrB/W,EAAO8F,OAAU,IAAGiR,EAKtB,OAFAjX,gBAAgByJ,OAAMvJ,EAAQ2O,eAEvB3O,EAGTgtB,gBACE,OAAOjnB,KAAKumB,iBAAmB3tB,OAC7BoH,KAAKumB,eAAeiB,YACpBxnB,KAAKumB,eAAe9f,UAGxBygB,mBACE,OAAOlnB,KAAKumB,eAAepM,cAAgB3iB,KAAKiwB,IAC9C9vB,SAAS4E,KAAK4d,aACdxiB,SAASkE,gBAAgBse,cAI7BuN,mBACE,OAAO1nB,KAAKumB,iBAAmB3tB,OAC7BA,OAAO+uB,YACP3nB,KAAKumB,eAAehgB,wBAAwB8gB,OAGhDT,WACE,MAAMngB,EAAYzG,KAAKinB,gBAAkBjnB,KAAKyL,QAAQpF,OAChD8T,EAAena,KAAKknB,mBACpBU,EAAY5nB,KAAKyL,QAAQpF,OAAS8T,EAAena,KAAK0nB,mBAM5D,GAJI1nB,KAAK2mB,gBAAkBxM,GACzBna,KAAK6mB,UAGHpgB,GAAamhB,EAAjB,CACE,MAAM7nB,EAASC,KAAKymB,SAASzmB,KAAKymB,SAASvmB,OAAS,GAEhDF,KAAK0mB,gBAAkB3mB,GACzBC,KAAK6nB,UAAU9nB,OAJnB,CAUA,GAAIC,KAAK0mB,eAAiBjgB,EAAYzG,KAAKwmB,SAAS,IAAMxmB,KAAKwmB,SAAS,GAAK,EAG3E,OAFAxmB,KAAK0mB,cAAgB,UACrB1mB,KAAK8nB,SAIP,IAAK,IAAI7nB,EAAID,KAAKwmB,SAAStmB,OAAQD,KACVD,KAAK0mB,gBAAkB1mB,KAAKymB,SAASxmB,IACxDwG,GAAazG,KAAKwmB,SAASvmB,UACM,IAAzBD,KAAKwmB,SAASvmB,EAAI,IAAsBwG,EAAYzG,KAAKwmB,SAASvmB,EAAI,KAGhFD,KAAK6nB,UAAU7nB,KAAKymB,SAASxmB,KAKnC4nB,UAAU9nB,GACRC,KAAK0mB,cAAgB3mB,EAErBC,KAAK8nB,SAEL,MAAMC,EAAU/nB,KAAKqR,UAAUjZ,MAAM,KAClC4d,IAAIje,GAAa,GAAEA,qBAA4BgI,OAAYhI,WAAkBgI,OAE1EioB,EAAOhhB,eAAeK,QAAQ0gB,EAAQE,KAAK,MAE7CD,EAAKxsB,UAAUC,SAjMU,kBAkM3BuL,eAAeK,QAzLY,mBAyLsB2gB,EAAKvjB,QA1LlC,cA2LjBjJ,UAAUuS,IAlMO,UAoMpBia,EAAKxsB,UAAUuS,IApMK,YAuMpBia,EAAKxsB,UAAUuS,IAvMK,UAyMpB/G,eAAeS,QAAQugB,EAtMG,qBAuMvB3tB,QAAQ6tB,IAGPlhB,eAAeY,KAAKsgB,EAAY,+BAC7B7tB,QAAQitB,GAAQA,EAAK9rB,UAAUuS,IA9MlB,WAiNhB/G,eAAeY,KAAKsgB,EA5MH,aA6Md7tB,QAAQ8tB,IACPnhB,eAAeM,SAAS6gB,EA/MX,aAgNV9tB,QAAQitB,GAAQA,EAAK9rB,UAAUuS,IApNtB,gBAyNtBvO,aAAawC,QAAQhC,KAAKumB,eAAgBb,eAAgB,CACxDjX,cAAe1O,IAInB+nB,SACE9gB,eAAeC,KAAKjH,KAAKqR,WACtBpL,OAAOmiB,GAAQA,EAAK5sB,UAAUC,SAhOX,WAiOnBpB,QAAQ+tB,GAAQA,EAAK5sB,UAAU4C,OAjOZ,WAsOFmF,uBAACtJ,GACrB,OAAO+F,KAAK4E,MAAK,WACf,IAAIC,EAAOrH,KAAKM,IAAIkC,KAAMqD,YAO1B,GAJKwB,IACHA,EAAO,IAAIyhB,UAAUtmB,KAHW,iBAAX/F,GAAuBA,IAMxB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjB4K,EAAK5K,GACd,MAAM,IAAIW,UAAW,oBAAmBX,MAG1C4K,EAAK5K,UAYbuF,aAAaiC,GAAG7I,OAAQgR,oBAAqB,KAC3C5C,eAAeC,KAAK4e,mBACjBxrB,QAAQguB,GAAO,IAAI/B,UAAU+B,EAAK3iB,YAAYI,kBAAkBuiB,OAUrExrB,mBAAmB2G,OAAM8iB,WClSzB,MAAM9iB,OAAO,MACPH,WAAW,SACXI,YAAa,UACbC,aAAe,YAEf2M,aAAc,cACdC,eAAgB,gBAChBH,aAAc,cACdC,cAAe,eACftM,qBAAwB,wBAExBwkB,yBAA2B,gBAC3BtjB,kBAAoB,SACpBhB,kBAAkB,OAClBC,kBAAkB,OAElBiiB,kBAAoB,YACpBJ,wBAA0B,oBAC1B1b,gBAAkB,UAClBme,mBAAqB,wBACrBtjB,qBAAuB,2EACvBkhB,yBAA2B,mBAC3BqC,+BAAiC,kCAQvC,MAAMC,YAAYvlB,cAGGG,sBACjB,MAjCa,SAsCfqO,OACE,GAAK1R,KAAKoD,SAASpI,YACjBgF,KAAKoD,SAASpI,WAAW1B,WAAagC,KAAKC,cAC3CyE,KAAKoD,SAAS5H,UAAUC,SA9BJ,WA+BpBJ,WAAW2E,KAAKoD,UAChB,OAGF,IAAIyE,EACJ,MAAM9H,EAASvH,uBAAuBwH,KAAKoD,UACrCslB,EAAc1oB,KAAKoD,SAASqB,QAhCN,qBAkC5B,GAAIikB,EAAa,CACf,MAAMC,EAAwC,OAAzBD,EAAY3L,UAA8C,OAAzB2L,EAAY3L,SAAoBwL,mBAlCpE,UAmClB1gB,EAAWb,eAAeC,KAAK0hB,EAAcD,GAC7C7gB,EAAWA,EAASA,EAAS3H,OAAS,GAGxC,MAAM0oB,EAAY/gB,EAChBrI,aAAawC,QAAQ6F,EAAUwI,aAAY,CACzC5B,cAAezO,KAAKoD,WAEtB,KAMF,GAJkB5D,aAAawC,QAAQhC,KAAKoD,SAAU+M,aAAY,CAChE1B,cAAe5G,IAGHvF,kBAAmC,OAAdsmB,GAAsBA,EAAUtmB,iBACjE,OAGFtC,KAAK6nB,UAAU7nB,KAAKoD,SAAUslB,GAE9B,MAAM9E,EAAW,KACfpkB,aAAawC,QAAQ6F,EAAUyI,eAAc,CAC3C7B,cAAezO,KAAKoD,WAEtB5D,aAAawC,QAAQhC,KAAKoD,SAAUgN,cAAa,CAC/C3B,cAAe5G,KAIf9H,EACFC,KAAK6nB,UAAU9nB,EAAQA,EAAO/E,WAAY4oB,GAE1CA,IAMJiE,UAAU/vB,EAAS+Z,EAAWpV,GAC5B,MAIMosB,IAJiBhX,GAAqC,OAAvBA,EAAUkL,UAA4C,OAAvBlL,EAAUkL,SAE5E/V,eAAeM,SAASuK,EA5EN,WA2ElB7K,eAAeC,KAAKshB,mBAAoB1W,IAGZ,GACxBS,EAAkB7V,GAAaosB,GAAUA,EAAOrtB,UAAUC,SApF5C,QAsFdmoB,EAAW,IAAM5jB,KAAK8oB,oBAAoBhxB,EAAS+wB,EAAQpsB,GAEjE,GAAIosB,GAAUvW,EAAiB,CAC7B,MAAM5Z,EAAqBD,iCAAiCowB,GAC5DA,EAAOrtB,UAAU4C,OAzFC,QA2FlBoB,aAAakC,IAAImnB,EAAQ,gBAAiBjF,GAC1CrqB,qBAAqBsvB,EAAQnwB,QAE7BkrB,IAIJkF,oBAAoBhxB,EAAS+wB,EAAQpsB,GACnC,GAAIosB,EAAQ,CACVA,EAAOrtB,UAAU4C,OAtGG,UAwGpB,MAAM2qB,EAAgB/hB,eAAeK,QAAQmhB,+BAAgCK,EAAO7tB,YAEhF+tB,GACFA,EAAcvtB,UAAU4C,OA3GN,UA8GgB,QAAhCyqB,EAAO7wB,aAAa,SACtB6wB,EAAOzjB,aAAa,iBAAiB,GAIzCtN,EAAQ0D,UAAUuS,IAnHI,UAoHe,QAAjCjW,EAAQE,aAAa,SACvBF,EAAQsN,aAAa,iBAAiB,GAGxCjJ,OAAOrE,GAEHA,EAAQ0D,UAAUC,SAzHF,SA0HlB3D,EAAQ0D,UAAUuS,IAzHA,QA4HhBjW,EAAQkD,YAAclD,EAAQkD,WAAWQ,UAAUC,SA/H1B,mBAgIH3D,EAAQ2M,QA3HZ,cA8HlBuC,eAAeC,KAzHU,oBA0HtB5M,QAAQ2uB,GAAYA,EAASxtB,UAAUuS,IAnIxB,WAsIpBjW,EAAQsN,aAAa,iBAAiB,IAGpC3I,GACFA,IAMkB8G,uBAACtJ,GACrB,OAAO+F,KAAK4E,MAAK,WACf,MAAMC,EAAOrH,KAAKM,IAAIkC,KA7JX,WA6J8B,IAAIyoB,IAAIzoB,MAEjD,GAAsB,iBAAX/F,EAAqB,CAC9B,QAA4B,IAAjB4K,EAAK5K,GACd,MAAM,IAAIW,UAAW,oBAAmBX,MAG1C4K,EAAK5K,UAYbuF,aAAaiC,GAAG9J,SAAUmM,qBAAsBmB,sBAAsB,SAAU5F,GAC9EA,EAAM2D,kBAEOxF,KAAKM,IAAIkC,KAnLP,WAmL0B,IAAIyoB,IAAIzoB,OAC5C0R,UAUP7U,mBA/La,MA+LY4rB,KChMzB,MAAMjlB,KAAO,QACPH,SAAW,WACXI,UAAa,YAEb2T,oBAAuB,yBACvB/G,WAAc,gBACdC,aAAgB,kBAChBH,WAAc,gBACdC,YAAe,iBAEfpM,gBAAkB,OAClBilB,gBAAkB,OAClBhlB,gBAAkB,OAClBilB,mBAAqB,UAErBtgB,YAAc,CAClBqX,UAAW,UACXkJ,SAAU,UACV/I,MAAO,UAGH/X,QAAU,CACd4X,WAAW,EACXkJ,UAAU,EACV/I,MAAO,KAGHtI,sBAAwB,4BAQ9B,MAAMsR,cAAclmB,cAClBC,YAAYrL,EAASmC,GACnB+Q,MAAMlT,GAENkI,KAAKyL,QAAUzL,KAAK0L,WAAWzR,GAC/B+F,KAAKmiB,SAAW,KAChBniB,KAAKuiB,gBAKe3Z,yBACpB,OAAOA,YAGSP,qBAChB,OAAOA,QAGUhF,sBACjB,OAAOA,SAKTqO,OAGE,GAFkBlS,aAAawC,QAAQhC,KAAKoD,SAAU+M,YAExC7N,iBACZ,OAGFtC,KAAKqpB,gBAEDrpB,KAAKyL,QAAQwU,WACfjgB,KAAKoD,SAAS5H,UAAUuS,IA5DN,QA+DpB,MAAM6V,EAAW,KACf5jB,KAAKoD,SAAS5H,UAAU4C,OA7DH,WA8DrB4B,KAAKoD,SAAS5H,UAAUuS,IA/DN,QAiElBvO,aAAawC,QAAQhC,KAAKoD,SAAUgN,aAEhCpQ,KAAKyL,QAAQ0d,WACfnpB,KAAKmiB,SAAWroB,WAAW,KACzBkG,KAAKyR,QACJzR,KAAKyL,QAAQ2U,SAOpB,GAHApgB,KAAKoD,SAAS5H,UAAU4C,OA3EJ,QA4EpBjC,OAAO6D,KAAKoD,UACZpD,KAAKoD,SAAS5H,UAAUuS,IA3ED,WA4EnB/N,KAAKyL,QAAQwU,UAAW,CAC1B,MAAMvnB,EAAqBD,iCAAiCuH,KAAKoD,UAEjE5D,aAAakC,IAAI1B,KAAKoD,SAAU,gBAAiBwgB,GACjDrqB,qBAAqByG,KAAKoD,SAAU1K,QAEpCkrB,IAIJnS,OACE,IAAKzR,KAAKoD,SAAS5H,UAAUC,SAxFT,QAyFlB,OAKF,GAFkB+D,aAAawC,QAAQhC,KAAKoD,SAAUiN,YAExC/N,iBACZ,OAGF,MAAMshB,EAAW,KACf5jB,KAAKoD,SAAS5H,UAAUuS,IApGN,QAqGlBvO,aAAawC,QAAQhC,KAAKoD,SAAUkN,eAItC,GADAtQ,KAAKoD,SAAS5H,UAAU4C,OAvGJ,QAwGhB4B,KAAKyL,QAAQwU,UAAW,CAC1B,MAAMvnB,EAAqBD,iCAAiCuH,KAAKoD,UAEjE5D,aAAakC,IAAI1B,KAAKoD,SAAU,gBAAiBwgB,GACjDrqB,qBAAqByG,KAAKoD,SAAU1K,QAEpCkrB,IAIJtgB,UACEtD,KAAKqpB,gBAEDrpB,KAAKoD,SAAS5H,UAAUC,SArHR,SAsHlBuE,KAAKoD,SAAS5H,UAAU4C,OAtHN,QAyHpBoB,aAAaC,IAAIO,KAAKoD,SAAUgU,qBAEhCpM,MAAM1H,UACNtD,KAAKyL,QAAU,KAKjBC,WAAWzR,GAST,OARAA,EAAS,IACJoO,WACA3C,YAAYI,kBAAkB9F,KAAKoD,aAChB,iBAAXnJ,GAAuBA,EAASA,EAAS,IAGtDF,gBAAgByJ,KAAMvJ,EAAQ+F,KAAKmD,YAAYyF,aAExC3O,EAGTsoB,gBACE/iB,aAAaiC,GAAGzB,KAAKoD,SAAUgU,oBAAqBU,sBAAuB,IAAM9X,KAAKyR,QAGxF4X,gBACEzb,aAAa5N,KAAKmiB,UAClBniB,KAAKmiB,SAAW,KAKI5e,uBAACtJ,GACrB,OAAO+F,KAAK4E,MAAK,WACf,IAAIC,EAAOrH,KAAKM,IAAIkC,KAAMqD,UAO1B,GAJKwB,IACHA,EAAO,IAAIukB,MAAMppB,KAHe,iBAAX/F,GAAuBA,IAMxB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjB4K,EAAK5K,GACd,MAAM,IAAIW,UAAW,oBAAmBX,MAG1C4K,EAAK5K,GAAQ+F,WAarBnD,mBAAmB2G,KAAM4lB", "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttr = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttr || (!hrefAttr.includes('#') && !hrefAttr.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttr.includes('#') && !hrefAttr.startsWith('#')) {\n      hrefAttr = '#' + hrefAttr.split('#')[1]\n    }\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => (obj[0] || obj).nodeType\n\nconst emulateTransitionEnd = (element, duration) => {\n  let called = false\n  const durationPadding = 5\n  const emulatedDuration = duration + durationPadding\n\n  function listener() {\n    called = true\n    element.removeEventListener(TRANSITION_END, listener)\n  }\n\n  element.addEventListener(TRANSITION_END, listener)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(element)\n    }\n  }, emulatedDuration)\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes).forEach(property => {\n    const expectedTypes = configTypes[property]\n    const value = config[property]\n    const valueType = value && isElement(value) ? 'element' : toType(value)\n\n    if (!new RegExp(expectedTypes).test(valueType)) {\n      throw new TypeError(\n        `${componentName.toUpperCase()}: ` +\n        `Option \"${property}\" provided type \"${valueType}\" ` +\n        `but expected type \"${expectedTypes}\".`\n      )\n    }\n  })\n}\n\nconst isVisible = element => {\n  if (!element) {\n    return false\n  }\n\n  if (element.style && element.parentNode && element.parentNode.style) {\n    const elementStyle = getComputedStyle(element)\n    const parentNodeStyle = getComputedStyle(element.parentNode)\n\n    return elementStyle.display !== 'none' &&\n      parentNodeStyle.display !== 'none' &&\n      elementStyle.visibility !== 'hidden'\n  }\n\n  return false\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => function () {}\n\nconst reflow = element => element.offsetHeight\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    document.addEventListener('DOMContentLoaded', callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = (name, plugin) => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nexport {\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  emulateTransitionEnd,\n  typeCheckConfig,\n  isVisible,\n  isDisabled,\n  findShadowRoot,\n  noop,\n  reflow,\n  getjQuery,\n  onDOMContentLoaded,\n  isRTL,\n  defineJQueryPlugin\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst elementMap = new Map()\n\nexport default {\n  set(element, key, instance) {\n    if (!elementMap.has(element)) {\n      elementMap.set(element, new Map())\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    // make it clear we only want one instance per element\n    // can be removed later when multiple key/instances are fine to be used\n    if (!instanceMap.has(key) && instanceMap.size !== 0) {\n      // eslint-disable-next-line no-console\n      console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(instanceMap.keys())[0]}.`)\n      return\n    }\n\n    instanceMap.set(key, instance)\n  },\n\n  get(element, key) {\n    if (elementMap.has(element)) {\n      return elementMap.get(element).get(key) || null\n    }\n\n    return null\n  },\n\n  remove(element, key) {\n    if (!elementMap.has(element)) {\n      return\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    instanceMap.delete(key)\n\n    // free up element references if there are no instances left for an element\n    if (instanceMap.size === 0) {\n      elementMap.delete(element)\n    }\n  }\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\nconst nativeEvents = new Set([\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n])\n\n/**\n * ------------------------------------------------------------------------\n * Private methods\n * ------------------------------------------------------------------------\n */\n\nfunction getUidEvent(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getEvent(element) {\n  const uid = getUidEvent(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    event.delegateTarget = element\n\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (let i = domElements.length; i--;) {\n        if (domElements[i] === target) {\n          event.delegateTarget = target\n\n          if (handler.oneOff) {\n            // eslint-disable-next-line unicorn/consistent-destructuring\n            EventHandler.off(element, event.type, fn)\n          }\n\n          return fn.apply(target, [event])\n        }\n      }\n    }\n\n    // To please ESLint\n    return null\n  }\n}\n\nfunction findHandler(events, handler, delegationSelector = null) {\n  const uidEventList = Object.keys(events)\n\n  for (let i = 0, len = uidEventList.length; i < len; i++) {\n    const event = events[uidEventList[i]]\n\n    if (event.originalHandler === handler && event.delegationSelector === delegationSelector) {\n      return event\n    }\n  }\n\n  return null\n}\n\nfunction normalizeParams(originalTypeEvent, handler, delegationFn) {\n  const delegation = typeof handler === 'string'\n  const originalHandler = delegation ? delegationFn : handler\n\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  let typeEvent = originalTypeEvent.replace(stripNameRegex, '')\n  const custom = customEvents[typeEvent]\n\n  if (custom) {\n    typeEvent = custom\n  }\n\n  const isNative = nativeEvents.has(typeEvent)\n\n  if (!isNative) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [delegation, originalHandler, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFn, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  if (!handler) {\n    handler = delegationFn\n    delegationFn = null\n  }\n\n  const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n  const events = getEvent(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFn = findHandler(handlers, originalHandler, delegation ? handler : null)\n\n  if (previousFn) {\n    previousFn.oneOff = previousFn.oneOff && oneOff\n\n    return\n  }\n\n  const uid = getUidEvent(originalHandler, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = delegation ?\n    bootstrapDelegationHandler(element, handler, delegationFn) :\n    bootstrapHandler(element, handler)\n\n  fn.delegationSelector = delegation ? handler : null\n  fn.originalHandler = originalHandler\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, delegation)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  Object.keys(storeElementEvent).forEach(handlerKey => {\n    if (handlerKey.includes(namespace)) {\n      const event = storeElementEvent[handlerKey]\n\n      removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n    }\n  })\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, false)\n  },\n\n  one(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFn) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getEvent(element)\n    const isNamespace = originalTypeEvent.startsWith('.')\n\n    if (typeof originalHandler !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!events || !events[typeEvent]) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, originalHandler, delegation ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      Object.keys(events).forEach(elementEvent => {\n        removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n      })\n    }\n\n    const storeElementEvent = events[typeEvent] || {}\n    Object.keys(storeElementEvent).forEach(keyHandlers => {\n      const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n      if (!inNamespace || originalTypeEvent.includes(handlerKey)) {\n        const event = storeElementEvent[keyHandlers]\n\n        removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n      }\n    })\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const $ = getjQuery()\n    const typeEvent = event.replace(stripNameRegex, '')\n    const inNamespace = event !== typeEvent\n    const isNative = nativeEvents.has(typeEvent)\n\n    let jQueryEvent\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n    let evt = null\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    if (isNative) {\n      evt = document.createEvent('HTMLEvents')\n      evt.initEvent(typeEvent, bubbles, true)\n    } else {\n      evt = new CustomEvent(event, {\n        bubbles,\n        cancelable: true\n      })\n    }\n\n    // merge custom information in our event\n    if (typeof args !== 'undefined') {\n      Object.keys(args).forEach(key => {\n        Object.defineProperty(evt, key, {\n          get() {\n            return args[key]\n          }\n        })\n      })\n    }\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && typeof jQueryEvent !== 'undefined') {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nexport default EventHandler\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): base-component.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Data from './dom/data'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst VERSION = '5.0.0-beta3'\n\nclass BaseComponent {\n  constructor(element) {\n    element = typeof element === 'string' ? document.querySelector(element) : element\n\n    if (!element) {\n      return\n    }\n\n    this._element = element\n    Data.set(this._element, this.constructor.DATA_KEY, this)\n  }\n\n  dispose() {\n    Data.remove(this._element, this.constructor.DATA_KEY)\n    this._element = null\n  }\n\n  /** Static */\n\n  static getInstance(element) {\n    return Data.get(element, this.DATA_KEY)\n  }\n\n  static get VERSION() {\n    return VERSION\n  }\n}\n\nexport default BaseComponent\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'alert'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst SELECTOR_DISMISS = '[data-bs-dismiss=\"alert\"]'\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_ALERT = 'alert'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Alert extends BaseComponent {\n  // Getters\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  close(element) {\n    const rootElement = element ? this._getRootElement(element) : this._element\n    const customEvent = this._triggerCloseEvent(rootElement)\n\n    if (customEvent === null || customEvent.defaultPrevented) {\n      return\n    }\n\n    this._removeElement(rootElement)\n  }\n\n  // Private\n\n  _getRootElement(element) {\n    return getElementFromSelector(element) || element.closest(`.${CLASS_NAME_ALERT}`)\n  }\n\n  _triggerCloseEvent(element) {\n    return EventHandler.trigger(element, EVENT_CLOSE)\n  }\n\n  _removeElement(element) {\n    element.classList.remove(CLASS_NAME_SHOW)\n\n    if (!element.classList.contains(CLASS_NAME_FADE)) {\n      this._destroyElement(element)\n      return\n    }\n\n    const transitionDuration = getTransitionDurationFromElement(element)\n\n    EventHandler.one(element, 'transitionend', () => this._destroyElement(element))\n    emulateTransitionEnd(element, transitionDuration)\n  }\n\n  _destroyElement(element) {\n    if (element.parentNode) {\n      element.parentNode.removeChild(element)\n    }\n\n    EventHandler.trigger(element, EVENT_CLOSED)\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n\n      if (!data) {\n        data = new Alert(this)\n      }\n\n      if (config === 'close') {\n        data[config](this)\n      }\n    })\n  }\n\n  static handleDismiss(alertInstance) {\n    return function (event) {\n      if (event) {\n        event.preventDefault()\n      }\n\n      alertInstance.close(this)\n    }\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DISMISS, Alert.handleDismiss(new Alert()))\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Alert to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Alert)\n\nexport default Alert\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'button'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"button\"]'\n\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Button extends BaseComponent {\n  // Getters\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n\n      if (!data) {\n        data = new Button(this)\n      }\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, event => {\n  event.preventDefault()\n\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE)\n\n  let data = Data.get(button, DATA_KEY)\n  if (!data) {\n    data = new Button(button)\n  }\n\n  data.toggle()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Button to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Button)\n\nexport default Button\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(val) {\n  if (val === 'true') {\n    return true\n  }\n\n  if (val === 'false') {\n    return false\n  }\n\n  if (val === Number(val).toString()) {\n    return Number(val)\n  }\n\n  if (val === '' || val === 'null') {\n    return null\n  }\n\n  return val\n}\n\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.to<PERSON><PERSON><PERSON><PERSON>ase()}`)\n}\n\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-bs-${normalizeDataKey(key)}`, value)\n  },\n\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-bs-${normalizeDataKey(key)}`)\n  },\n\n  getDataAttributes(element) {\n    if (!element) {\n      return {}\n    }\n\n    const attributes = {}\n\n    Object.keys(element.dataset)\n      .filter(key => key.startsWith('bs'))\n      .forEach(key => {\n        let pureKey = key.replace(/^bs/, '')\n        pureKey = pureKey.charAt(0).toLowerCase() + pureKey.slice(1, pureKey.length)\n        attributes[pureKey] = normalizeData(element.dataset[key])\n      })\n\n    return attributes\n  },\n\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-bs-${normalizeDataKey(key)}`))\n  },\n\n  offset(element) {\n    const rect = element.getBoundingClientRect()\n\n    return {\n      top: rect.top + document.body.scrollTop,\n      left: rect.left + document.body.scrollLeft\n    }\n  },\n\n  position(element) {\n    return {\n      top: element.offsetTop,\n      left: element.offsetLeft\n    }\n  }\n}\n\nexport default Manipulator\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NODE_TEXT = 3\n\nconst SelectorEngine = {\n  find(selector, element = document.documentElement) {\n    return [].concat(...Element.prototype.querySelectorAll.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return Element.prototype.querySelector.call(element, selector)\n  },\n\n  children(element, selector) {\n    return [].concat(...element.children)\n      .filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n\n    let ancestor = element.parentNode\n\n    while (ancestor && ancestor.nodeType === Node.ELEMENT_NODE && ancestor.nodeType !== NODE_TEXT) {\n      if (ancestor.matches(selector)) {\n        parents.push(ancestor)\n      }\n\n      ancestor = ancestor.parentNode\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (next.matches(selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  }\n}\n\nexport default SelectorEngine\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isRTL,\n  isVisible,\n  reflow,\n  triggerTransitionEnd,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'carousel'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  slide: false,\n  pause: 'hover',\n  wrap: true,\n  touch: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)',\n  keyboard: 'boolean',\n  slide: '(boolean|string)',\n  pause: '(string|boolean)',\n  wrap: 'boolean',\n  touch: 'boolean'\n}\n\nconst ORDER_NEXT = 'next'\nconst ORDER_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_END = 'carousel-item-end'\nconst CLASS_NAME_START = 'carousel-item-start'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_ITEM = '.active.carousel-item'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_NEXT_PREV = '.carousel-item-next, .carousel-item-prev'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_INDICATOR = '[data-bs-target]'\nconst SELECTOR_DATA_SLIDE = '[data-bs-slide], [data-bs-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-bs-ride=\"carousel\"]'\n\nconst POINTER_TYPE_TOUCH = 'touch'\nconst POINTER_TYPE_PEN = 'pen'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\nclass Carousel extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._items = null\n    this._interval = null\n    this._activeElement = null\n    this._isPaused = false\n    this._isSliding = false\n    this.touchTimeout = null\n    this.touchStartX = 0\n    this.touchDeltaX = 0\n\n    this._config = this._getConfig(config)\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element)\n    this._touchSupported = 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n    this._pointerEvent = Boolean(window.PointerEvent)\n\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  next() {\n    if (!this._isSliding) {\n      this._slide(ORDER_NEXT)\n    }\n  }\n\n  nextWhenVisible() {\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next()\n    }\n  }\n\n  prev() {\n    if (!this._isSliding) {\n      this._slide(ORDER_PREV)\n    }\n  }\n\n  pause(event) {\n    if (!event) {\n      this._isPaused = true\n    }\n\n    if (SelectorEngine.findOne(SELECTOR_NEXT_PREV, this._element)) {\n      triggerTransitionEnd(this._element)\n      this.cycle(true)\n    }\n\n    clearInterval(this._interval)\n    this._interval = null\n  }\n\n  cycle(event) {\n    if (!event) {\n      this._isPaused = false\n    }\n\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n\n    if (this._config && this._config.interval && !this._isPaused) {\n      this._updateInterval()\n\n      this._interval = setInterval(\n        (document.visibilityState ? this.nextWhenVisible : this.next).bind(this),\n        this._config.interval\n      )\n    }\n  }\n\n  to(index) {\n    this._activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeIndex = this._getItemIndex(this._activeElement)\n\n    if (index > this._items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    if (activeIndex === index) {\n      this.pause()\n      this.cycle()\n      return\n    }\n\n    const order = index > activeIndex ?\n      ORDER_NEXT :\n      ORDER_PREV\n\n    this._slide(order, this._items[index])\n  }\n\n  dispose() {\n    EventHandler.off(this._element, EVENT_KEY)\n\n    this._items = null\n    this._config = null\n    this._interval = null\n    this._isPaused = null\n    this._isSliding = null\n    this._activeElement = null\n    this._indicatorsElement = null\n\n    super.dispose()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _handleSwipe() {\n    const absDeltax = Math.abs(this.touchDeltaX)\n\n    if (absDeltax <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltax / this.touchDeltaX\n\n    this.touchDeltaX = 0\n\n    if (!direction) {\n      return\n    }\n\n    this._slide(direction > 0 ? DIRECTION_RIGHT : DIRECTION_LEFT)\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      EventHandler.on(this._element, EVENT_MOUSEENTER, event => this.pause(event))\n      EventHandler.on(this._element, EVENT_MOUSELEAVE, event => this.cycle(event))\n    }\n\n    if (this._config.touch && this._touchSupported) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    const start = event => {\n      if (this._pointerEvent && (event.pointerType === POINTER_TYPE_PEN || event.pointerType === POINTER_TYPE_TOUCH)) {\n        this.touchStartX = event.clientX\n      } else if (!this._pointerEvent) {\n        this.touchStartX = event.touches[0].clientX\n      }\n    }\n\n    const move = event => {\n      // ensure swiping with one touch and not pinching\n      this.touchDeltaX = event.touches && event.touches.length > 1 ?\n        0 :\n        event.touches[0].clientX - this.touchStartX\n    }\n\n    const end = event => {\n      if (this._pointerEvent && (event.pointerType === POINTER_TYPE_PEN || event.pointerType === POINTER_TYPE_TOUCH)) {\n        this.touchDeltaX = event.clientX - this.touchStartX\n      }\n\n      this._handleSwipe()\n      if (this._config.pause === 'hover') {\n        // If it's a touch-enabled device, mouseenter/leave are fired as\n        // part of the mouse compatibility events on first tap - the carousel\n        // would stop cycling until user tapped out of it;\n        // here, we listen for touchend, explicitly pause the carousel\n        // (as if it's the second time we tap on it, mouseenter compat event\n        // is NOT fired) and after a timeout (to allow for mouse compatibility\n        // events to fire) we explicitly restart cycling\n\n        this.pause()\n        if (this.touchTimeout) {\n          clearTimeout(this.touchTimeout)\n        }\n\n        this.touchTimeout = setTimeout(event => this.cycle(event), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n      }\n    }\n\n    SelectorEngine.find(SELECTOR_ITEM_IMG, this._element).forEach(itemImg => {\n      EventHandler.on(itemImg, EVENT_DRAG_START, e => e.preventDefault())\n    })\n\n    if (this._pointerEvent) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => start(event))\n      EventHandler.on(this._element, EVENT_POINTERUP, event => end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => start(event))\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => move(event))\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => end(event))\n    }\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    if (event.key === ARROW_LEFT_KEY) {\n      event.preventDefault()\n      this._slide(DIRECTION_LEFT)\n    } else if (event.key === ARROW_RIGHT_KEY) {\n      event.preventDefault()\n      this._slide(DIRECTION_RIGHT)\n    }\n  }\n\n  _getItemIndex(element) {\n    this._items = element && element.parentNode ?\n      SelectorEngine.find(SELECTOR_ITEM, element.parentNode) :\n      []\n\n    return this._items.indexOf(element)\n  }\n\n  _getItemByOrder(order, activeElement) {\n    const isNext = order === ORDER_NEXT\n    const isPrev = order === ORDER_PREV\n    const activeIndex = this._getItemIndex(activeElement)\n    const lastItemIndex = this._items.length - 1\n    const isGoingToWrap = (isPrev && activeIndex === 0) || (isNext && activeIndex === lastItemIndex)\n\n    if (isGoingToWrap && !this._config.wrap) {\n      return activeElement\n    }\n\n    const delta = isPrev ? -1 : 1\n    const itemIndex = (activeIndex + delta) % this._items.length\n\n    return itemIndex === -1 ?\n      this._items[this._items.length - 1] :\n      this._items[itemIndex]\n  }\n\n  _triggerSlideEvent(relatedTarget, eventDirectionName) {\n    const targetIndex = this._getItemIndex(relatedTarget)\n    const fromIndex = this._getItemIndex(SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element))\n\n    return EventHandler.trigger(this._element, EVENT_SLIDE, {\n      relatedTarget,\n      direction: eventDirectionName,\n      from: fromIndex,\n      to: targetIndex\n    })\n  }\n\n  _setActiveIndicatorElement(element) {\n    if (this._indicatorsElement) {\n      const activeIndicator = SelectorEngine.findOne(SELECTOR_ACTIVE, this._indicatorsElement)\n\n      activeIndicator.classList.remove(CLASS_NAME_ACTIVE)\n      activeIndicator.removeAttribute('aria-current')\n\n      const indicators = SelectorEngine.find(SELECTOR_INDICATOR, this._indicatorsElement)\n\n      for (let i = 0; i < indicators.length; i++) {\n        if (Number.parseInt(indicators[i].getAttribute('data-bs-slide-to'), 10) === this._getItemIndex(element)) {\n          indicators[i].classList.add(CLASS_NAME_ACTIVE)\n          indicators[i].setAttribute('aria-current', 'true')\n          break\n        }\n      }\n    }\n  }\n\n  _updateInterval() {\n    const element = this._activeElement || SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n\n    if (!element) {\n      return\n    }\n\n    const elementInterval = Number.parseInt(element.getAttribute('data-bs-interval'), 10)\n\n    if (elementInterval) {\n      this._config.defaultInterval = this._config.defaultInterval || this._config.interval\n      this._config.interval = elementInterval\n    } else {\n      this._config.interval = this._config.defaultInterval || this._config.interval\n    }\n  }\n\n  _slide(directionOrOrder, element) {\n    const order = this._directionToOrder(directionOrOrder)\n    const activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeElementIndex = this._getItemIndex(activeElement)\n    const nextElement = element || this._getItemByOrder(order, activeElement)\n\n    const nextElementIndex = this._getItemIndex(nextElement)\n    const isCycling = Boolean(this._interval)\n\n    const isNext = order === ORDER_NEXT\n    const directionalClassName = isNext ? CLASS_NAME_START : CLASS_NAME_END\n    const orderClassName = isNext ? CLASS_NAME_NEXT : CLASS_NAME_PREV\n    const eventDirectionName = this._orderToDirection(order)\n\n    if (nextElement && nextElement.classList.contains(CLASS_NAME_ACTIVE)) {\n      this._isSliding = false\n      return\n    }\n\n    const slideEvent = this._triggerSlideEvent(nextElement, eventDirectionName)\n    if (slideEvent.defaultPrevented) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      return\n    }\n\n    this._isSliding = true\n\n    if (isCycling) {\n      this.pause()\n    }\n\n    this._setActiveIndicatorElement(nextElement)\n    this._activeElement = nextElement\n\n    if (this._element.classList.contains(CLASS_NAME_SLIDE)) {\n      nextElement.classList.add(orderClassName)\n\n      reflow(nextElement)\n\n      activeElement.classList.add(directionalClassName)\n      nextElement.classList.add(directionalClassName)\n\n      const transitionDuration = getTransitionDurationFromElement(activeElement)\n\n      EventHandler.one(activeElement, 'transitionend', () => {\n        nextElement.classList.remove(directionalClassName, orderClassName)\n        nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n        activeElement.classList.remove(CLASS_NAME_ACTIVE, orderClassName, directionalClassName)\n\n        this._isSliding = false\n\n        setTimeout(() => {\n          EventHandler.trigger(this._element, EVENT_SLID, {\n            relatedTarget: nextElement,\n            direction: eventDirectionName,\n            from: activeElementIndex,\n            to: nextElementIndex\n          })\n        }, 0)\n      })\n\n      emulateTransitionEnd(activeElement, transitionDuration)\n    } else {\n      activeElement.classList.remove(CLASS_NAME_ACTIVE)\n      nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n      this._isSliding = false\n      EventHandler.trigger(this._element, EVENT_SLID, {\n        relatedTarget: nextElement,\n        direction: eventDirectionName,\n        from: activeElementIndex,\n        to: nextElementIndex\n      })\n    }\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  _directionToOrder(direction) {\n    if (![DIRECTION_RIGHT, DIRECTION_LEFT].includes(direction)) {\n      return direction\n    }\n\n    if (isRTL()) {\n      return direction === DIRECTION_RIGHT ? ORDER_PREV : ORDER_NEXT\n    }\n\n    return direction === DIRECTION_RIGHT ? ORDER_NEXT : ORDER_PREV\n  }\n\n  _orderToDirection(order) {\n    if (![ORDER_NEXT, ORDER_PREV].includes(order)) {\n      return order\n    }\n\n    if (isRTL()) {\n      return order === ORDER_NEXT ? DIRECTION_LEFT : DIRECTION_RIGHT\n    }\n\n    return order === ORDER_NEXT ? DIRECTION_RIGHT : DIRECTION_LEFT\n  }\n\n  // Static\n\n  static carouselInterface(element, config) {\n    let data = Data.get(element, DATA_KEY)\n    let _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element)\n    }\n\n    if (typeof config === 'object') {\n      _config = {\n        ..._config,\n        ...config\n      }\n    }\n\n    const action = typeof config === 'string' ? config : _config.slide\n\n    if (!data) {\n      data = new Carousel(element, _config)\n    }\n\n    if (typeof config === 'number') {\n      data.to(config)\n    } else if (typeof action === 'string') {\n      if (typeof data[action] === 'undefined') {\n        throw new TypeError(`No method named \"${action}\"`)\n      }\n\n      data[action]()\n    } else if (_config.interval && _config.ride) {\n      data.pause()\n      data.cycle()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Carousel.carouselInterface(this, config)\n    })\n  }\n\n  static dataApiClickHandler(event) {\n    const target = getElementFromSelector(this)\n\n    if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n      return\n    }\n\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n    const slideIndex = this.getAttribute('data-bs-slide-to')\n\n    if (slideIndex) {\n      config.interval = false\n    }\n\n    Carousel.carouselInterface(target, config)\n\n    if (slideIndex) {\n      Data.get(target, DATA_KEY).to(slideIndex)\n    }\n\n    event.preventDefault()\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, Carousel.dataApiClickHandler)\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE)\n\n  for (let i = 0, len = carousels.length; i < len; i++) {\n    Carousel.carouselInterface(carousels[i], Data.get(carousels[i], DATA_KEY))\n  }\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Carousel to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Carousel)\n\nexport default Carousel\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  emulateTransitionEnd,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isElement,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'collapse'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  toggle: true,\n  parent: ''\n}\n\nconst DefaultType = {\n  toggle: 'boolean',\n  parent: '(string|element)'\n}\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.show, .collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"collapse\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Collapse extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._isTransitioning = false\n    this._config = this._getConfig(config)\n    this._triggerArray = SelectorEngine.find(\n      `${SELECTOR_DATA_TOGGLE}[href=\"#${this._element.id}\"],` +\n      `${SELECTOR_DATA_TOGGLE}[data-bs-target=\"#${this._element.id}\"]`\n    )\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggleList.length; i < len; i++) {\n      const elem = toggleList[i]\n      const selector = getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElem => foundElem === this._element)\n\n      if (selector !== null && filterElement.length) {\n        this._selector = selector\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._parent = this._config.parent ? this._getParent() : null\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._element, this._triggerArray)\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning || this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    let actives\n    let activesData\n\n    if (this._parent) {\n      actives = SelectorEngine.find(SELECTOR_ACTIVES, this._parent)\n        .filter(elem => {\n          if (typeof this._config.parent === 'string') {\n            return elem.getAttribute('data-bs-parent') === this._config.parent\n          }\n\n          return elem.classList.contains(CLASS_NAME_COLLAPSE)\n        })\n\n      if (actives.length === 0) {\n        actives = null\n      }\n    }\n\n    const container = SelectorEngine.findOne(this._selector)\n    if (actives) {\n      const tempActiveData = actives.find(elem => container !== elem)\n      activesData = tempActiveData ? Data.get(tempActiveData, DATA_KEY) : null\n\n      if (activesData && activesData._isTransitioning) {\n        return\n      }\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    if (actives) {\n      actives.forEach(elemActive => {\n        if (container !== elemActive) {\n          Collapse.collapseInterface(elemActive, 'hide')\n        }\n\n        if (!activesData) {\n          Data.set(elemActive, DATA_KEY, null)\n        }\n      })\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    if (this._triggerArray.length) {\n      this._triggerArray.forEach(element => {\n        element.classList.remove(CLASS_NAME_COLLAPSED)\n        element.setAttribute('aria-expanded', true)\n      })\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      this.setTransitioning(false)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n    const transitionDuration = getTransitionDurationFromElement(this._element)\n\n    EventHandler.one(this._element, 'transitionend', complete)\n\n    emulateTransitionEnd(this._element, transitionDuration)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning || !this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    const triggerArrayLength = this._triggerArray.length\n    if (triggerArrayLength > 0) {\n      for (let i = 0; i < triggerArrayLength; i++) {\n        const trigger = this._triggerArray[i]\n        const elem = getElementFromSelector(trigger)\n\n        if (elem && !elem.classList.contains(CLASS_NAME_SHOW)) {\n          trigger.classList.add(CLASS_NAME_COLLAPSED)\n          trigger.setAttribute('aria-expanded', false)\n        }\n      }\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this.setTransitioning(false)\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n    const transitionDuration = getTransitionDurationFromElement(this._element)\n\n    EventHandler.one(this._element, 'transitionend', complete)\n    emulateTransitionEnd(this._element, transitionDuration)\n  }\n\n  setTransitioning(isTransitioning) {\n    this._isTransitioning = isTransitioning\n  }\n\n  dispose() {\n    super.dispose()\n    this._config = null\n    this._parent = null\n    this._triggerArray = null\n    this._isTransitioning = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _getDimension() {\n    return this._element.classList.contains(WIDTH) ? WIDTH : HEIGHT\n  }\n\n  _getParent() {\n    let { parent } = this._config\n\n    if (isElement(parent)) {\n      // it's a jQuery object\n      if (typeof parent.jquery !== 'undefined' || typeof parent[0] !== 'undefined') {\n        parent = parent[0]\n      }\n    } else {\n      parent = SelectorEngine.findOne(parent)\n    }\n\n    const selector = `${SELECTOR_DATA_TOGGLE}[data-bs-parent=\"${parent}\"]`\n\n    SelectorEngine.find(selector, parent)\n      .forEach(element => {\n        const selected = getElementFromSelector(element)\n\n        this._addAriaAndCollapsedClass(\n          selected,\n          [element]\n        )\n      })\n\n    return parent\n  }\n\n  _addAriaAndCollapsedClass(element, triggerArray) {\n    if (!element || !triggerArray.length) {\n      return\n    }\n\n    const isOpen = element.classList.contains(CLASS_NAME_SHOW)\n\n    triggerArray.forEach(elem => {\n      if (isOpen) {\n        elem.classList.remove(CLASS_NAME_COLLAPSED)\n      } else {\n        elem.classList.add(CLASS_NAME_COLLAPSED)\n      }\n\n      elem.setAttribute('aria-expanded', isOpen)\n    })\n  }\n\n  // Static\n\n  static collapseInterface(element, config) {\n    let data = Data.get(element, DATA_KEY)\n    const _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (!data && _config.toggle && typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false\n    }\n\n    if (!data) {\n      data = new Collapse(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Collapse.collapseInterface(this, config)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A' || (event.delegateTarget && event.delegateTarget.tagName === 'A')) {\n    event.preventDefault()\n  }\n\n  const triggerData = Manipulator.getDataAttributes(this)\n  const selector = getSelectorFromElement(this)\n  const selectorElements = SelectorEngine.find(selector)\n\n  selectorElements.forEach(element => {\n    const data = Data.get(element, DATA_KEY)\n    let config\n    if (data) {\n      // update parent attribute\n      if (data._parent === null && typeof triggerData.parent === 'string') {\n        data._config.parent = triggerData.parent\n        data._parent = data._getParent()\n      }\n\n      config = 'toggle'\n    } else {\n      config = triggerData\n    }\n\n    Collapse.collapseInterface(element, config)\n  })\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Collapse to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Collapse)\n\nexport default Collapse\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isElement,\n  isVisible,\n  isRTL,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'dropdown'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst SPACE_KEY = 'Space'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst REGEXP_KEYDOWN = new RegExp(`${ARROW_UP_KEY}|${ARROW_DOWN_KEY}|${ESCAPE_KEY}`)\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DISABLED = 'disabled'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPEND = 'dropend'\nconst CLASS_NAME_DROPSTART = 'dropstart'\nconst CLASS_NAME_NAVBAR = 'navbar'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"dropdown\"]'\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = isRTL() ? 'top-end' : 'top-start'\nconst PLACEMENT_TOPEND = isRTL() ? 'top-start' : 'top-end'\nconst PLACEMENT_BOTTOM = isRTL() ? 'bottom-end' : 'bottom-start'\nconst PLACEMENT_BOTTOMEND = isRTL() ? 'bottom-start' : 'bottom-end'\nconst PLACEMENT_RIGHT = isRTL() ? 'left-start' : 'right-start'\nconst PLACEMENT_LEFT = isRTL() ? 'right-start' : 'left-start'\n\nconst Default = {\n  offset: [0, 2],\n  boundary: 'clippingParents',\n  reference: 'toggle',\n  display: 'dynamic',\n  popperConfig: null\n}\n\nconst DefaultType = {\n  offset: '(array|string|function)',\n  boundary: '(string|element)',\n  reference: '(string|element|object)',\n  display: 'string',\n  popperConfig: '(null|object|function)'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Dropdown extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._popper = null\n    this._config = this._getConfig(config)\n    this._menu = this._getMenuElement()\n    this._inNavbar = this._detectNavbar()\n\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const isActive = this._element.classList.contains(CLASS_NAME_SHOW)\n\n    Dropdown.clearMenus()\n\n    if (isActive) {\n      return\n    }\n\n    this.show()\n  }\n\n  show() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED) || this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    // Totally disable Popper for Dropdowns in Navbar\n    if (this._inNavbar) {\n      Manipulator.setDataAttribute(this._menu, 'popper', 'none')\n    } else {\n      if (typeof Popper === 'undefined') {\n        throw new TypeError('Bootstrap\\'s dropdowns require Popper (https://popper.js.org)')\n      }\n\n      let referenceElement = this._element\n\n      if (this._config.reference === 'parent') {\n        referenceElement = parent\n      } else if (isElement(this._config.reference)) {\n        referenceElement = this._config.reference\n\n        // Check if it's jQuery element\n        if (typeof this._config.reference.jquery !== 'undefined') {\n          referenceElement = this._config.reference[0]\n        }\n      } else if (typeof this._config.reference === 'object') {\n        referenceElement = this._config.reference\n      }\n\n      const popperConfig = this._getPopperConfig()\n      const isDisplayStatic = popperConfig.modifiers.find(modifier => modifier.name === 'applyStyles' && modifier.enabled === false)\n\n      this._popper = Popper.createPopper(referenceElement, this._menu, popperConfig)\n\n      if (isDisplayStatic) {\n        Manipulator.setDataAttribute(this._menu, 'popper', 'static')\n      }\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement &&\n      !parent.closest(SELECTOR_NAVBAR_NAV)) {\n      [].concat(...document.body.children)\n        .forEach(elem => EventHandler.on(elem, 'mouseover', null, noop()))\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    this._menu.classList.toggle(CLASS_NAME_SHOW)\n    this._element.classList.toggle(CLASS_NAME_SHOW)\n    EventHandler.trigger(this._element, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED) || !this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE, relatedTarget)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._menu.classList.toggle(CLASS_NAME_SHOW)\n    this._element.classList.toggle(CLASS_NAME_SHOW)\n    Manipulator.removeDataAttribute(this._menu, 'popper')\n    EventHandler.trigger(this._element, EVENT_HIDDEN, relatedTarget)\n  }\n\n  dispose() {\n    EventHandler.off(this._element, EVENT_KEY)\n    this._menu = null\n\n    if (this._popper) {\n      this._popper.destroy()\n      this._popper = null\n    }\n\n    super.dispose()\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Private\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_CLICK, event => {\n      event.preventDefault()\n      this.toggle()\n    })\n  }\n\n  _getConfig(config) {\n    config = {\n      ...this.constructor.Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...config\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    if (typeof config.reference === 'object' && !isElement(config.reference) &&\n      typeof config.reference.getBoundingClientRect !== 'function'\n    ) {\n      // Popper virtual elements require a getBoundingClientRect method\n      throw new TypeError(`${NAME.toUpperCase()}: Option \"reference\" provided type \"object\" without a required \"getBoundingClientRect\" method.`)\n    }\n\n    return config\n  }\n\n  _getMenuElement() {\n    return SelectorEngine.next(this._element, SELECTOR_MENU)[0]\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._element.parentNode\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPEND)) {\n      return PLACEMENT_RIGHT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPSTART)) {\n      return PLACEMENT_LEFT\n    }\n\n    // We need to trim the value because custom properties can also include spaces\n    const isEnd = getComputedStyle(this._menu).getPropertyValue('--bs-position').trim() === 'end'\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      return isEnd ? PLACEMENT_TOPEND : PLACEMENT_TOP\n    }\n\n    return isEnd ? PLACEMENT_BOTTOMEND : PLACEMENT_BOTTOM\n  }\n\n  _detectNavbar() {\n    return this._element.closest(`.${CLASS_NAME_NAVBAR}`) !== null\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(val => Number.parseInt(val, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const defaultBsPopperConfig = {\n      placement: this._getPlacement(),\n      modifiers: [{\n        name: 'preventOverflow',\n        options: {\n          boundary: this._config.boundary\n        }\n      },\n      {\n        name: 'offset',\n        options: {\n          offset: this._getOffset()\n        }\n      }]\n    }\n\n    // Disable Popper if we have a static display\n    if (this._config.display === 'static') {\n      defaultBsPopperConfig.modifiers = [{\n        name: 'applyStyles',\n        enabled: false\n      }]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...(typeof this._config.popperConfig === 'function' ? this._config.popperConfig(defaultBsPopperConfig) : this._config.popperConfig)\n    }\n  }\n\n  // Static\n\n  static dropdownInterface(element, config) {\n    let data = Data.get(element, DATA_KEY)\n    const _config = typeof config === 'object' ? config : null\n\n    if (!data) {\n      data = new Dropdown(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Dropdown.dropdownInterface(this, config)\n    })\n  }\n\n  static clearMenus(event) {\n    if (event) {\n      if (event.button === RIGHT_MOUSE_BUTTON || (event.type === 'keyup' && event.key !== TAB_KEY)) {\n        return\n      }\n\n      if (/input|select|textarea|form/i.test(event.target.tagName)) {\n        return\n      }\n    }\n\n    const toggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggles.length; i < len; i++) {\n      const context = Data.get(toggles[i], DATA_KEY)\n      const relatedTarget = {\n        relatedTarget: toggles[i]\n      }\n\n      if (event && event.type === 'click') {\n        relatedTarget.clickEvent = event\n      }\n\n      if (!context) {\n        continue\n      }\n\n      const dropdownMenu = context._menu\n      if (!toggles[i].classList.contains(CLASS_NAME_SHOW)) {\n        continue\n      }\n\n      if (event) {\n        // Don't close the menu if the clicked element or one of its parents is the dropdown button\n        if ([context._element].some(element => event.composedPath().includes(element))) {\n          continue\n        }\n\n        // Tab navigation through the dropdown menu shouldn't close the menu\n        if (event.type === 'keyup' && event.key === TAB_KEY && dropdownMenu.contains(event.target)) {\n          continue\n        }\n      }\n\n      const hideEvent = EventHandler.trigger(toggles[i], EVENT_HIDE, relatedTarget)\n      if (hideEvent.defaultPrevented) {\n        continue\n      }\n\n      // If this is a touch-enabled device we remove the extra\n      // empty mouseover listeners we added for iOS support\n      if ('ontouchstart' in document.documentElement) {\n        [].concat(...document.body.children)\n          .forEach(elem => EventHandler.off(elem, 'mouseover', null, noop()))\n      }\n\n      toggles[i].setAttribute('aria-expanded', 'false')\n\n      if (context._popper) {\n        context._popper.destroy()\n      }\n\n      dropdownMenu.classList.remove(CLASS_NAME_SHOW)\n      toggles[i].classList.remove(CLASS_NAME_SHOW)\n      Manipulator.removeDataAttribute(dropdownMenu, 'popper')\n      EventHandler.trigger(toggles[i], EVENT_HIDDEN, relatedTarget)\n    }\n  }\n\n  static getParentFromElement(element) {\n    return getElementFromSelector(element) || element.parentNode\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not input/textarea:\n    //  - And not a key in REGEXP_KEYDOWN => not a dropdown command\n    // If input/textarea:\n    //  - If space key => not a dropdown command\n    //  - If key is other than escape\n    //    - If key is not up or down => not a dropdown command\n    //    - If trigger inside the menu => not a dropdown command\n    if (/input|textarea/i.test(event.target.tagName) ?\n      event.key === SPACE_KEY || (event.key !== ESCAPE_KEY &&\n      ((event.key !== ARROW_DOWN_KEY && event.key !== ARROW_UP_KEY) ||\n        event.target.closest(SELECTOR_MENU))) :\n      !REGEXP_KEYDOWN.test(event.key)) {\n      return\n    }\n\n    event.preventDefault()\n    event.stopPropagation()\n\n    if (this.disabled || this.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this)\n    const isActive = this.classList.contains(CLASS_NAME_SHOW)\n\n    if (event.key === ESCAPE_KEY) {\n      const button = this.matches(SELECTOR_DATA_TOGGLE) ? this : SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0]\n      button.focus()\n      Dropdown.clearMenus()\n      return\n    }\n\n    if (!isActive && (event.key === ARROW_UP_KEY || event.key === ARROW_DOWN_KEY)) {\n      const button = this.matches(SELECTOR_DATA_TOGGLE) ? this : SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0]\n      button.click()\n      return\n    }\n\n    if (!isActive || event.key === SPACE_KEY) {\n      Dropdown.clearMenus()\n      return\n    }\n\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, parent).filter(isVisible)\n\n    if (!items.length) {\n      return\n    }\n\n    let index = items.indexOf(event.target)\n\n    // Up\n    if (event.key === ARROW_UP_KEY && index > 0) {\n      index--\n    }\n\n    // Down\n    if (event.key === ARROW_DOWN_KEY && index < items.length - 1) {\n      index++\n    }\n\n    // index is -1 if the first keydown is an ArrowUp\n    index = index === -1 ? 0 : index\n\n    items[index].focus()\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  Dropdown.dropdownInterface(this)\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Dropdown to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Dropdown)\n\nexport default Dropdown\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isVisible,\n  isRTL,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'modal'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  focus: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  focus: 'boolean'\n}\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEUP_DISMISS = `mouseup.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SCROLLBAR_MEASURER = 'modal-scrollbar-measure'\nconst CLASS_NAME_BACKDROP = 'modal-backdrop'\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"modal\"]'\nconst SELECTOR_DATA_DISMISS = '[data-bs-dismiss=\"modal\"]'\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Modal extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, this._element)\n    this._backdrop = null\n    this._isShown = false\n    this._isBodyOverflowing = false\n    this._ignoreBackdropClick = false\n    this._isTransitioning = false\n    this._scrollbarWidth = 0\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    if (this._isAnimated()) {\n      this._isTransitioning = true\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (this._isShown || showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n\n    this._checkScrollbar()\n    this._setScrollbar()\n\n    this._adjustDialog()\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, event => this.hide(event))\n\n    EventHandler.on(this._dialog, EVENT_MOUSEDOWN_DISMISS, () => {\n      EventHandler.one(this._element, EVENT_MOUSEUP_DISMISS, event => {\n        if (event.target === this._element) {\n          this._ignoreBackdropClick = true\n        }\n      })\n    })\n\n    this._showBackdrop(() => this._showElement(relatedTarget))\n  }\n\n  hide(event) {\n    if (event) {\n      event.preventDefault()\n    }\n\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    const isAnimated = this._isAnimated()\n\n    if (isAnimated) {\n      this._isTransitioning = true\n    }\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n    EventHandler.off(this._dialog, EVENT_MOUSEDOWN_DISMISS)\n\n    if (isAnimated) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, 'transitionend', event => this._hideModal(event))\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      this._hideModal()\n    }\n  }\n\n  dispose() {\n    [window, this._element, this._dialog]\n      .forEach(htmlElement => EventHandler.off(htmlElement, EVENT_KEY))\n\n    super.dispose()\n\n    /**\n     * `document` has 2 events `EVENT_FOCUSIN` and `EVENT_CLICK_DATA_API`\n     * Do not move `document` in `htmlElements` array\n     * It will remove `EVENT_CLICK_DATA_API` event that should remain\n     */\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    this._config = null\n    this._dialog = null\n    this._backdrop = null\n    this._isShown = null\n    this._isBodyOverflowing = null\n    this._ignoreBackdropClick = null\n    this._isTransitioning = null\n    this._scrollbarWidth = null\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _showElement(relatedTarget) {\n    const isAnimated = this._isAnimated()\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n\n    if (!this._element.parentNode || this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {\n      // Don't move modal's DOM position\n      document.body.appendChild(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    if (isAnimated) {\n      reflow(this._element)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    if (this._config.focus) {\n      this._enforceFocus()\n    }\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._element.focus()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    if (isAnimated) {\n      const transitionDuration = getTransitionDurationFromElement(this._dialog)\n\n      EventHandler.one(this._dialog, 'transitionend', transitionComplete)\n      emulateTransitionEnd(this._dialog, transitionDuration)\n    } else {\n      transitionComplete()\n    }\n  }\n\n  _enforceFocus() {\n    EventHandler.off(document, EVENT_FOCUSIN) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => {\n      if (document !== event.target &&\n          this._element !== event.target &&\n          !this._element.contains(event.target)) {\n        this._element.focus()\n      }\n    })\n  }\n\n  _setEscapeEvent() {\n    if (this._isShown) {\n      EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n        if (this._config.keyboard && event.key === ESCAPE_KEY) {\n          event.preventDefault()\n          this.hide()\n        } else if (!this._config.keyboard && event.key === ESCAPE_KEY) {\n          this._triggerBackdropTransition()\n        }\n      })\n    } else {\n      EventHandler.off(this._element, EVENT_KEYDOWN_DISMISS)\n    }\n  }\n\n  _setResizeEvent() {\n    if (this._isShown) {\n      EventHandler.on(window, EVENT_RESIZE, () => this._adjustDialog())\n    } else {\n      EventHandler.off(window, EVENT_RESIZE)\n    }\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n    this._showBackdrop(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._resetScrollbar()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _removeBackdrop() {\n    this._backdrop.parentNode.removeChild(this._backdrop)\n    this._backdrop = null\n  }\n\n  _showBackdrop(callback) {\n    const isAnimated = this._isAnimated()\n    if (this._isShown && this._config.backdrop) {\n      this._backdrop = document.createElement('div')\n      this._backdrop.className = CLASS_NAME_BACKDROP\n\n      if (isAnimated) {\n        this._backdrop.classList.add(CLASS_NAME_FADE)\n      }\n\n      document.body.appendChild(this._backdrop)\n\n      EventHandler.on(this._element, EVENT_CLICK_DISMISS, event => {\n        if (this._ignoreBackdropClick) {\n          this._ignoreBackdropClick = false\n          return\n        }\n\n        if (event.target !== event.currentTarget) {\n          return\n        }\n\n        if (this._config.backdrop === 'static') {\n          this._triggerBackdropTransition()\n        } else {\n          this.hide()\n        }\n      })\n\n      if (isAnimated) {\n        reflow(this._backdrop)\n      }\n\n      this._backdrop.classList.add(CLASS_NAME_SHOW)\n\n      if (!isAnimated) {\n        callback()\n        return\n      }\n\n      const backdropTransitionDuration = getTransitionDurationFromElement(this._backdrop)\n\n      EventHandler.one(this._backdrop, 'transitionend', callback)\n      emulateTransitionEnd(this._backdrop, backdropTransitionDuration)\n    } else if (!this._isShown && this._backdrop) {\n      this._backdrop.classList.remove(CLASS_NAME_SHOW)\n\n      const callbackRemove = () => {\n        this._removeBackdrop()\n        callback()\n      }\n\n      if (isAnimated) {\n        const backdropTransitionDuration = getTransitionDurationFromElement(this._backdrop)\n        EventHandler.one(this._backdrop, 'transitionend', callbackRemove)\n        emulateTransitionEnd(this._backdrop, backdropTransitionDuration)\n      } else {\n        callbackRemove()\n      }\n    } else {\n      callback()\n    }\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_FADE)\n  }\n\n  _triggerBackdropTransition() {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n\n    if (!isModalOverflowing) {\n      this._element.style.overflowY = 'hidden'\n    }\n\n    this._element.classList.add(CLASS_NAME_STATIC)\n    const modalTransitionDuration = getTransitionDurationFromElement(this._dialog)\n    EventHandler.off(this._element, 'transitionend')\n    EventHandler.one(this._element, 'transitionend', () => {\n      this._element.classList.remove(CLASS_NAME_STATIC)\n      if (!isModalOverflowing) {\n        EventHandler.one(this._element, 'transitionend', () => {\n          this._element.style.overflowY = ''\n        })\n        emulateTransitionEnd(this._element, modalTransitionDuration)\n      }\n    })\n    emulateTransitionEnd(this._element, modalTransitionDuration)\n    this._element.focus()\n  }\n\n  // ----------------------------------------------------------------------\n  // the following methods are used to handle overflowing modals\n  // ----------------------------------------------------------------------\n\n  _adjustDialog() {\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n\n    if ((!this._isBodyOverflowing && isModalOverflowing && !isRTL()) || (this._isBodyOverflowing && !isModalOverflowing && isRTL())) {\n      this._element.style.paddingLeft = `${this._scrollbarWidth}px`\n    }\n\n    if ((this._isBodyOverflowing && !isModalOverflowing && !isRTL()) || (!this._isBodyOverflowing && isModalOverflowing && isRTL())) {\n      this._element.style.paddingRight = `${this._scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  _checkScrollbar() {\n    const rect = document.body.getBoundingClientRect()\n    this._isBodyOverflowing = Math.round(rect.left + rect.right) < window.innerWidth\n    this._scrollbarWidth = this._getScrollbarWidth()\n  }\n\n  _setScrollbar() {\n    if (this._isBodyOverflowing) {\n      this._setElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight', calculatedValue => calculatedValue + this._scrollbarWidth)\n      this._setElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight', calculatedValue => calculatedValue - this._scrollbarWidth)\n      this._setElementAttributes('body', 'paddingRight', calculatedValue => calculatedValue + this._scrollbarWidth)\n    }\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n  }\n\n  _setElementAttributes(selector, styleProp, callback) {\n    SelectorEngine.find(selector)\n      .forEach(element => {\n        if (element !== document.body && window.innerWidth > element.clientWidth + this._scrollbarWidth) {\n          return\n        }\n\n        const actualValue = element.style[styleProp]\n        const calculatedValue = window.getComputedStyle(element)[styleProp]\n        Manipulator.setDataAttribute(element, styleProp, actualValue)\n        element.style[styleProp] = callback(Number.parseFloat(calculatedValue)) + 'px'\n      })\n  }\n\n  _resetScrollbar() {\n    this._resetElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight')\n    this._resetElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight')\n    this._resetElementAttributes('body', 'paddingRight')\n  }\n\n  _resetElementAttributes(selector, styleProp) {\n    SelectorEngine.find(selector).forEach(element => {\n      const value = Manipulator.getDataAttribute(element, styleProp)\n      if (typeof value === 'undefined' && element === document.body) {\n        element.style[styleProp] = ''\n      } else {\n        Manipulator.removeDataAttribute(element, styleProp)\n        element.style[styleProp] = value\n      }\n    })\n  }\n\n  _getScrollbarWidth() { // thx d.walsh\n    const scrollDiv = document.createElement('div')\n    scrollDiv.className = CLASS_NAME_SCROLLBAR_MEASURER\n    document.body.appendChild(scrollDiv)\n    const scrollbarWidth = scrollDiv.getBoundingClientRect().width - scrollDiv.clientWidth\n    document.body.removeChild(scrollDiv)\n    return scrollbarWidth\n  }\n\n  // Static\n\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n      const _config = {\n        ...Default,\n        ...Manipulator.getDataAttributes(this),\n        ...(typeof config === 'object' && config ? config : {})\n      }\n\n      if (!data) {\n        data = new Modal(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](relatedTarget)\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (this.tagName === 'A' || this.tagName === 'AREA') {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  let data = Data.get(target, DATA_KEY)\n  if (!data) {\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n\n    data = new Modal(target, config)\n  }\n\n  data.toggle(this)\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Modal to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Modal)\n\nexport default Modal\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): util/scrollBar.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport SelectorEngine from '../dom/selector-engine'\nimport Manipulator from '../dom/manipulator'\n\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\n\nconst getWidth = () => {\n  // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n  const documentWidth = document.documentElement.clientWidth\n  return Math.abs(window.innerWidth - documentWidth)\n}\n\nconst hide = (width = getWidth()) => {\n  document.body.style.overflow = 'hidden'\n  _setElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight', calculatedValue => calculatedValue + width)\n  _setElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight', calculatedValue => calculatedValue - width)\n  _setElementAttributes('body', 'paddingRight', calculatedValue => calculatedValue + width)\n}\n\nconst _setElementAttributes = (selector, styleProp, callback) => {\n  const scrollbarWidth = getWidth()\n  SelectorEngine.find(selector)\n    .forEach(element => {\n      if (element !== document.body && window.innerWidth > element.clientWidth + scrollbarWidth) {\n        return\n      }\n\n      const actualValue = element.style[styleProp]\n      const calculatedValue = window.getComputedStyle(element)[styleProp]\n      Manipulator.setDataAttribute(element, styleProp, actualValue)\n      element.style[styleProp] = callback(Number.parseFloat(calculatedValue)) + 'px'\n    })\n}\n\nconst reset = () => {\n  document.body.style.overflow = 'auto'\n  _resetElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight')\n  _resetElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight')\n  _resetElementAttributes('body', 'paddingRight')\n}\n\nconst _resetElementAttributes = (selector, styleProp) => {\n  SelectorEngine.find(selector).forEach(element => {\n    const value = Manipulator.getDataAttribute(element, styleProp)\n    if (typeof value === 'undefined' && element === document.body) {\n      element.style.removeProperty(styleProp)\n    } else {\n      Manipulator.removeDataAttribute(element, styleProp)\n      element.style[styleProp] = value\n    }\n  })\n}\n\nconst isBodyOverflowing = () => {\n  return getWidth() > 0\n}\n\nexport {\n  getWidth,\n  hide,\n  isBodyOverflowing,\n  reset\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): offcanvas.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  getSelectorFromElement,\n  getTransitionDurationFromElement,\n  isDisabled,\n  isVisible,\n  typeCheckConfig\n} from './util/index'\nimport { hide as scrollBarHide, reset as scrollBarReset } from './util/scrollbar'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\nimport SelectorEngine from './dom/selector-engine'\nimport Manipulator from './dom/manipulator'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'offcanvas'\nconst DATA_KEY = 'bs.offcanvas'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  scroll: false\n}\n\nconst DefaultType = {\n  backdrop: 'boolean',\n  keyboard: 'boolean',\n  scroll: 'boolean'\n}\n\nconst CLASS_NAME_BACKDROP_BODY = 'offcanvas-backdrop'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_TOGGLING = 'offcanvas-toggling'\nconst OPEN_SELECTOR = '.offcanvas.show'\nconst ACTIVE_SELECTOR = `${OPEN_SELECTOR}, .${CLASS_NAME_TOGGLING}`\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\n\nconst SELECTOR_DATA_DISMISS = '[data-bs-dismiss=\"offcanvas\"]'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"offcanvas\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Offcanvas extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._isShown = false\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, { relatedTarget })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._element.style.visibility = 'visible'\n\n    if (this._config.backdrop) {\n      document.body.classList.add(CLASS_NAME_BACKDROP_BODY)\n    }\n\n    if (!this._config.scroll) {\n      scrollBarHide()\n    }\n\n    this._element.classList.add(CLASS_NAME_TOGGLING)\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    const completeCallBack = () => {\n      this._element.classList.remove(CLASS_NAME_TOGGLING)\n      EventHandler.trigger(this._element, EVENT_SHOWN, { relatedTarget })\n      this._enforceFocusOnElement(this._element)\n    }\n\n    setTimeout(completeCallBack, getTransitionDurationFromElement(this._element))\n  }\n\n  hide() {\n    if (!this._isShown) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._element.classList.add(CLASS_NAME_TOGGLING)\n    EventHandler.off(document, EVENT_FOCUSIN)\n    this._element.blur()\n    this._isShown = false\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    const completeCallback = () => {\n      this._element.setAttribute('aria-hidden', true)\n      this._element.removeAttribute('aria-modal')\n      this._element.removeAttribute('role')\n      this._element.style.visibility = 'hidden'\n\n      if (this._config.backdrop) {\n        document.body.classList.remove(CLASS_NAME_BACKDROP_BODY)\n      }\n\n      if (!this._config.scroll) {\n        scrollBarReset()\n      }\n\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n      this._element.classList.remove(CLASS_NAME_TOGGLING)\n    }\n\n    setTimeout(completeCallback, getTransitionDurationFromElement(this._element))\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' ? config : {})\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _enforceFocusOnElement(element) {\n    EventHandler.off(document, EVENT_FOCUSIN) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => {\n      if (document !== event.target &&\n        element !== event.target &&\n        !element.contains(event.target)) {\n        element.focus()\n      }\n    })\n    element.focus()\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, () => this.hide())\n\n    EventHandler.on(document, 'keydown', event => {\n      if (this._config.keyboard && event.key === ESCAPE_KEY) {\n        this.hide()\n      }\n    })\n\n    EventHandler.on(document, EVENT_CLICK_DATA_API, event => {\n      const target = SelectorEngine.findOne(getSelectorFromElement(event.target))\n      if (!this._element.contains(event.target) && target !== this._element) {\n        this.hide()\n      }\n    })\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Data.get(this, DATA_KEY) || new Offcanvas(this, typeof config === 'object' ? config : {})\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  EventHandler.one(target, EVENT_HIDDEN, () => {\n    // focus on trigger when it is closed\n    if (isVisible(this)) {\n      this.focus()\n    }\n  })\n\n  // avoid conflict when clicking a toggler of an offcanvas, while another is open\n  const allReadyOpen = SelectorEngine.findOne(ACTIVE_SELECTOR)\n  if (allReadyOpen && allReadyOpen !== target) {\n    return\n  }\n\n  const data = Data.get(target, DATA_KEY) || new Offcanvas(target)\n\n  data.toggle(this)\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  SelectorEngine.find(OPEN_SELECTOR).forEach(el => (Data.get(el, DATA_KEY) || new Offcanvas(el)).show())\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\ndefineJQueryPlugin(NAME, Offcanvas)\n\nexport default Offcanvas\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttrs = new Set([\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n])\n\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/i\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[\\d+/a-z]+=*$/i\n\nconst allowedAttribute = (attr, allowedAttributeList) => {\n  const attrName = attr.nodeName.toLowerCase()\n\n  if (allowedAttributeList.includes(attrName)) {\n    if (uriAttrs.has(attrName)) {\n      return Boolean(SAFE_URL_PATTERN.test(attr.nodeValue) || DATA_URL_PATTERN.test(attr.nodeValue))\n    }\n\n    return true\n  }\n\n  const regExp = allowedAttributeList.filter(attrRegex => attrRegex instanceof RegExp)\n\n  // Check if a regular expression validates the attribute.\n  for (let i = 0, len = regExp.length; i < len; i++) {\n    if (regExp[i].test(attrName)) {\n      return true\n    }\n  }\n\n  return false\n}\n\nexport const DefaultAllowlist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n\nexport function sanitizeHtml(unsafeHtml, allowList, sanitizeFn) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFn && typeof sanitizeFn === 'function') {\n    return sanitizeFn(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const allowlistKeys = Object.keys(allowList)\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (let i = 0, len = elements.length; i < len; i++) {\n    const el = elements[i]\n    const elName = el.nodeName.toLowerCase()\n\n    if (!allowlistKeys.includes(elName)) {\n      el.parentNode.removeChild(el)\n\n      continue\n    }\n\n    const attributeList = [].concat(...el.attributes)\n    const allowedAttributes = [].concat(allowList['*'] || [], allowList[elName] || [])\n\n    attributeList.forEach(attr => {\n      if (!allowedAttribute(attr, allowedAttributes)) {\n        el.removeAttribute(attr.nodeName)\n      }\n    })\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\n\nimport {\n  defineJQueryPlugin,\n  emulateTransitionEnd,\n  findShadowRoot,\n  getTransitionDurationFromElement,\n  getUID,\n  isElement,\n  isRTL,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport {\n  DefaultAllowlist,\n  sanitizeHtml\n} from './util/sanitizer'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tooltip'\nconst DATA_KEY = 'bs.tooltip'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-tooltip'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\nconst DISALLOWED_ATTRIBUTES = new Set(['sanitize', 'allowList', 'sanitizeFn'])\n\nconst DefaultType = {\n  animation: 'boolean',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string',\n  delay: '(number|object)',\n  html: 'boolean',\n  selector: '(string|boolean)',\n  placement: '(string|function)',\n  offset: '(array|string|function)',\n  container: '(string|element|boolean)',\n  fallbackPlacements: 'array',\n  boundary: '(string|element)',\n  customClass: '(string|function)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  allowList: 'object',\n  popperConfig: '(null|object|function)'\n}\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: isRTL() ? 'left' : 'right',\n  BOTTOM: 'bottom',\n  LEFT: isRTL() ? 'right' : 'left'\n}\n\nconst Default = {\n  animation: true,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n              '<div class=\"tooltip-arrow\"></div>' +\n              '<div class=\"tooltip-inner\"></div>' +\n            '</div>',\n  trigger: 'hover focus',\n  title: '',\n  delay: 0,\n  html: false,\n  selector: false,\n  placement: 'top',\n  offset: [0, 0],\n  container: false,\n  fallbackPlacements: ['top', 'right', 'bottom', 'left'],\n  boundary: 'clippingParents',\n  customClass: '',\n  sanitize: true,\n  sanitizeFn: null,\n  allowList: DefaultAllowlist,\n  popperConfig: null\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst HOVER_STATE_SHOW = 'show'\nconst HOVER_STATE_OUT = 'out'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tooltip extends BaseComponent {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper (https://popper.js.org)')\n    }\n\n    super(element)\n\n    // private\n    this._isEnabled = true\n    this._timeout = 0\n    this._hoverState = ''\n    this._activeTrigger = {}\n    this._popper = null\n\n    // Protected\n    this.config = this._getConfig(config)\n    this.tip = null\n\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle(event) {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (event) {\n      const context = this._initializeOnDelegatedTarget(event)\n\n      context._activeTrigger.click = !context._activeTrigger.click\n\n      if (context._isWithActiveTrigger()) {\n        context._enter(null, context)\n      } else {\n        context._leave(null, context)\n      }\n    } else {\n      if (this.getTipElement().classList.contains(CLASS_NAME_SHOW)) {\n        this._leave(null, this)\n        return\n      }\n\n      this._enter(null, this)\n    }\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    EventHandler.off(this._element, this.constructor.EVENT_KEY)\n    EventHandler.off(this._element.closest(`.${CLASS_NAME_MODAL}`), 'hide.bs.modal', this._hideModalHandler)\n\n    if (this.tip && this.tip.parentNode) {\n      this.tip.parentNode.removeChild(this.tip)\n    }\n\n    this._isEnabled = null\n    this._timeout = null\n    this._hoverState = null\n    this._activeTrigger = null\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._popper = null\n    this.config = null\n    this.tip = null\n    super.dispose()\n  }\n\n  show() {\n    if (this._element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (!(this.isWithContent() && this._isEnabled)) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, this.constructor.Event.SHOW)\n    const shadowRoot = findShadowRoot(this._element)\n    const isInTheDom = shadowRoot === null ?\n      this._element.ownerDocument.documentElement.contains(this._element) :\n      shadowRoot.contains(this._element)\n\n    if (showEvent.defaultPrevented || !isInTheDom) {\n      return\n    }\n\n    const tip = this.getTipElement()\n    const tipId = getUID(this.constructor.NAME)\n\n    tip.setAttribute('id', tipId)\n    this._element.setAttribute('aria-describedby', tipId)\n\n    this.setContent()\n\n    if (this.config.animation) {\n      tip.classList.add(CLASS_NAME_FADE)\n    }\n\n    const placement = typeof this.config.placement === 'function' ?\n      this.config.placement.call(this, tip, this._element) :\n      this.config.placement\n\n    const attachment = this._getAttachment(placement)\n    this._addAttachmentClass(attachment)\n\n    const container = this._getContainer()\n    Data.set(tip, this.constructor.DATA_KEY, this)\n\n    if (!this._element.ownerDocument.documentElement.contains(this.tip)) {\n      container.appendChild(tip)\n      EventHandler.trigger(this._element, this.constructor.Event.INSERTED)\n    }\n\n    if (this._popper) {\n      this._popper.update()\n    } else {\n      this._popper = Popper.createPopper(this._element, tip, this._getPopperConfig(attachment))\n    }\n\n    tip.classList.add(CLASS_NAME_SHOW)\n\n    const customClass = typeof this.config.customClass === 'function' ? this.config.customClass() : this.config.customClass\n    if (customClass) {\n      tip.classList.add(...customClass.split(' '))\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children).forEach(element => {\n        EventHandler.on(element, 'mouseover', noop())\n      })\n    }\n\n    const complete = () => {\n      const prevHoverState = this._hoverState\n\n      this._hoverState = null\n      EventHandler.trigger(this._element, this.constructor.Event.SHOWN)\n\n      if (prevHoverState === HOVER_STATE_OUT) {\n        this._leave(null, this)\n      }\n    }\n\n    if (this.tip.classList.contains(CLASS_NAME_FADE)) {\n      const transitionDuration = getTransitionDurationFromElement(this.tip)\n      EventHandler.one(this.tip, 'transitionend', complete)\n      emulateTransitionEnd(this.tip, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  hide() {\n    if (!this._popper) {\n      return\n    }\n\n    const tip = this.getTipElement()\n    const complete = () => {\n      if (this._isWithActiveTrigger()) {\n        return\n      }\n\n      if (this._hoverState !== HOVER_STATE_SHOW && tip.parentNode) {\n        tip.parentNode.removeChild(tip)\n      }\n\n      this._cleanTipClass()\n      this._element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this._element, this.constructor.Event.HIDDEN)\n\n      if (this._popper) {\n        this._popper.destroy()\n        this._popper = null\n      }\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, this.constructor.Event.HIDE)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children)\n        .forEach(element => EventHandler.off(element, 'mouseover', noop))\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n\n    if (this.tip.classList.contains(CLASS_NAME_FADE)) {\n      const transitionDuration = getTransitionDurationFromElement(tip)\n\n      EventHandler.one(tip, 'transitionend', complete)\n      emulateTransitionEnd(tip, transitionDuration)\n    } else {\n      complete()\n    }\n\n    this._hoverState = ''\n  }\n\n  update() {\n    if (this._popper !== null) {\n      this._popper.update()\n    }\n  }\n\n  // Protected\n\n  isWithContent() {\n    return Boolean(this.getTitle())\n  }\n\n  getTipElement() {\n    if (this.tip) {\n      return this.tip\n    }\n\n    const element = document.createElement('div')\n    element.innerHTML = this.config.template\n\n    this.tip = element.children[0]\n    return this.tip\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TOOLTIP_INNER, tip), this.getTitle())\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  setElementContent(element, content) {\n    if (element === null) {\n      return\n    }\n\n    if (typeof content === 'object' && isElement(content)) {\n      if (content.jquery) {\n        content = content[0]\n      }\n\n      // content is a DOM node or a jQuery\n      if (this.config.html) {\n        if (content.parentNode !== element) {\n          element.innerHTML = ''\n          element.appendChild(content)\n        }\n      } else {\n        element.textContent = content.textContent\n      }\n\n      return\n    }\n\n    if (this.config.html) {\n      if (this.config.sanitize) {\n        content = sanitizeHtml(content, this.config.allowList, this.config.sanitizeFn)\n      }\n\n      element.innerHTML = content\n    } else {\n      element.textContent = content\n    }\n  }\n\n  getTitle() {\n    let title = this._element.getAttribute('data-bs-original-title')\n\n    if (!title) {\n      title = typeof this.config.title === 'function' ?\n        this.config.title.call(this._element) :\n        this.config.title\n    }\n\n    return title\n  }\n\n  updateAttachment(attachment) {\n    if (attachment === 'right') {\n      return 'end'\n    }\n\n    if (attachment === 'left') {\n      return 'start'\n    }\n\n    return attachment\n  }\n\n  // Private\n\n  _initializeOnDelegatedTarget(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || Data.get(event.delegateTarget, dataKey)\n\n    if (!context) {\n      context = new this.constructor(event.delegateTarget, this._getDelegateConfig())\n      Data.set(event.delegateTarget, dataKey, context)\n    }\n\n    return context\n  }\n\n  _getOffset() {\n    const { offset } = this.config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(val => Number.parseInt(val, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _getPopperConfig(attachment) {\n    const defaultBsPopperConfig = {\n      placement: attachment,\n      modifiers: [\n        {\n          name: 'flip',\n          options: {\n            altBoundary: true,\n            fallbackPlacements: this.config.fallbackPlacements\n          }\n        },\n        {\n          name: 'offset',\n          options: {\n            offset: this._getOffset()\n          }\n        },\n        {\n          name: 'preventOverflow',\n          options: {\n            boundary: this.config.boundary\n          }\n        },\n        {\n          name: 'arrow',\n          options: {\n            element: `.${this.constructor.NAME}-arrow`\n          }\n        },\n        {\n          name: 'onChange',\n          enabled: true,\n          phase: 'afterWrite',\n          fn: data => this._handlePopperPlacementChange(data)\n        }\n      ],\n      onFirstUpdate: data => {\n        if (data.options.placement !== data.placement) {\n          this._handlePopperPlacementChange(data)\n        }\n      }\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...(typeof this.config.popperConfig === 'function' ? this.config.popperConfig(defaultBsPopperConfig) : this.config.popperConfig)\n    }\n  }\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${this.updateAttachment(attachment)}`)\n  }\n\n  _getContainer() {\n    if (this.config.container === false) {\n      return document.body\n    }\n\n    if (isElement(this.config.container)) {\n      return this.config.container\n    }\n\n    return SelectorEngine.findOne(this.config.container)\n  }\n\n  _getAttachment(placement) {\n    return AttachmentMap[placement.toUpperCase()]\n  }\n\n  _setListeners() {\n    const triggers = this.config.trigger.split(' ')\n\n    triggers.forEach(trigger => {\n      if (trigger === 'click') {\n        EventHandler.on(this._element, this.constructor.Event.CLICK, this.config.selector, event => this.toggle(event))\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSEENTER :\n          this.constructor.Event.FOCUSIN\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSELEAVE :\n          this.constructor.Event.FOCUSOUT\n\n        EventHandler.on(this._element, eventIn, this.config.selector, event => this._enter(event))\n        EventHandler.on(this._element, eventOut, this.config.selector, event => this._leave(event))\n      }\n    })\n\n    this._hideModalHandler = () => {\n      if (this._element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this._element.closest(`.${CLASS_NAME_MODAL}`), 'hide.bs.modal', this._hideModalHandler)\n\n    if (this.config.selector) {\n      this.config = {\n        ...this.config,\n        trigger: 'manual',\n        selector: ''\n      }\n    } else {\n      this._fixTitle()\n    }\n  }\n\n  _fixTitle() {\n    const title = this._element.getAttribute('title')\n    const originalTitleType = typeof this._element.getAttribute('data-bs-original-title')\n\n    if (title || originalTitleType !== 'string') {\n      this._element.setAttribute('data-bs-original-title', title || '')\n      if (title && !this._element.getAttribute('aria-label') && !this._element.textContent) {\n        this._element.setAttribute('aria-label', title)\n      }\n\n      this._element.setAttribute('title', '')\n    }\n  }\n\n  _enter(event, context) {\n    context = this._initializeOnDelegatedTarget(event, context)\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = true\n    }\n\n    if (context.getTipElement().classList.contains(CLASS_NAME_SHOW) || context._hoverState === HOVER_STATE_SHOW) {\n      context._hoverState = HOVER_STATE_SHOW\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_SHOW\n\n    if (!context.config.delay || !context.config.delay.show) {\n      context.show()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_SHOW) {\n        context.show()\n      }\n    }, context.config.delay.show)\n  }\n\n  _leave(event, context) {\n    context = this._initializeOnDelegatedTarget(event, context)\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = context._element.contains(event.relatedTarget)\n    }\n\n    if (context._isWithActiveTrigger()) {\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_OUT\n\n    if (!context.config.delay || !context.config.delay.hide) {\n      context.hide()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_OUT) {\n        context.hide()\n      }\n    }, context.config.delay.hide)\n  }\n\n  _isWithActiveTrigger() {\n    for (const trigger in this._activeTrigger) {\n      if (this._activeTrigger[trigger]) {\n        return true\n      }\n    }\n\n    return false\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this._element)\n\n    Object.keys(dataAttributes).forEach(dataAttr => {\n      if (DISALLOWED_ATTRIBUTES.has(dataAttr)) {\n        delete dataAttributes[dataAttr]\n      }\n    })\n\n    if (config && typeof config.container === 'object' && config.container.jquery) {\n      config.container = config.container[0]\n    }\n\n    config = {\n      ...this.constructor.Default,\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    if (config.sanitize) {\n      config.template = sanitizeHtml(config.template, config.allowList, config.sanitizeFn)\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    if (this.config) {\n      for (const key in this.config) {\n        if (this.constructor.Default[key] !== this.config[key]) {\n          config[key] = this.config[key]\n        }\n      }\n    }\n\n    return config\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  _handlePopperPlacementChange(popperData) {\n    const { state } = popperData\n\n    if (!state) {\n      return\n    }\n\n    this.tip = state.elements.popper\n    this._cleanTipClass()\n    this._addAttachmentClass(this._getAttachment(state.placement))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Tooltip(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tooltip to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Tooltip)\n\nexport default Tooltip\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport Data from './dom/data'\nimport SelectorEngine from './dom/selector-engine'\nimport Tooltip from './tooltip'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'popover'\nconst DATA_KEY = 'bs.popover'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-popover'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\n\nconst Default = {\n  ...Tooltip.Default,\n  placement: 'right',\n  offset: [0, 8],\n  trigger: 'click',\n  content: '',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n              '<div class=\"popover-arrow\"></div>' +\n                '<h3 class=\"popover-header\"></h3>' +\n              '<div class=\"popover-body\"></div>' +\n            '</div>'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(string|element|function)'\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Popover extends Tooltip {\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Overrides\n\n  isWithContent() {\n    return this.getTitle() || this._getContent()\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n\n    // we use append for html objects to maintain js events\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TITLE, tip), this.getTitle())\n    let content = this._getContent()\n    if (typeof content === 'function') {\n      content = content.call(this._element)\n    }\n\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_CONTENT, tip), content)\n\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  // Private\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${this.updateAttachment(attachment)}`)\n  }\n\n  _getContent() {\n    return this._element.getAttribute('data-bs-content') || this.config.content\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n      const _config = typeof config === 'object' ? config : null\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Popover(this, _config)\n        Data.set(this, DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Popover to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Popover)\n\nexport default Popover\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getSelectorFromElement,\n  getUID,\n  isElement,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'scrollspy'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  offset: 10,\n  method: 'auto',\n  target: ''\n}\n\nconst DefaultType = {\n  offset: 'number',\n  method: 'string',\n  target: '(string|element)'\n}\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_SCROLL = `scroll${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-bs-spy=\"scroll\"]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst METHOD_OFFSET = 'offset'\nconst METHOD_POSITION = 'position'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass ScrollSpy extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n    this._scrollElement = this._element.tagName === 'BODY' ? window : this._element\n    this._config = this._getConfig(config)\n    this._selector = `${this._config.target} ${SELECTOR_NAV_LINKS}, ${this._config.target} ${SELECTOR_LIST_ITEMS}, ${this._config.target} .${CLASS_NAME_DROPDOWN_ITEM}`\n    this._offsets = []\n    this._targets = []\n    this._activeTarget = null\n    this._scrollHeight = 0\n\n    EventHandler.on(this._scrollElement, EVENT_SCROLL, () => this._process())\n\n    this.refresh()\n    this._process()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  refresh() {\n    const autoMethod = this._scrollElement === this._scrollElement.window ?\n      METHOD_OFFSET :\n      METHOD_POSITION\n\n    const offsetMethod = this._config.method === 'auto' ?\n      autoMethod :\n      this._config.method\n\n    const offsetBase = offsetMethod === METHOD_POSITION ?\n      this._getScrollTop() :\n      0\n\n    this._offsets = []\n    this._targets = []\n    this._scrollHeight = this._getScrollHeight()\n\n    const targets = SelectorEngine.find(this._selector)\n\n    targets.map(element => {\n      const targetSelector = getSelectorFromElement(element)\n      const target = targetSelector ? SelectorEngine.findOne(targetSelector) : null\n\n      if (target) {\n        const targetBCR = target.getBoundingClientRect()\n        if (targetBCR.width || targetBCR.height) {\n          return [\n            Manipulator[offsetMethod](target).top + offsetBase,\n            targetSelector\n          ]\n        }\n      }\n\n      return null\n    })\n      .filter(item => item)\n      .sort((a, b) => a[0] - b[0])\n      .forEach(item => {\n        this._offsets.push(item[0])\n        this._targets.push(item[1])\n      })\n  }\n\n  dispose() {\n    super.dispose()\n    EventHandler.off(this._scrollElement, EVENT_KEY)\n\n    this._scrollElement = null\n    this._config = null\n    this._selector = null\n    this._offsets = null\n    this._targets = null\n    this._activeTarget = null\n    this._scrollHeight = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (typeof config.target !== 'string' && isElement(config.target)) {\n      let { id } = config.target\n      if (!id) {\n        id = getUID(NAME)\n        config.target.id = id\n      }\n\n      config.target = `#${id}`\n    }\n\n    typeCheckConfig(NAME, config, DefaultType)\n\n    return config\n  }\n\n  _getScrollTop() {\n    return this._scrollElement === window ?\n      this._scrollElement.pageYOffset :\n      this._scrollElement.scrollTop\n  }\n\n  _getScrollHeight() {\n    return this._scrollElement.scrollHeight || Math.max(\n      document.body.scrollHeight,\n      document.documentElement.scrollHeight\n    )\n  }\n\n  _getOffsetHeight() {\n    return this._scrollElement === window ?\n      window.innerHeight :\n      this._scrollElement.getBoundingClientRect().height\n  }\n\n  _process() {\n    const scrollTop = this._getScrollTop() + this._config.offset\n    const scrollHeight = this._getScrollHeight()\n    const maxScroll = this._config.offset + scrollHeight - this._getOffsetHeight()\n\n    if (this._scrollHeight !== scrollHeight) {\n      this.refresh()\n    }\n\n    if (scrollTop >= maxScroll) {\n      const target = this._targets[this._targets.length - 1]\n\n      if (this._activeTarget !== target) {\n        this._activate(target)\n      }\n\n      return\n    }\n\n    if (this._activeTarget && scrollTop < this._offsets[0] && this._offsets[0] > 0) {\n      this._activeTarget = null\n      this._clear()\n      return\n    }\n\n    for (let i = this._offsets.length; i--;) {\n      const isActiveTarget = this._activeTarget !== this._targets[i] &&\n          scrollTop >= this._offsets[i] &&\n          (typeof this._offsets[i + 1] === 'undefined' || scrollTop < this._offsets[i + 1])\n\n      if (isActiveTarget) {\n        this._activate(this._targets[i])\n      }\n    }\n  }\n\n  _activate(target) {\n    this._activeTarget = target\n\n    this._clear()\n\n    const queries = this._selector.split(',')\n      .map(selector => `${selector}[data-bs-target=\"${target}\"],${selector}[href=\"${target}\"]`)\n\n    const link = SelectorEngine.findOne(queries.join(','))\n\n    if (link.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE, link.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n\n      link.classList.add(CLASS_NAME_ACTIVE)\n    } else {\n      // Set triggered link as active\n      link.classList.add(CLASS_NAME_ACTIVE)\n\n      SelectorEngine.parents(link, SELECTOR_NAV_LIST_GROUP)\n        .forEach(listGroup => {\n          // Set triggered links parents as active\n          // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n          SelectorEngine.prev(listGroup, `${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`)\n            .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n\n          // Handle special case when .nav-link is inside .nav-item\n          SelectorEngine.prev(listGroup, SELECTOR_NAV_ITEMS)\n            .forEach(navItem => {\n              SelectorEngine.children(navItem, SELECTOR_NAV_LINKS)\n                .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n            })\n        })\n    }\n\n    EventHandler.trigger(this._scrollElement, EVENT_ACTIVATE, {\n      relatedTarget: target\n    })\n  }\n\n  _clear() {\n    SelectorEngine.find(this._selector)\n      .filter(node => node.classList.contains(CLASS_NAME_ACTIVE))\n      .forEach(node => node.classList.remove(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new ScrollSpy(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  SelectorEngine.find(SELECTOR_DATA_SPY)\n    .forEach(spy => new ScrollSpy(spy, Manipulator.getDataAttributes(spy)))\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .ScrollSpy to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, ScrollSpy)\n\nexport default ScrollSpy\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isDisabled,\n  reflow\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tab'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_MENU = 'dropdown-menu'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_UL = ':scope > li > .active'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"tab\"], [data-bs-toggle=\"pill\"], [data-bs-toggle=\"list\"]'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_ACTIVE_CHILD = ':scope > .dropdown-menu .active'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tab extends BaseComponent {\n  // Getters\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  show() {\n    if ((this._element.parentNode &&\n      this._element.parentNode.nodeType === Node.ELEMENT_NODE &&\n      this._element.classList.contains(CLASS_NAME_ACTIVE)) ||\n      isDisabled(this._element)) {\n      return\n    }\n\n    let previous\n    const target = getElementFromSelector(this._element)\n    const listElement = this._element.closest(SELECTOR_NAV_LIST_GROUP)\n\n    if (listElement) {\n      const itemSelector = listElement.nodeName === 'UL' || listElement.nodeName === 'OL' ? SELECTOR_ACTIVE_UL : SELECTOR_ACTIVE\n      previous = SelectorEngine.find(itemSelector, listElement)\n      previous = previous[previous.length - 1]\n    }\n\n    const hideEvent = previous ?\n      EventHandler.trigger(previous, EVENT_HIDE, {\n        relatedTarget: this._element\n      }) :\n      null\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget: previous\n    })\n\n    if (showEvent.defaultPrevented || (hideEvent !== null && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._activate(this._element, listElement)\n\n    const complete = () => {\n      EventHandler.trigger(previous, EVENT_HIDDEN, {\n        relatedTarget: this._element\n      })\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget: previous\n      })\n    }\n\n    if (target) {\n      this._activate(target, target.parentNode, complete)\n    } else {\n      complete()\n    }\n  }\n\n  // Private\n\n  _activate(element, container, callback) {\n    const activeElements = container && (container.nodeName === 'UL' || container.nodeName === 'OL') ?\n      SelectorEngine.find(SELECTOR_ACTIVE_UL, container) :\n      SelectorEngine.children(container, SELECTOR_ACTIVE)\n\n    const active = activeElements[0]\n    const isTransitioning = callback && (active && active.classList.contains(CLASS_NAME_FADE))\n\n    const complete = () => this._transitionComplete(element, active, callback)\n\n    if (active && isTransitioning) {\n      const transitionDuration = getTransitionDurationFromElement(active)\n      active.classList.remove(CLASS_NAME_SHOW)\n\n      EventHandler.one(active, 'transitionend', complete)\n      emulateTransitionEnd(active, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  _transitionComplete(element, active, callback) {\n    if (active) {\n      active.classList.remove(CLASS_NAME_ACTIVE)\n\n      const dropdownChild = SelectorEngine.findOne(SELECTOR_DROPDOWN_ACTIVE_CHILD, active.parentNode)\n\n      if (dropdownChild) {\n        dropdownChild.classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      if (active.getAttribute('role') === 'tab') {\n        active.setAttribute('aria-selected', false)\n      }\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n    if (element.getAttribute('role') === 'tab') {\n      element.setAttribute('aria-selected', true)\n    }\n\n    reflow(element)\n\n    if (element.classList.contains(CLASS_NAME_FADE)) {\n      element.classList.add(CLASS_NAME_SHOW)\n    }\n\n    if (element.parentNode && element.parentNode.classList.contains(CLASS_NAME_DROPDOWN_MENU)) {\n      const dropdownElement = element.closest(SELECTOR_DROPDOWN)\n\n      if (dropdownElement) {\n        SelectorEngine.find(SELECTOR_DROPDOWN_TOGGLE)\n          .forEach(dropdown => dropdown.classList.add(CLASS_NAME_ACTIVE))\n      }\n\n      element.setAttribute('aria-expanded', true)\n    }\n\n    if (callback) {\n      callback()\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Data.get(this, DATA_KEY) || new Tab(this)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n\n  const data = Data.get(this, DATA_KEY) || new Tab(this)\n  data.show()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tab to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Tab)\n\nexport default Tab\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  emulateTransitionEnd,\n  getTransitionDurationFromElement,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'toast'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 5000\n}\n\nconst SELECTOR_DATA_DISMISS = '[data-bs-dismiss=\"toast\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Toast extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._timeout = null\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      this._element.classList.add(CLASS_NAME_SHOW)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      if (this._config.autohide) {\n        this._timeout = setTimeout(() => {\n          this.hide()\n        }, this._config.delay)\n      }\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE)\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    if (this._config.animation) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, 'transitionend', complete)\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  hide() {\n    if (!this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    if (this._config.animation) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, 'transitionend', complete)\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n\n    super.dispose()\n    this._config = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    return config\n  }\n\n  _setListeners() {\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, () => this.hide())\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new Toast(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Toast to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Toast)\n\nexport default Toast\n"]}