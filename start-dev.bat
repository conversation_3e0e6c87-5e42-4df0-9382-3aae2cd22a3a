@echo off
echo Starting BAJA Development Environment...
echo.

echo Killing any existing processes on ports 3000 and 5000...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :3000') do taskkill /f /pid %%a 2>nul
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :5000') do taskkill /f /pid %%a 2>nul

echo.
echo Starting Backend (Port 5000)...
cd baja-backend
start "BAJA Backend" cmd /k "npm run build && node dist/server.js"

echo.
echo Waiting for backend to start...
timeout /t 5 /nobreak >nul

echo.
echo Starting Frontend (Port 3000)...
cd ../baja-frontend
start "BAJA Frontend" cmd /k "npx next dev -p 3000"

echo.
echo Development environment started!
echo Frontend: http://localhost:3000
echo Backend: http://localhost:5000
echo Backend Health: http://localhost:5000/health
echo.
pause
