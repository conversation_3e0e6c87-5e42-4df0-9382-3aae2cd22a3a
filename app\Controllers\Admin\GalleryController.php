<?php
namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\Gallery;

class GalleryController extends BaseController
{
    public function index()
    {
        $galleryModel = new Gallery();
        $data['gallery'] = $galleryModel->findAll();
        return view('admin/gallery/index', $data);
    }

    public function create()
    {
        return view('admin/gallery/create');
    }

    public function store()
    {
        $validationRules = [
            'images'      => 'uploaded[images]|max_size[images,2048]|is_image[images,jpg,png,jpeg]',
            'description' => 'required|min_length[10]',
        ];

        if (!$this->validate($validationRules)) {
            return redirect()->back()
                ->withInput()
                ->with('errors', $this->validator->getErrors());
        }

        $imageFile = $this->request->getFile('images');
        if ($imageFile->isValid() && !$imageFile->hasMoved()) {
            $newName = $imageFile->getRandomName();
            $destinationPath = ROOTPATH . 'public/uploads/file-gallery/';
            if (!$imageFile->move($destinationPath, $newName)) {
                return redirect()->back()
                    ->withInput()
                    ->with('error', 'Gagal mengunggah gambar.');
            }

            $galleryModel = new Gallery();
            $insertData = [
                'images'      => $newName,
                'description' => $this->request->getPost('description'),
            ];

            if ($galleryModel->insert($insertData) === false) {
                return redirect()->back()
                    ->withInput()
                    ->with('errors', $galleryModel->errors());
            }

            return redirect()->to('/gallery')
                ->with('success', 'Gambar berhasil ditambahkan.');
        }

        return redirect()->back()
            ->withInput()
            ->with('error', 'Gagal mengunggah gambar.');
    }

    public function edit($id)
    {
        $galleryModel = new Gallery();
        $data['gallery'] = $galleryModel->find($id);
        if (!$data['gallery']) {
            return redirect()->to('/gallery')
                ->with('error', 'Data tidak ditemukan.');
        }
        return view('admin/gallery/edit', $data);
    }

    public function update($id)
    {
        $galleryModel = new Gallery();
        $existingGallery = $galleryModel->find($id);
        if (!$existingGallery) {
            return redirect()->to('/gallery')
                ->with('error', 'Data tidak ditemukan.');
        }

        $validationRules = [
            'images'      => 'permit_empty|uploaded[images]|max_size[images,2048]|is_image[images]',
            'description' => 'required|min_length[10]',
        ];

        if (!$this->validate($validationRules)) {
            return redirect()->back()
                ->withInput()
                ->with('errors', $this->validator->getErrors());
        }

        $updateData = [
            'description' => $this->request->getPost('description'),
        ];

        $imageFile = $this->request->getFile('images');
        if ($imageFile && $imageFile->isValid() && !$imageFile->hasMoved()) {
            $newName = $imageFile->getRandomName();
            $destinationPath = ROOTPATH . 'public/uploads/file-gallery/';
            if (!$imageFile->move($destinationPath, $newName)) {
                return redirect()->back()
                    ->withInput()
                    ->with('error', 'Gagal mengunggah gambar.');
            }

            // Hapus gambar lama jika ada
            $oldImage = $existingGallery['images'];
            $oldImagePath = $destinationPath . $oldImage;
            if ($oldImage && file_exists($oldImagePath)) {
                @unlink($oldImagePath);
            }
            $updateData['images'] = $newName;
        }

        if ($galleryModel->update($id, $updateData) === false) {
            return redirect()->back()
                ->withInput()
                ->with('errors', $galleryModel->errors());
        }

        return redirect()->to('/gallery')
            ->with('success', 'Gambar berhasil diubah.');
    }

    public function delete($id)
    {
        $galleryModel = new Gallery();
        $galleryData = $galleryModel->find($id);
        if (!$galleryData) {
            return redirect()->to('/gallery')
                ->with('error', 'Data tidak ditemukan.');
        }

        $destinationPath = ROOTPATH . 'public/uploads/file-gallery/';
        $image = $galleryData['images'];
        $imagePath = $destinationPath . $image;
        if ($image && file_exists($imagePath)) {
            @unlink($imagePath);
        }

        if (!$galleryModel->delete($id)) {
            return redirect()->to('/gallery')
                ->with('error', 'Gagal menghapus data.');
        }

        return redirect()->to('/gallery')
            ->with('success', 'Gambar berhasil dihapus.');
    }
}
