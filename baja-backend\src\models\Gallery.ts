import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../config/database';
import { Gallery as GalleryInterface } from '../types';

interface GalleryCreationAttributes extends Optional<GalleryInterface, 'id' | 'created_at' | 'updated_at'> {}

class Gallery extends Model<GalleryInterface, GalleryCreationAttributes> implements GalleryInterface {
  public id!: number;
  public images!: string;
  public description?: string;
  public readonly created_at!: Date;
  public readonly updated_at!: Date;
}

Gallery.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    images: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    tableName: 'gallery',
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  }
);

export default Gallery;
