# BAJA Event Organizer - Backend API

Backend API untuk sistem manajemen event olahraga bela diri BAJA Event Organizer.

## Tech Stack

- **Node.js** - Runtime environment
- **Express.js** - Web framework
- **TypeScript** - Programming language
- **Sequelize** - ORM untuk database
- **MySQL** - Database
- **JWT** - Authentication
- **Bcrypt** - Password hashing
- **Multer** - File upload
- **Joi** - Data validation

## Features

- 🔐 **Authentication & Authorization** - JWT-based auth dengan role-based access
- 👥 **User Management** - Manajemen user dengan berbagai role
- 🏆 **Event Management** - CRUD operations untuk event
- 🥋 **Athlete Management** - Manajemen data atlet
- 👨‍💼 **Team Management** - Manajemen kontingen
- 📊 **Dashboard Analytics** - Statistik dan analytics
- 📁 **File Upload** - Upload gambar dan dokumen
- 🔒 **Security** - Rate limiting, helmet, CORS protection

## Installation

1. Clone repository
```bash
git clone <repository-url>
cd baja-backend
```

2. Install dependencies
```bash
npm install
```

3. Setup environment variables
```bash
cp .env.example .env
```

Edit file `.env` sesuai dengan konfigurasi Anda:
```env
NODE_ENV=development
PORT=5000
DB_HOST=127.0.0.1
DB_PORT=3306
DB_NAME=db_baja
DB_USER=root
DB_PASSWORD=root
JWT_SECRET=your-super-secret-jwt-key-here
```

4. Setup database
Pastikan MySQL server berjalan dan database `db_baja` sudah dibuat.

5. Run development server
```bash
npm run dev
```

Server akan berjalan di `http://localhost:5000`

## API Endpoints

### Authentication
- `POST /api/v1/auth/register` - Register user baru
- `POST /api/v1/auth/login` - Login user
- `POST /api/v1/auth/logout` - Logout user
- `GET /api/v1/auth/profile` - Get user profile
- `PUT /api/v1/auth/profile` - Update user profile

### Dashboard
- `GET /api/v1/dashboard/stats` - Get dashboard statistics

### Events
- `GET /api/v1/events` - Get all events (public)
- `POST /api/v1/events` - Create new event (admin/admin-event)
- `GET /api/v1/events/:id` - Get event by ID
- `PUT /api/v1/events/:id` - Update event (admin/admin-event)
- `DELETE /api/v1/events/:id` - Delete event (admin/admin-event)

### Users (Admin only)
- `GET /api/v1/users` - Get all users
- `POST /api/v1/users` - Create new user
- `GET /api/v1/users/:id` - Get user by ID
- `PUT /api/v1/users/:id` - Update user
- `DELETE /api/v1/users/:id` - Delete user

### Athletes
- `GET /api/v1/atlet` - Get athletes
- `POST /api/v1/atlet` - Create athlete (ketua-kontingen)
- `GET /api/v1/atlet/:id` - Get athlete by ID
- `PUT /api/v1/atlet/:id` - Update athlete
- `DELETE /api/v1/atlet/:id` - Delete athlete

### Teams (Kontingen)
- `GET /api/v1/kontingen` - Get teams
- `POST /api/v1/kontingen` - Create team (ketua-kontingen)
- `GET /api/v1/kontingen/:id` - Get team by ID
- `PUT /api/v1/kontingen/:id` - Update team
- `DELETE /api/v1/kontingen/:id` - Delete team

### Officials
- `GET /api/v1/official` - Get officials
- `POST /api/v1/official` - Create official (ketua-kontingen)
- `GET /api/v1/official/:id` - Get official by ID
- `PUT /api/v1/official/:id` - Update official
- `DELETE /api/v1/official/:id` - Delete official

### Packages
- `GET /api/v1/paket` - Get all packages (public)
- `POST /api/v1/paket` - Create package (admin)
- `GET /api/v1/paket/:id` - Get package by ID
- `PUT /api/v1/paket/:id` - Update package (admin)
- `DELETE /api/v1/paket/:id` - Delete package (admin)

### Gallery
- `GET /api/v1/gallery` - Get all gallery items (public)
- `POST /api/v1/gallery` - Create gallery item (admin)
- `GET /api/v1/gallery/:id` - Get gallery item by ID
- `PUT /api/v1/gallery/:id` - Update gallery item (admin)
- `DELETE /api/v1/gallery/:id` - Delete gallery item (admin)

## User Roles

### Admin
- Full access ke semua fitur
- Manajemen user, event, paket, gallery
- Dashboard dengan statistik global

### Admin Event
- Manajemen event yang dibuat sendiri
- Melihat atlet dan kontingen yang terdaftar di eventnya
- Dashboard dengan statistik event sendiri

### Ketua Kontingen
- Manajemen kontingen sendiri
- Registrasi atlet dan official
- Pendaftaran ke event
- Dashboard dengan statistik kontingen sendiri

## Database Models

### User
- id, name, email, password, role, status
- profile, no_hp, alamat, agama
- timestamps

### Event
- id, name, description, start_date, end_date
- lokasi, biaya_registrasi, metode_pembayaran
- status, event_image, event_proposal, event_pemenang
- id_user (foreign key)
- timestamps

### Atlet
- id, nik, name, no_hp, tanggal_lahir
- jenis_kelamin, agama, alamat, umur
- berat_badan, tinggi_badan, status_verifikasi
- id_user, id_kontingen (foreign keys)
- timestamps

### Kontingen
- id, name, alamat, no_hp, email
- id_user (foreign key)
- timestamps

### Official
- id, nik, name, no_hp, tanggal_lahir
- jenis_kelamin, agama, alamat, jabatan
- id_user, id_kontingen (foreign keys)
- timestamps

### Paket
- id, name, description, price, features
- is_active
- timestamps

### Gallery
- id, title, description, image_url
- is_active
- timestamps

## Scripts

- `npm run dev` - Start development server dengan nodemon
- `npm run build` - Build TypeScript ke JavaScript
- `npm start` - Start production server
- `npm run lint` - Run ESLint
- `npm run test` - Run tests

## Contributing

1. Fork repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## License

This project is licensed under the MIT License.
