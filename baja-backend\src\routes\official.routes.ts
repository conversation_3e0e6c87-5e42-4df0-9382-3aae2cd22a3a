import { Router } from 'express';
import { authenticate, authorize } from '../middleware/auth.middleware';
import {
  getAllOfficial,
  getOfficialById,
  createOfficial,
  updateOfficial,
  deleteOfficial,
  uploadOfficialPhoto,
  upload
} from '../controllers/official.controller';

const router = Router();

// All routes require authentication
router.use(authenticate);

router.get('/', getAllOfficial);
router.post('/', authorize('admin', 'ketua-kontingen'), createOfficial);
router.get('/:id', getOfficialById);
router.put('/:id', authorize('admin', 'ketua-kontingen'), updateOfficial);
router.delete('/:id', authorize('admin', 'ketua-kontingen'), deleteOfficial);
router.post('/:id/upload', authorize('admin', 'ketua-kontingen'), upload.single('profile'), uploadOfficialPhoto);

export default router;
