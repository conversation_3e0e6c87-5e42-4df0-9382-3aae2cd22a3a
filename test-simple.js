const http = require('http');

// Test main events endpoint
const testMainEvents = () => {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 5000,
      path: '/api/v1/events',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          console.log('✅ Main Events Endpoint Test:');
          console.log(`Status: ${res.statusCode}`);
          console.log(`Success: ${response.success}`);
          console.log(`Events Count: ${response.data?.events?.length || 0}`);
          console.log(`Message: ${response.message}`);
          resolve(response);
        } catch (error) {
          console.log('❌ Failed to parse response:', error.message);
          reject(error);
        }
      });
    });

    req.on('error', (error) => {
      console.log('❌ Request failed:', error.message);
      reject(error);
    });

    req.end();
  });
};

// Test dashboard events endpoint (requires auth)
const testDashboardEvents = () => {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 5000,
      path: '/api/v1/admin/dashboard-events',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          console.log('✅ Dashboard Events Endpoint Test:');
          console.log(`Status: ${res.statusCode}`);
          console.log(`Success: ${response.success}`);
          console.log(`Message: ${response.message}`);
          resolve(response);
        } catch (error) {
          console.log('❌ Failed to parse response:', error.message);
          reject(error);
        }
      });
    });

    req.on('error', (error) => {
      console.log('❌ Request failed:', error.message);
      reject(error);
    });

    req.end();
  });
};

// Run tests
console.log('🚀 Testing BAJA Backend Endpoints...\n');

testMainEvents()
  .then(() => {
    console.log('\n');
    return testDashboardEvents();
  })
  .then(() => {
    console.log('\n✅ All tests completed!');
    console.log('\n📝 Summary:');
    console.log('- Main events endpoint (/api/v1/events) is working');
    console.log('- Dashboard events endpoint (/api/v1/admin/dashboard-events) is accessible');
    console.log('- Backend is running on port 5000');
    console.log('- Frontend should be able to fetch events');
  })
  .catch((error) => {
    console.log('\n❌ Test failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('- Check if backend is running on port 5000');
    console.log('- Check database connection');
    console.log('- Check for any compilation errors');
  });
