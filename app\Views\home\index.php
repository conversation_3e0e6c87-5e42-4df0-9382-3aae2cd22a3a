<?= $this->extend('layouts/frontend/template') ?>
<?= $this->section('content') ?>

<section id="hero" class="hero d-flex align-items-center">
    <div class="container">
        <div class="row">
            <div class="col-lg-6 d-flex flex-column justify-content-center">
                <h1 data-aos="fade-up">Selamat Datang di Baja Event Organizer</h1>
                <h2 data-aos="fade-up" data-aos-delay="400">Sukseskan Bersama <PERSON>mi</h2>
                <div data-aos="fade-up" data-aos-delay="600">
                    <div class="text-center text-lg-start">
                        <a href="#event" class="btn-get-started scrollto d-inline-flex align-items-center justify-content-center align-self-center">
                            <span>Lihat Event</span>
                            <i class="bi bi-arrow-right"></i>
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 hero-img" data-aos="zoom-out" data-aos-delay="200">
                <img src="assets-fe/img/bg-silat-baja.jpeg" class="img-fluid" alt="">
            </div>
        </div>
    </div>
</section>

<main id="main">
    <section id="counts" class="counts">
        <div class="container" data-aos="fade-up">
            <div class="row gy-4">
                <?php 
                $counts = [
                    ['icon' => 'calendar-event', 'count' => $count_event, 'label' => 'JUMLAH EVENT'],
                    ['icon' => 'house-fill',     'count' => $count_kontingen, 'label' => 'KONTINGEN'],
                    ['icon' => 'person-workspace','count' => $count_atlet, 'label' => 'ATLET'],
                    ['icon' => 'bank',           'count' => $count_official, 'label' => 'JUMLAH OFFICIAL'],
                ];
                foreach ($counts as $item): ?>
                <div class="col-lg-3 col-md-6">
                    <div class="count-box">
                        <i class="bi bi-<?= $item['icon'] ?>"></i>
                        <div>
                            <span data-purecounter-start="0" data-purecounter-end="<?= $item['count'] ?>" data-purecounter-duration="1" class="purecounter"></span>
                            <p><?= $item['label'] ?></p>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>

    <section id="features" class="values">
    <div class="container" data-aos="fade-up">
        <header class="section-header">
            <p>PAKET YANG KAMI SEDIAKAN</p>
        </header>
            <div class="row">
                <?php if (!empty($paket)): ?>
                    <?php foreach ($paket as $p): ?>
                        <div class="col-lg-4" data-aos="fade-up" data-aos-delay="200">
                            <div class="box">
                                <img src="<?= base_url('uploads/file-paket/' . $p['images']) ?>" class="img-fluid" alt="<?= $p['name'] ?>">
                                <h3><?= $p['name'] ?></h3>
                                <p><?= $p['description'] ?></p>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="col-12">
                        <p>Tidak ada paket yang tersedia.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>


    <section id="event" class="testimonials">
        <div class="container" data-aos="fade-up">
            <header class="section-header">
                <p>EVENT</p>
            </header>
            <div class="testimonials-slider swiper" data-aos="fade-up" data-aos-delay="200">
                <div class="swiper-wrapper">
                    <?php foreach ($events as $row): ?>
                    <div class="swiper-slide">
                        <a href="<?= base_url('event/' . strtolower(str_replace(' ', '-', $row['name']))) ?>">
                            <div class="testimonial-item">
                                <div class="profile mt-auto">
                                    <img src="<?= base_url('uploads/file-event/' . strtolower(str_replace(' ', '-', $row['name'])) . '/' . $row['event_image']) ?>"<?= $row['event_image'] ?> alt="Event Image" class="img-fluid" style="max-height: 200px; object-fit: cover;">
                                    <h3><?= $row['name'] ?></h3>
                                </div>
                                <p><?= $row['description'] ?></p>
                                <?php if (session()->get('logged_in')) : ?>
                                    <a href="<?= base_url('dashboard') ?>" class="btn btn-primary">Lihat Event</a>
                                <?php else : ?>
                                    <a href="<?= base_url('login') ?>" class="btn btn-primary">Lihat Event</a>
                                <?php endif; ?>
                            </div>
                        </a>
                    </div>
                    <?php endforeach; ?>
                </div>
                <div class="swiper-pagination"></div>
            </div>
        </div>
    </section>

    <section id="gallery" class="portfolio">
    <div class="container" data-aos="fade-up">
        <header class="section-header">
            <p>Gallery</p>
        </header>
        <div class="row" data-aos="fade-up" data-aos-delay="100">
            <div class="col-lg-12 d-flex justify-content-center">
                <ul id="portfolio-flters">
                    <li data-filter="*" class="filter-active">All</li>
                </ul>
            </div>
        </div>
        <div class="row gy-4 portfolio-container" data-aos="fade-up" data-aos-delay="200">
            <?php foreach ($gallery as $row): ?>
            <div class="col-lg-4 col-md-6 portfolio-item">
                <div class="portfolio-wrap">
                    <img src="<?= base_url('uploads/file-gallery/' . $row['images']) ?>" class="img-fluid" alt="<?= $row['description'] ?>">
                    <div class="portfolio-info">
                        <h4><?= $row['description'] ?></h4>
                        <div class="portfolio-links">
                            <a href="<?= base_url('uploads/file-gallery/' . $row['images']) ?>" data-gallery="portfolioGallery" class="portfokio-lightbox" title="<?= $row['description'] ?>">
                                <i class="bi bi-plus"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>
</main>

<?= $this->endSection() ?>
