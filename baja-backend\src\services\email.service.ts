import nodemailer from 'nodemailer';

export class EmailService {
  private transporter: nodemailer.Transporter;

  constructor() {
    console.log('📧 Initializing email service...');
    console.log('EMAIL_USER:', process.env.EMAIL_USER ? 'Set' : 'Not set');
    console.log('EMAIL_PASS:', process.env.EMAIL_PASS ? 'Set (length: ' + process.env.EMAIL_PASS.length + ')' : 'Not set');

    this.transporter = nodemailer.createTransport({
      service: 'gmail',
      host: 'smtp.gmail.com',
      port: 587,
      secure: false, // true for 465, false for other ports
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS, // Use App Password for Gmail
      },
      tls: {
        rejectUnauthorized: false
      },
      debug: process.env.NODE_ENV === 'development',
      logger: process.env.NODE_ENV === 'development'
    });
  }

  async sendEmail(to: string, subject: string, html: string): Promise<void> {
    try {
      const mailOptions = {
        from: {
          name: 'BAJA Event Organizer',
          address: process.env.EMAIL_USER || '<EMAIL>'
        },
        to,
        subject: `[BAJA] ${subject}`, // Add prefix to avoid spam
        html,
        text: html.replace(/<[^>]*>/g, ''), // Plain text version
        headers: {
          'X-Priority': '1',
          'X-MSMail-Priority': 'High',
          'Importance': 'high'
        }
      };

      console.log(`📧 Attempting to send email to: ${to}`);
      console.log(`📧 From: ${mailOptions.from.address}`);
      console.log(`📧 Subject: ${mailOptions.subject}`);

      const result = await this.transporter.sendMail(mailOptions);

      console.log(`✅ Email sent successfully to ${to}`);
      console.log(`📧 Message ID: ${result.messageId}`);
      console.log(`📧 Response: ${result.response}`);

    } catch (error: any) {
      console.error('❌ Error sending email:', error);
      console.error('❌ Error details:', {
        code: error.code,
        command: error.command,
        response: error.response,
        responseCode: error.responseCode
      });

      // Log the OTP for testing if email fails
      console.log('🔐 EMAIL FAILED - OTP for testing:');
      console.log(`📧 Email: ${to}`);
      console.log(`📧 Subject: ${subject}`);

      // Extract OTP from HTML if possible
      const otpMatch = html.match(/(\d{6})/);
      if (otpMatch) {
        console.log(`🔑 OTP Code: ${otpMatch[1]}`);
      }

      throw new Error(`Failed to send email: ${error.message}`);
    }
  }

  async sendWelcomeEmail(to: string, name: string): Promise<void> {
    const subject = 'Selamat Datang di BAJA Event Organizer';
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #000000 0%, #FFD700 100%); padding: 30px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 28px;">BAJA Event Organizer</h1>
        </div>
        
        <div style="padding: 30px; background-color: #f9f9f9;">
          <h2 style="color: #333; margin-bottom: 20px;">Selamat Datang, ${name}!</h2>
          
          <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
            Terima kasih telah bergabung dengan BAJA Event Organizer. Akun Anda telah berhasil dibuat dan diverifikasi.
          </p>
          
          <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
            Dengan akun BAJA, Anda dapat:
          </p>
          
          <ul style="color: #666; line-height: 1.8; margin-bottom: 30px;">
            <li>Mendaftar untuk berbagai event menarik</li>
            <li>Mengelola kontingen dan atlet</li>
            <li>Mengakses paket layanan eksklusif</li>
            <li>Melihat galeri event terbaru</li>
          </ul>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.FRONTEND_URL || 'http://localhost:3000'}/dashboard" 
               style="background: linear-gradient(135deg, #000000 0%, #FFD700 100%); 
                      color: white; 
                      padding: 15px 30px; 
                      text-decoration: none; 
                      border-radius: 5px; 
                      font-weight: bold;
                      display: inline-block;">
              Mulai Jelajahi Dashboard
            </a>
          </div>
          
          <p style="color: #666; line-height: 1.6; font-size: 14px; margin-top: 30px;">
            Jika Anda memiliki pertanyaan, jangan ragu untuk menghubungi tim support kami.
          </p>
        </div>
        
        <div style="background-color: #333; padding: 20px; text-align: center;">
          <p style="color: #ccc; margin: 0; font-size: 14px;">
            © 2024 BAJA Event Organizer. All rights reserved.
          </p>
        </div>
      </div>
    `;

    await this.sendEmail(to, subject, html);
  }

  async sendPasswordResetEmail(to: string, resetToken: string): Promise<void> {
    const resetUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/auth/reset-password?token=${resetToken}`;
    
    const subject = 'Reset Password BAJA Account';
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #000000 0%, #FFD700 100%); padding: 30px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 28px;">BAJA Event Organizer</h1>
        </div>
        
        <div style="padding: 30px; background-color: #f9f9f9;">
          <h2 style="color: #333; margin-bottom: 20px;">Reset Password</h2>
          
          <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
            Anda telah meminta untuk mereset password akun BAJA Anda.
          </p>
          
          <p style="color: #666; line-height: 1.6; margin-bottom: 30px;">
            Klik tombol di bawah ini untuk membuat password baru:
          </p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${resetUrl}" 
               style="background: linear-gradient(135deg, #000000 0%, #FFD700 100%); 
                      color: white; 
                      padding: 15px 30px; 
                      text-decoration: none; 
                      border-radius: 5px; 
                      font-weight: bold;
                      display: inline-block;">
              Reset Password
            </a>
          </div>
          
          <p style="color: #666; line-height: 1.6; font-size: 14px;">
            Link ini akan kedaluwarsa dalam 1 jam. Jika Anda tidak meminta reset password, abaikan email ini.
          </p>
        </div>
        
        <div style="background-color: #333; padding: 20px; text-align: center;">
          <p style="color: #ccc; margin: 0; font-size: 14px;">
            © 2024 BAJA Event Organizer. All rights reserved.
          </p>
        </div>
      </div>
    `;

    await this.sendEmail(to, subject, html);
  }
}

export const emailService = new EmailService();
