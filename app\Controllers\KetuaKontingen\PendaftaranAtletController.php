<?php

namespace App\Controllers\KetuaKontingen;

use App\Controllers\BaseController;
use App\Models\Atlet;
use App\Models\PendaftaranAtlet;
use App\Models\PendaftaranAtletDetail;
use App\Models\PendaftaranEvent;
use App\Models\Kontingen;
use App\Models\Event;

class PendaftaranAtletController extends BaseController
{
    /**
     * Menampilkan daftar event yang telah didaftarkan oleh kontingen.
     */
    public function index()
    {
        $userId = session()->get('id');
        if (!$userId) {
            return redirect()->to('/login')->with('error', 'Anda harus login terlebih dahulu.');
        }

        $pendaftaranEventModel = new PendaftaranEvent();
        $kontingenModel = new Kontingen();

        // Ambil data kontingen milik user yang login
        $kontingen = $kontingenModel->where('id_user', $userId)->first();

        $registeredEvents = [];
        if ($kontingen) {
            // Ambil event yang sudah didaftarkan oleh kontingen
            $registeredEvents = $pendaftaranEventModel
                ->select('event.*')
                ->join('event', 'event.id = pendaftaran_event.id_event')
                ->where('pendaftaran_event.id_kontingen', $kontingen['id'])
                ->findAll();
        }

        $data['events']    = $registeredEvents;
        $data['kontingen'] = $kontingen;

        return view('ketua-kontingen/pendaftaran-atlet/index', $data);
    }

    /**
     * Menampilkan detail pendaftaran atlet untuk sebuah event.
     */
    public function detail($id)
    {
        $pendaftaranAtletModel       = new PendaftaranAtlet();
        $pendaftaranAtletDetailModel = new PendaftaranAtletDetail();
        $atletModel                  = new Atlet();
        $eventModel                  = new Event();
        $kontingenModel              = new Kontingen();

        // Ambil kontingen milik user yang login
        $currentKontingen = $kontingenModel->where('id_user', session()->get('id'))->first();
        if (!$currentKontingen) {
            return redirect()->back()->with('error', 'Data kontingen tidak ditemukan.');
        }

        // Ambil detail event (asumsi $id adalah id_pendaftaran_event)
        $data['event'] = $eventModel->find($id);

        // Ambil semua pendaftaran atlet untuk event ini
        $registrations = $pendaftaranAtletModel
            ->where('id_pendaftaran_event', $id)
            ->findAll();

        $filteredRegistrations = [];
        foreach ($registrations as $registration) {
            // Ambil detail pendaftaran untuk masing-masing registration
            $details = $pendaftaranAtletDetailModel
                ->where('id_pendaftaran_atlet', $registration['id'])
                ->findAll();

            $allBelong = true;
            $atletNames = [];
            foreach ($details as $detail) {
                $atlet = $atletModel->find($detail['id_atlet']);
                if ($atlet) {
                    // Hanya ambil atlet jika id_kontingen-nya sama dengan kontingen yang login
                    if ($atlet['id_kontingen'] != $currentKontingen['id']) {
                        $allBelong = false;
                        break;
                    }
                    $atletNames[] = $atlet['name'];
                }
            }
            // Hanya tambahkan pendaftaran jika seluruh atlet pada pendaftaran tersebut milik kontingen yang login
            if ($allBelong) {
                $registration['atlet_names'] = implode(', ', $atletNames);
                $filteredRegistrations[] = $registration;
            }
        }

        $data['registrations'] = $filteredRegistrations;
        return view('ketua-kontingen/pendaftaran-atlet/detail', $data);
    }

    /**
     * Menampilkan form pendaftaran atlet untuk sebuah event.
     */
    public function create($id_pendaftaran_event)
    {
        $atletModel = new Atlet();
        $kontingenModel = new Kontingen();

        // Ambil data kontingen milik user yang login
        $kontingen = $kontingenModel->where('id_user', session()->get('id'))->first();
        if (!$kontingen) {
            return redirect()->back()->with('error', 'Anda tidak memiliki kontingen.');
        }

        // Ambil data atlet yang sudah diverifikasi dari kontingen
        $data['atlet'] = $atletModel
            ->where('id_kontingen', $kontingen['id'])
            ->where('status_verifikasi', 'verified')
            ->findAll();

        $data['id_pendaftaran_event'] = $id_pendaftaran_event;
        return view('ketua-kontingen/pendaftaran-atlet/create', $data);
    }

    /**
     * Menyimpan data pendaftaran atlet.
     */
    public function store()
    {
        $pendaftaranAtletModel       = new PendaftaranAtlet();
        $pendaftaranAtletDetailModel = new PendaftaranAtletDetail();

        // Ambil input jenis tanding
        $jenis_tanding = $this->request->getPost('jenis_tanding');

        // Susun aturan validasi berdasarkan jenis tanding
        if ($jenis_tanding === 'TANDING') {
            $validationRules = [
                'jenis_tanding'         => 'required',
                'kategori_umur'         => 'required',
                'kelas_tanding'         => 'required',
                'id_pendaftaran_event'  => 'required|numeric',
                'id_atlet'              => 'required',          // Array ID atlet
                'id_atlet.*'            => 'numeric',
            ];
        } else {
            $validationRules = [
                'jenis_tanding'         => 'required',
                'kategori_umur'         => 'required',
                'kelas_tanding'         => 'permit_empty',
                'id_pendaftaran_event'  => 'required|numeric',
                'id_atlet'              => 'required',          // Array ID atlet
                'id_atlet.*'            => 'numeric',
            ];
        }

        if (!$this->validate($validationRules)) {
            return redirect()->back()
                ->withInput()
                ->with('errors', $this->validator->getErrors());
        }

        // Siapkan data utama
        $dataMain = [
            'id_pendaftaran_event' => $this->request->getPost('id_pendaftaran_event'),
            'jenis_tanding'        => $this->request->getPost('jenis_tanding'),
            'kategori_umur'        => $this->request->getPost('kategori_umur'),
            'kelas_tanding'        => $this->request->getPost('kelas_tanding'),
            'bukti_pembayaran'     => null,
            'status'               => 'pending',
        ];

        // Simpan data utama dan ambil ID pendaftaran
        $pendaftaranAtletModel->insert($dataMain);
        $id_pendaftaran_atlet = $pendaftaranAtletModel->getInsertID();

        // Simpan detail pendaftaran untuk setiap atlet yang dipilih
        $atletIds = $this->request->getPost('id_atlet');
        foreach ($atletIds as $id_atlet) {
            $dataDetail = [
                'id_pendaftaran_atlet' => $id_pendaftaran_atlet,
                'id_atlet'             => $id_atlet,
            ];
            $pendaftaranAtletDetailModel->insert($dataDetail);
        }

        return redirect()->to('/pendaftaran-atlet')
            ->with('success', 'Data pendaftaran berhasil disimpan. Silakan upload bukti pembayaran melalui tombol action di tabel.');
    }

    /**
     * Memproses upload bukti pembayaran untuk pendaftaran atlet.
     */
    public function uploadBukti($id)
    {
        $pendaftaranModel = new PendaftaranAtlet();

        // Validasi file bukti pembayaran
        $validationRules = [
            'bukti_pembayaran' => [
                'rules'  => 'uploaded[bukti_pembayaran]|max_size[bukti_pembayaran,2048]|ext_in[bukti_pembayaran,pdf,png,jpg,jpeg]',
                'errors' => [
                    'uploaded' => 'Harap pilih file',
                    'ext_in'   => 'Hanya file PDF/PNG/JPG yang diperbolehkan',
                    'max_size' => 'Ukuran maksimal 2MB'
                ]
            ]
        ];

        if (!$this->validate($validationRules)) {
            return redirect()->back()
                ->withInput()
                ->with('error', $this->validator->listErrors());
        }

        $file = $this->request->getFile('bukti_pembayaran');
        if ($file->isValid() && !$file->hasMoved()) {
            $newName = $file->getRandomName();
            if (!$file->move(ROOTPATH . 'public/uploads/file-bukti-pembayaran', $newName)) {
                return redirect()->back()->with('error', 'Gagal menyimpan file bukti pembayaran.');
            }
            
            // Update record pendaftaran dengan file bukti pembayaran dan ubah status menjadi 'oncheck'
            $pendaftaranModel->update($id, [
                'bukti_pembayaran' => $newName,
                'status'           => 'oncheck'
            ]);
            
            return redirect()->to('/pendaftaran-atlet')
                ->with('success', 'Bukti pembayaran berhasil diupload.');
        }

        return redirect()->back()
            ->with('error', 'Terjadi kesalahan saat upload file.');
    }

    /**
     * Menghapus data pendaftaran atlet.
     */
    public function delete($id)
    {
        $pendaftaranModel = new PendaftaranAtlet();
        if ($pendaftaranModel->delete($id)) {
            return redirect()->to('/pendaftaran-atlet')
                ->with('success', 'Data pendaftaran berhasil dihapus.');
        }
        return redirect()->back()->with('error', 'Gagal menghapus data pendaftaran.');
    }
}
