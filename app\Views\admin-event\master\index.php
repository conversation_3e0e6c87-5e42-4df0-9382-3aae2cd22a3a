<?= $this->extend('layouts/backend/template') ?>
<?= $this->section('content') ?>

<div id="content">
<div class="container-fluid">

                    <!-- Page Heading -->
                    <h1 class="h3 mb-2 text-gray-800">Master</h1>
                    <h6 class="m-0 mb-3 font-weight-bold text-primary"><a href="<?= base_url('master/export') ?>" class="btn btn-primary">Export Excel</a></h6>

                    <!-- DataTales Example -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Data Master</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>No</th>
                                            <th>Inpa ID</th>
                                            <th>Name</th>
                                            <th>Gender</th>
                                            <th>Birth Date</th>
                                            <th>Participant Name</th>
                                            <th>Match Category</th>
                                            <th>Weight</th>
                                            <th>Height</th>
                                            <th>Category</th>
                                            <th>Match Category</th>
                                            <th>Class</th>
                                            <th>Picture</th>
                                            <th>Delegation</th>
                                            <th>Picture</th>
                                            <th>Bukti Pembayaran</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($atlets as $key => $row): ?>
                                            <tr>
                                                <td><?= $key + 1 ?></td>
                                                <td></td>
                                                <td><?= $row['name'] ?></td>
                                                <td><?= $row['jenis_kelamin'] ?></td>
                                                <td><?= $row['tanggal_lahir'] ?></td>
                                                <td><?= $row['kontingen_name'] ?></td>
                                                <td><?= $row['kategori_umur'] ?></td>
                                                <td><?= $row['berat_badan'] ?></td>
                                                <td><?= $row['tinggi_badan'] ?></td>
                                                <td><?= $row['jenis_tanding'] ?></td>
                                                <td><?= $row['kategori_umur'] ?></td>
                                                <td><?= $row['kelas_tanding'] ?></td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td>
                                                    <?php if ($row['bukti_pembayaran']): ?>
                                                        <?php if ($row['status'] === 'oncheck'): ?>
                                                        <a href="<?= base_url('uploads/file-bukti-pembayaran/' . $row['bukti_pembayaran']) ?>" 
                                                        target="_blank" 
                                                        class="btn btn-success">
                                                            Lihat Bukti
                                                        </a>
                                                        <a href="<?= base_url('master/konfirmasi/' . $row['pendaftaran_atlet_id']) ?>" 
                                                        class="btn btn-primary" 
                                                        onclick="return confirm('Konfirmasi pembayaran?')">
                                                            Konfirmasi
                                                        </a>
                                                        <?php elseif ($row['status'] === 'paid'): ?>
                                                            <span class="badge badge-success">Sudah dikonfirmasi</span>
                                                        <?php endif ?>
                                                    <?php else: ?>
                                                        <span class="badge badge-warning">Belum Upload</span>
                                                    <?php endif ?>
                                                </td>
                                            </tr>
                                        <?php endforeach ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->


<?= $this->endSection(); ?>