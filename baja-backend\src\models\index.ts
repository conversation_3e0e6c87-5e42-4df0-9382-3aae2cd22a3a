// Import all models
import User from './User';
import Event from './Event';
import Kontingen from './Kontingen';
import Atlet from './Atlet';
import Official from './Official';
import Paket from './Paket';
import Gallery from './Gallery';
import PendaftaranEvent from './PendaftaranEvent';
import PendaftaranAtlet from './PendaftaranAtlet';
import PendaftaranAtletDetail from './PendaftaranAtletDetail';
import AtletFile from './AtletFile';
import Negara from './Negara';
import <PERSON><PERSON><PERSON> from './Provinsi';
import KabupatenKota from './KabupatenKota';

// Setup associations
const setupAssociations = () => {
  // User associations
  User.hasMany(Event, { foreignKey: 'id_user', as: 'userEvents' });
  User.hasMany(Kontingen, { foreignKey: 'id_user', as: 'userKontingen' });
  User.hasMany(Atlet, { foreignKey: 'id_user', as: 'userAtlet' });

  // Event associations
  Event.belongsTo(User, { foreignKey: 'id_user', as: 'eventUser' });
  Event.hasMany(PendaftaranEvent, { foreignKey: 'id_event', as: 'eventPendaftaran' });

  // Kontingen associations
  Kontingen.belongsTo(User, { foreignKey: 'id_user', as: 'kontingenUser' });
  Kontingen.hasMany(Atlet, { foreignKey: 'id_kontingen', as: 'kontingenAtlet' });
  Kontingen.hasMany(Official, { foreignKey: 'id_kontingen', as: 'kontingenOfficials' });
  Kontingen.hasMany(PendaftaranEvent, { foreignKey: 'id_kontingen', as: 'kontingenPendaftaran' });

  // Atlet associations
  Atlet.belongsTo(User, { foreignKey: 'id_user', as: 'atletUser' });
  Atlet.belongsTo(Kontingen, { foreignKey: 'id_kontingen', as: 'atletKontingen' });
  Atlet.hasMany(AtletFile, { foreignKey: 'id_atlet', as: 'atletFiles' });
  Atlet.hasMany(PendaftaranAtletDetail, { foreignKey: 'id_atlet', as: 'atletPendaftaran' });

  // Official associations
  Official.belongsTo(Kontingen, { foreignKey: 'id_kontingen', as: 'officialKontingen' });

  // PendaftaranEvent associations
  PendaftaranEvent.belongsTo(Event, { foreignKey: 'id_event', as: 'event' });
  PendaftaranEvent.belongsTo(Kontingen, { foreignKey: 'id_kontingen', as: 'kontingen' });
  PendaftaranEvent.hasMany(PendaftaranAtlet, { foreignKey: 'id_pendaftaran_event', as: 'pendaftaranAtlet' });

  // PendaftaranAtlet associations
  PendaftaranAtlet.belongsTo(PendaftaranEvent, { foreignKey: 'id_pendaftaran_event', as: 'pendaftaranEvent' });
  PendaftaranAtlet.hasMany(PendaftaranAtletDetail, { foreignKey: 'id_pendaftaran_atlet', as: 'atletDetails' });

  // PendaftaranAtletDetail associations
  PendaftaranAtletDetail.belongsTo(PendaftaranAtlet, { foreignKey: 'id_pendaftaran_atlet', as: 'pendaftaranAtlet' });
  PendaftaranAtletDetail.belongsTo(Atlet, { foreignKey: 'id_atlet', as: 'atlet' });

  // AtletFile associations
  AtletFile.belongsTo(Atlet, { foreignKey: 'id_atlet', as: 'atlet' });

  // Location associations
  Negara.hasMany(Provinsi, { foreignKey: 'id_negara', as: 'provinsi' });
  Provinsi.belongsTo(Negara, { foreignKey: 'id_negara', as: 'negara' });
  Provinsi.hasMany(KabupatenKota, { foreignKey: 'id_provinsi', as: 'kabupatenKota' });
  KabupatenKota.belongsTo(Provinsi, { foreignKey: 'id_provinsi', as: 'provinsi' });

  console.log('✅ Database associations set up successfully');
};

// Call setup associations
setupAssociations();

export {
  User,
  Event,
  Kontingen,
  Atlet,
  Official,
  Paket,
  Gallery,
  PendaftaranEvent,
  PendaftaranAtlet,
  PendaftaranAtletDetail,
  AtletFile,
  Negara,
  Provinsi,
  KabupatenKota,
};

export default {
  User,
  Event,
  Kontingen,
  Atlet,
  Official,
  Paket,
  Gallery,
  PendaftaranEvent,
  PendaftaranAtlet,
  PendaftaranAtletDetail,
  AtletFile,
  Negara,
  Provinsi,
  KabupatenKota,
};
