/**
 * Offline Storage Service for PWA
 * Handles caching and offline data management
 */

interface CacheItem<T = any> {
  data: T;
  timestamp: number;
  expiry?: number;
}

interface SyncQueueItem {
  id: string;
  method: 'POST' | 'PUT' | 'DELETE';
  url: string;
  data?: any;
  timestamp: number;
}

class OfflineService {
  private readonly CACHE_PREFIX = 'baja_cache_';
  private readonly SYNC_QUEUE_KEY = 'baja_sync_queue';
  private readonly DEFAULT_EXPIRY = 24 * 60 * 60 * 1000; // 24 hours

  /**
   * Store data in localStorage with expiry
   */
  setCache<T>(key: string, data: T, expiry?: number): void {
    try {
      const cacheItem: CacheItem<T> = {
        data,
        timestamp: Date.now(),
        expiry: expiry || this.DEFAULT_EXPIRY
      };
      
      localStorage.setItem(
        this.CACHE_PREFIX + key,
        JSON.stringify(cacheItem)
      );
    } catch (error) {
      console.error('Error setting cache:', error);
    }
  }

  /**
   * Get data from localStorage
   */
  getCache<T>(key: string): T | null {
    try {
      const cached = localStorage.getItem(this.CACHE_PREFIX + key);
      if (!cached) return null;

      const cacheItem: CacheItem<T> = JSON.parse(cached);
      
      // Check if expired
      if (cacheItem.expiry && Date.now() - cacheItem.timestamp > cacheItem.expiry) {
        this.removeCache(key);
        return null;
      }

      return cacheItem.data;
    } catch (error) {
      console.error('Error getting cache:', error);
      return null;
    }
  }

  /**
   * Remove data from cache
   */
  removeCache(key: string): void {
    try {
      localStorage.removeItem(this.CACHE_PREFIX + key);
    } catch (error) {
      console.error('Error removing cache:', error);
    }
  }

  /**
   * Clear all cache
   */
  clearCache(): void {
    try {
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith(this.CACHE_PREFIX)) {
          localStorage.removeItem(key);
        }
      });
    } catch (error) {
      console.error('Error clearing cache:', error);
    }
  }

  /**
   * Add item to sync queue for when back online
   */
  addToSyncQueue(item: Omit<SyncQueueItem, 'id' | 'timestamp'>): void {
    try {
      const queue = this.getSyncQueue();
      const newItem: SyncQueueItem = {
        ...item,
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        timestamp: Date.now()
      };
      
      queue.push(newItem);
      localStorage.setItem(this.SYNC_QUEUE_KEY, JSON.stringify(queue));
    } catch (error) {
      console.error('Error adding to sync queue:', error);
    }
  }

  /**
   * Get sync queue
   */
  getSyncQueue(): SyncQueueItem[] {
    try {
      const queue = localStorage.getItem(this.SYNC_QUEUE_KEY);
      return queue ? JSON.parse(queue) : [];
    } catch (error) {
      console.error('Error getting sync queue:', error);
      return [];
    }
  }

  /**
   * Remove item from sync queue
   */
  removeFromSyncQueue(id: string): void {
    try {
      const queue = this.getSyncQueue();
      const filteredQueue = queue.filter(item => item.id !== id);
      localStorage.setItem(this.SYNC_QUEUE_KEY, JSON.stringify(filteredQueue));
    } catch (error) {
      console.error('Error removing from sync queue:', error);
    }
  }

  /**
   * Clear sync queue
   */
  clearSyncQueue(): void {
    try {
      localStorage.removeItem(this.SYNC_QUEUE_KEY);
    } catch (error) {
      console.error('Error clearing sync queue:', error);
    }
  }

  /**
   * Check if online
   */
  isOnline(): boolean {
    return navigator.onLine;
  }

  /**
   * Cache API response
   */
  cacheApiResponse(endpoint: string, data: any, expiry?: number): void {
    const key = `api_${endpoint.replace(/[^a-zA-Z0-9]/g, '_')}`;
    this.setCache(key, data, expiry);
  }

  /**
   * Get cached API response
   */
  getCachedApiResponse<T>(endpoint: string): T | null {
    const key = `api_${endpoint.replace(/[^a-zA-Z0-9]/g, '_')}`;
    return this.getCache<T>(key);
  }

  /**
   * Cache user data
   */
  cacheUserData(userId: string, data: any): void {
    this.setCache(`user_${userId}`, data);
  }

  /**
   * Get cached user data
   */
  getCachedUserData(userId: string): any | null {
    return this.getCache(`user_${userId}`);
  }

  /**
   * Cache athletes data
   */
  cacheAthletes(userId: string, athletes: any[]): void {
    this.setCache(`athletes_${userId}`, athletes);
  }

  /**
   * Get cached athletes
   */
  getCachedAthletes(userId: string): any[] | null {
    return this.getCache(`athletes_${userId}`);
  }

  /**
   * Cache events data
   */
  cacheEvents(userId: string, events: any[]): void {
    this.setCache(`events_${userId}`, events);
  }

  /**
   * Get cached events
   */
  getCachedEvents(userId: string): any[] | null {
    return this.getCache(`events_${userId}`);
  }

  /**
   * Cache dashboard stats
   */
  cacheDashboardStats(userId: string, stats: any): void {
    this.setCache(`dashboard_stats_${userId}`, stats, 30 * 60 * 1000); // 30 minutes
  }

  /**
   * Get cached dashboard stats
   */
  getCachedDashboardStats(userId: string): any | null {
    return this.getCache(`dashboard_stats_${userId}`);
  }

  /**
   * Get cache size info
   */
  getCacheInfo(): { totalItems: number; totalSize: string } {
    let totalItems = 0;
    let totalSize = 0;

    try {
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith(this.CACHE_PREFIX)) {
          totalItems++;
          totalSize += localStorage.getItem(key)?.length || 0;
        }
      });

      return {
        totalItems,
        totalSize: `${(totalSize / 1024).toFixed(2)} KB`
      };
    } catch (error) {
      console.error('Error getting cache info:', error);
      return { totalItems: 0, totalSize: '0 KB' };
    }
  }
}

export const offlineService = new OfflineService();
