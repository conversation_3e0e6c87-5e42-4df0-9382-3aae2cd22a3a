<?php

namespace App\Controllers\KetuaKontingen;

use App\Controllers\BaseController;
use App\Models\Event;
use App\Models\PendaftaranEvent;
use App\Models\Kontingen;

class PendaftaranEventController extends BaseController
{
    public function index()
    {
        $userId = session()->get('id');
        if (!$userId) {
            return redirect()->to('/login')->with('error', 'Anda harus login terlebih dahulu.');
        }

        $eventModel = new Event();
        $pendaftaranEventModel = new PendaftaranEvent();
        $kontingenModel = new Kontingen();

        // Ambil semua event
        $events = $eventModel->findAll();

        // Ambil data kontingen milik user yang login
        $kontingen = $kontingenModel->where('id_user', $userId)->first();

        // Cek event yang sudah didaftarkan oleh kontingen
        $registeredEventIds = [];
        if ($kontingen) {
            $registeredEvents = $pendaftaranEventModel
                ->where('id_kontingen', $kontingen['id'])
                ->findAll();
            $registeredEventIds = array_column($registeredEvents, 'id_event');
        }

        // Tandai setiap event apakah sudah terdaftar dan tampilkan file pemenang jika ada
        foreach ($events as &$event) {
            $event['is_registered'] = in_array($event['id'], $registeredEventIds);
            $event['event_pemenang'] = $event['event_pemenang'] ?? null;
        }

        $data['events'] = $events;
        $data['kontingen'] = $kontingen;

        return view('ketua-kontingen/pendaftaran-event/index', $data);
    }

    public function store()
    {
        $userId = session()->get('id');
        if (!$userId) {
            return redirect()->to('/login')->with('error', 'Anda harus login terlebih dahulu.');
        }

        $pendaftaranEventModel = new PendaftaranEvent();
        $kontingenModel = new Kontingen();

        // Ambil data kontingen user dari session (menghindari manipulasi input)
        $kontingen = $kontingenModel->where('id_user', $userId)->first();
        if (!$kontingen) {
            return redirect()->back()->with('error', 'Data kontingen tidak ditemukan.');
        }

        // Validasi input (id_event harus numeric)
        $validationRules = [
            'id_event' => 'required|numeric',
        ];

        if (!$this->validate($validationRules)) {
            return redirect()->back()
                ->withInput()
                ->with('errors', $this->validator->getErrors());
        }

        $id_event = $this->request->getPost('id_event');

        // Cek duplikasi pendaftaran untuk event yang sama
        $existing = $pendaftaranEventModel
            ->where('id_event', $id_event)
            ->where('id_kontingen', $kontingen['id'])
            ->first();
        if ($existing) {
            return redirect()->back()->with('error', 'Anda sudah terdaftar pada event ini.');
        }

        // Siapkan data pendaftaran menggunakan data dari session untuk id_kontingen
        $data = [
            'id_event'     => $id_event,
            'id_kontingen' => $kontingen['id'],
        ];

        if ($pendaftaranEventModel->insert($data)) {
            return redirect()->to('/pendaftaran-event')->with('success', 'Pendaftaran berhasil disimpan.');
        } else {
            return redirect()->back()->with('error', 'Gagal menyimpan pendaftaran. Periksa kembali data Anda.');
        }
    }
}
