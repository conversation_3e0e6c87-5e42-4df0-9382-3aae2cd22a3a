import { Request, Response, NextFunction } from 'express';

// Simple in-memory session store (for development)
// In production, use Redis or similar
const sessionStore: { [key: string]: any } = {};

export interface SessionRequest extends Request {
  session: { [key: string]: any };
}

export const sessionMiddleware = (req: SessionRequest, res: Response, next: NextFunction) => {
  // Get session ID from cookie or create new one
  let sessionId = req.cookies.sessionId;
  
  if (!sessionId) {
    sessionId = generateSessionId();
    res.cookie('sessionId', sessionId, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
    });
  }

  // Initialize session data
  if (!sessionStore[sessionId]) {
    sessionStore[sessionId] = {};
  }

  // Attach session to request
  req.session = sessionStore[sessionId];

  // Save session data after response
  const originalSend = res.send;
  res.send = function(data) {
    sessionStore[sessionId] = req.session;
    return originalSend.call(this, data);
  };

  next();
};

function generateSessionId(): string {
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15) + 
         Date.now().toString(36);
}

// Clean up expired sessions (call this periodically)
export const cleanupSessions = () => {
  const now = Date.now();
  const maxAge = 24 * 60 * 60 * 1000; // 24 hours

  Object.keys(sessionStore).forEach(sessionId => {
    const session = sessionStore[sessionId];
    if (session._lastAccess && (now - session._lastAccess) > maxAge) {
      delete sessionStore[sessionId];
    }
  });
};

// Update last access time
export const updateSessionAccess = (req: SessionRequest, res: Response, next: NextFunction) => {
  if (req.session) {
    req.session._lastAccess = Date.now();
  }
  next();
};
