{"version": 3, "file": "bootstrap.bundle.js", "sources": ["../../js/src/util/index.js", "../../js/src/dom/data.js", "../../js/src/dom/event-handler.js", "../../js/src/base-component.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/dom/manipulator.js", "../../js/src/dom/selector-engine.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../node_modules/@popperjs/core/lib/enums.js", "../../node_modules/@popperjs/core/lib/dom-utils/getNodeName.js", "../../node_modules/@popperjs/core/lib/dom-utils/getWindow.js", "../../node_modules/@popperjs/core/lib/dom-utils/instanceOf.js", "../../node_modules/@popperjs/core/lib/modifiers/applyStyles.js", "../../node_modules/@popperjs/core/lib/utils/getBasePlacement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getLayoutRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/contains.js", "../../node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js", "../../node_modules/@popperjs/core/lib/dom-utils/isTableElement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getParentNode.js", "../../node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js", "../../node_modules/@popperjs/core/lib/utils/getMainAxisFromPlacement.js", "../../node_modules/@popperjs/core/lib/utils/math.js", "../../node_modules/@popperjs/core/lib/utils/within.js", "../../node_modules/@popperjs/core/lib/utils/getFreshSideObject.js", "../../node_modules/@popperjs/core/lib/utils/mergePaddingObject.js", "../../node_modules/@popperjs/core/lib/utils/expandToHashMap.js", "../../node_modules/@popperjs/core/lib/modifiers/arrow.js", "../../node_modules/@popperjs/core/lib/modifiers/computeStyles.js", "../../node_modules/@popperjs/core/lib/modifiers/eventListeners.js", "../../node_modules/@popperjs/core/lib/utils/getOppositePlacement.js", "../../node_modules/@popperjs/core/lib/utils/getOppositeVariationPlacement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getWindowScroll.js", "../../node_modules/@popperjs/core/lib/dom-utils/getWindowScrollBarX.js", "../../node_modules/@popperjs/core/lib/dom-utils/getViewportRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getDocumentRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/isScrollParent.js", "../../node_modules/@popperjs/core/lib/dom-utils/getScrollParent.js", "../../node_modules/@popperjs/core/lib/dom-utils/listScrollParents.js", "../../node_modules/@popperjs/core/lib/utils/rectToClientRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getClippingRect.js", "../../node_modules/@popperjs/core/lib/utils/getVariation.js", "../../node_modules/@popperjs/core/lib/utils/computeOffsets.js", "../../node_modules/@popperjs/core/lib/utils/detectOverflow.js", "../../node_modules/@popperjs/core/lib/utils/computeAutoPlacement.js", "../../node_modules/@popperjs/core/lib/modifiers/flip.js", "../../node_modules/@popperjs/core/lib/modifiers/hide.js", "../../node_modules/@popperjs/core/lib/modifiers/offset.js", "../../node_modules/@popperjs/core/lib/modifiers/popperOffsets.js", "../../node_modules/@popperjs/core/lib/utils/getAltAxis.js", "../../node_modules/@popperjs/core/lib/modifiers/preventOverflow.js", "../../node_modules/@popperjs/core/lib/dom-utils/getHTMLElementScroll.js", "../../node_modules/@popperjs/core/lib/dom-utils/getNodeScroll.js", "../../node_modules/@popperjs/core/lib/dom-utils/getCompositeRect.js", "../../node_modules/@popperjs/core/lib/utils/orderModifiers.js", "../../node_modules/@popperjs/core/lib/utils/debounce.js", "../../node_modules/@popperjs/core/lib/utils/mergeByName.js", "../../node_modules/@popperjs/core/lib/createPopper.js", "../../node_modules/@popperjs/core/lib/popper-lite.js", "../../node_modules/@popperjs/core/lib/popper.js", "../../js/src/dropdown.js", "../../js/src/modal.js", "../../js/src/util/scrollbar.js", "../../js/src/offcanvas.js", "../../js/src/util/sanitizer.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/toast.js", "../../js/index.umd.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttr = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttr || (!hrefAttr.includes('#') && !hrefAttr.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttr.includes('#') && !hrefAttr.startsWith('#')) {\n      hrefAttr = '#' + hrefAttr.split('#')[1]\n    }\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => (obj[0] || obj).nodeType\n\nconst emulateTransitionEnd = (element, duration) => {\n  let called = false\n  const durationPadding = 5\n  const emulatedDuration = duration + durationPadding\n\n  function listener() {\n    called = true\n    element.removeEventListener(TRANSITION_END, listener)\n  }\n\n  element.addEventListener(TRANSITION_END, listener)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(element)\n    }\n  }, emulatedDuration)\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes).forEach(property => {\n    const expectedTypes = configTypes[property]\n    const value = config[property]\n    const valueType = value && isElement(value) ? 'element' : toType(value)\n\n    if (!new RegExp(expectedTypes).test(valueType)) {\n      throw new TypeError(\n        `${componentName.toUpperCase()}: ` +\n        `Option \"${property}\" provided type \"${valueType}\" ` +\n        `but expected type \"${expectedTypes}\".`\n      )\n    }\n  })\n}\n\nconst isVisible = element => {\n  if (!element) {\n    return false\n  }\n\n  if (element.style && element.parentNode && element.parentNode.style) {\n    const elementStyle = getComputedStyle(element)\n    const parentNodeStyle = getComputedStyle(element.parentNode)\n\n    return elementStyle.display !== 'none' &&\n      parentNodeStyle.display !== 'none' &&\n      elementStyle.visibility !== 'hidden'\n  }\n\n  return false\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => function () {}\n\nconst reflow = element => element.offsetHeight\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    document.addEventListener('DOMContentLoaded', callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = (name, plugin) => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nexport {\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  emulateTransitionEnd,\n  typeCheckConfig,\n  isVisible,\n  isDisabled,\n  findShadowRoot,\n  noop,\n  reflow,\n  getjQuery,\n  onDOMContentLoaded,\n  isRTL,\n  defineJQueryPlugin\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst elementMap = new Map()\n\nexport default {\n  set(element, key, instance) {\n    if (!elementMap.has(element)) {\n      elementMap.set(element, new Map())\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    // make it clear we only want one instance per element\n    // can be removed later when multiple key/instances are fine to be used\n    if (!instanceMap.has(key) && instanceMap.size !== 0) {\n      // eslint-disable-next-line no-console\n      console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(instanceMap.keys())[0]}.`)\n      return\n    }\n\n    instanceMap.set(key, instance)\n  },\n\n  get(element, key) {\n    if (elementMap.has(element)) {\n      return elementMap.get(element).get(key) || null\n    }\n\n    return null\n  },\n\n  remove(element, key) {\n    if (!elementMap.has(element)) {\n      return\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    instanceMap.delete(key)\n\n    // free up element references if there are no instances left for an element\n    if (instanceMap.size === 0) {\n      elementMap.delete(element)\n    }\n  }\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\nconst nativeEvents = new Set([\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n])\n\n/**\n * ------------------------------------------------------------------------\n * Private methods\n * ------------------------------------------------------------------------\n */\n\nfunction getUidEvent(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getEvent(element) {\n  const uid = getUidEvent(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    event.delegateTarget = element\n\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (let i = domElements.length; i--;) {\n        if (domElements[i] === target) {\n          event.delegateTarget = target\n\n          if (handler.oneOff) {\n            // eslint-disable-next-line unicorn/consistent-destructuring\n            EventHandler.off(element, event.type, fn)\n          }\n\n          return fn.apply(target, [event])\n        }\n      }\n    }\n\n    // To please ESLint\n    return null\n  }\n}\n\nfunction findHandler(events, handler, delegationSelector = null) {\n  const uidEventList = Object.keys(events)\n\n  for (let i = 0, len = uidEventList.length; i < len; i++) {\n    const event = events[uidEventList[i]]\n\n    if (event.originalHandler === handler && event.delegationSelector === delegationSelector) {\n      return event\n    }\n  }\n\n  return null\n}\n\nfunction normalizeParams(originalTypeEvent, handler, delegationFn) {\n  const delegation = typeof handler === 'string'\n  const originalHandler = delegation ? delegationFn : handler\n\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  let typeEvent = originalTypeEvent.replace(stripNameRegex, '')\n  const custom = customEvents[typeEvent]\n\n  if (custom) {\n    typeEvent = custom\n  }\n\n  const isNative = nativeEvents.has(typeEvent)\n\n  if (!isNative) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [delegation, originalHandler, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFn, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  if (!handler) {\n    handler = delegationFn\n    delegationFn = null\n  }\n\n  const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n  const events = getEvent(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFn = findHandler(handlers, originalHandler, delegation ? handler : null)\n\n  if (previousFn) {\n    previousFn.oneOff = previousFn.oneOff && oneOff\n\n    return\n  }\n\n  const uid = getUidEvent(originalHandler, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = delegation ?\n    bootstrapDelegationHandler(element, handler, delegationFn) :\n    bootstrapHandler(element, handler)\n\n  fn.delegationSelector = delegation ? handler : null\n  fn.originalHandler = originalHandler\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, delegation)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  Object.keys(storeElementEvent).forEach(handlerKey => {\n    if (handlerKey.includes(namespace)) {\n      const event = storeElementEvent[handlerKey]\n\n      removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n    }\n  })\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, false)\n  },\n\n  one(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFn) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getEvent(element)\n    const isNamespace = originalTypeEvent.startsWith('.')\n\n    if (typeof originalHandler !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!events || !events[typeEvent]) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, originalHandler, delegation ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      Object.keys(events).forEach(elementEvent => {\n        removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n      })\n    }\n\n    const storeElementEvent = events[typeEvent] || {}\n    Object.keys(storeElementEvent).forEach(keyHandlers => {\n      const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n      if (!inNamespace || originalTypeEvent.includes(handlerKey)) {\n        const event = storeElementEvent[keyHandlers]\n\n        removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n      }\n    })\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const $ = getjQuery()\n    const typeEvent = event.replace(stripNameRegex, '')\n    const inNamespace = event !== typeEvent\n    const isNative = nativeEvents.has(typeEvent)\n\n    let jQueryEvent\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n    let evt = null\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    if (isNative) {\n      evt = document.createEvent('HTMLEvents')\n      evt.initEvent(typeEvent, bubbles, true)\n    } else {\n      evt = new CustomEvent(event, {\n        bubbles,\n        cancelable: true\n      })\n    }\n\n    // merge custom information in our event\n    if (typeof args !== 'undefined') {\n      Object.keys(args).forEach(key => {\n        Object.defineProperty(evt, key, {\n          get() {\n            return args[key]\n          }\n        })\n      })\n    }\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && typeof jQueryEvent !== 'undefined') {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nexport default EventHandler\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): base-component.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Data from './dom/data'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst VERSION = '5.0.0-beta3'\n\nclass BaseComponent {\n  constructor(element) {\n    element = typeof element === 'string' ? document.querySelector(element) : element\n\n    if (!element) {\n      return\n    }\n\n    this._element = element\n    Data.set(this._element, this.constructor.DATA_KEY, this)\n  }\n\n  dispose() {\n    Data.remove(this._element, this.constructor.DATA_KEY)\n    this._element = null\n  }\n\n  /** Static */\n\n  static getInstance(element) {\n    return Data.get(element, this.DATA_KEY)\n  }\n\n  static get VERSION() {\n    return VERSION\n  }\n}\n\nexport default BaseComponent\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'alert'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst SELECTOR_DISMISS = '[data-bs-dismiss=\"alert\"]'\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_ALERT = 'alert'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Alert extends BaseComponent {\n  // Getters\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  close(element) {\n    const rootElement = element ? this._getRootElement(element) : this._element\n    const customEvent = this._triggerCloseEvent(rootElement)\n\n    if (customEvent === null || customEvent.defaultPrevented) {\n      return\n    }\n\n    this._removeElement(rootElement)\n  }\n\n  // Private\n\n  _getRootElement(element) {\n    return getElementFromSelector(element) || element.closest(`.${CLASS_NAME_ALERT}`)\n  }\n\n  _triggerCloseEvent(element) {\n    return EventHandler.trigger(element, EVENT_CLOSE)\n  }\n\n  _removeElement(element) {\n    element.classList.remove(CLASS_NAME_SHOW)\n\n    if (!element.classList.contains(CLASS_NAME_FADE)) {\n      this._destroyElement(element)\n      return\n    }\n\n    const transitionDuration = getTransitionDurationFromElement(element)\n\n    EventHandler.one(element, 'transitionend', () => this._destroyElement(element))\n    emulateTransitionEnd(element, transitionDuration)\n  }\n\n  _destroyElement(element) {\n    if (element.parentNode) {\n      element.parentNode.removeChild(element)\n    }\n\n    EventHandler.trigger(element, EVENT_CLOSED)\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n\n      if (!data) {\n        data = new Alert(this)\n      }\n\n      if (config === 'close') {\n        data[config](this)\n      }\n    })\n  }\n\n  static handleDismiss(alertInstance) {\n    return function (event) {\n      if (event) {\n        event.preventDefault()\n      }\n\n      alertInstance.close(this)\n    }\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DISMISS, Alert.handleDismiss(new Alert()))\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Alert to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Alert)\n\nexport default Alert\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'button'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"button\"]'\n\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Button extends BaseComponent {\n  // Getters\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n\n      if (!data) {\n        data = new Button(this)\n      }\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, event => {\n  event.preventDefault()\n\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE)\n\n  let data = Data.get(button, DATA_KEY)\n  if (!data) {\n    data = new Button(button)\n  }\n\n  data.toggle()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Button to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Button)\n\nexport default Button\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(val) {\n  if (val === 'true') {\n    return true\n  }\n\n  if (val === 'false') {\n    return false\n  }\n\n  if (val === Number(val).toString()) {\n    return Number(val)\n  }\n\n  if (val === '' || val === 'null') {\n    return null\n  }\n\n  return val\n}\n\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.to<PERSON><PERSON><PERSON><PERSON>ase()}`)\n}\n\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-bs-${normalizeDataKey(key)}`, value)\n  },\n\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-bs-${normalizeDataKey(key)}`)\n  },\n\n  getDataAttributes(element) {\n    if (!element) {\n      return {}\n    }\n\n    const attributes = {}\n\n    Object.keys(element.dataset)\n      .filter(key => key.startsWith('bs'))\n      .forEach(key => {\n        let pureKey = key.replace(/^bs/, '')\n        pureKey = pureKey.charAt(0).toLowerCase() + pureKey.slice(1, pureKey.length)\n        attributes[pureKey] = normalizeData(element.dataset[key])\n      })\n\n    return attributes\n  },\n\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-bs-${normalizeDataKey(key)}`))\n  },\n\n  offset(element) {\n    const rect = element.getBoundingClientRect()\n\n    return {\n      top: rect.top + document.body.scrollTop,\n      left: rect.left + document.body.scrollLeft\n    }\n  },\n\n  position(element) {\n    return {\n      top: element.offsetTop,\n      left: element.offsetLeft\n    }\n  }\n}\n\nexport default Manipulator\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NODE_TEXT = 3\n\nconst SelectorEngine = {\n  find(selector, element = document.documentElement) {\n    return [].concat(...Element.prototype.querySelectorAll.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return Element.prototype.querySelector.call(element, selector)\n  },\n\n  children(element, selector) {\n    return [].concat(...element.children)\n      .filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n\n    let ancestor = element.parentNode\n\n    while (ancestor && ancestor.nodeType === Node.ELEMENT_NODE && ancestor.nodeType !== NODE_TEXT) {\n      if (ancestor.matches(selector)) {\n        parents.push(ancestor)\n      }\n\n      ancestor = ancestor.parentNode\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (next.matches(selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  }\n}\n\nexport default SelectorEngine\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isRTL,\n  isVisible,\n  reflow,\n  triggerTransitionEnd,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'carousel'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  slide: false,\n  pause: 'hover',\n  wrap: true,\n  touch: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)',\n  keyboard: 'boolean',\n  slide: '(boolean|string)',\n  pause: '(string|boolean)',\n  wrap: 'boolean',\n  touch: 'boolean'\n}\n\nconst ORDER_NEXT = 'next'\nconst ORDER_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_END = 'carousel-item-end'\nconst CLASS_NAME_START = 'carousel-item-start'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_ITEM = '.active.carousel-item'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_NEXT_PREV = '.carousel-item-next, .carousel-item-prev'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_INDICATOR = '[data-bs-target]'\nconst SELECTOR_DATA_SLIDE = '[data-bs-slide], [data-bs-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-bs-ride=\"carousel\"]'\n\nconst POINTER_TYPE_TOUCH = 'touch'\nconst POINTER_TYPE_PEN = 'pen'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\nclass Carousel extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._items = null\n    this._interval = null\n    this._activeElement = null\n    this._isPaused = false\n    this._isSliding = false\n    this.touchTimeout = null\n    this.touchStartX = 0\n    this.touchDeltaX = 0\n\n    this._config = this._getConfig(config)\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element)\n    this._touchSupported = 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n    this._pointerEvent = Boolean(window.PointerEvent)\n\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  next() {\n    if (!this._isSliding) {\n      this._slide(ORDER_NEXT)\n    }\n  }\n\n  nextWhenVisible() {\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next()\n    }\n  }\n\n  prev() {\n    if (!this._isSliding) {\n      this._slide(ORDER_PREV)\n    }\n  }\n\n  pause(event) {\n    if (!event) {\n      this._isPaused = true\n    }\n\n    if (SelectorEngine.findOne(SELECTOR_NEXT_PREV, this._element)) {\n      triggerTransitionEnd(this._element)\n      this.cycle(true)\n    }\n\n    clearInterval(this._interval)\n    this._interval = null\n  }\n\n  cycle(event) {\n    if (!event) {\n      this._isPaused = false\n    }\n\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n\n    if (this._config && this._config.interval && !this._isPaused) {\n      this._updateInterval()\n\n      this._interval = setInterval(\n        (document.visibilityState ? this.nextWhenVisible : this.next).bind(this),\n        this._config.interval\n      )\n    }\n  }\n\n  to(index) {\n    this._activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeIndex = this._getItemIndex(this._activeElement)\n\n    if (index > this._items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    if (activeIndex === index) {\n      this.pause()\n      this.cycle()\n      return\n    }\n\n    const order = index > activeIndex ?\n      ORDER_NEXT :\n      ORDER_PREV\n\n    this._slide(order, this._items[index])\n  }\n\n  dispose() {\n    EventHandler.off(this._element, EVENT_KEY)\n\n    this._items = null\n    this._config = null\n    this._interval = null\n    this._isPaused = null\n    this._isSliding = null\n    this._activeElement = null\n    this._indicatorsElement = null\n\n    super.dispose()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _handleSwipe() {\n    const absDeltax = Math.abs(this.touchDeltaX)\n\n    if (absDeltax <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltax / this.touchDeltaX\n\n    this.touchDeltaX = 0\n\n    if (!direction) {\n      return\n    }\n\n    this._slide(direction > 0 ? DIRECTION_RIGHT : DIRECTION_LEFT)\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      EventHandler.on(this._element, EVENT_MOUSEENTER, event => this.pause(event))\n      EventHandler.on(this._element, EVENT_MOUSELEAVE, event => this.cycle(event))\n    }\n\n    if (this._config.touch && this._touchSupported) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    const start = event => {\n      if (this._pointerEvent && (event.pointerType === POINTER_TYPE_PEN || event.pointerType === POINTER_TYPE_TOUCH)) {\n        this.touchStartX = event.clientX\n      } else if (!this._pointerEvent) {\n        this.touchStartX = event.touches[0].clientX\n      }\n    }\n\n    const move = event => {\n      // ensure swiping with one touch and not pinching\n      this.touchDeltaX = event.touches && event.touches.length > 1 ?\n        0 :\n        event.touches[0].clientX - this.touchStartX\n    }\n\n    const end = event => {\n      if (this._pointerEvent && (event.pointerType === POINTER_TYPE_PEN || event.pointerType === POINTER_TYPE_TOUCH)) {\n        this.touchDeltaX = event.clientX - this.touchStartX\n      }\n\n      this._handleSwipe()\n      if (this._config.pause === 'hover') {\n        // If it's a touch-enabled device, mouseenter/leave are fired as\n        // part of the mouse compatibility events on first tap - the carousel\n        // would stop cycling until user tapped out of it;\n        // here, we listen for touchend, explicitly pause the carousel\n        // (as if it's the second time we tap on it, mouseenter compat event\n        // is NOT fired) and after a timeout (to allow for mouse compatibility\n        // events to fire) we explicitly restart cycling\n\n        this.pause()\n        if (this.touchTimeout) {\n          clearTimeout(this.touchTimeout)\n        }\n\n        this.touchTimeout = setTimeout(event => this.cycle(event), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n      }\n    }\n\n    SelectorEngine.find(SELECTOR_ITEM_IMG, this._element).forEach(itemImg => {\n      EventHandler.on(itemImg, EVENT_DRAG_START, e => e.preventDefault())\n    })\n\n    if (this._pointerEvent) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => start(event))\n      EventHandler.on(this._element, EVENT_POINTERUP, event => end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => start(event))\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => move(event))\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => end(event))\n    }\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    if (event.key === ARROW_LEFT_KEY) {\n      event.preventDefault()\n      this._slide(DIRECTION_LEFT)\n    } else if (event.key === ARROW_RIGHT_KEY) {\n      event.preventDefault()\n      this._slide(DIRECTION_RIGHT)\n    }\n  }\n\n  _getItemIndex(element) {\n    this._items = element && element.parentNode ?\n      SelectorEngine.find(SELECTOR_ITEM, element.parentNode) :\n      []\n\n    return this._items.indexOf(element)\n  }\n\n  _getItemByOrder(order, activeElement) {\n    const isNext = order === ORDER_NEXT\n    const isPrev = order === ORDER_PREV\n    const activeIndex = this._getItemIndex(activeElement)\n    const lastItemIndex = this._items.length - 1\n    const isGoingToWrap = (isPrev && activeIndex === 0) || (isNext && activeIndex === lastItemIndex)\n\n    if (isGoingToWrap && !this._config.wrap) {\n      return activeElement\n    }\n\n    const delta = isPrev ? -1 : 1\n    const itemIndex = (activeIndex + delta) % this._items.length\n\n    return itemIndex === -1 ?\n      this._items[this._items.length - 1] :\n      this._items[itemIndex]\n  }\n\n  _triggerSlideEvent(relatedTarget, eventDirectionName) {\n    const targetIndex = this._getItemIndex(relatedTarget)\n    const fromIndex = this._getItemIndex(SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element))\n\n    return EventHandler.trigger(this._element, EVENT_SLIDE, {\n      relatedTarget,\n      direction: eventDirectionName,\n      from: fromIndex,\n      to: targetIndex\n    })\n  }\n\n  _setActiveIndicatorElement(element) {\n    if (this._indicatorsElement) {\n      const activeIndicator = SelectorEngine.findOne(SELECTOR_ACTIVE, this._indicatorsElement)\n\n      activeIndicator.classList.remove(CLASS_NAME_ACTIVE)\n      activeIndicator.removeAttribute('aria-current')\n\n      const indicators = SelectorEngine.find(SELECTOR_INDICATOR, this._indicatorsElement)\n\n      for (let i = 0; i < indicators.length; i++) {\n        if (Number.parseInt(indicators[i].getAttribute('data-bs-slide-to'), 10) === this._getItemIndex(element)) {\n          indicators[i].classList.add(CLASS_NAME_ACTIVE)\n          indicators[i].setAttribute('aria-current', 'true')\n          break\n        }\n      }\n    }\n  }\n\n  _updateInterval() {\n    const element = this._activeElement || SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n\n    if (!element) {\n      return\n    }\n\n    const elementInterval = Number.parseInt(element.getAttribute('data-bs-interval'), 10)\n\n    if (elementInterval) {\n      this._config.defaultInterval = this._config.defaultInterval || this._config.interval\n      this._config.interval = elementInterval\n    } else {\n      this._config.interval = this._config.defaultInterval || this._config.interval\n    }\n  }\n\n  _slide(directionOrOrder, element) {\n    const order = this._directionToOrder(directionOrOrder)\n    const activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeElementIndex = this._getItemIndex(activeElement)\n    const nextElement = element || this._getItemByOrder(order, activeElement)\n\n    const nextElementIndex = this._getItemIndex(nextElement)\n    const isCycling = Boolean(this._interval)\n\n    const isNext = order === ORDER_NEXT\n    const directionalClassName = isNext ? CLASS_NAME_START : CLASS_NAME_END\n    const orderClassName = isNext ? CLASS_NAME_NEXT : CLASS_NAME_PREV\n    const eventDirectionName = this._orderToDirection(order)\n\n    if (nextElement && nextElement.classList.contains(CLASS_NAME_ACTIVE)) {\n      this._isSliding = false\n      return\n    }\n\n    const slideEvent = this._triggerSlideEvent(nextElement, eventDirectionName)\n    if (slideEvent.defaultPrevented) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      return\n    }\n\n    this._isSliding = true\n\n    if (isCycling) {\n      this.pause()\n    }\n\n    this._setActiveIndicatorElement(nextElement)\n    this._activeElement = nextElement\n\n    if (this._element.classList.contains(CLASS_NAME_SLIDE)) {\n      nextElement.classList.add(orderClassName)\n\n      reflow(nextElement)\n\n      activeElement.classList.add(directionalClassName)\n      nextElement.classList.add(directionalClassName)\n\n      const transitionDuration = getTransitionDurationFromElement(activeElement)\n\n      EventHandler.one(activeElement, 'transitionend', () => {\n        nextElement.classList.remove(directionalClassName, orderClassName)\n        nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n        activeElement.classList.remove(CLASS_NAME_ACTIVE, orderClassName, directionalClassName)\n\n        this._isSliding = false\n\n        setTimeout(() => {\n          EventHandler.trigger(this._element, EVENT_SLID, {\n            relatedTarget: nextElement,\n            direction: eventDirectionName,\n            from: activeElementIndex,\n            to: nextElementIndex\n          })\n        }, 0)\n      })\n\n      emulateTransitionEnd(activeElement, transitionDuration)\n    } else {\n      activeElement.classList.remove(CLASS_NAME_ACTIVE)\n      nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n      this._isSliding = false\n      EventHandler.trigger(this._element, EVENT_SLID, {\n        relatedTarget: nextElement,\n        direction: eventDirectionName,\n        from: activeElementIndex,\n        to: nextElementIndex\n      })\n    }\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  _directionToOrder(direction) {\n    if (![DIRECTION_RIGHT, DIRECTION_LEFT].includes(direction)) {\n      return direction\n    }\n\n    if (isRTL()) {\n      return direction === DIRECTION_RIGHT ? ORDER_PREV : ORDER_NEXT\n    }\n\n    return direction === DIRECTION_RIGHT ? ORDER_NEXT : ORDER_PREV\n  }\n\n  _orderToDirection(order) {\n    if (![ORDER_NEXT, ORDER_PREV].includes(order)) {\n      return order\n    }\n\n    if (isRTL()) {\n      return order === ORDER_NEXT ? DIRECTION_LEFT : DIRECTION_RIGHT\n    }\n\n    return order === ORDER_NEXT ? DIRECTION_RIGHT : DIRECTION_LEFT\n  }\n\n  // Static\n\n  static carouselInterface(element, config) {\n    let data = Data.get(element, DATA_KEY)\n    let _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element)\n    }\n\n    if (typeof config === 'object') {\n      _config = {\n        ..._config,\n        ...config\n      }\n    }\n\n    const action = typeof config === 'string' ? config : _config.slide\n\n    if (!data) {\n      data = new Carousel(element, _config)\n    }\n\n    if (typeof config === 'number') {\n      data.to(config)\n    } else if (typeof action === 'string') {\n      if (typeof data[action] === 'undefined') {\n        throw new TypeError(`No method named \"${action}\"`)\n      }\n\n      data[action]()\n    } else if (_config.interval && _config.ride) {\n      data.pause()\n      data.cycle()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Carousel.carouselInterface(this, config)\n    })\n  }\n\n  static dataApiClickHandler(event) {\n    const target = getElementFromSelector(this)\n\n    if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n      return\n    }\n\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n    const slideIndex = this.getAttribute('data-bs-slide-to')\n\n    if (slideIndex) {\n      config.interval = false\n    }\n\n    Carousel.carouselInterface(target, config)\n\n    if (slideIndex) {\n      Data.get(target, DATA_KEY).to(slideIndex)\n    }\n\n    event.preventDefault()\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, Carousel.dataApiClickHandler)\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE)\n\n  for (let i = 0, len = carousels.length; i < len; i++) {\n    Carousel.carouselInterface(carousels[i], Data.get(carousels[i], DATA_KEY))\n  }\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Carousel to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Carousel)\n\nexport default Carousel\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  emulateTransitionEnd,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isElement,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'collapse'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  toggle: true,\n  parent: ''\n}\n\nconst DefaultType = {\n  toggle: 'boolean',\n  parent: '(string|element)'\n}\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.show, .collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"collapse\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Collapse extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._isTransitioning = false\n    this._config = this._getConfig(config)\n    this._triggerArray = SelectorEngine.find(\n      `${SELECTOR_DATA_TOGGLE}[href=\"#${this._element.id}\"],` +\n      `${SELECTOR_DATA_TOGGLE}[data-bs-target=\"#${this._element.id}\"]`\n    )\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggleList.length; i < len; i++) {\n      const elem = toggleList[i]\n      const selector = getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElem => foundElem === this._element)\n\n      if (selector !== null && filterElement.length) {\n        this._selector = selector\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._parent = this._config.parent ? this._getParent() : null\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._element, this._triggerArray)\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning || this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    let actives\n    let activesData\n\n    if (this._parent) {\n      actives = SelectorEngine.find(SELECTOR_ACTIVES, this._parent)\n        .filter(elem => {\n          if (typeof this._config.parent === 'string') {\n            return elem.getAttribute('data-bs-parent') === this._config.parent\n          }\n\n          return elem.classList.contains(CLASS_NAME_COLLAPSE)\n        })\n\n      if (actives.length === 0) {\n        actives = null\n      }\n    }\n\n    const container = SelectorEngine.findOne(this._selector)\n    if (actives) {\n      const tempActiveData = actives.find(elem => container !== elem)\n      activesData = tempActiveData ? Data.get(tempActiveData, DATA_KEY) : null\n\n      if (activesData && activesData._isTransitioning) {\n        return\n      }\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    if (actives) {\n      actives.forEach(elemActive => {\n        if (container !== elemActive) {\n          Collapse.collapseInterface(elemActive, 'hide')\n        }\n\n        if (!activesData) {\n          Data.set(elemActive, DATA_KEY, null)\n        }\n      })\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    if (this._triggerArray.length) {\n      this._triggerArray.forEach(element => {\n        element.classList.remove(CLASS_NAME_COLLAPSED)\n        element.setAttribute('aria-expanded', true)\n      })\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      this.setTransitioning(false)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n    const transitionDuration = getTransitionDurationFromElement(this._element)\n\n    EventHandler.one(this._element, 'transitionend', complete)\n\n    emulateTransitionEnd(this._element, transitionDuration)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning || !this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    const triggerArrayLength = this._triggerArray.length\n    if (triggerArrayLength > 0) {\n      for (let i = 0; i < triggerArrayLength; i++) {\n        const trigger = this._triggerArray[i]\n        const elem = getElementFromSelector(trigger)\n\n        if (elem && !elem.classList.contains(CLASS_NAME_SHOW)) {\n          trigger.classList.add(CLASS_NAME_COLLAPSED)\n          trigger.setAttribute('aria-expanded', false)\n        }\n      }\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this.setTransitioning(false)\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n    const transitionDuration = getTransitionDurationFromElement(this._element)\n\n    EventHandler.one(this._element, 'transitionend', complete)\n    emulateTransitionEnd(this._element, transitionDuration)\n  }\n\n  setTransitioning(isTransitioning) {\n    this._isTransitioning = isTransitioning\n  }\n\n  dispose() {\n    super.dispose()\n    this._config = null\n    this._parent = null\n    this._triggerArray = null\n    this._isTransitioning = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _getDimension() {\n    return this._element.classList.contains(WIDTH) ? WIDTH : HEIGHT\n  }\n\n  _getParent() {\n    let { parent } = this._config\n\n    if (isElement(parent)) {\n      // it's a jQuery object\n      if (typeof parent.jquery !== 'undefined' || typeof parent[0] !== 'undefined') {\n        parent = parent[0]\n      }\n    } else {\n      parent = SelectorEngine.findOne(parent)\n    }\n\n    const selector = `${SELECTOR_DATA_TOGGLE}[data-bs-parent=\"${parent}\"]`\n\n    SelectorEngine.find(selector, parent)\n      .forEach(element => {\n        const selected = getElementFromSelector(element)\n\n        this._addAriaAndCollapsedClass(\n          selected,\n          [element]\n        )\n      })\n\n    return parent\n  }\n\n  _addAriaAndCollapsedClass(element, triggerArray) {\n    if (!element || !triggerArray.length) {\n      return\n    }\n\n    const isOpen = element.classList.contains(CLASS_NAME_SHOW)\n\n    triggerArray.forEach(elem => {\n      if (isOpen) {\n        elem.classList.remove(CLASS_NAME_COLLAPSED)\n      } else {\n        elem.classList.add(CLASS_NAME_COLLAPSED)\n      }\n\n      elem.setAttribute('aria-expanded', isOpen)\n    })\n  }\n\n  // Static\n\n  static collapseInterface(element, config) {\n    let data = Data.get(element, DATA_KEY)\n    const _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (!data && _config.toggle && typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false\n    }\n\n    if (!data) {\n      data = new Collapse(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Collapse.collapseInterface(this, config)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A' || (event.delegateTarget && event.delegateTarget.tagName === 'A')) {\n    event.preventDefault()\n  }\n\n  const triggerData = Manipulator.getDataAttributes(this)\n  const selector = getSelectorFromElement(this)\n  const selectorElements = SelectorEngine.find(selector)\n\n  selectorElements.forEach(element => {\n    const data = Data.get(element, DATA_KEY)\n    let config\n    if (data) {\n      // update parent attribute\n      if (data._parent === null && typeof triggerData.parent === 'string') {\n        data._config.parent = triggerData.parent\n        data._parent = data._getParent()\n      }\n\n      config = 'toggle'\n    } else {\n      config = triggerData\n    }\n\n    Collapse.collapseInterface(element, config)\n  })\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Collapse to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Collapse)\n\nexport default Collapse\n", "export var top = 'top';\nexport var bottom = 'bottom';\nexport var right = 'right';\nexport var left = 'left';\nexport var auto = 'auto';\nexport var basePlacements = [top, bottom, right, left];\nexport var start = 'start';\nexport var end = 'end';\nexport var clippingParents = 'clippingParents';\nexport var viewport = 'viewport';\nexport var popper = 'popper';\nexport var reference = 'reference';\nexport var variationPlacements = /*#__PURE__*/basePlacements.reduce(function (acc, placement) {\n  return acc.concat([placement + \"-\" + start, placement + \"-\" + end]);\n}, []);\nexport var placements = /*#__PURE__*/[].concat(basePlacements, [auto]).reduce(function (acc, placement) {\n  return acc.concat([placement, placement + \"-\" + start, placement + \"-\" + end]);\n}, []); // modifiers that need to read the DOM\n\nexport var beforeRead = 'beforeRead';\nexport var read = 'read';\nexport var afterRead = 'afterRead'; // pure-logic modifiers\n\nexport var beforeMain = 'beforeMain';\nexport var main = 'main';\nexport var afterMain = 'afterMain'; // modifier with the purpose to write to the DOM (or write into a framework state)\n\nexport var beforeWrite = 'beforeWrite';\nexport var write = 'write';\nexport var afterWrite = 'afterWrite';\nexport var modifierPhases = [beforeRead, read, afterRead, beforeMain, main, afterMain, beforeWrite, write, afterWrite];", "export default function getNodeName(element) {\n  return element ? (element.nodeName || '').toLowerCase() : null;\n}", "export default function getWindow(node) {\n  if (node == null) {\n    return window;\n  }\n\n  if (node.toString() !== '[object Window]') {\n    var ownerDocument = node.ownerDocument;\n    return ownerDocument ? ownerDocument.defaultView || window : window;\n  }\n\n  return node;\n}", "import getWindow from \"./getWindow.js\";\n\nfunction isElement(node) {\n  var OwnElement = getWindow(node).Element;\n  return node instanceof OwnElement || node instanceof Element;\n}\n\nfunction isHTMLElement(node) {\n  var OwnElement = getWindow(node).HTMLElement;\n  return node instanceof OwnElement || node instanceof HTMLElement;\n}\n\nfunction isShadowRoot(node) {\n  // IE 11 has no ShadowRoot\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n\n  var OwnElement = getWindow(node).ShadowRoot;\n  return node instanceof OwnElement || node instanceof ShadowRoot;\n}\n\nexport { isElement, isHTMLElement, isShadowRoot };", "import getNodeName from \"../dom-utils/getNodeName.js\";\nimport { isHTMLElement } from \"../dom-utils/instanceOf.js\"; // This modifier takes the styles prepared by the `computeStyles` modifier\n// and applies them to the HTMLElements such as popper and arrow\n\nfunction applyStyles(_ref) {\n  var state = _ref.state;\n  Object.keys(state.elements).forEach(function (name) {\n    var style = state.styles[name] || {};\n    var attributes = state.attributes[name] || {};\n    var element = state.elements[name]; // arrow is optional + virtual elements\n\n    if (!isHTMLElement(element) || !getNodeName(element)) {\n      return;\n    } // Flow doesn't support to extend this property, but it's the most\n    // effective way to apply styles to an HTMLElement\n    // $FlowFixMe[cannot-write]\n\n\n    Object.assign(element.style, style);\n    Object.keys(attributes).forEach(function (name) {\n      var value = attributes[name];\n\n      if (value === false) {\n        element.removeAttribute(name);\n      } else {\n        element.setAttribute(name, value === true ? '' : value);\n      }\n    });\n  });\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state;\n  var initialStyles = {\n    popper: {\n      position: state.options.strategy,\n      left: '0',\n      top: '0',\n      margin: '0'\n    },\n    arrow: {\n      position: 'absolute'\n    },\n    reference: {}\n  };\n  Object.assign(state.elements.popper.style, initialStyles.popper);\n  state.styles = initialStyles;\n\n  if (state.elements.arrow) {\n    Object.assign(state.elements.arrow.style, initialStyles.arrow);\n  }\n\n  return function () {\n    Object.keys(state.elements).forEach(function (name) {\n      var element = state.elements[name];\n      var attributes = state.attributes[name] || {};\n      var styleProperties = Object.keys(state.styles.hasOwnProperty(name) ? state.styles[name] : initialStyles[name]); // Set all values to an empty string to unset them\n\n      var style = styleProperties.reduce(function (style, property) {\n        style[property] = '';\n        return style;\n      }, {}); // arrow is optional + virtual elements\n\n      if (!isHTMLElement(element) || !getNodeName(element)) {\n        return;\n      }\n\n      Object.assign(element.style, style);\n      Object.keys(attributes).forEach(function (attribute) {\n        element.removeAttribute(attribute);\n      });\n    });\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'applyStyles',\n  enabled: true,\n  phase: 'write',\n  fn: applyStyles,\n  effect: effect,\n  requires: ['computeStyles']\n};", "import { auto } from \"../enums.js\";\nexport default function getBasePlacement(placement) {\n  return placement.split('-')[0];\n}", "export default function getBoundingClientRect(element) {\n  var rect = element.getBoundingClientRect();\n  return {\n    width: rect.width,\n    height: rect.height,\n    top: rect.top,\n    right: rect.right,\n    bottom: rect.bottom,\n    left: rect.left,\n    x: rect.left,\n    y: rect.top\n  };\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\"; // Returns the layout rect of an element relative to its offsetParent. Layout\n// means it doesn't take into account transforms.\n\nexport default function getLayoutRect(element) {\n  var clientRect = getBoundingClientRect(element); // Use the clientRect sizes if it's not been transformed.\n  // Fixes https://github.com/popperjs/popper-core/issues/1223\n\n  var width = element.offsetWidth;\n  var height = element.offsetHeight;\n\n  if (Math.abs(clientRect.width - width) <= 1) {\n    width = clientRect.width;\n  }\n\n  if (Math.abs(clientRect.height - height) <= 1) {\n    height = clientRect.height;\n  }\n\n  return {\n    x: element.offsetLeft,\n    y: element.offsetTop,\n    width: width,\n    height: height\n  };\n}", "import { isShadowRoot } from \"./instanceOf.js\";\nexport default function contains(parent, child) {\n  var rootNode = child.getRootNode && child.getRootNode(); // First, attempt with faster native method\n\n  if (parent.contains(child)) {\n    return true;\n  } // then fallback to custom implementation with Shadow DOM support\n  else if (rootNode && isShadowRoot(rootNode)) {\n      var next = child;\n\n      do {\n        if (next && parent.isSameNode(next)) {\n          return true;\n        } // $FlowFixMe[prop-missing]: need a better way to handle this...\n\n\n        next = next.parentNode || next.host;\n      } while (next);\n    } // Give up, the result is false\n\n\n  return false;\n}", "import getWindow from \"./getWindow.js\";\nexport default function getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}", "import getNodeName from \"./getNodeName.js\";\nexport default function isTableElement(element) {\n  return ['table', 'td', 'th'].indexOf(getNodeName(element)) >= 0;\n}", "import { isElement } from \"./instanceOf.js\";\nexport default function getDocumentElement(element) {\n  // $FlowFixMe[incompatible-return]: assume body is always available\n  return ((isElement(element) ? element.ownerDocument : // $FlowFixMe[prop-missing]\n  element.document) || window.document).documentElement;\n}", "import getNodeName from \"./getNodeName.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport { isShadowRoot } from \"./instanceOf.js\";\nexport default function getParentNode(element) {\n  if (getNodeName(element) === 'html') {\n    return element;\n  }\n\n  return (// this is a quicker (but less type safe) way to save quite some bytes from the bundle\n    // $FlowFixMe[incompatible-return]\n    // $FlowFixMe[prop-missing]\n    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node\n    element.parentNode || ( // DOM Element detected\n    isShadowRoot(element) ? element.host : null) || // ShadowRoot detected\n    // $FlowFixMe[incompatible-call]: HTMLElement is a Node\n    getDocumentElement(element) // fallback\n\n  );\n}", "import getWindow from \"./getWindow.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport isTableElement from \"./isTableElement.js\";\nimport getParentNode from \"./getParentNode.js\";\n\nfunction getTrueOffsetParent(element) {\n  if (!isHTMLElement(element) || // https://github.com/popperjs/popper-core/issues/837\n  getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n\n  return element.offsetParent;\n} // `.offsetParent` reports `null` for fixed elements, while absolute elements\n// return the containing block\n\n\nfunction getContainingBlock(element) {\n  var isFirefox = navigator.userAgent.toLowerCase().indexOf('firefox') !== -1;\n  var currentNode = getParentNode(element);\n\n  while (isHTMLElement(currentNode) && ['html', 'body'].indexOf(getNodeName(currentNode)) < 0) {\n    var css = getComputedStyle(currentNode); // This is non-exhaustive but covers the most common CSS properties that\n    // create a containing block.\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n\n    if (css.transform !== 'none' || css.perspective !== 'none' || css.contain === 'paint' || ['transform', 'perspective'].indexOf(css.willChange) !== -1 || isFirefox && css.willChange === 'filter' || isFirefox && css.filter && css.filter !== 'none') {\n      return currentNode;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return null;\n} // Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\n\n\nexport default function getOffsetParent(element) {\n  var window = getWindow(element);\n  var offsetParent = getTrueOffsetParent(element);\n\n  while (offsetParent && isTableElement(offsetParent) && getComputedStyle(offsetParent).position === 'static') {\n    offsetParent = getTrueOffsetParent(offsetParent);\n  }\n\n  if (offsetParent && (getNodeName(offsetParent) === 'html' || getNodeName(offsetParent) === 'body' && getComputedStyle(offsetParent).position === 'static')) {\n    return window;\n  }\n\n  return offsetParent || getContainingBlock(element) || window;\n}", "export default function getMainAxisFromPlacement(placement) {\n  return ['top', 'bottom'].indexOf(placement) >= 0 ? 'x' : 'y';\n}", "export var max = Math.max;\nexport var min = Math.min;\nexport var round = Math.round;", "import { max as mathMax, min as mathMin } from \"./math.js\";\nexport default function within(min, value, max) {\n  return mathMax(min, mathMin(value, max));\n}", "export default function getFreshSideObject() {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  };\n}", "import getFreshSideObject from \"./getFreshSideObject.js\";\nexport default function mergePaddingObject(paddingObject) {\n  return Object.assign({}, getFreshSideObject(), paddingObject);\n}", "export default function expandToHashMap(value, keys) {\n  return keys.reduce(function (hashMap, key) {\n    hashMap[key] = value;\n    return hashMap;\n  }, {});\n}", "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport contains from \"../dom-utils/contains.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport within from \"../utils/within.js\";\nimport mergePaddingObject from \"../utils/mergePaddingObject.js\";\nimport expandToHashMap from \"../utils/expandToHashMap.js\";\nimport { left, right, basePlacements, top, bottom } from \"../enums.js\";\nimport { isHTMLElement } from \"../dom-utils/instanceOf.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar toPaddingObject = function toPaddingObject(padding, state) {\n  padding = typeof padding === 'function' ? padding(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : padding;\n  return mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n};\n\nfunction arrow(_ref) {\n  var _state$modifiersData$;\n\n  var state = _ref.state,\n      name = _ref.name,\n      options = _ref.options;\n  var arrowElement = state.elements.arrow;\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var basePlacement = getBasePlacement(state.placement);\n  var axis = getMainAxisFromPlacement(basePlacement);\n  var isVertical = [left, right].indexOf(basePlacement) >= 0;\n  var len = isVertical ? 'height' : 'width';\n\n  if (!arrowElement || !popperOffsets) {\n    return;\n  }\n\n  var paddingObject = toPaddingObject(options.padding, state);\n  var arrowRect = getLayoutRect(arrowElement);\n  var minProp = axis === 'y' ? top : left;\n  var maxProp = axis === 'y' ? bottom : right;\n  var endDiff = state.rects.reference[len] + state.rects.reference[axis] - popperOffsets[axis] - state.rects.popper[len];\n  var startDiff = popperOffsets[axis] - state.rects.reference[axis];\n  var arrowOffsetParent = getOffsetParent(arrowElement);\n  var clientSize = arrowOffsetParent ? axis === 'y' ? arrowOffsetParent.clientHeight || 0 : arrowOffsetParent.clientWidth || 0 : 0;\n  var centerToReference = endDiff / 2 - startDiff / 2; // Make sure the arrow doesn't overflow the popper if the center point is\n  // outside of the popper bounds\n\n  var min = paddingObject[minProp];\n  var max = clientSize - arrowRect[len] - paddingObject[maxProp];\n  var center = clientSize / 2 - arrowRect[len] / 2 + centerToReference;\n  var offset = within(min, center, max); // Prevents breaking syntax highlighting...\n\n  var axisProp = axis;\n  state.modifiersData[name] = (_state$modifiersData$ = {}, _state$modifiersData$[axisProp] = offset, _state$modifiersData$.centerOffset = offset - center, _state$modifiersData$);\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options;\n  var _options$element = options.element,\n      arrowElement = _options$element === void 0 ? '[data-popper-arrow]' : _options$element;\n\n  if (arrowElement == null) {\n    return;\n  } // CSS selector\n\n\n  if (typeof arrowElement === 'string') {\n    arrowElement = state.elements.popper.querySelector(arrowElement);\n\n    if (!arrowElement) {\n      return;\n    }\n  }\n\n  if (process.env.NODE_ENV !== \"production\") {\n    if (!isHTMLElement(arrowElement)) {\n      console.error(['Popper: \"arrow\" element must be an HTMLElement (not an SVGElement).', 'To use an SVG arrow, wrap it in an HTMLElement that will be used as', 'the arrow.'].join(' '));\n    }\n  }\n\n  if (!contains(state.elements.popper, arrowElement)) {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(['Popper: \"arrow\" modifier\\'s `element` must be a child of the popper', 'element.'].join(' '));\n    }\n\n    return;\n  }\n\n  state.elements.arrow = arrowElement;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'arrow',\n  enabled: true,\n  phase: 'main',\n  fn: arrow,\n  effect: effect,\n  requires: ['popperOffsets'],\n  requiresIfExists: ['preventOverflow']\n};", "import { top, left, right, bottom } from \"../enums.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getWindow from \"../dom-utils/getWindow.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getComputedStyle from \"../dom-utils/getComputedStyle.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport { round } from \"../utils/math.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar unsetSides = {\n  top: 'auto',\n  right: 'auto',\n  bottom: 'auto',\n  left: 'auto'\n}; // Round the offsets to the nearest suitable subpixel based on the DPR.\n// Zooming can change the DPR, but it seems to report a value that will\n// cleanly divide the values into the appropriate subpixels.\n\nfunction roundOffsetsByDPR(_ref) {\n  var x = _ref.x,\n      y = _ref.y;\n  var win = window;\n  var dpr = win.devicePixelRatio || 1;\n  return {\n    x: round(round(x * dpr) / dpr) || 0,\n    y: round(round(y * dpr) / dpr) || 0\n  };\n}\n\nexport function mapToStyles(_ref2) {\n  var _Object$assign2;\n\n  var popper = _ref2.popper,\n      popperRect = _ref2.popperRect,\n      placement = _ref2.placement,\n      offsets = _ref2.offsets,\n      position = _ref2.position,\n      gpuAcceleration = _ref2.gpuAcceleration,\n      adaptive = _ref2.adaptive,\n      roundOffsets = _ref2.roundOffsets;\n\n  var _ref3 = roundOffsets === true ? roundOffsetsByDPR(offsets) : typeof roundOffsets === 'function' ? roundOffsets(offsets) : offsets,\n      _ref3$x = _ref3.x,\n      x = _ref3$x === void 0 ? 0 : _ref3$x,\n      _ref3$y = _ref3.y,\n      y = _ref3$y === void 0 ? 0 : _ref3$y;\n\n  var hasX = offsets.hasOwnProperty('x');\n  var hasY = offsets.hasOwnProperty('y');\n  var sideX = left;\n  var sideY = top;\n  var win = window;\n\n  if (adaptive) {\n    var offsetParent = getOffsetParent(popper);\n    var heightProp = 'clientHeight';\n    var widthProp = 'clientWidth';\n\n    if (offsetParent === getWindow(popper)) {\n      offsetParent = getDocumentElement(popper);\n\n      if (getComputedStyle(offsetParent).position !== 'static') {\n        heightProp = 'scrollHeight';\n        widthProp = 'scrollWidth';\n      }\n    } // $FlowFixMe[incompatible-cast]: force type refinement, we compare offsetParent with window above, but Flow doesn't detect it\n\n\n    offsetParent = offsetParent;\n\n    if (placement === top) {\n      sideY = bottom; // $FlowFixMe[prop-missing]\n\n      y -= offsetParent[heightProp] - popperRect.height;\n      y *= gpuAcceleration ? 1 : -1;\n    }\n\n    if (placement === left) {\n      sideX = right; // $FlowFixMe[prop-missing]\n\n      x -= offsetParent[widthProp] - popperRect.width;\n      x *= gpuAcceleration ? 1 : -1;\n    }\n  }\n\n  var commonStyles = Object.assign({\n    position: position\n  }, adaptive && unsetSides);\n\n  if (gpuAcceleration) {\n    var _Object$assign;\n\n    return Object.assign({}, commonStyles, (_Object$assign = {}, _Object$assign[sideY] = hasY ? '0' : '', _Object$assign[sideX] = hasX ? '0' : '', _Object$assign.transform = (win.devicePixelRatio || 1) < 2 ? \"translate(\" + x + \"px, \" + y + \"px)\" : \"translate3d(\" + x + \"px, \" + y + \"px, 0)\", _Object$assign));\n  }\n\n  return Object.assign({}, commonStyles, (_Object$assign2 = {}, _Object$assign2[sideY] = hasY ? y + \"px\" : '', _Object$assign2[sideX] = hasX ? x + \"px\" : '', _Object$assign2.transform = '', _Object$assign2));\n}\n\nfunction computeStyles(_ref4) {\n  var state = _ref4.state,\n      options = _ref4.options;\n  var _options$gpuAccelerat = options.gpuAcceleration,\n      gpuAcceleration = _options$gpuAccelerat === void 0 ? true : _options$gpuAccelerat,\n      _options$adaptive = options.adaptive,\n      adaptive = _options$adaptive === void 0 ? true : _options$adaptive,\n      _options$roundOffsets = options.roundOffsets,\n      roundOffsets = _options$roundOffsets === void 0 ? true : _options$roundOffsets;\n\n  if (process.env.NODE_ENV !== \"production\") {\n    var transitionProperty = getComputedStyle(state.elements.popper).transitionProperty || '';\n\n    if (adaptive && ['transform', 'top', 'right', 'bottom', 'left'].some(function (property) {\n      return transitionProperty.indexOf(property) >= 0;\n    })) {\n      console.warn(['Popper: Detected CSS transitions on at least one of the following', 'CSS properties: \"transform\", \"top\", \"right\", \"bottom\", \"left\".', '\\n\\n', 'Disable the \"computeStyles\" modifier\\'s `adaptive` option to allow', 'for smooth transitions, or remove these properties from the CSS', 'transition declaration on the popper element if only transitioning', 'opacity or background-color for example.', '\\n\\n', 'We recommend using the popper element as a wrapper around an inner', 'element that can have any CSS property transitioned for animations.'].join(' '));\n    }\n  }\n\n  var commonStyles = {\n    placement: getBasePlacement(state.placement),\n    popper: state.elements.popper,\n    popperRect: state.rects.popper,\n    gpuAcceleration: gpuAcceleration\n  };\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.styles.popper = Object.assign({}, state.styles.popper, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.popperOffsets,\n      position: state.options.strategy,\n      adaptive: adaptive,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  if (state.modifiersData.arrow != null) {\n    state.styles.arrow = Object.assign({}, state.styles.arrow, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.arrow,\n      position: 'absolute',\n      adaptive: false,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-placement': state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'computeStyles',\n  enabled: true,\n  phase: 'beforeWrite',\n  fn: computeStyles,\n  data: {}\n};", "import getWindow from \"../dom-utils/getWindow.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar passive = {\n  passive: true\n};\n\nfunction effect(_ref) {\n  var state = _ref.state,\n      instance = _ref.instance,\n      options = _ref.options;\n  var _options$scroll = options.scroll,\n      scroll = _options$scroll === void 0 ? true : _options$scroll,\n      _options$resize = options.resize,\n      resize = _options$resize === void 0 ? true : _options$resize;\n  var window = getWindow(state.elements.popper);\n  var scrollParents = [].concat(state.scrollParents.reference, state.scrollParents.popper);\n\n  if (scroll) {\n    scrollParents.forEach(function (scrollParent) {\n      scrollParent.addEventListener('scroll', instance.update, passive);\n    });\n  }\n\n  if (resize) {\n    window.addEventListener('resize', instance.update, passive);\n  }\n\n  return function () {\n    if (scroll) {\n      scrollParents.forEach(function (scrollParent) {\n        scrollParent.removeEventListener('scroll', instance.update, passive);\n      });\n    }\n\n    if (resize) {\n      window.removeEventListener('resize', instance.update, passive);\n    }\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'eventListeners',\n  enabled: true,\n  phase: 'write',\n  fn: function fn() {},\n  effect: effect,\n  data: {}\n};", "var hash = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nexport default function getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, function (matched) {\n    return hash[matched];\n  });\n}", "var hash = {\n  start: 'end',\n  end: 'start'\n};\nexport default function getOppositeVariationPlacement(placement) {\n  return placement.replace(/start|end/g, function (matched) {\n    return hash[matched];\n  });\n}", "import getWindow from \"./getWindow.js\";\nexport default function getWindowScroll(node) {\n  var win = getWindow(node);\n  var scrollLeft = win.pageXOffset;\n  var scrollTop = win.pageYOffset;\n  return {\n    scrollLeft: scrollLeft,\n    scrollTop: scrollTop\n  };\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nexport default function getWindowScrollBarX(element) {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  // Popper 1 is broken in this case and never had a bug report so let's assume\n  // it's not an issue. I don't think anyone ever specifies width on <html>\n  // anyway.\n  // Browsers where the left scrollbar doesn't cause an issue report `0` for\n  // this (e.g. Edge 2019, IE11, Safari)\n  return getBoundingClientRect(getDocumentElement(element)).left + getWindowScroll(element).scrollLeft;\n}", "import getWindow from \"./getWindow.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nexport default function getViewportRect(element) {\n  var win = getWindow(element);\n  var html = getDocumentElement(element);\n  var visualViewport = win.visualViewport;\n  var width = html.clientWidth;\n  var height = html.clientHeight;\n  var x = 0;\n  var y = 0; // NB: This isn't supported on iOS <= 12. If the keyboard is open, the popper\n  // can be obscured underneath it.\n  // Also, `html.clientHeight` adds the bottom bar height in Safari iOS, even\n  // if it isn't open, so if this isn't available, the popper will be detected\n  // to overflow the bottom of the screen too early.\n\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height; // Uses Layout Viewport (like Chrome; Safari does not currently)\n    // In Chrome, it returns a value very close to 0 (+/-) but contains rounding\n    // errors due to floating point numbers, so we need to check precision.\n    // Safari returns a number <= 0, usually < -1 when pinch-zoomed\n    // Feature detection fails in mobile emulation mode in Chrome.\n    // Math.abs(win.innerWidth / visualViewport.scale - visualViewport.width) <\n    // 0.001\n    // Fallback here: \"Not Safari\" userAgent\n\n    if (!/^((?!chrome|android).)*safari/i.test(navigator.userAgent)) {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x + getWindowScrollBarX(element),\n    y: y\n  };\n}", "import getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nimport { max } from \"../utils/math.js\"; // Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable\n\nexport default function getDocumentRect(element) {\n  var _element$ownerDocumen;\n\n  var html = getDocumentElement(element);\n  var winScroll = getWindowScroll(element);\n  var body = (_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body;\n  var width = max(html.scrollWidth, html.clientWidth, body ? body.scrollWidth : 0, body ? body.clientWidth : 0);\n  var height = max(html.scrollHeight, html.clientHeight, body ? body.scrollHeight : 0, body ? body.clientHeight : 0);\n  var x = -winScroll.scrollLeft + getWindowScrollBarX(element);\n  var y = -winScroll.scrollTop;\n\n  if (getComputedStyle(body || html).direction === 'rtl') {\n    x += max(html.clientWidth, body ? body.clientWidth : 0) - width;\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x,\n    y: y\n  };\n}", "import getComputedStyle from \"./getComputedStyle.js\";\nexport default function isScrollParent(element) {\n  // Firefox wants us to check `-x` and `-y` variations as well\n  var _getComputedStyle = getComputedStyle(element),\n      overflow = _getComputedStyle.overflow,\n      overflowX = _getComputedStyle.overflowX,\n      overflowY = _getComputedStyle.overflowY;\n\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);\n}", "import getParentNode from \"./getParentNode.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nexport default function getScrollParent(node) {\n  if (['html', 'body', '#document'].indexOf(getNodeName(node)) >= 0) {\n    // $FlowFixMe[incompatible-return]: assume body is always available\n    return node.ownerDocument.body;\n  }\n\n  if (isHTMLElement(node) && isScrollParent(node)) {\n    return node;\n  }\n\n  return getScrollParent(getParentNode(node));\n}", "import getScrollParent from \"./getScrollParent.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getWindow from \"./getWindow.js\";\nimport isScrollParent from \"./isScrollParent.js\";\n/*\ngiven a DOM element, return the list of all scroll parents, up the list of ancesors\nuntil we get to the top window object. This list is what we attach scroll listeners\nto, because if any of these parent elements scroll, we'll need to re-calculate the\nreference element's position.\n*/\n\nexport default function listScrollParents(element, list) {\n  var _element$ownerDocumen;\n\n  if (list === void 0) {\n    list = [];\n  }\n\n  var scrollParent = getScrollParent(element);\n  var isBody = scrollParent === ((_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body);\n  var win = getWindow(scrollParent);\n  var target = isBody ? [win].concat(win.visualViewport || [], isScrollParent(scrollParent) ? scrollParent : []) : scrollParent;\n  var updatedList = list.concat(target);\n  return isBody ? updatedList : // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here\n  updatedList.concat(listScrollParents(getParentNode(target)));\n}", "export default function rectToClientRect(rect) {\n  return Object.assign({}, rect, {\n    left: rect.x,\n    top: rect.y,\n    right: rect.x + rect.width,\n    bottom: rect.y + rect.height\n  });\n}", "import { viewport } from \"../enums.js\";\nimport getViewportRect from \"./getViewportRect.js\";\nimport getDocumentRect from \"./getDocumentRect.js\";\nimport listScrollParents from \"./listScrollParents.js\";\nimport getOffsetParent from \"./getOffsetParent.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isElement, isHTMLElement } from \"./instanceOf.js\";\nimport getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport contains from \"./contains.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport rectToClientRect from \"../utils/rectToClientRect.js\";\nimport { max, min } from \"../utils/math.js\";\n\nfunction getInnerBoundingClientRect(element) {\n  var rect = getBoundingClientRect(element);\n  rect.top = rect.top + element.clientTop;\n  rect.left = rect.left + element.clientLeft;\n  rect.bottom = rect.top + element.clientHeight;\n  rect.right = rect.left + element.clientWidth;\n  rect.width = element.clientWidth;\n  rect.height = element.clientHeight;\n  rect.x = rect.left;\n  rect.y = rect.top;\n  return rect;\n}\n\nfunction getClientRectFromMixedType(element, clippingParent) {\n  return clippingParent === viewport ? rectToClientRect(getViewportRect(element)) : isHTMLElement(clippingParent) ? getInnerBoundingClientRect(clippingParent) : rectToClientRect(getDocumentRect(getDocumentElement(element)));\n} // A \"clipping parent\" is an overflowable container with the characteristic of\n// clipping (or hiding) overflowing elements with a position different from\n// `initial`\n\n\nfunction getClippingParents(element) {\n  var clippingParents = listScrollParents(getParentNode(element));\n  var canEscapeClipping = ['absolute', 'fixed'].indexOf(getComputedStyle(element).position) >= 0;\n  var clipperElement = canEscapeClipping && isHTMLElement(element) ? getOffsetParent(element) : element;\n\n  if (!isElement(clipperElement)) {\n    return [];\n  } // $FlowFixMe[incompatible-return]: https://github.com/facebook/flow/issues/1414\n\n\n  return clippingParents.filter(function (clippingParent) {\n    return isElement(clippingParent) && contains(clippingParent, clipperElement) && getNodeName(clippingParent) !== 'body';\n  });\n} // Gets the maximum area that the element is visible in due to any number of\n// clipping parents\n\n\nexport default function getClippingRect(element, boundary, rootBoundary) {\n  var mainClippingParents = boundary === 'clippingParents' ? getClippingParents(element) : [].concat(boundary);\n  var clippingParents = [].concat(mainClippingParents, [rootBoundary]);\n  var firstClippingParent = clippingParents[0];\n  var clippingRect = clippingParents.reduce(function (accRect, clippingParent) {\n    var rect = getClientRectFromMixedType(element, clippingParent);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromMixedType(element, firstClippingParent));\n  clippingRect.width = clippingRect.right - clippingRect.left;\n  clippingRect.height = clippingRect.bottom - clippingRect.top;\n  clippingRect.x = clippingRect.left;\n  clippingRect.y = clippingRect.top;\n  return clippingRect;\n}", "export default function getVariation(placement) {\n  return placement.split('-')[1];\n}", "import getBasePlacement from \"./getBasePlacement.js\";\nimport getVariation from \"./getVariation.js\";\nimport getMainAxisFromPlacement from \"./getMainAxisFromPlacement.js\";\nimport { top, right, bottom, left, start, end } from \"../enums.js\";\nexport default function computeOffsets(_ref) {\n  var reference = _ref.reference,\n      element = _ref.element,\n      placement = _ref.placement;\n  var basePlacement = placement ? getBasePlacement(placement) : null;\n  var variation = placement ? getVariation(placement) : null;\n  var commonX = reference.x + reference.width / 2 - element.width / 2;\n  var commonY = reference.y + reference.height / 2 - element.height / 2;\n  var offsets;\n\n  switch (basePlacement) {\n    case top:\n      offsets = {\n        x: commonX,\n        y: reference.y - element.height\n      };\n      break;\n\n    case bottom:\n      offsets = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n\n    case right:\n      offsets = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n\n    case left:\n      offsets = {\n        x: reference.x - element.width,\n        y: commonY\n      };\n      break;\n\n    default:\n      offsets = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n\n  var mainAxis = basePlacement ? getMainAxisFromPlacement(basePlacement) : null;\n\n  if (mainAxis != null) {\n    var len = mainAxis === 'y' ? 'height' : 'width';\n\n    switch (variation) {\n      case start:\n        offsets[mainAxis] = offsets[mainAxis] - (reference[len] / 2 - element[len] / 2);\n        break;\n\n      case end:\n        offsets[mainAxis] = offsets[mainAxis] + (reference[len] / 2 - element[len] / 2);\n        break;\n\n      default:\n    }\n  }\n\n  return offsets;\n}", "import getBoundingClientRect from \"../dom-utils/getBoundingClientRect.js\";\nimport getClippingRect from \"../dom-utils/getClippingRect.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport computeOffsets from \"./computeOffsets.js\";\nimport rectToClientRect from \"./rectToClientRect.js\";\nimport { clippingParents, reference, popper, bottom, top, right, basePlacements, viewport } from \"../enums.js\";\nimport { isElement } from \"../dom-utils/instanceOf.js\";\nimport mergePaddingObject from \"./mergePaddingObject.js\";\nimport expandToHashMap from \"./expandToHashMap.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport default function detectOverflow(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      _options$placement = _options.placement,\n      placement = _options$placement === void 0 ? state.placement : _options$placement,\n      _options$boundary = _options.boundary,\n      boundary = _options$boundary === void 0 ? clippingParents : _options$boundary,\n      _options$rootBoundary = _options.rootBoundary,\n      rootBoundary = _options$rootBoundary === void 0 ? viewport : _options$rootBoundary,\n      _options$elementConte = _options.elementContext,\n      elementContext = _options$elementConte === void 0 ? popper : _options$elementConte,\n      _options$altBoundary = _options.altBoundary,\n      altBoundary = _options$altBoundary === void 0 ? false : _options$altBoundary,\n      _options$padding = _options.padding,\n      padding = _options$padding === void 0 ? 0 : _options$padding;\n  var paddingObject = mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n  var altContext = elementContext === popper ? reference : popper;\n  var referenceElement = state.elements.reference;\n  var popperRect = state.rects.popper;\n  var element = state.elements[altBoundary ? altContext : elementContext];\n  var clippingClientRect = getClippingRect(isElement(element) ? element : element.contextElement || getDocumentElement(state.elements.popper), boundary, rootBoundary);\n  var referenceClientRect = getBoundingClientRect(referenceElement);\n  var popperOffsets = computeOffsets({\n    reference: referenceClientRect,\n    element: popperRect,\n    strategy: 'absolute',\n    placement: placement\n  });\n  var popperClientRect = rectToClientRect(Object.assign({}, popperRect, popperOffsets));\n  var elementClientRect = elementContext === popper ? popperClientRect : referenceClientRect; // positive = overflowing the clipping rect\n  // 0 or negative = within the clipping rect\n\n  var overflowOffsets = {\n    top: clippingClientRect.top - elementClientRect.top + paddingObject.top,\n    bottom: elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom,\n    left: clippingClientRect.left - elementClientRect.left + paddingObject.left,\n    right: elementClientRect.right - clippingClientRect.right + paddingObject.right\n  };\n  var offsetData = state.modifiersData.offset; // Offsets can be applied only to the popper element\n\n  if (elementContext === popper && offsetData) {\n    var offset = offsetData[placement];\n    Object.keys(overflowOffsets).forEach(function (key) {\n      var multiply = [right, bottom].indexOf(key) >= 0 ? 1 : -1;\n      var axis = [top, bottom].indexOf(key) >= 0 ? 'y' : 'x';\n      overflowOffsets[key] += offset[axis] * multiply;\n    });\n  }\n\n  return overflowOffsets;\n}", "import getVariation from \"./getVariation.js\";\nimport { variationPlacements, basePlacements, placements as allPlacements } from \"../enums.js\";\nimport detectOverflow from \"./detectOverflow.js\";\nimport getBasePlacement from \"./getBasePlacement.js\";\nexport default function computeAutoPlacement(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      placement = _options.placement,\n      boundary = _options.boundary,\n      rootBoundary = _options.rootBoundary,\n      padding = _options.padding,\n      flipVariations = _options.flipVariations,\n      _options$allowedAutoP = _options.allowedAutoPlacements,\n      allowedAutoPlacements = _options$allowedAutoP === void 0 ? allPlacements : _options$allowedAutoP;\n  var variation = getVariation(placement);\n  var placements = variation ? flipVariations ? variationPlacements : variationPlacements.filter(function (placement) {\n    return getVariation(placement) === variation;\n  }) : basePlacements;\n  var allowedPlacements = placements.filter(function (placement) {\n    return allowedAutoPlacements.indexOf(placement) >= 0;\n  });\n\n  if (allowedPlacements.length === 0) {\n    allowedPlacements = placements;\n\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(['Popper: The `allowedAutoPlacements` option did not allow any', 'placements. Ensure the `placement` option matches the variation', 'of the allowed placements.', 'For example, \"auto\" cannot be used to allow \"bottom-start\".', 'Use \"auto-start\" instead.'].join(' '));\n    }\n  } // $FlowFixMe[incompatible-type]: Flow seems to have problems with two array unions...\n\n\n  var overflows = allowedPlacements.reduce(function (acc, placement) {\n    acc[placement] = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding\n    })[getBasePlacement(placement)];\n    return acc;\n  }, {});\n  return Object.keys(overflows).sort(function (a, b) {\n    return overflows[a] - overflows[b];\n  });\n}", "import getOppositePlacement from \"../utils/getOppositePlacement.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getOppositeVariationPlacement from \"../utils/getOppositeVariationPlacement.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport computeAutoPlacement from \"../utils/computeAutoPlacement.js\";\nimport { bottom, top, start, right, left, auto } from \"../enums.js\";\nimport getVariation from \"../utils/getVariation.js\"; // eslint-disable-next-line import/no-unused-modules\n\nfunction getExpandedFallbackPlacements(placement) {\n  if (getBasePlacement(placement) === auto) {\n    return [];\n  }\n\n  var oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeVariationPlacement(placement), oppositePlacement, getOppositeVariationPlacement(oppositePlacement)];\n}\n\nfunction flip(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n\n  if (state.modifiersData[name]._skip) {\n    return;\n  }\n\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? true : _options$altAxis,\n      specifiedFallbackPlacements = options.fallbackPlacements,\n      padding = options.padding,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      _options$flipVariatio = options.flipVariations,\n      flipVariations = _options$flipVariatio === void 0 ? true : _options$flipVariatio,\n      allowedAutoPlacements = options.allowedAutoPlacements;\n  var preferredPlacement = state.options.placement;\n  var basePlacement = getBasePlacement(preferredPlacement);\n  var isBasePlacement = basePlacement === preferredPlacement;\n  var fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipVariations ? [getOppositePlacement(preferredPlacement)] : getExpandedFallbackPlacements(preferredPlacement));\n  var placements = [preferredPlacement].concat(fallbackPlacements).reduce(function (acc, placement) {\n    return acc.concat(getBasePlacement(placement) === auto ? computeAutoPlacement(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding,\n      flipVariations: flipVariations,\n      allowedAutoPlacements: allowedAutoPlacements\n    }) : placement);\n  }, []);\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var checksMap = new Map();\n  var makeFallbackChecks = true;\n  var firstFittingPlacement = placements[0];\n\n  for (var i = 0; i < placements.length; i++) {\n    var placement = placements[i];\n\n    var _basePlacement = getBasePlacement(placement);\n\n    var isStartVariation = getVariation(placement) === start;\n    var isVertical = [top, bottom].indexOf(_basePlacement) >= 0;\n    var len = isVertical ? 'width' : 'height';\n    var overflow = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      altBoundary: altBoundary,\n      padding: padding\n    });\n    var mainVariationSide = isVertical ? isStartVariation ? right : left : isStartVariation ? bottom : top;\n\n    if (referenceRect[len] > popperRect[len]) {\n      mainVariationSide = getOppositePlacement(mainVariationSide);\n    }\n\n    var altVariationSide = getOppositePlacement(mainVariationSide);\n    var checks = [];\n\n    if (checkMainAxis) {\n      checks.push(overflow[_basePlacement] <= 0);\n    }\n\n    if (checkAltAxis) {\n      checks.push(overflow[mainVariationSide] <= 0, overflow[altVariationSide] <= 0);\n    }\n\n    if (checks.every(function (check) {\n      return check;\n    })) {\n      firstFittingPlacement = placement;\n      makeFallbackChecks = false;\n      break;\n    }\n\n    checksMap.set(placement, checks);\n  }\n\n  if (makeFallbackChecks) {\n    // `2` may be desired in some cases – research later\n    var numberOfChecks = flipVariations ? 3 : 1;\n\n    var _loop = function _loop(_i) {\n      var fittingPlacement = placements.find(function (placement) {\n        var checks = checksMap.get(placement);\n\n        if (checks) {\n          return checks.slice(0, _i).every(function (check) {\n            return check;\n          });\n        }\n      });\n\n      if (fittingPlacement) {\n        firstFittingPlacement = fittingPlacement;\n        return \"break\";\n      }\n    };\n\n    for (var _i = numberOfChecks; _i > 0; _i--) {\n      var _ret = _loop(_i);\n\n      if (_ret === \"break\") break;\n    }\n  }\n\n  if (state.placement !== firstFittingPlacement) {\n    state.modifiersData[name]._skip = true;\n    state.placement = firstFittingPlacement;\n    state.reset = true;\n  }\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'flip',\n  enabled: true,\n  phase: 'main',\n  fn: flip,\n  requiresIfExists: ['offset'],\n  data: {\n    _skip: false\n  }\n};", "import { top, bottom, left, right } from \"../enums.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\n\nfunction getSideOffsets(overflow, rect, preventedOffsets) {\n  if (preventedOffsets === void 0) {\n    preventedOffsets = {\n      x: 0,\n      y: 0\n    };\n  }\n\n  return {\n    top: overflow.top - rect.height - preventedOffsets.y,\n    right: overflow.right - rect.width + preventedOffsets.x,\n    bottom: overflow.bottom - rect.height + preventedOffsets.y,\n    left: overflow.left - rect.width - preventedOffsets.x\n  };\n}\n\nfunction isAnySideFullyClipped(overflow) {\n  return [top, right, bottom, left].some(function (side) {\n    return overflow[side] >= 0;\n  });\n}\n\nfunction hide(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var preventedOffsets = state.modifiersData.preventOverflow;\n  var referenceOverflow = detectOverflow(state, {\n    elementContext: 'reference'\n  });\n  var popperAltOverflow = detectOverflow(state, {\n    altBoundary: true\n  });\n  var referenceClippingOffsets = getSideOffsets(referenceOverflow, referenceRect);\n  var popperEscapeOffsets = getSideOffsets(popperAltOverflow, popperRect, preventedOffsets);\n  var isReferenceHidden = isAnySideFullyClipped(referenceClippingOffsets);\n  var hasPopperEscaped = isAnySideFullyClipped(popperEscapeOffsets);\n  state.modifiersData[name] = {\n    referenceClippingOffsets: referenceClippingOffsets,\n    popperEscapeOffsets: popperEscapeOffsets,\n    isReferenceHidden: isReferenceHidden,\n    hasPopperEscaped: hasPopperEscaped\n  };\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-reference-hidden': isReferenceHidden,\n    'data-popper-escaped': hasPopperEscaped\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'hide',\n  enabled: true,\n  phase: 'main',\n  requiresIfExists: ['preventOverflow'],\n  fn: hide\n};", "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport { top, left, right, placements } from \"../enums.js\";\nexport function distanceAndSkiddingToXY(placement, rects, offset) {\n  var basePlacement = getBasePlacement(placement);\n  var invertDistance = [left, top].indexOf(basePlacement) >= 0 ? -1 : 1;\n\n  var _ref = typeof offset === 'function' ? offset(Object.assign({}, rects, {\n    placement: placement\n  })) : offset,\n      skidding = _ref[0],\n      distance = _ref[1];\n\n  skidding = skidding || 0;\n  distance = (distance || 0) * invertDistance;\n  return [left, right].indexOf(basePlacement) >= 0 ? {\n    x: distance,\n    y: skidding\n  } : {\n    x: skidding,\n    y: distance\n  };\n}\n\nfunction offset(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options,\n      name = _ref2.name;\n  var _options$offset = options.offset,\n      offset = _options$offset === void 0 ? [0, 0] : _options$offset;\n  var data = placements.reduce(function (acc, placement) {\n    acc[placement] = distanceAndSkiddingToXY(placement, state.rects, offset);\n    return acc;\n  }, {});\n  var _data$state$placement = data[state.placement],\n      x = _data$state$placement.x,\n      y = _data$state$placement.y;\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.modifiersData.popperOffsets.x += x;\n    state.modifiersData.popperOffsets.y += y;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'offset',\n  enabled: true,\n  phase: 'main',\n  requires: ['popperOffsets'],\n  fn: offset\n};", "import computeOffsets from \"../utils/computeOffsets.js\";\n\nfunction popperOffsets(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  // Offsets are the actual position the popper needs to have to be\n  // properly positioned near its reference element\n  // This is the most basic placement, and will be adjusted by\n  // the modifiers in the next step\n  state.modifiersData[name] = computeOffsets({\n    reference: state.rects.reference,\n    element: state.rects.popper,\n    strategy: 'absolute',\n    placement: state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'popperOffsets',\n  enabled: true,\n  phase: 'read',\n  fn: popperOffsets,\n  data: {}\n};", "export default function getAltAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}", "import { top, left, right, bottom, start } from \"../enums.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport getAltAxis from \"../utils/getAltAxis.js\";\nimport within from \"../utils/within.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport getFreshSideObject from \"../utils/getFreshSideObject.js\";\nimport { max as mathMax, min as mathMin } from \"../utils/math.js\";\n\nfunction preventOverflow(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? false : _options$altAxis,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      padding = options.padding,\n      _options$tether = options.tether,\n      tether = _options$tether === void 0 ? true : _options$tether,\n      _options$tetherOffset = options.tetherOffset,\n      tetherOffset = _options$tetherOffset === void 0 ? 0 : _options$tetherOffset;\n  var overflow = detectOverflow(state, {\n    boundary: boundary,\n    rootBoundary: rootBoundary,\n    padding: padding,\n    altBoundary: altBoundary\n  });\n  var basePlacement = getBasePlacement(state.placement);\n  var variation = getVariation(state.placement);\n  var isBasePlacement = !variation;\n  var mainAxis = getMainAxisFromPlacement(basePlacement);\n  var altAxis = getAltAxis(mainAxis);\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var tetherOffsetValue = typeof tetherOffset === 'function' ? tetherOffset(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : tetherOffset;\n  var data = {\n    x: 0,\n    y: 0\n  };\n\n  if (!popperOffsets) {\n    return;\n  }\n\n  if (checkMainAxis || checkAltAxis) {\n    var mainSide = mainAxis === 'y' ? top : left;\n    var altSide = mainAxis === 'y' ? bottom : right;\n    var len = mainAxis === 'y' ? 'height' : 'width';\n    var offset = popperOffsets[mainAxis];\n    var min = popperOffsets[mainAxis] + overflow[mainSide];\n    var max = popperOffsets[mainAxis] - overflow[altSide];\n    var additive = tether ? -popperRect[len] / 2 : 0;\n    var minLen = variation === start ? referenceRect[len] : popperRect[len];\n    var maxLen = variation === start ? -popperRect[len] : -referenceRect[len]; // We need to include the arrow in the calculation so the arrow doesn't go\n    // outside the reference bounds\n\n    var arrowElement = state.elements.arrow;\n    var arrowRect = tether && arrowElement ? getLayoutRect(arrowElement) : {\n      width: 0,\n      height: 0\n    };\n    var arrowPaddingObject = state.modifiersData['arrow#persistent'] ? state.modifiersData['arrow#persistent'].padding : getFreshSideObject();\n    var arrowPaddingMin = arrowPaddingObject[mainSide];\n    var arrowPaddingMax = arrowPaddingObject[altSide]; // If the reference length is smaller than the arrow length, we don't want\n    // to include its full size in the calculation. If the reference is small\n    // and near the edge of a boundary, the popper can overflow even if the\n    // reference is not overflowing as well (e.g. virtual elements with no\n    // width or height)\n\n    var arrowLen = within(0, referenceRect[len], arrowRect[len]);\n    var minOffset = isBasePlacement ? referenceRect[len] / 2 - additive - arrowLen - arrowPaddingMin - tetherOffsetValue : minLen - arrowLen - arrowPaddingMin - tetherOffsetValue;\n    var maxOffset = isBasePlacement ? -referenceRect[len] / 2 + additive + arrowLen + arrowPaddingMax + tetherOffsetValue : maxLen + arrowLen + arrowPaddingMax + tetherOffsetValue;\n    var arrowOffsetParent = state.elements.arrow && getOffsetParent(state.elements.arrow);\n    var clientOffset = arrowOffsetParent ? mainAxis === 'y' ? arrowOffsetParent.clientTop || 0 : arrowOffsetParent.clientLeft || 0 : 0;\n    var offsetModifierValue = state.modifiersData.offset ? state.modifiersData.offset[state.placement][mainAxis] : 0;\n    var tetherMin = popperOffsets[mainAxis] + minOffset - offsetModifierValue - clientOffset;\n    var tetherMax = popperOffsets[mainAxis] + maxOffset - offsetModifierValue;\n\n    if (checkMainAxis) {\n      var preventedOffset = within(tether ? mathMin(min, tetherMin) : min, offset, tether ? mathMax(max, tetherMax) : max);\n      popperOffsets[mainAxis] = preventedOffset;\n      data[mainAxis] = preventedOffset - offset;\n    }\n\n    if (checkAltAxis) {\n      var _mainSide = mainAxis === 'x' ? top : left;\n\n      var _altSide = mainAxis === 'x' ? bottom : right;\n\n      var _offset = popperOffsets[altAxis];\n\n      var _min = _offset + overflow[_mainSide];\n\n      var _max = _offset - overflow[_altSide];\n\n      var _preventedOffset = within(tether ? mathMin(_min, tetherMin) : _min, _offset, tether ? mathMax(_max, tetherMax) : _max);\n\n      popperOffsets[altAxis] = _preventedOffset;\n      data[altAxis] = _preventedOffset - _offset;\n    }\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'preventOverflow',\n  enabled: true,\n  phase: 'main',\n  fn: preventOverflow,\n  requiresIfExists: ['offset']\n};", "export default function getHTMLElementScroll(element) {\n  return {\n    scrollLeft: element.scrollLeft,\n    scrollTop: element.scrollTop\n  };\n}", "import getWindowScroll from \"./getWindowScroll.js\";\nimport getWindow from \"./getWindow.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getHTMLElementScroll from \"./getHTMLElementScroll.js\";\nexport default function getNodeScroll(node) {\n  if (node === getWindow(node) || !isHTMLElement(node)) {\n    return getWindowScroll(node);\n  } else {\n    return getHTMLElementScroll(node);\n  }\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getNodeScroll from \"./getNodeScroll.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport isScrollParent from \"./isScrollParent.js\"; // Returns the composite rect of an element relative to its offsetParent.\n// Composite means it takes into account transforms as well as layout.\n\nexport default function getCompositeRect(elementOrVirtualElement, offsetParent, isFixed) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n\n  var documentElement = getDocumentElement(offsetParent);\n  var rect = getBoundingClientRect(elementOrVirtualElement);\n  var isOffsetParentAnElement = isHTMLElement(offsetParent);\n  var scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  var offsets = {\n    x: 0,\n    y: 0\n  };\n\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || // https://github.com/popperjs/popper-core/issues/1078\n    isScrollParent(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n\n    if (isHTMLElement(offsetParent)) {\n      offsets = getBoundingClientRect(offsetParent);\n      offsets.x += offsetParent.clientLeft;\n      offsets.y += offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n\n  return {\n    x: rect.left + scroll.scrollLeft - offsets.x,\n    y: rect.top + scroll.scrollTop - offsets.y,\n    width: rect.width,\n    height: rect.height\n  };\n}", "import { modifierPhases } from \"../enums.js\"; // source: https://stackoverflow.com/questions/49875255\n\nfunction order(modifiers) {\n  var map = new Map();\n  var visited = new Set();\n  var result = [];\n  modifiers.forEach(function (modifier) {\n    map.set(modifier.name, modifier);\n  }); // On visiting object, check for its dependencies and visit them recursively\n\n  function sort(modifier) {\n    visited.add(modifier.name);\n    var requires = [].concat(modifier.requires || [], modifier.requiresIfExists || []);\n    requires.forEach(function (dep) {\n      if (!visited.has(dep)) {\n        var depModifier = map.get(dep);\n\n        if (depModifier) {\n          sort(depModifier);\n        }\n      }\n    });\n    result.push(modifier);\n  }\n\n  modifiers.forEach(function (modifier) {\n    if (!visited.has(modifier.name)) {\n      // check for visited object\n      sort(modifier);\n    }\n  });\n  return result;\n}\n\nexport default function orderModifiers(modifiers) {\n  // order based on dependencies\n  var orderedModifiers = order(modifiers); // order based on phase\n\n  return modifierPhases.reduce(function (acc, phase) {\n    return acc.concat(orderedModifiers.filter(function (modifier) {\n      return modifier.phase === phase;\n    }));\n  }, []);\n}", "export default function debounce(fn) {\n  var pending;\n  return function () {\n    if (!pending) {\n      pending = new Promise(function (resolve) {\n        Promise.resolve().then(function () {\n          pending = undefined;\n          resolve(fn());\n        });\n      });\n    }\n\n    return pending;\n  };\n}", "export default function mergeByName(modifiers) {\n  var merged = modifiers.reduce(function (merged, current) {\n    var existing = merged[current.name];\n    merged[current.name] = existing ? Object.assign({}, existing, current, {\n      options: Object.assign({}, existing.options, current.options),\n      data: Object.assign({}, existing.data, current.data)\n    }) : current;\n    return merged;\n  }, {}); // IE11 does not support Object.values\n\n  return Object.keys(merged).map(function (key) {\n    return merged[key];\n  });\n}", "import getCompositeRect from \"./dom-utils/getCompositeRect.js\";\nimport getLayoutRect from \"./dom-utils/getLayoutRect.js\";\nimport listScrollParents from \"./dom-utils/listScrollParents.js\";\nimport getOffsetParent from \"./dom-utils/getOffsetParent.js\";\nimport getComputedStyle from \"./dom-utils/getComputedStyle.js\";\nimport orderModifiers from \"./utils/orderModifiers.js\";\nimport debounce from \"./utils/debounce.js\";\nimport validateModifiers from \"./utils/validateModifiers.js\";\nimport uniqueBy from \"./utils/uniqueBy.js\";\nimport getBasePlacement from \"./utils/getBasePlacement.js\";\nimport mergeByName from \"./utils/mergeByName.js\";\nimport detectOverflow from \"./utils/detectOverflow.js\";\nimport { isElement } from \"./dom-utils/instanceOf.js\";\nimport { auto } from \"./enums.js\";\nvar INVALID_ELEMENT_ERROR = 'Popper: Invalid reference or popper argument provided. They must be either a DOM element or virtual element.';\nvar INFINITE_LOOP_ERROR = 'Popper: An infinite loop in the modifiers cycle has been detected! The cycle has been interrupted to prevent a browser crash.';\nvar DEFAULT_OPTIONS = {\n  placement: 'bottom',\n  modifiers: [],\n  strategy: 'absolute'\n};\n\nfunction areValidElements() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  return !args.some(function (element) {\n    return !(element && typeof element.getBoundingClientRect === 'function');\n  });\n}\n\nexport function popperGenerator(generatorOptions) {\n  if (generatorOptions === void 0) {\n    generatorOptions = {};\n  }\n\n  var _generatorOptions = generatorOptions,\n      _generatorOptions$def = _generatorOptions.defaultModifiers,\n      defaultModifiers = _generatorOptions$def === void 0 ? [] : _generatorOptions$def,\n      _generatorOptions$def2 = _generatorOptions.defaultOptions,\n      defaultOptions = _generatorOptions$def2 === void 0 ? DEFAULT_OPTIONS : _generatorOptions$def2;\n  return function createPopper(reference, popper, options) {\n    if (options === void 0) {\n      options = defaultOptions;\n    }\n\n    var state = {\n      placement: 'bottom',\n      orderedModifiers: [],\n      options: Object.assign({}, DEFAULT_OPTIONS, defaultOptions),\n      modifiersData: {},\n      elements: {\n        reference: reference,\n        popper: popper\n      },\n      attributes: {},\n      styles: {}\n    };\n    var effectCleanupFns = [];\n    var isDestroyed = false;\n    var instance = {\n      state: state,\n      setOptions: function setOptions(options) {\n        cleanupModifierEffects();\n        state.options = Object.assign({}, defaultOptions, state.options, options);\n        state.scrollParents = {\n          reference: isElement(reference) ? listScrollParents(reference) : reference.contextElement ? listScrollParents(reference.contextElement) : [],\n          popper: listScrollParents(popper)\n        }; // Orders the modifiers based on their dependencies and `phase`\n        // properties\n\n        var orderedModifiers = orderModifiers(mergeByName([].concat(defaultModifiers, state.options.modifiers))); // Strip out disabled modifiers\n\n        state.orderedModifiers = orderedModifiers.filter(function (m) {\n          return m.enabled;\n        }); // Validate the provided modifiers so that the consumer will get warned\n        // if one of the modifiers is invalid for any reason\n\n        if (process.env.NODE_ENV !== \"production\") {\n          var modifiers = uniqueBy([].concat(orderedModifiers, state.options.modifiers), function (_ref) {\n            var name = _ref.name;\n            return name;\n          });\n          validateModifiers(modifiers);\n\n          if (getBasePlacement(state.options.placement) === auto) {\n            var flipModifier = state.orderedModifiers.find(function (_ref2) {\n              var name = _ref2.name;\n              return name === 'flip';\n            });\n\n            if (!flipModifier) {\n              console.error(['Popper: \"auto\" placements require the \"flip\" modifier be', 'present and enabled to work.'].join(' '));\n            }\n          }\n\n          var _getComputedStyle = getComputedStyle(popper),\n              marginTop = _getComputedStyle.marginTop,\n              marginRight = _getComputedStyle.marginRight,\n              marginBottom = _getComputedStyle.marginBottom,\n              marginLeft = _getComputedStyle.marginLeft; // We no longer take into account `margins` on the popper, and it can\n          // cause bugs with positioning, so we'll warn the consumer\n\n\n          if ([marginTop, marginRight, marginBottom, marginLeft].some(function (margin) {\n            return parseFloat(margin);\n          })) {\n            console.warn(['Popper: CSS \"margin\" styles cannot be used to apply padding', 'between the popper and its reference element or boundary.', 'To replicate margin, use the `offset` modifier, as well as', 'the `padding` option in the `preventOverflow` and `flip`', 'modifiers.'].join(' '));\n          }\n        }\n\n        runModifierEffects();\n        return instance.update();\n      },\n      // Sync update – it will always be executed, even if not necessary. This\n      // is useful for low frequency updates where sync behavior simplifies the\n      // logic.\n      // For high frequency updates (e.g. `resize` and `scroll` events), always\n      // prefer the async Popper#update method\n      forceUpdate: function forceUpdate() {\n        if (isDestroyed) {\n          return;\n        }\n\n        var _state$elements = state.elements,\n            reference = _state$elements.reference,\n            popper = _state$elements.popper; // Don't proceed if `reference` or `popper` are not valid elements\n        // anymore\n\n        if (!areValidElements(reference, popper)) {\n          if (process.env.NODE_ENV !== \"production\") {\n            console.error(INVALID_ELEMENT_ERROR);\n          }\n\n          return;\n        } // Store the reference and popper rects to be read by modifiers\n\n\n        state.rects = {\n          reference: getCompositeRect(reference, getOffsetParent(popper), state.options.strategy === 'fixed'),\n          popper: getLayoutRect(popper)\n        }; // Modifiers have the ability to reset the current update cycle. The\n        // most common use case for this is the `flip` modifier changing the\n        // placement, which then needs to re-run all the modifiers, because the\n        // logic was previously ran for the previous placement and is therefore\n        // stale/incorrect\n\n        state.reset = false;\n        state.placement = state.options.placement; // On each update cycle, the `modifiersData` property for each modifier\n        // is filled with the initial data specified by the modifier. This means\n        // it doesn't persist and is fresh on each update.\n        // To ensure persistent data, use `${name}#persistent`\n\n        state.orderedModifiers.forEach(function (modifier) {\n          return state.modifiersData[modifier.name] = Object.assign({}, modifier.data);\n        });\n        var __debug_loops__ = 0;\n\n        for (var index = 0; index < state.orderedModifiers.length; index++) {\n          if (process.env.NODE_ENV !== \"production\") {\n            __debug_loops__ += 1;\n\n            if (__debug_loops__ > 100) {\n              console.error(INFINITE_LOOP_ERROR);\n              break;\n            }\n          }\n\n          if (state.reset === true) {\n            state.reset = false;\n            index = -1;\n            continue;\n          }\n\n          var _state$orderedModifie = state.orderedModifiers[index],\n              fn = _state$orderedModifie.fn,\n              _state$orderedModifie2 = _state$orderedModifie.options,\n              _options = _state$orderedModifie2 === void 0 ? {} : _state$orderedModifie2,\n              name = _state$orderedModifie.name;\n\n          if (typeof fn === 'function') {\n            state = fn({\n              state: state,\n              options: _options,\n              name: name,\n              instance: instance\n            }) || state;\n          }\n        }\n      },\n      // Async and optimistically optimized update – it will not be executed if\n      // not necessary (debounced to run at most once-per-tick)\n      update: debounce(function () {\n        return new Promise(function (resolve) {\n          instance.forceUpdate();\n          resolve(state);\n        });\n      }),\n      destroy: function destroy() {\n        cleanupModifierEffects();\n        isDestroyed = true;\n      }\n    };\n\n    if (!areValidElements(reference, popper)) {\n      if (process.env.NODE_ENV !== \"production\") {\n        console.error(INVALID_ELEMENT_ERROR);\n      }\n\n      return instance;\n    }\n\n    instance.setOptions(options).then(function (state) {\n      if (!isDestroyed && options.onFirstUpdate) {\n        options.onFirstUpdate(state);\n      }\n    }); // Modifiers have the ability to execute arbitrary code before the first\n    // update cycle runs. They will be executed in the same order as the update\n    // cycle. This is useful when a modifier adds some persistent data that\n    // other modifiers need to use, but the modifier is run after the dependent\n    // one.\n\n    function runModifierEffects() {\n      state.orderedModifiers.forEach(function (_ref3) {\n        var name = _ref3.name,\n            _ref3$options = _ref3.options,\n            options = _ref3$options === void 0 ? {} : _ref3$options,\n            effect = _ref3.effect;\n\n        if (typeof effect === 'function') {\n          var cleanupFn = effect({\n            state: state,\n            name: name,\n            instance: instance,\n            options: options\n          });\n\n          var noopFn = function noopFn() {};\n\n          effectCleanupFns.push(cleanupFn || noopFn);\n        }\n      });\n    }\n\n    function cleanupModifierEffects() {\n      effectCleanupFns.forEach(function (fn) {\n        return fn();\n      });\n      effectCleanupFns = [];\n    }\n\n    return instance;\n  };\n}\nexport var createPopper = /*#__PURE__*/popperGenerator(); // eslint-disable-next-line import/no-unused-modules\n\nexport { detectOverflow };", "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow };", "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nimport offset from \"./modifiers/offset.js\";\nimport flip from \"./modifiers/flip.js\";\nimport preventOverflow from \"./modifiers/preventOverflow.js\";\nimport arrow from \"./modifiers/arrow.js\";\nimport hide from \"./modifiers/hide.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles, offset, flip, preventOverflow, arrow, hide];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow }; // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper as createPopperLite } from \"./popper-lite.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport * from \"./modifiers/index.js\";", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isElement,\n  isVisible,\n  isRTL,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'dropdown'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst SPACE_KEY = 'Space'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst REGEXP_KEYDOWN = new RegExp(`${ARROW_UP_KEY}|${ARROW_DOWN_KEY}|${ESCAPE_KEY}`)\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DISABLED = 'disabled'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPEND = 'dropend'\nconst CLASS_NAME_DROPSTART = 'dropstart'\nconst CLASS_NAME_NAVBAR = 'navbar'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"dropdown\"]'\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = isRTL() ? 'top-end' : 'top-start'\nconst PLACEMENT_TOPEND = isRTL() ? 'top-start' : 'top-end'\nconst PLACEMENT_BOTTOM = isRTL() ? 'bottom-end' : 'bottom-start'\nconst PLACEMENT_BOTTOMEND = isRTL() ? 'bottom-start' : 'bottom-end'\nconst PLACEMENT_RIGHT = isRTL() ? 'left-start' : 'right-start'\nconst PLACEMENT_LEFT = isRTL() ? 'right-start' : 'left-start'\n\nconst Default = {\n  offset: [0, 2],\n  boundary: 'clippingParents',\n  reference: 'toggle',\n  display: 'dynamic',\n  popperConfig: null\n}\n\nconst DefaultType = {\n  offset: '(array|string|function)',\n  boundary: '(string|element)',\n  reference: '(string|element|object)',\n  display: 'string',\n  popperConfig: '(null|object|function)'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Dropdown extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._popper = null\n    this._config = this._getConfig(config)\n    this._menu = this._getMenuElement()\n    this._inNavbar = this._detectNavbar()\n\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const isActive = this._element.classList.contains(CLASS_NAME_SHOW)\n\n    Dropdown.clearMenus()\n\n    if (isActive) {\n      return\n    }\n\n    this.show()\n  }\n\n  show() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED) || this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    // Totally disable Popper for Dropdowns in Navbar\n    if (this._inNavbar) {\n      Manipulator.setDataAttribute(this._menu, 'popper', 'none')\n    } else {\n      if (typeof Popper === 'undefined') {\n        throw new TypeError('Bootstrap\\'s dropdowns require Popper (https://popper.js.org)')\n      }\n\n      let referenceElement = this._element\n\n      if (this._config.reference === 'parent') {\n        referenceElement = parent\n      } else if (isElement(this._config.reference)) {\n        referenceElement = this._config.reference\n\n        // Check if it's jQuery element\n        if (typeof this._config.reference.jquery !== 'undefined') {\n          referenceElement = this._config.reference[0]\n        }\n      } else if (typeof this._config.reference === 'object') {\n        referenceElement = this._config.reference\n      }\n\n      const popperConfig = this._getPopperConfig()\n      const isDisplayStatic = popperConfig.modifiers.find(modifier => modifier.name === 'applyStyles' && modifier.enabled === false)\n\n      this._popper = Popper.createPopper(referenceElement, this._menu, popperConfig)\n\n      if (isDisplayStatic) {\n        Manipulator.setDataAttribute(this._menu, 'popper', 'static')\n      }\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement &&\n      !parent.closest(SELECTOR_NAVBAR_NAV)) {\n      [].concat(...document.body.children)\n        .forEach(elem => EventHandler.on(elem, 'mouseover', null, noop()))\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    this._menu.classList.toggle(CLASS_NAME_SHOW)\n    this._element.classList.toggle(CLASS_NAME_SHOW)\n    EventHandler.trigger(this._element, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED) || !this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE, relatedTarget)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._menu.classList.toggle(CLASS_NAME_SHOW)\n    this._element.classList.toggle(CLASS_NAME_SHOW)\n    Manipulator.removeDataAttribute(this._menu, 'popper')\n    EventHandler.trigger(this._element, EVENT_HIDDEN, relatedTarget)\n  }\n\n  dispose() {\n    EventHandler.off(this._element, EVENT_KEY)\n    this._menu = null\n\n    if (this._popper) {\n      this._popper.destroy()\n      this._popper = null\n    }\n\n    super.dispose()\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Private\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_CLICK, event => {\n      event.preventDefault()\n      this.toggle()\n    })\n  }\n\n  _getConfig(config) {\n    config = {\n      ...this.constructor.Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...config\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    if (typeof config.reference === 'object' && !isElement(config.reference) &&\n      typeof config.reference.getBoundingClientRect !== 'function'\n    ) {\n      // Popper virtual elements require a getBoundingClientRect method\n      throw new TypeError(`${NAME.toUpperCase()}: Option \"reference\" provided type \"object\" without a required \"getBoundingClientRect\" method.`)\n    }\n\n    return config\n  }\n\n  _getMenuElement() {\n    return SelectorEngine.next(this._element, SELECTOR_MENU)[0]\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._element.parentNode\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPEND)) {\n      return PLACEMENT_RIGHT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPSTART)) {\n      return PLACEMENT_LEFT\n    }\n\n    // We need to trim the value because custom properties can also include spaces\n    const isEnd = getComputedStyle(this._menu).getPropertyValue('--bs-position').trim() === 'end'\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      return isEnd ? PLACEMENT_TOPEND : PLACEMENT_TOP\n    }\n\n    return isEnd ? PLACEMENT_BOTTOMEND : PLACEMENT_BOTTOM\n  }\n\n  _detectNavbar() {\n    return this._element.closest(`.${CLASS_NAME_NAVBAR}`) !== null\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(val => Number.parseInt(val, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const defaultBsPopperConfig = {\n      placement: this._getPlacement(),\n      modifiers: [{\n        name: 'preventOverflow',\n        options: {\n          boundary: this._config.boundary\n        }\n      },\n      {\n        name: 'offset',\n        options: {\n          offset: this._getOffset()\n        }\n      }]\n    }\n\n    // Disable Popper if we have a static display\n    if (this._config.display === 'static') {\n      defaultBsPopperConfig.modifiers = [{\n        name: 'applyStyles',\n        enabled: false\n      }]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...(typeof this._config.popperConfig === 'function' ? this._config.popperConfig(defaultBsPopperConfig) : this._config.popperConfig)\n    }\n  }\n\n  // Static\n\n  static dropdownInterface(element, config) {\n    let data = Data.get(element, DATA_KEY)\n    const _config = typeof config === 'object' ? config : null\n\n    if (!data) {\n      data = new Dropdown(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Dropdown.dropdownInterface(this, config)\n    })\n  }\n\n  static clearMenus(event) {\n    if (event) {\n      if (event.button === RIGHT_MOUSE_BUTTON || (event.type === 'keyup' && event.key !== TAB_KEY)) {\n        return\n      }\n\n      if (/input|select|textarea|form/i.test(event.target.tagName)) {\n        return\n      }\n    }\n\n    const toggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggles.length; i < len; i++) {\n      const context = Data.get(toggles[i], DATA_KEY)\n      const relatedTarget = {\n        relatedTarget: toggles[i]\n      }\n\n      if (event && event.type === 'click') {\n        relatedTarget.clickEvent = event\n      }\n\n      if (!context) {\n        continue\n      }\n\n      const dropdownMenu = context._menu\n      if (!toggles[i].classList.contains(CLASS_NAME_SHOW)) {\n        continue\n      }\n\n      if (event) {\n        // Don't close the menu if the clicked element or one of its parents is the dropdown button\n        if ([context._element].some(element => event.composedPath().includes(element))) {\n          continue\n        }\n\n        // Tab navigation through the dropdown menu shouldn't close the menu\n        if (event.type === 'keyup' && event.key === TAB_KEY && dropdownMenu.contains(event.target)) {\n          continue\n        }\n      }\n\n      const hideEvent = EventHandler.trigger(toggles[i], EVENT_HIDE, relatedTarget)\n      if (hideEvent.defaultPrevented) {\n        continue\n      }\n\n      // If this is a touch-enabled device we remove the extra\n      // empty mouseover listeners we added for iOS support\n      if ('ontouchstart' in document.documentElement) {\n        [].concat(...document.body.children)\n          .forEach(elem => EventHandler.off(elem, 'mouseover', null, noop()))\n      }\n\n      toggles[i].setAttribute('aria-expanded', 'false')\n\n      if (context._popper) {\n        context._popper.destroy()\n      }\n\n      dropdownMenu.classList.remove(CLASS_NAME_SHOW)\n      toggles[i].classList.remove(CLASS_NAME_SHOW)\n      Manipulator.removeDataAttribute(dropdownMenu, 'popper')\n      EventHandler.trigger(toggles[i], EVENT_HIDDEN, relatedTarget)\n    }\n  }\n\n  static getParentFromElement(element) {\n    return getElementFromSelector(element) || element.parentNode\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not input/textarea:\n    //  - And not a key in REGEXP_KEYDOWN => not a dropdown command\n    // If input/textarea:\n    //  - If space key => not a dropdown command\n    //  - If key is other than escape\n    //    - If key is not up or down => not a dropdown command\n    //    - If trigger inside the menu => not a dropdown command\n    if (/input|textarea/i.test(event.target.tagName) ?\n      event.key === SPACE_KEY || (event.key !== ESCAPE_KEY &&\n      ((event.key !== ARROW_DOWN_KEY && event.key !== ARROW_UP_KEY) ||\n        event.target.closest(SELECTOR_MENU))) :\n      !REGEXP_KEYDOWN.test(event.key)) {\n      return\n    }\n\n    event.preventDefault()\n    event.stopPropagation()\n\n    if (this.disabled || this.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this)\n    const isActive = this.classList.contains(CLASS_NAME_SHOW)\n\n    if (event.key === ESCAPE_KEY) {\n      const button = this.matches(SELECTOR_DATA_TOGGLE) ? this : SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0]\n      button.focus()\n      Dropdown.clearMenus()\n      return\n    }\n\n    if (!isActive && (event.key === ARROW_UP_KEY || event.key === ARROW_DOWN_KEY)) {\n      const button = this.matches(SELECTOR_DATA_TOGGLE) ? this : SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0]\n      button.click()\n      return\n    }\n\n    if (!isActive || event.key === SPACE_KEY) {\n      Dropdown.clearMenus()\n      return\n    }\n\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, parent).filter(isVisible)\n\n    if (!items.length) {\n      return\n    }\n\n    let index = items.indexOf(event.target)\n\n    // Up\n    if (event.key === ARROW_UP_KEY && index > 0) {\n      index--\n    }\n\n    // Down\n    if (event.key === ARROW_DOWN_KEY && index < items.length - 1) {\n      index++\n    }\n\n    // index is -1 if the first keydown is an ArrowUp\n    index = index === -1 ? 0 : index\n\n    items[index].focus()\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  Dropdown.dropdownInterface(this)\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Dropdown to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Dropdown)\n\nexport default Dropdown\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isVisible,\n  isRTL,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'modal'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  focus: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  focus: 'boolean'\n}\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEUP_DISMISS = `mouseup.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SCROLLBAR_MEASURER = 'modal-scrollbar-measure'\nconst CLASS_NAME_BACKDROP = 'modal-backdrop'\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"modal\"]'\nconst SELECTOR_DATA_DISMISS = '[data-bs-dismiss=\"modal\"]'\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Modal extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, this._element)\n    this._backdrop = null\n    this._isShown = false\n    this._isBodyOverflowing = false\n    this._ignoreBackdropClick = false\n    this._isTransitioning = false\n    this._scrollbarWidth = 0\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    if (this._isAnimated()) {\n      this._isTransitioning = true\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (this._isShown || showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n\n    this._checkScrollbar()\n    this._setScrollbar()\n\n    this._adjustDialog()\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, event => this.hide(event))\n\n    EventHandler.on(this._dialog, EVENT_MOUSEDOWN_DISMISS, () => {\n      EventHandler.one(this._element, EVENT_MOUSEUP_DISMISS, event => {\n        if (event.target === this._element) {\n          this._ignoreBackdropClick = true\n        }\n      })\n    })\n\n    this._showBackdrop(() => this._showElement(relatedTarget))\n  }\n\n  hide(event) {\n    if (event) {\n      event.preventDefault()\n    }\n\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    const isAnimated = this._isAnimated()\n\n    if (isAnimated) {\n      this._isTransitioning = true\n    }\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n    EventHandler.off(this._dialog, EVENT_MOUSEDOWN_DISMISS)\n\n    if (isAnimated) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, 'transitionend', event => this._hideModal(event))\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      this._hideModal()\n    }\n  }\n\n  dispose() {\n    [window, this._element, this._dialog]\n      .forEach(htmlElement => EventHandler.off(htmlElement, EVENT_KEY))\n\n    super.dispose()\n\n    /**\n     * `document` has 2 events `EVENT_FOCUSIN` and `EVENT_CLICK_DATA_API`\n     * Do not move `document` in `htmlElements` array\n     * It will remove `EVENT_CLICK_DATA_API` event that should remain\n     */\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    this._config = null\n    this._dialog = null\n    this._backdrop = null\n    this._isShown = null\n    this._isBodyOverflowing = null\n    this._ignoreBackdropClick = null\n    this._isTransitioning = null\n    this._scrollbarWidth = null\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _showElement(relatedTarget) {\n    const isAnimated = this._isAnimated()\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n\n    if (!this._element.parentNode || this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {\n      // Don't move modal's DOM position\n      document.body.appendChild(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    if (isAnimated) {\n      reflow(this._element)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    if (this._config.focus) {\n      this._enforceFocus()\n    }\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._element.focus()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    if (isAnimated) {\n      const transitionDuration = getTransitionDurationFromElement(this._dialog)\n\n      EventHandler.one(this._dialog, 'transitionend', transitionComplete)\n      emulateTransitionEnd(this._dialog, transitionDuration)\n    } else {\n      transitionComplete()\n    }\n  }\n\n  _enforceFocus() {\n    EventHandler.off(document, EVENT_FOCUSIN) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => {\n      if (document !== event.target &&\n          this._element !== event.target &&\n          !this._element.contains(event.target)) {\n        this._element.focus()\n      }\n    })\n  }\n\n  _setEscapeEvent() {\n    if (this._isShown) {\n      EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n        if (this._config.keyboard && event.key === ESCAPE_KEY) {\n          event.preventDefault()\n          this.hide()\n        } else if (!this._config.keyboard && event.key === ESCAPE_KEY) {\n          this._triggerBackdropTransition()\n        }\n      })\n    } else {\n      EventHandler.off(this._element, EVENT_KEYDOWN_DISMISS)\n    }\n  }\n\n  _setResizeEvent() {\n    if (this._isShown) {\n      EventHandler.on(window, EVENT_RESIZE, () => this._adjustDialog())\n    } else {\n      EventHandler.off(window, EVENT_RESIZE)\n    }\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n    this._showBackdrop(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._resetScrollbar()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _removeBackdrop() {\n    this._backdrop.parentNode.removeChild(this._backdrop)\n    this._backdrop = null\n  }\n\n  _showBackdrop(callback) {\n    const isAnimated = this._isAnimated()\n    if (this._isShown && this._config.backdrop) {\n      this._backdrop = document.createElement('div')\n      this._backdrop.className = CLASS_NAME_BACKDROP\n\n      if (isAnimated) {\n        this._backdrop.classList.add(CLASS_NAME_FADE)\n      }\n\n      document.body.appendChild(this._backdrop)\n\n      EventHandler.on(this._element, EVENT_CLICK_DISMISS, event => {\n        if (this._ignoreBackdropClick) {\n          this._ignoreBackdropClick = false\n          return\n        }\n\n        if (event.target !== event.currentTarget) {\n          return\n        }\n\n        if (this._config.backdrop === 'static') {\n          this._triggerBackdropTransition()\n        } else {\n          this.hide()\n        }\n      })\n\n      if (isAnimated) {\n        reflow(this._backdrop)\n      }\n\n      this._backdrop.classList.add(CLASS_NAME_SHOW)\n\n      if (!isAnimated) {\n        callback()\n        return\n      }\n\n      const backdropTransitionDuration = getTransitionDurationFromElement(this._backdrop)\n\n      EventHandler.one(this._backdrop, 'transitionend', callback)\n      emulateTransitionEnd(this._backdrop, backdropTransitionDuration)\n    } else if (!this._isShown && this._backdrop) {\n      this._backdrop.classList.remove(CLASS_NAME_SHOW)\n\n      const callbackRemove = () => {\n        this._removeBackdrop()\n        callback()\n      }\n\n      if (isAnimated) {\n        const backdropTransitionDuration = getTransitionDurationFromElement(this._backdrop)\n        EventHandler.one(this._backdrop, 'transitionend', callbackRemove)\n        emulateTransitionEnd(this._backdrop, backdropTransitionDuration)\n      } else {\n        callbackRemove()\n      }\n    } else {\n      callback()\n    }\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_FADE)\n  }\n\n  _triggerBackdropTransition() {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n\n    if (!isModalOverflowing) {\n      this._element.style.overflowY = 'hidden'\n    }\n\n    this._element.classList.add(CLASS_NAME_STATIC)\n    const modalTransitionDuration = getTransitionDurationFromElement(this._dialog)\n    EventHandler.off(this._element, 'transitionend')\n    EventHandler.one(this._element, 'transitionend', () => {\n      this._element.classList.remove(CLASS_NAME_STATIC)\n      if (!isModalOverflowing) {\n        EventHandler.one(this._element, 'transitionend', () => {\n          this._element.style.overflowY = ''\n        })\n        emulateTransitionEnd(this._element, modalTransitionDuration)\n      }\n    })\n    emulateTransitionEnd(this._element, modalTransitionDuration)\n    this._element.focus()\n  }\n\n  // ----------------------------------------------------------------------\n  // the following methods are used to handle overflowing modals\n  // ----------------------------------------------------------------------\n\n  _adjustDialog() {\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n\n    if ((!this._isBodyOverflowing && isModalOverflowing && !isRTL()) || (this._isBodyOverflowing && !isModalOverflowing && isRTL())) {\n      this._element.style.paddingLeft = `${this._scrollbarWidth}px`\n    }\n\n    if ((this._isBodyOverflowing && !isModalOverflowing && !isRTL()) || (!this._isBodyOverflowing && isModalOverflowing && isRTL())) {\n      this._element.style.paddingRight = `${this._scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  _checkScrollbar() {\n    const rect = document.body.getBoundingClientRect()\n    this._isBodyOverflowing = Math.round(rect.left + rect.right) < window.innerWidth\n    this._scrollbarWidth = this._getScrollbarWidth()\n  }\n\n  _setScrollbar() {\n    if (this._isBodyOverflowing) {\n      this._setElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight', calculatedValue => calculatedValue + this._scrollbarWidth)\n      this._setElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight', calculatedValue => calculatedValue - this._scrollbarWidth)\n      this._setElementAttributes('body', 'paddingRight', calculatedValue => calculatedValue + this._scrollbarWidth)\n    }\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n  }\n\n  _setElementAttributes(selector, styleProp, callback) {\n    SelectorEngine.find(selector)\n      .forEach(element => {\n        if (element !== document.body && window.innerWidth > element.clientWidth + this._scrollbarWidth) {\n          return\n        }\n\n        const actualValue = element.style[styleProp]\n        const calculatedValue = window.getComputedStyle(element)[styleProp]\n        Manipulator.setDataAttribute(element, styleProp, actualValue)\n        element.style[styleProp] = callback(Number.parseFloat(calculatedValue)) + 'px'\n      })\n  }\n\n  _resetScrollbar() {\n    this._resetElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight')\n    this._resetElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight')\n    this._resetElementAttributes('body', 'paddingRight')\n  }\n\n  _resetElementAttributes(selector, styleProp) {\n    SelectorEngine.find(selector).forEach(element => {\n      const value = Manipulator.getDataAttribute(element, styleProp)\n      if (typeof value === 'undefined' && element === document.body) {\n        element.style[styleProp] = ''\n      } else {\n        Manipulator.removeDataAttribute(element, styleProp)\n        element.style[styleProp] = value\n      }\n    })\n  }\n\n  _getScrollbarWidth() { // thx d.walsh\n    const scrollDiv = document.createElement('div')\n    scrollDiv.className = CLASS_NAME_SCROLLBAR_MEASURER\n    document.body.appendChild(scrollDiv)\n    const scrollbarWidth = scrollDiv.getBoundingClientRect().width - scrollDiv.clientWidth\n    document.body.removeChild(scrollDiv)\n    return scrollbarWidth\n  }\n\n  // Static\n\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n      const _config = {\n        ...Default,\n        ...Manipulator.getDataAttributes(this),\n        ...(typeof config === 'object' && config ? config : {})\n      }\n\n      if (!data) {\n        data = new Modal(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](relatedTarget)\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (this.tagName === 'A' || this.tagName === 'AREA') {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  let data = Data.get(target, DATA_KEY)\n  if (!data) {\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n\n    data = new Modal(target, config)\n  }\n\n  data.toggle(this)\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Modal to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Modal)\n\nexport default Modal\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): util/scrollBar.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport SelectorEngine from '../dom/selector-engine'\nimport Manipulator from '../dom/manipulator'\n\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\n\nconst getWidth = () => {\n  // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n  const documentWidth = document.documentElement.clientWidth\n  return Math.abs(window.innerWidth - documentWidth)\n}\n\nconst hide = (width = getWidth()) => {\n  document.body.style.overflow = 'hidden'\n  _setElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight', calculatedValue => calculatedValue + width)\n  _setElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight', calculatedValue => calculatedValue - width)\n  _setElementAttributes('body', 'paddingRight', calculatedValue => calculatedValue + width)\n}\n\nconst _setElementAttributes = (selector, styleProp, callback) => {\n  const scrollbarWidth = getWidth()\n  SelectorEngine.find(selector)\n    .forEach(element => {\n      if (element !== document.body && window.innerWidth > element.clientWidth + scrollbarWidth) {\n        return\n      }\n\n      const actualValue = element.style[styleProp]\n      const calculatedValue = window.getComputedStyle(element)[styleProp]\n      Manipulator.setDataAttribute(element, styleProp, actualValue)\n      element.style[styleProp] = callback(Number.parseFloat(calculatedValue)) + 'px'\n    })\n}\n\nconst reset = () => {\n  document.body.style.overflow = 'auto'\n  _resetElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight')\n  _resetElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight')\n  _resetElementAttributes('body', 'paddingRight')\n}\n\nconst _resetElementAttributes = (selector, styleProp) => {\n  SelectorEngine.find(selector).forEach(element => {\n    const value = Manipulator.getDataAttribute(element, styleProp)\n    if (typeof value === 'undefined' && element === document.body) {\n      element.style.removeProperty(styleProp)\n    } else {\n      Manipulator.removeDataAttribute(element, styleProp)\n      element.style[styleProp] = value\n    }\n  })\n}\n\nconst isBodyOverflowing = () => {\n  return getWidth() > 0\n}\n\nexport {\n  getWidth,\n  hide,\n  isBodyOverflowing,\n  reset\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): offcanvas.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  getSelectorFromElement,\n  getTransitionDurationFromElement,\n  isDisabled,\n  isVisible,\n  typeCheckConfig\n} from './util/index'\nimport { hide as scrollBarHide, reset as scrollBarReset } from './util/scrollbar'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\nimport SelectorEngine from './dom/selector-engine'\nimport Manipulator from './dom/manipulator'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'offcanvas'\nconst DATA_KEY = 'bs.offcanvas'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  scroll: false\n}\n\nconst DefaultType = {\n  backdrop: 'boolean',\n  keyboard: 'boolean',\n  scroll: 'boolean'\n}\n\nconst CLASS_NAME_BACKDROP_BODY = 'offcanvas-backdrop'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_TOGGLING = 'offcanvas-toggling'\nconst OPEN_SELECTOR = '.offcanvas.show'\nconst ACTIVE_SELECTOR = `${OPEN_SELECTOR}, .${CLASS_NAME_TOGGLING}`\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\n\nconst SELECTOR_DATA_DISMISS = '[data-bs-dismiss=\"offcanvas\"]'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"offcanvas\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Offcanvas extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._isShown = false\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, { relatedTarget })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._element.style.visibility = 'visible'\n\n    if (this._config.backdrop) {\n      document.body.classList.add(CLASS_NAME_BACKDROP_BODY)\n    }\n\n    if (!this._config.scroll) {\n      scrollBarHide()\n    }\n\n    this._element.classList.add(CLASS_NAME_TOGGLING)\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    const completeCallBack = () => {\n      this._element.classList.remove(CLASS_NAME_TOGGLING)\n      EventHandler.trigger(this._element, EVENT_SHOWN, { relatedTarget })\n      this._enforceFocusOnElement(this._element)\n    }\n\n    setTimeout(completeCallBack, getTransitionDurationFromElement(this._element))\n  }\n\n  hide() {\n    if (!this._isShown) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._element.classList.add(CLASS_NAME_TOGGLING)\n    EventHandler.off(document, EVENT_FOCUSIN)\n    this._element.blur()\n    this._isShown = false\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    const completeCallback = () => {\n      this._element.setAttribute('aria-hidden', true)\n      this._element.removeAttribute('aria-modal')\n      this._element.removeAttribute('role')\n      this._element.style.visibility = 'hidden'\n\n      if (this._config.backdrop) {\n        document.body.classList.remove(CLASS_NAME_BACKDROP_BODY)\n      }\n\n      if (!this._config.scroll) {\n        scrollBarReset()\n      }\n\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n      this._element.classList.remove(CLASS_NAME_TOGGLING)\n    }\n\n    setTimeout(completeCallback, getTransitionDurationFromElement(this._element))\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' ? config : {})\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _enforceFocusOnElement(element) {\n    EventHandler.off(document, EVENT_FOCUSIN) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => {\n      if (document !== event.target &&\n        element !== event.target &&\n        !element.contains(event.target)) {\n        element.focus()\n      }\n    })\n    element.focus()\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, () => this.hide())\n\n    EventHandler.on(document, 'keydown', event => {\n      if (this._config.keyboard && event.key === ESCAPE_KEY) {\n        this.hide()\n      }\n    })\n\n    EventHandler.on(document, EVENT_CLICK_DATA_API, event => {\n      const target = SelectorEngine.findOne(getSelectorFromElement(event.target))\n      if (!this._element.contains(event.target) && target !== this._element) {\n        this.hide()\n      }\n    })\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Data.get(this, DATA_KEY) || new Offcanvas(this, typeof config === 'object' ? config : {})\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  EventHandler.one(target, EVENT_HIDDEN, () => {\n    // focus on trigger when it is closed\n    if (isVisible(this)) {\n      this.focus()\n    }\n  })\n\n  // avoid conflict when clicking a toggler of an offcanvas, while another is open\n  const allReadyOpen = SelectorEngine.findOne(ACTIVE_SELECTOR)\n  if (allReadyOpen && allReadyOpen !== target) {\n    return\n  }\n\n  const data = Data.get(target, DATA_KEY) || new Offcanvas(target)\n\n  data.toggle(this)\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  SelectorEngine.find(OPEN_SELECTOR).forEach(el => (Data.get(el, DATA_KEY) || new Offcanvas(el)).show())\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\ndefineJQueryPlugin(NAME, Offcanvas)\n\nexport default Offcanvas\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttrs = new Set([\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n])\n\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/i\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[\\d+/a-z]+=*$/i\n\nconst allowedAttribute = (attr, allowedAttributeList) => {\n  const attrName = attr.nodeName.toLowerCase()\n\n  if (allowedAttributeList.includes(attrName)) {\n    if (uriAttrs.has(attrName)) {\n      return Boolean(SAFE_URL_PATTERN.test(attr.nodeValue) || DATA_URL_PATTERN.test(attr.nodeValue))\n    }\n\n    return true\n  }\n\n  const regExp = allowedAttributeList.filter(attrRegex => attrRegex instanceof RegExp)\n\n  // Check if a regular expression validates the attribute.\n  for (let i = 0, len = regExp.length; i < len; i++) {\n    if (regExp[i].test(attrName)) {\n      return true\n    }\n  }\n\n  return false\n}\n\nexport const DefaultAllowlist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n\nexport function sanitizeHtml(unsafeHtml, allowList, sanitizeFn) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFn && typeof sanitizeFn === 'function') {\n    return sanitizeFn(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const allowlistKeys = Object.keys(allowList)\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (let i = 0, len = elements.length; i < len; i++) {\n    const el = elements[i]\n    const elName = el.nodeName.toLowerCase()\n\n    if (!allowlistKeys.includes(elName)) {\n      el.parentNode.removeChild(el)\n\n      continue\n    }\n\n    const attributeList = [].concat(...el.attributes)\n    const allowedAttributes = [].concat(allowList['*'] || [], allowList[elName] || [])\n\n    attributeList.forEach(attr => {\n      if (!allowedAttribute(attr, allowedAttributes)) {\n        el.removeAttribute(attr.nodeName)\n      }\n    })\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\n\nimport {\n  defineJQueryPlugin,\n  emulateTransitionEnd,\n  findShadowRoot,\n  getTransitionDurationFromElement,\n  getUID,\n  isElement,\n  isRTL,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport {\n  DefaultAllowlist,\n  sanitizeHtml\n} from './util/sanitizer'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tooltip'\nconst DATA_KEY = 'bs.tooltip'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-tooltip'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\nconst DISALLOWED_ATTRIBUTES = new Set(['sanitize', 'allowList', 'sanitizeFn'])\n\nconst DefaultType = {\n  animation: 'boolean',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string',\n  delay: '(number|object)',\n  html: 'boolean',\n  selector: '(string|boolean)',\n  placement: '(string|function)',\n  offset: '(array|string|function)',\n  container: '(string|element|boolean)',\n  fallbackPlacements: 'array',\n  boundary: '(string|element)',\n  customClass: '(string|function)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  allowList: 'object',\n  popperConfig: '(null|object|function)'\n}\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: isRTL() ? 'left' : 'right',\n  BOTTOM: 'bottom',\n  LEFT: isRTL() ? 'right' : 'left'\n}\n\nconst Default = {\n  animation: true,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n              '<div class=\"tooltip-arrow\"></div>' +\n              '<div class=\"tooltip-inner\"></div>' +\n            '</div>',\n  trigger: 'hover focus',\n  title: '',\n  delay: 0,\n  html: false,\n  selector: false,\n  placement: 'top',\n  offset: [0, 0],\n  container: false,\n  fallbackPlacements: ['top', 'right', 'bottom', 'left'],\n  boundary: 'clippingParents',\n  customClass: '',\n  sanitize: true,\n  sanitizeFn: null,\n  allowList: DefaultAllowlist,\n  popperConfig: null\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst HOVER_STATE_SHOW = 'show'\nconst HOVER_STATE_OUT = 'out'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tooltip extends BaseComponent {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper (https://popper.js.org)')\n    }\n\n    super(element)\n\n    // private\n    this._isEnabled = true\n    this._timeout = 0\n    this._hoverState = ''\n    this._activeTrigger = {}\n    this._popper = null\n\n    // Protected\n    this.config = this._getConfig(config)\n    this.tip = null\n\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle(event) {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (event) {\n      const context = this._initializeOnDelegatedTarget(event)\n\n      context._activeTrigger.click = !context._activeTrigger.click\n\n      if (context._isWithActiveTrigger()) {\n        context._enter(null, context)\n      } else {\n        context._leave(null, context)\n      }\n    } else {\n      if (this.getTipElement().classList.contains(CLASS_NAME_SHOW)) {\n        this._leave(null, this)\n        return\n      }\n\n      this._enter(null, this)\n    }\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    EventHandler.off(this._element, this.constructor.EVENT_KEY)\n    EventHandler.off(this._element.closest(`.${CLASS_NAME_MODAL}`), 'hide.bs.modal', this._hideModalHandler)\n\n    if (this.tip && this.tip.parentNode) {\n      this.tip.parentNode.removeChild(this.tip)\n    }\n\n    this._isEnabled = null\n    this._timeout = null\n    this._hoverState = null\n    this._activeTrigger = null\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._popper = null\n    this.config = null\n    this.tip = null\n    super.dispose()\n  }\n\n  show() {\n    if (this._element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (!(this.isWithContent() && this._isEnabled)) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, this.constructor.Event.SHOW)\n    const shadowRoot = findShadowRoot(this._element)\n    const isInTheDom = shadowRoot === null ?\n      this._element.ownerDocument.documentElement.contains(this._element) :\n      shadowRoot.contains(this._element)\n\n    if (showEvent.defaultPrevented || !isInTheDom) {\n      return\n    }\n\n    const tip = this.getTipElement()\n    const tipId = getUID(this.constructor.NAME)\n\n    tip.setAttribute('id', tipId)\n    this._element.setAttribute('aria-describedby', tipId)\n\n    this.setContent()\n\n    if (this.config.animation) {\n      tip.classList.add(CLASS_NAME_FADE)\n    }\n\n    const placement = typeof this.config.placement === 'function' ?\n      this.config.placement.call(this, tip, this._element) :\n      this.config.placement\n\n    const attachment = this._getAttachment(placement)\n    this._addAttachmentClass(attachment)\n\n    const container = this._getContainer()\n    Data.set(tip, this.constructor.DATA_KEY, this)\n\n    if (!this._element.ownerDocument.documentElement.contains(this.tip)) {\n      container.appendChild(tip)\n      EventHandler.trigger(this._element, this.constructor.Event.INSERTED)\n    }\n\n    if (this._popper) {\n      this._popper.update()\n    } else {\n      this._popper = Popper.createPopper(this._element, tip, this._getPopperConfig(attachment))\n    }\n\n    tip.classList.add(CLASS_NAME_SHOW)\n\n    const customClass = typeof this.config.customClass === 'function' ? this.config.customClass() : this.config.customClass\n    if (customClass) {\n      tip.classList.add(...customClass.split(' '))\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children).forEach(element => {\n        EventHandler.on(element, 'mouseover', noop())\n      })\n    }\n\n    const complete = () => {\n      const prevHoverState = this._hoverState\n\n      this._hoverState = null\n      EventHandler.trigger(this._element, this.constructor.Event.SHOWN)\n\n      if (prevHoverState === HOVER_STATE_OUT) {\n        this._leave(null, this)\n      }\n    }\n\n    if (this.tip.classList.contains(CLASS_NAME_FADE)) {\n      const transitionDuration = getTransitionDurationFromElement(this.tip)\n      EventHandler.one(this.tip, 'transitionend', complete)\n      emulateTransitionEnd(this.tip, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  hide() {\n    if (!this._popper) {\n      return\n    }\n\n    const tip = this.getTipElement()\n    const complete = () => {\n      if (this._isWithActiveTrigger()) {\n        return\n      }\n\n      if (this._hoverState !== HOVER_STATE_SHOW && tip.parentNode) {\n        tip.parentNode.removeChild(tip)\n      }\n\n      this._cleanTipClass()\n      this._element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this._element, this.constructor.Event.HIDDEN)\n\n      if (this._popper) {\n        this._popper.destroy()\n        this._popper = null\n      }\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, this.constructor.Event.HIDE)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children)\n        .forEach(element => EventHandler.off(element, 'mouseover', noop))\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n\n    if (this.tip.classList.contains(CLASS_NAME_FADE)) {\n      const transitionDuration = getTransitionDurationFromElement(tip)\n\n      EventHandler.one(tip, 'transitionend', complete)\n      emulateTransitionEnd(tip, transitionDuration)\n    } else {\n      complete()\n    }\n\n    this._hoverState = ''\n  }\n\n  update() {\n    if (this._popper !== null) {\n      this._popper.update()\n    }\n  }\n\n  // Protected\n\n  isWithContent() {\n    return Boolean(this.getTitle())\n  }\n\n  getTipElement() {\n    if (this.tip) {\n      return this.tip\n    }\n\n    const element = document.createElement('div')\n    element.innerHTML = this.config.template\n\n    this.tip = element.children[0]\n    return this.tip\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TOOLTIP_INNER, tip), this.getTitle())\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  setElementContent(element, content) {\n    if (element === null) {\n      return\n    }\n\n    if (typeof content === 'object' && isElement(content)) {\n      if (content.jquery) {\n        content = content[0]\n      }\n\n      // content is a DOM node or a jQuery\n      if (this.config.html) {\n        if (content.parentNode !== element) {\n          element.innerHTML = ''\n          element.appendChild(content)\n        }\n      } else {\n        element.textContent = content.textContent\n      }\n\n      return\n    }\n\n    if (this.config.html) {\n      if (this.config.sanitize) {\n        content = sanitizeHtml(content, this.config.allowList, this.config.sanitizeFn)\n      }\n\n      element.innerHTML = content\n    } else {\n      element.textContent = content\n    }\n  }\n\n  getTitle() {\n    let title = this._element.getAttribute('data-bs-original-title')\n\n    if (!title) {\n      title = typeof this.config.title === 'function' ?\n        this.config.title.call(this._element) :\n        this.config.title\n    }\n\n    return title\n  }\n\n  updateAttachment(attachment) {\n    if (attachment === 'right') {\n      return 'end'\n    }\n\n    if (attachment === 'left') {\n      return 'start'\n    }\n\n    return attachment\n  }\n\n  // Private\n\n  _initializeOnDelegatedTarget(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || Data.get(event.delegateTarget, dataKey)\n\n    if (!context) {\n      context = new this.constructor(event.delegateTarget, this._getDelegateConfig())\n      Data.set(event.delegateTarget, dataKey, context)\n    }\n\n    return context\n  }\n\n  _getOffset() {\n    const { offset } = this.config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(val => Number.parseInt(val, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _getPopperConfig(attachment) {\n    const defaultBsPopperConfig = {\n      placement: attachment,\n      modifiers: [\n        {\n          name: 'flip',\n          options: {\n            altBoundary: true,\n            fallbackPlacements: this.config.fallbackPlacements\n          }\n        },\n        {\n          name: 'offset',\n          options: {\n            offset: this._getOffset()\n          }\n        },\n        {\n          name: 'preventOverflow',\n          options: {\n            boundary: this.config.boundary\n          }\n        },\n        {\n          name: 'arrow',\n          options: {\n            element: `.${this.constructor.NAME}-arrow`\n          }\n        },\n        {\n          name: 'onChange',\n          enabled: true,\n          phase: 'afterWrite',\n          fn: data => this._handlePopperPlacementChange(data)\n        }\n      ],\n      onFirstUpdate: data => {\n        if (data.options.placement !== data.placement) {\n          this._handlePopperPlacementChange(data)\n        }\n      }\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...(typeof this.config.popperConfig === 'function' ? this.config.popperConfig(defaultBsPopperConfig) : this.config.popperConfig)\n    }\n  }\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${this.updateAttachment(attachment)}`)\n  }\n\n  _getContainer() {\n    if (this.config.container === false) {\n      return document.body\n    }\n\n    if (isElement(this.config.container)) {\n      return this.config.container\n    }\n\n    return SelectorEngine.findOne(this.config.container)\n  }\n\n  _getAttachment(placement) {\n    return AttachmentMap[placement.toUpperCase()]\n  }\n\n  _setListeners() {\n    const triggers = this.config.trigger.split(' ')\n\n    triggers.forEach(trigger => {\n      if (trigger === 'click') {\n        EventHandler.on(this._element, this.constructor.Event.CLICK, this.config.selector, event => this.toggle(event))\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSEENTER :\n          this.constructor.Event.FOCUSIN\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSELEAVE :\n          this.constructor.Event.FOCUSOUT\n\n        EventHandler.on(this._element, eventIn, this.config.selector, event => this._enter(event))\n        EventHandler.on(this._element, eventOut, this.config.selector, event => this._leave(event))\n      }\n    })\n\n    this._hideModalHandler = () => {\n      if (this._element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this._element.closest(`.${CLASS_NAME_MODAL}`), 'hide.bs.modal', this._hideModalHandler)\n\n    if (this.config.selector) {\n      this.config = {\n        ...this.config,\n        trigger: 'manual',\n        selector: ''\n      }\n    } else {\n      this._fixTitle()\n    }\n  }\n\n  _fixTitle() {\n    const title = this._element.getAttribute('title')\n    const originalTitleType = typeof this._element.getAttribute('data-bs-original-title')\n\n    if (title || originalTitleType !== 'string') {\n      this._element.setAttribute('data-bs-original-title', title || '')\n      if (title && !this._element.getAttribute('aria-label') && !this._element.textContent) {\n        this._element.setAttribute('aria-label', title)\n      }\n\n      this._element.setAttribute('title', '')\n    }\n  }\n\n  _enter(event, context) {\n    context = this._initializeOnDelegatedTarget(event, context)\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = true\n    }\n\n    if (context.getTipElement().classList.contains(CLASS_NAME_SHOW) || context._hoverState === HOVER_STATE_SHOW) {\n      context._hoverState = HOVER_STATE_SHOW\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_SHOW\n\n    if (!context.config.delay || !context.config.delay.show) {\n      context.show()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_SHOW) {\n        context.show()\n      }\n    }, context.config.delay.show)\n  }\n\n  _leave(event, context) {\n    context = this._initializeOnDelegatedTarget(event, context)\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = context._element.contains(event.relatedTarget)\n    }\n\n    if (context._isWithActiveTrigger()) {\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_OUT\n\n    if (!context.config.delay || !context.config.delay.hide) {\n      context.hide()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_OUT) {\n        context.hide()\n      }\n    }, context.config.delay.hide)\n  }\n\n  _isWithActiveTrigger() {\n    for (const trigger in this._activeTrigger) {\n      if (this._activeTrigger[trigger]) {\n        return true\n      }\n    }\n\n    return false\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this._element)\n\n    Object.keys(dataAttributes).forEach(dataAttr => {\n      if (DISALLOWED_ATTRIBUTES.has(dataAttr)) {\n        delete dataAttributes[dataAttr]\n      }\n    })\n\n    if (config && typeof config.container === 'object' && config.container.jquery) {\n      config.container = config.container[0]\n    }\n\n    config = {\n      ...this.constructor.Default,\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    if (config.sanitize) {\n      config.template = sanitizeHtml(config.template, config.allowList, config.sanitizeFn)\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    if (this.config) {\n      for (const key in this.config) {\n        if (this.constructor.Default[key] !== this.config[key]) {\n          config[key] = this.config[key]\n        }\n      }\n    }\n\n    return config\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  _handlePopperPlacementChange(popperData) {\n    const { state } = popperData\n\n    if (!state) {\n      return\n    }\n\n    this.tip = state.elements.popper\n    this._cleanTipClass()\n    this._addAttachmentClass(this._getAttachment(state.placement))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Tooltip(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tooltip to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Tooltip)\n\nexport default Tooltip\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport Data from './dom/data'\nimport SelectorEngine from './dom/selector-engine'\nimport Tooltip from './tooltip'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'popover'\nconst DATA_KEY = 'bs.popover'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-popover'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\n\nconst Default = {\n  ...Tooltip.Default,\n  placement: 'right',\n  offset: [0, 8],\n  trigger: 'click',\n  content: '',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n              '<div class=\"popover-arrow\"></div>' +\n                '<h3 class=\"popover-header\"></h3>' +\n              '<div class=\"popover-body\"></div>' +\n            '</div>'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(string|element|function)'\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Popover extends Tooltip {\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Overrides\n\n  isWithContent() {\n    return this.getTitle() || this._getContent()\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n\n    // we use append for html objects to maintain js events\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TITLE, tip), this.getTitle())\n    let content = this._getContent()\n    if (typeof content === 'function') {\n      content = content.call(this._element)\n    }\n\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_CONTENT, tip), content)\n\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  // Private\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${this.updateAttachment(attachment)}`)\n  }\n\n  _getContent() {\n    return this._element.getAttribute('data-bs-content') || this.config.content\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n      const _config = typeof config === 'object' ? config : null\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Popover(this, _config)\n        Data.set(this, DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Popover to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Popover)\n\nexport default Popover\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getSelectorFromElement,\n  getUID,\n  isElement,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'scrollspy'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  offset: 10,\n  method: 'auto',\n  target: ''\n}\n\nconst DefaultType = {\n  offset: 'number',\n  method: 'string',\n  target: '(string|element)'\n}\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_SCROLL = `scroll${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-bs-spy=\"scroll\"]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst METHOD_OFFSET = 'offset'\nconst METHOD_POSITION = 'position'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass ScrollSpy extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n    this._scrollElement = this._element.tagName === 'BODY' ? window : this._element\n    this._config = this._getConfig(config)\n    this._selector = `${this._config.target} ${SELECTOR_NAV_LINKS}, ${this._config.target} ${SELECTOR_LIST_ITEMS}, ${this._config.target} .${CLASS_NAME_DROPDOWN_ITEM}`\n    this._offsets = []\n    this._targets = []\n    this._activeTarget = null\n    this._scrollHeight = 0\n\n    EventHandler.on(this._scrollElement, EVENT_SCROLL, () => this._process())\n\n    this.refresh()\n    this._process()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  refresh() {\n    const autoMethod = this._scrollElement === this._scrollElement.window ?\n      METHOD_OFFSET :\n      METHOD_POSITION\n\n    const offsetMethod = this._config.method === 'auto' ?\n      autoMethod :\n      this._config.method\n\n    const offsetBase = offsetMethod === METHOD_POSITION ?\n      this._getScrollTop() :\n      0\n\n    this._offsets = []\n    this._targets = []\n    this._scrollHeight = this._getScrollHeight()\n\n    const targets = SelectorEngine.find(this._selector)\n\n    targets.map(element => {\n      const targetSelector = getSelectorFromElement(element)\n      const target = targetSelector ? SelectorEngine.findOne(targetSelector) : null\n\n      if (target) {\n        const targetBCR = target.getBoundingClientRect()\n        if (targetBCR.width || targetBCR.height) {\n          return [\n            Manipulator[offsetMethod](target).top + offsetBase,\n            targetSelector\n          ]\n        }\n      }\n\n      return null\n    })\n      .filter(item => item)\n      .sort((a, b) => a[0] - b[0])\n      .forEach(item => {\n        this._offsets.push(item[0])\n        this._targets.push(item[1])\n      })\n  }\n\n  dispose() {\n    super.dispose()\n    EventHandler.off(this._scrollElement, EVENT_KEY)\n\n    this._scrollElement = null\n    this._config = null\n    this._selector = null\n    this._offsets = null\n    this._targets = null\n    this._activeTarget = null\n    this._scrollHeight = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (typeof config.target !== 'string' && isElement(config.target)) {\n      let { id } = config.target\n      if (!id) {\n        id = getUID(NAME)\n        config.target.id = id\n      }\n\n      config.target = `#${id}`\n    }\n\n    typeCheckConfig(NAME, config, DefaultType)\n\n    return config\n  }\n\n  _getScrollTop() {\n    return this._scrollElement === window ?\n      this._scrollElement.pageYOffset :\n      this._scrollElement.scrollTop\n  }\n\n  _getScrollHeight() {\n    return this._scrollElement.scrollHeight || Math.max(\n      document.body.scrollHeight,\n      document.documentElement.scrollHeight\n    )\n  }\n\n  _getOffsetHeight() {\n    return this._scrollElement === window ?\n      window.innerHeight :\n      this._scrollElement.getBoundingClientRect().height\n  }\n\n  _process() {\n    const scrollTop = this._getScrollTop() + this._config.offset\n    const scrollHeight = this._getScrollHeight()\n    const maxScroll = this._config.offset + scrollHeight - this._getOffsetHeight()\n\n    if (this._scrollHeight !== scrollHeight) {\n      this.refresh()\n    }\n\n    if (scrollTop >= maxScroll) {\n      const target = this._targets[this._targets.length - 1]\n\n      if (this._activeTarget !== target) {\n        this._activate(target)\n      }\n\n      return\n    }\n\n    if (this._activeTarget && scrollTop < this._offsets[0] && this._offsets[0] > 0) {\n      this._activeTarget = null\n      this._clear()\n      return\n    }\n\n    for (let i = this._offsets.length; i--;) {\n      const isActiveTarget = this._activeTarget !== this._targets[i] &&\n          scrollTop >= this._offsets[i] &&\n          (typeof this._offsets[i + 1] === 'undefined' || scrollTop < this._offsets[i + 1])\n\n      if (isActiveTarget) {\n        this._activate(this._targets[i])\n      }\n    }\n  }\n\n  _activate(target) {\n    this._activeTarget = target\n\n    this._clear()\n\n    const queries = this._selector.split(',')\n      .map(selector => `${selector}[data-bs-target=\"${target}\"],${selector}[href=\"${target}\"]`)\n\n    const link = SelectorEngine.findOne(queries.join(','))\n\n    if (link.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE, link.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n\n      link.classList.add(CLASS_NAME_ACTIVE)\n    } else {\n      // Set triggered link as active\n      link.classList.add(CLASS_NAME_ACTIVE)\n\n      SelectorEngine.parents(link, SELECTOR_NAV_LIST_GROUP)\n        .forEach(listGroup => {\n          // Set triggered links parents as active\n          // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n          SelectorEngine.prev(listGroup, `${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`)\n            .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n\n          // Handle special case when .nav-link is inside .nav-item\n          SelectorEngine.prev(listGroup, SELECTOR_NAV_ITEMS)\n            .forEach(navItem => {\n              SelectorEngine.children(navItem, SELECTOR_NAV_LINKS)\n                .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n            })\n        })\n    }\n\n    EventHandler.trigger(this._scrollElement, EVENT_ACTIVATE, {\n      relatedTarget: target\n    })\n  }\n\n  _clear() {\n    SelectorEngine.find(this._selector)\n      .filter(node => node.classList.contains(CLASS_NAME_ACTIVE))\n      .forEach(node => node.classList.remove(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new ScrollSpy(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  SelectorEngine.find(SELECTOR_DATA_SPY)\n    .forEach(spy => new ScrollSpy(spy, Manipulator.getDataAttributes(spy)))\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .ScrollSpy to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, ScrollSpy)\n\nexport default ScrollSpy\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isDisabled,\n  reflow\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tab'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_MENU = 'dropdown-menu'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_UL = ':scope > li > .active'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"tab\"], [data-bs-toggle=\"pill\"], [data-bs-toggle=\"list\"]'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_ACTIVE_CHILD = ':scope > .dropdown-menu .active'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tab extends BaseComponent {\n  // Getters\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  show() {\n    if ((this._element.parentNode &&\n      this._element.parentNode.nodeType === Node.ELEMENT_NODE &&\n      this._element.classList.contains(CLASS_NAME_ACTIVE)) ||\n      isDisabled(this._element)) {\n      return\n    }\n\n    let previous\n    const target = getElementFromSelector(this._element)\n    const listElement = this._element.closest(SELECTOR_NAV_LIST_GROUP)\n\n    if (listElement) {\n      const itemSelector = listElement.nodeName === 'UL' || listElement.nodeName === 'OL' ? SELECTOR_ACTIVE_UL : SELECTOR_ACTIVE\n      previous = SelectorEngine.find(itemSelector, listElement)\n      previous = previous[previous.length - 1]\n    }\n\n    const hideEvent = previous ?\n      EventHandler.trigger(previous, EVENT_HIDE, {\n        relatedTarget: this._element\n      }) :\n      null\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget: previous\n    })\n\n    if (showEvent.defaultPrevented || (hideEvent !== null && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._activate(this._element, listElement)\n\n    const complete = () => {\n      EventHandler.trigger(previous, EVENT_HIDDEN, {\n        relatedTarget: this._element\n      })\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget: previous\n      })\n    }\n\n    if (target) {\n      this._activate(target, target.parentNode, complete)\n    } else {\n      complete()\n    }\n  }\n\n  // Private\n\n  _activate(element, container, callback) {\n    const activeElements = container && (container.nodeName === 'UL' || container.nodeName === 'OL') ?\n      SelectorEngine.find(SELECTOR_ACTIVE_UL, container) :\n      SelectorEngine.children(container, SELECTOR_ACTIVE)\n\n    const active = activeElements[0]\n    const isTransitioning = callback && (active && active.classList.contains(CLASS_NAME_FADE))\n\n    const complete = () => this._transitionComplete(element, active, callback)\n\n    if (active && isTransitioning) {\n      const transitionDuration = getTransitionDurationFromElement(active)\n      active.classList.remove(CLASS_NAME_SHOW)\n\n      EventHandler.one(active, 'transitionend', complete)\n      emulateTransitionEnd(active, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  _transitionComplete(element, active, callback) {\n    if (active) {\n      active.classList.remove(CLASS_NAME_ACTIVE)\n\n      const dropdownChild = SelectorEngine.findOne(SELECTOR_DROPDOWN_ACTIVE_CHILD, active.parentNode)\n\n      if (dropdownChild) {\n        dropdownChild.classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      if (active.getAttribute('role') === 'tab') {\n        active.setAttribute('aria-selected', false)\n      }\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n    if (element.getAttribute('role') === 'tab') {\n      element.setAttribute('aria-selected', true)\n    }\n\n    reflow(element)\n\n    if (element.classList.contains(CLASS_NAME_FADE)) {\n      element.classList.add(CLASS_NAME_SHOW)\n    }\n\n    if (element.parentNode && element.parentNode.classList.contains(CLASS_NAME_DROPDOWN_MENU)) {\n      const dropdownElement = element.closest(SELECTOR_DROPDOWN)\n\n      if (dropdownElement) {\n        SelectorEngine.find(SELECTOR_DROPDOWN_TOGGLE)\n          .forEach(dropdown => dropdown.classList.add(CLASS_NAME_ACTIVE))\n      }\n\n      element.setAttribute('aria-expanded', true)\n    }\n\n    if (callback) {\n      callback()\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Data.get(this, DATA_KEY) || new Tab(this)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n\n  const data = Data.get(this, DATA_KEY) || new Tab(this)\n  data.show()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tab to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Tab)\n\nexport default Tab\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  emulateTransitionEnd,\n  getTransitionDurationFromElement,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'toast'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 5000\n}\n\nconst SELECTOR_DATA_DISMISS = '[data-bs-dismiss=\"toast\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Toast extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._timeout = null\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      this._element.classList.add(CLASS_NAME_SHOW)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      if (this._config.autohide) {\n        this._timeout = setTimeout(() => {\n          this.hide()\n        }, this._config.delay)\n      }\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE)\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    if (this._config.animation) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, 'transitionend', complete)\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  hide() {\n    if (!this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    if (this._config.animation) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, 'transitionend', complete)\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n\n    super.dispose()\n    this._config = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    return config\n  }\n\n  _setListeners() {\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, () => this.hide())\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new Toast(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Toast to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Toast)\n\nexport default Toast\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): index.umd.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport <PERSON><PERSON> from './src/alert'\nimport But<PERSON> from './src/button'\nimport Carousel from './src/carousel'\nimport Collapse from './src/collapse'\nimport Dropdown from './src/dropdown'\nimport Modal from './src/modal'\nimport Offcanvas from './src/offcanvas'\nimport Popover from './src/popover'\nimport ScrollSpy from './src/scrollspy'\nimport Tab from './src/tab'\nimport Toast from './src/toast'\nimport Tooltip from './src/tooltip'\n\nexport default {\n  <PERSON><PERSON>,\n  Button,\n  Carousel,\n  Collapse,\n  Dropdown,\n  Modal,\n  Offcanvas,\n  Popover,\n  ScrollSpy,\n  Tab,\n  Toast,\n  Tooltip\n}\n"], "names": ["MAX_UID", "MILLISECONDS_MULTIPLIER", "TRANSITION_END", "toType", "obj", "undefined", "toString", "call", "match", "toLowerCase", "getUID", "prefix", "Math", "floor", "random", "document", "getElementById", "getSelector", "element", "selector", "getAttribute", "hrefAttr", "includes", "startsWith", "split", "trim", "getSelectorFromElement", "querySelector", "getElementFromSelector", "getTransitionDurationFromElement", "transitionDuration", "transitionDelay", "window", "getComputedStyle", "floatTransitionDuration", "Number", "parseFloat", "floatTransitionDelay", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "nodeType", "emulateTransitionEnd", "duration", "called", "durationPadding", "emulatedDuration", "listener", "removeEventListener", "addEventListener", "setTimeout", "typeCheckConfig", "componentName", "config", "configTypes", "Object", "keys", "for<PERSON>ach", "property", "expectedTypes", "value", "valueType", "RegExp", "test", "TypeError", "toUpperCase", "isVisible", "style", "parentNode", "elementStyle", "parentNodeStyle", "display", "visibility", "isDisabled", "Node", "ELEMENT_NODE", "classList", "contains", "disabled", "hasAttribute", "findShadowRoot", "documentElement", "attachShadow", "getRootNode", "root", "ShadowRoot", "noop", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "body", "onDOMContentLoaded", "callback", "readyState", "isRTL", "dir", "defineJQueryPlugin", "name", "plugin", "$", "JQUERY_NO_CONFLICT", "fn", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "elementMap", "Map", "set", "key", "instance", "has", "instanceMap", "get", "size", "console", "error", "Array", "from", "remove", "delete", "namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "nativeEvents", "Set", "getUidEvent", "uid", "getEvent", "bootstrapHandler", "handler", "event", "<PERSON><PERSON><PERSON><PERSON>", "oneOff", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "dom<PERSON><PERSON>s", "querySelectorAll", "target", "i", "length", "<PERSON><PERSON><PERSON><PERSON>", "events", "delegationSelector", "uidEventList", "len", "<PERSON><PERSON><PERSON><PERSON>", "normalizeParams", "originalTypeEvent", "delegationFn", "delegation", "typeEvent", "replace", "custom", "isNative", "add<PERSON><PERSON><PERSON>", "handlers", "previousFn", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "removeNamespacedHandlers", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "on", "one", "inNamespace", "isNamespace", "elementEvent", "slice", "keyHandlers", "trigger", "args", "jQueryEvent", "bubbles", "nativeDispatch", "defaultPrevented", "evt", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "createEvent", "initEvent", "CustomEvent", "cancelable", "defineProperty", "preventDefault", "VERSION", "BaseComponent", "constructor", "_element", "Data", "DATA_KEY", "dispose", "getInstance", "NAME", "EVENT_KEY", "DATA_API_KEY", "SELECTOR_DISMISS", "EVENT_CLOSE", "EVENT_CLOSED", "EVENT_CLICK_DATA_API", "CLASS_NAME_ALERT", "CLASS_NAME_FADE", "CLASS_NAME_SHOW", "<PERSON><PERSON>", "close", "rootElement", "_getRootElement", "customEvent", "_triggerCloseEvent", "_removeElement", "closest", "_destroyElement", "<PERSON><PERSON><PERSON><PERSON>", "each", "data", "handle<PERSON><PERSON><PERSON>", "alertInstance", "CLASS_NAME_ACTIVE", "SELECTOR_DATA_TOGGLE", "<PERSON><PERSON>", "toggle", "setAttribute", "button", "normalizeData", "val", "normalizeDataKey", "chr", "Manipulator", "setDataAttribute", "removeDataAttribute", "removeAttribute", "getDataAttributes", "attributes", "dataset", "filter", "pureKey", "char<PERSON>t", "getDataAttribute", "offset", "rect", "getBoundingClientRect", "top", "scrollTop", "left", "scrollLeft", "position", "offsetTop", "offsetLeft", "NODE_TEXT", "SelectorEngine", "find", "concat", "Element", "prototype", "findOne", "children", "child", "matches", "parents", "ancestor", "push", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "ARROW_LEFT_KEY", "ARROW_RIGHT_KEY", "TOUCHEVENT_COMPAT_WAIT", "SWIPE_THRESHOLD", "<PERSON><PERSON><PERSON>", "interval", "keyboard", "slide", "pause", "wrap", "touch", "DefaultType", "ORDER_NEXT", "ORDER_PREV", "DIRECTION_LEFT", "DIRECTION_RIGHT", "EVENT_SLIDE", "EVENT_SLID", "EVENT_KEYDOWN", "EVENT_MOUSEENTER", "EVENT_MOUSELEAVE", "EVENT_TOUCHSTART", "EVENT_TOUCHMOVE", "EVENT_TOUCHEND", "EVENT_POINTERDOWN", "EVENT_POINTERUP", "EVENT_DRAG_START", "EVENT_LOAD_DATA_API", "CLASS_NAME_CAROUSEL", "CLASS_NAME_SLIDE", "CLASS_NAME_END", "CLASS_NAME_START", "CLASS_NAME_NEXT", "CLASS_NAME_PREV", "CLASS_NAME_POINTER_EVENT", "SELECTOR_ACTIVE", "SELECTOR_ACTIVE_ITEM", "SELECTOR_ITEM", "SELECTOR_ITEM_IMG", "SELECTOR_NEXT_PREV", "SELECTOR_INDICATORS", "SELECTOR_INDICATOR", "SELECTOR_DATA_SLIDE", "SELECTOR_DATA_RIDE", "POINTER_TYPE_TOUCH", "POINTER_TYPE_PEN", "Carousel", "_items", "_interval", "_activeElement", "_isPaused", "_isSliding", "touchTimeout", "touchStartX", "touchDeltaX", "_config", "_getConfig", "_indicatorsElement", "_touchSupported", "navigator", "maxTouchPoints", "_pointerEvent", "PointerEvent", "_addEventListeners", "_slide", "nextWhenVisible", "hidden", "cycle", "clearInterval", "_updateInterval", "setInterval", "visibilityState", "bind", "to", "index", "activeIndex", "_getItemIndex", "order", "_handleSwipe", "absDeltax", "abs", "direction", "_keydown", "_addTouchEventListeners", "start", "pointerType", "clientX", "touches", "move", "end", "clearTimeout", "itemImg", "e", "add", "tagName", "indexOf", "_getItemByOrder", "activeElement", "isNext", "isPrev", "lastItemIndex", "isGoingToWrap", "delta", "itemIndex", "_triggerSlideEvent", "relatedTarget", "eventDirectionName", "targetIndex", "fromIndex", "_setActiveIndicatorElement", "activeIndicator", "indicators", "parseInt", "elementInterval", "defaultInterval", "directionOrOrder", "_directionToOrder", "activeElementIndex", "nextElement", "nextElementIndex", "isCycling", "directionalClassName", "orderClassName", "_orderToDirection", "slideEvent", "carouselInterface", "action", "ride", "dataApiClickHandler", "slideIndex", "carousels", "parent", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_HIDE", "EVENT_HIDDEN", "CLASS_NAME_COLLAPSE", "CLASS_NAME_COLLAPSING", "CLASS_NAME_COLLAPSED", "WIDTH", "HEIGHT", "SELECTOR_ACTIVES", "Collapse", "_isTransitioning", "_triggerArray", "id", "toggleList", "elem", "filterElement", "foundElem", "_selector", "_parent", "_getParent", "_addAriaAndCollapsedClass", "hide", "show", "actives", "activesData", "container", "tempActiveData", "startEvent", "elemActive", "collapseInterface", "dimension", "_getDimension", "setTransitioning", "complete", "capitalizedDimension", "scrollSize", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isTransitioning", "j<PERSON>y", "selected", "trigger<PERSON><PERSON>y", "isOpen", "triggerData", "selectorElements", "effect", "min", "max", "mathMax", "mathMin", "hash", "allPlacements", "placements", "createPopper", "defaultModifiers", "popperOffsets", "computeStyles", "applyStyles", "flip", "preventOverflow", "arrow", "ESCAPE_KEY", "SPACE_KEY", "TAB_KEY", "ARROW_UP_KEY", "ARROW_DOWN_KEY", "RIGHT_MOUSE_BUTTON", "REGEXP_KEYDOWN", "EVENT_CLICK", "EVENT_KEYDOWN_DATA_API", "EVENT_KEYUP_DATA_API", "CLASS_NAME_DISABLED", "CLASS_NAME_DROPUP", "CLASS_NAME_DROPEND", "CLASS_NAME_DROPSTART", "CLASS_NAME_NAVBAR", "SELECTOR_MENU", "SELECTOR_NAVBAR_NAV", "SELECTOR_VISIBLE_ITEMS", "PLACEMENT_TOP", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "boundary", "reference", "popperConfig", "Dropdown", "_popper", "_menu", "_getMenuElement", "_inNavbar", "_detectNavbar", "isActive", "clearMenus", "getParentFromElement", "showEvent", "<PERSON><PERSON>", "referenceElement", "_getPopperConfig", "isDisplayStatic", "modifiers", "modifier", "enabled", "focus", "hideEvent", "destroy", "update", "_getPlacement", "parentDropdown", "isEnd", "getPropertyValue", "_getOffset", "map", "popperData", "defaultBsPopperConfig", "placement", "options", "dropdownInterface", "toggles", "context", "clickEvent", "dropdownMenu", "some", "<PERSON><PERSON><PERSON>", "dataApiKeydownHandler", "stopPropagation", "click", "items", "backdrop", "EVENT_HIDE_PREVENTED", "EVENT_FOCUSIN", "EVENT_RESIZE", "EVENT_CLICK_DISMISS", "EVENT_KEYDOWN_DISMISS", "EVENT_MOUSEUP_DISMISS", "EVENT_MOUSEDOWN_DISMISS", "CLASS_NAME_SCROLLBAR_MEASURER", "CLASS_NAME_BACKDROP", "CLASS_NAME_OPEN", "CLASS_NAME_STATIC", "SELECTOR_DIALOG", "SELECTOR_MODAL_BODY", "SELECTOR_DATA_DISMISS", "SELECTOR_FIXED_CONTENT", "SELECTOR_STICKY_CONTENT", "Modal", "_dialog", "_backdrop", "_isShown", "_isBodyOverflowing", "_ignoreBackdropClick", "_scrollbarWidth", "_isAnimated", "_checkScrollbar", "_setScrollbar", "_adjustDialog", "_setEscapeEvent", "_setResizeEvent", "_showBackdrop", "_showElement", "isAnimated", "_hideModal", "htmlElement", "handleUpdate", "modalBody", "append<PERSON><PERSON><PERSON>", "_enforceFocus", "transitionComplete", "_triggerBackdropTransition", "_resetAdjustments", "_resetScrollbar", "_removeBackdrop", "createElement", "className", "currentTarget", "backdropTransitionDuration", "callback<PERSON><PERSON><PERSON>", "isModalOverflowing", "scrollHeight", "clientHeight", "overflowY", "modalTransitionDuration", "paddingLeft", "paddingRight", "round", "right", "innerWidth", "_getScrollbarWidth", "_setElementAttributes", "calculatedValue", "styleProp", "clientWidth", "actualValue", "_resetElementAttributes", "scrollDiv", "scrollbarWidth", "width", "getWidth", "documentWidth", "overflow", "reset", "removeProperty", "scroll", "CLASS_NAME_BACKDROP_BODY", "CLASS_NAME_TOGGLING", "OPEN_SELECTOR", "ACTIVE_SELECTOR", "<PERSON><PERSON><PERSON>", "scrollBarHide", "completeCallBack", "_enforceFocusOnElement", "blur", "completeCallback", "scrollBarReset", "allReadyOpen", "el", "uriAttrs", "ARIA_ATTRIBUTE_PATTERN", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "allowedAttribute", "attr", "allowedAttributeList", "attrName", "nodeName", "nodeValue", "regExp", "attrRegex", "DefaultAllowlist", "a", "area", "b", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "img", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "sanitizeHtml", "unsafeHtml", "allowList", "sanitizeFn", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createdDocument", "parseFromString", "allow<PERSON><PERSON><PERSON><PERSON>", "elements", "el<PERSON>ame", "attributeList", "allowedAttributes", "innerHTML", "CLASS_PREFIX", "BSCLS_PREFIX_REGEX", "DISALLOWED_ATTRIBUTES", "animation", "template", "title", "delay", "html", "fallbackPlacements", "customClass", "sanitize", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "HIDE", "HIDDEN", "SHOW", "SHOWN", "INSERTED", "CLICK", "FOCUSIN", "FOCUSOUT", "MOUSEENTER", "MOUSELEAVE", "CLASS_NAME_MODAL", "HOVER_STATE_SHOW", "HOVER_STATE_OUT", "SELECTOR_TOOLTIP_INNER", "TRIGGER_HOVER", "TRIGGER_FOCUS", "TRIGGER_CLICK", "TRIGGER_MANUAL", "<PERSON><PERSON><PERSON>", "_isEnabled", "_timeout", "_hoverState", "_activeTrigger", "tip", "_setListeners", "enable", "disable", "toggle<PERSON>nabled", "_initializeOnDelegatedTarget", "_isWithActiveTrigger", "_enter", "_leave", "getTipElement", "_hideModalHandler", "Error", "isWithContent", "shadowRoot", "isInTheDom", "ownerDocument", "tipId", "<PERSON><PERSON><PERSON><PERSON>", "attachment", "_getAttachment", "_addAttachmentClass", "_get<PERSON><PERSON><PERSON>", "prevHoverState", "_cleanTipClass", "getTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "textContent", "updateAttachment", "dataKey", "_getDelegateConfig", "altBoundary", "phase", "_handlePopperPlacementChange", "onFirstUpdate", "triggers", "eventIn", "eventOut", "_fixTitle", "originalTitleType", "dataAttributes", "dataAttr", "tabClass", "token", "tClass", "state", "popper", "SELECTOR_TITLE", "SELECTOR_CONTENT", "Popover", "_getContent", "method", "EVENT_ACTIVATE", "EVENT_SCROLL", "CLASS_NAME_DROPDOWN_ITEM", "SELECTOR_DATA_SPY", "SELECTOR_NAV_LIST_GROUP", "SELECTOR_NAV_LINKS", "SELECTOR_NAV_ITEMS", "SELECTOR_LIST_ITEMS", "SELECTOR_DROPDOWN", "SELECTOR_DROPDOWN_TOGGLE", "METHOD_OFFSET", "METHOD_POSITION", "ScrollSpy", "_scrollElement", "_offsets", "_targets", "_activeTarget", "_scrollHeight", "_process", "refresh", "autoMethod", "offsetMethod", "offsetBase", "_getScrollTop", "_getScrollHeight", "targets", "targetSelector", "targetBCR", "height", "item", "sort", "pageYOffset", "_getOffsetHeight", "innerHeight", "maxScroll", "_activate", "_clear", "isActiveTarget", "queries", "link", "join", "listGroup", "navItem", "node", "spy", "CLASS_NAME_DROPDOWN_MENU", "SELECTOR_ACTIVE_UL", "SELECTOR_DROPDOWN_ACTIVE_CHILD", "Tab", "listElement", "itemSelector", "activeElements", "active", "_transitionComplete", "dropdown<PERSON><PERSON>d", "dropdownElement", "dropdown", "CLASS_NAME_HIDE", "CLASS_NAME_SHOWING", "autohide", "Toast", "_clearTimeout"], "mappings": ";;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;EAEA,MAAMA,OAAO,GAAG,OAAhB;EACA,MAAMC,uBAAuB,GAAG,IAAhC;EACA,MAAMC,cAAc,GAAG,eAAvB;;EAGA,MAAMC,MAAM,GAAGC,GAAG,IAAI;EACpB,MAAIA,GAAG,KAAK,IAAR,IAAgBA,GAAG,KAAKC,SAA5B,EAAuC;EACrC,WAAQ,GAAED,GAAI,EAAd;EACD;;EAED,SAAO,GAAGE,QAAH,CAAYC,IAAZ,CAAiBH,GAAjB,EAAsBI,KAAtB,CAA4B,aAA5B,EAA2C,CAA3C,EAA8CC,WAA9C,EAAP;EACD,CAND;EAQA;EACA;EACA;EACA;EACA;;;EAEA,MAAMC,MAAM,GAAGC,MAAM,IAAI;EACvB,KAAG;EACDA,IAAAA,MAAM,IAAIC,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,MAAL,KAAgBd,OAA3B,CAAV;EACD,GAFD,QAESe,QAAQ,CAACC,cAAT,CAAwBL,MAAxB,CAFT;;EAIA,SAAOA,MAAP;EACD,CAND;;EAQA,MAAMM,WAAW,GAAGC,OAAO,IAAI;EAC7B,MAAIC,QAAQ,GAAGD,OAAO,CAACE,YAAR,CAAqB,gBAArB,CAAf;;EAEA,MAAI,CAACD,QAAD,IAAaA,QAAQ,KAAK,GAA9B,EAAmC;EACjC,QAAIE,QAAQ,GAAGH,OAAO,CAACE,YAAR,CAAqB,MAArB,CAAf,CADiC;EAIjC;EACA;EACA;;EACA,QAAI,CAACC,QAAD,IAAc,CAACA,QAAQ,CAACC,QAAT,CAAkB,GAAlB,CAAD,IAA2B,CAACD,QAAQ,CAACE,UAAT,CAAoB,GAApB,CAA9C,EAAyE;EACvE,aAAO,IAAP;EACD,KATgC;;;EAYjC,QAAIF,QAAQ,CAACC,QAAT,CAAkB,GAAlB,KAA0B,CAACD,QAAQ,CAACE,UAAT,CAAoB,GAApB,CAA/B,EAAyD;EACvDF,MAAAA,QAAQ,GAAG,MAAMA,QAAQ,CAACG,KAAT,CAAe,GAAf,EAAoB,CAApB,CAAjB;EACD;;EAEDL,IAAAA,QAAQ,GAAGE,QAAQ,IAAIA,QAAQ,KAAK,GAAzB,GAA+BA,QAAQ,CAACI,IAAT,EAA/B,GAAiD,IAA5D;EACD;;EAED,SAAON,QAAP;EACD,CAvBD;;EAyBA,MAAMO,sBAAsB,GAAGR,OAAO,IAAI;EACxC,QAAMC,QAAQ,GAAGF,WAAW,CAACC,OAAD,CAA5B;;EAEA,MAAIC,QAAJ,EAAc;EACZ,WAAOJ,QAAQ,CAACY,aAAT,CAAuBR,QAAvB,IAAmCA,QAAnC,GAA8C,IAArD;EACD;;EAED,SAAO,IAAP;EACD,CARD;;EAUA,MAAMS,sBAAsB,GAAGV,OAAO,IAAI;EACxC,QAAMC,QAAQ,GAAGF,WAAW,CAACC,OAAD,CAA5B;EAEA,SAAOC,QAAQ,GAAGJ,QAAQ,CAACY,aAAT,CAAuBR,QAAvB,CAAH,GAAsC,IAArD;EACD,CAJD;;EAMA,MAAMU,gCAAgC,GAAGX,OAAO,IAAI;EAClD,MAAI,CAACA,OAAL,EAAc;EACZ,WAAO,CAAP;EACD,GAHiD;;;EAMlD,MAAI;EAAEY,IAAAA,kBAAF;EAAsBC,IAAAA;EAAtB,MAA0CC,MAAM,CAACC,gBAAP,CAAwBf,OAAxB,CAA9C;EAEA,QAAMgB,uBAAuB,GAAGC,MAAM,CAACC,UAAP,CAAkBN,kBAAlB,CAAhC;EACA,QAAMO,oBAAoB,GAAGF,MAAM,CAACC,UAAP,CAAkBL,eAAlB,CAA7B,CATkD;;EAYlD,MAAI,CAACG,uBAAD,IAA4B,CAACG,oBAAjC,EAAuD;EACrD,WAAO,CAAP;EACD,GAdiD;;;EAiBlDP,EAAAA,kBAAkB,GAAGA,kBAAkB,CAACN,KAAnB,CAAyB,GAAzB,EAA8B,CAA9B,CAArB;EACAO,EAAAA,eAAe,GAAGA,eAAe,CAACP,KAAhB,CAAsB,GAAtB,EAA2B,CAA3B,CAAlB;EAEA,SAAO,CAACW,MAAM,CAACC,UAAP,CAAkBN,kBAAlB,IAAwCK,MAAM,CAACC,UAAP,CAAkBL,eAAlB,CAAzC,IAA+E9B,uBAAtF;EACD,CArBD;;EAuBA,MAAMqC,oBAAoB,GAAGpB,OAAO,IAAI;EACtCA,EAAAA,OAAO,CAACqB,aAAR,CAAsB,IAAIC,KAAJ,CAAUtC,cAAV,CAAtB;EACD,CAFD;;EAIA,MAAMuC,WAAS,GAAGrC,GAAG,IAAI,CAACA,GAAG,CAAC,CAAD,CAAH,IAAUA,GAAX,EAAgBsC,QAAzC;;EAEA,MAAMC,oBAAoB,GAAG,CAACzB,OAAD,EAAU0B,QAAV,KAAuB;EAClD,MAAIC,MAAM,GAAG,KAAb;EACA,QAAMC,eAAe,GAAG,CAAxB;EACA,QAAMC,gBAAgB,GAAGH,QAAQ,GAAGE,eAApC;;EAEA,WAASE,QAAT,GAAoB;EAClBH,IAAAA,MAAM,GAAG,IAAT;EACA3B,IAAAA,OAAO,CAAC+B,mBAAR,CAA4B/C,cAA5B,EAA4C8C,QAA5C;EACD;;EAED9B,EAAAA,OAAO,CAACgC,gBAAR,CAAyBhD,cAAzB,EAAyC8C,QAAzC;EACAG,EAAAA,UAAU,CAAC,MAAM;EACf,QAAI,CAACN,MAAL,EAAa;EACXP,MAAAA,oBAAoB,CAACpB,OAAD,CAApB;EACD;EACF,GAJS,EAIP6B,gBAJO,CAAV;EAKD,CAhBD;;EAkBA,MAAMK,eAAe,GAAG,CAACC,aAAD,EAAgBC,MAAhB,EAAwBC,WAAxB,KAAwC;EAC9DC,EAAAA,MAAM,CAACC,IAAP,CAAYF,WAAZ,EAAyBG,OAAzB,CAAiCC,QAAQ,IAAI;EAC3C,UAAMC,aAAa,GAAGL,WAAW,CAACI,QAAD,CAAjC;EACA,UAAME,KAAK,GAAGP,MAAM,CAACK,QAAD,CAApB;EACA,UAAMG,SAAS,GAAGD,KAAK,IAAIpB,WAAS,CAACoB,KAAD,CAAlB,GAA4B,SAA5B,GAAwC1D,MAAM,CAAC0D,KAAD,CAAhE;;EAEA,QAAI,CAAC,IAAIE,MAAJ,CAAWH,aAAX,EAA0BI,IAA1B,CAA+BF,SAA/B,CAAL,EAAgD;EAC9C,YAAM,IAAIG,SAAJ,CACH,GAAEZ,aAAa,CAACa,WAAd,EAA4B,IAA/B,GACC,WAAUP,QAAS,oBAAmBG,SAAU,IADjD,GAEC,sBAAqBF,aAAc,IAHhC,CAAN;EAKD;EACF,GAZD;EAaD,CAdD;;EAgBA,MAAMO,SAAS,GAAGjD,OAAO,IAAI;EAC3B,MAAI,CAACA,OAAL,EAAc;EACZ,WAAO,KAAP;EACD;;EAED,MAAIA,OAAO,CAACkD,KAAR,IAAiBlD,OAAO,CAACmD,UAAzB,IAAuCnD,OAAO,CAACmD,UAAR,CAAmBD,KAA9D,EAAqE;EACnE,UAAME,YAAY,GAAGrC,gBAAgB,CAACf,OAAD,CAArC;EACA,UAAMqD,eAAe,GAAGtC,gBAAgB,CAACf,OAAO,CAACmD,UAAT,CAAxC;EAEA,WAAOC,YAAY,CAACE,OAAb,KAAyB,MAAzB,IACLD,eAAe,CAACC,OAAhB,KAA4B,MADvB,IAELF,YAAY,CAACG,UAAb,KAA4B,QAF9B;EAGD;;EAED,SAAO,KAAP;EACD,CAfD;;EAiBA,MAAMC,UAAU,GAAGxD,OAAO,IAAI;EAC5B,MAAI,CAACA,OAAD,IAAYA,OAAO,CAACwB,QAAR,KAAqBiC,IAAI,CAACC,YAA1C,EAAwD;EACtD,WAAO,IAAP;EACD;;EAED,MAAI1D,OAAO,CAAC2D,SAAR,CAAkBC,QAAlB,CAA2B,UAA3B,CAAJ,EAA4C;EAC1C,WAAO,IAAP;EACD;;EAED,MAAI,OAAO5D,OAAO,CAAC6D,QAAf,KAA4B,WAAhC,EAA6C;EAC3C,WAAO7D,OAAO,CAAC6D,QAAf;EACD;;EAED,SAAO7D,OAAO,CAAC8D,YAAR,CAAqB,UAArB,KAAoC9D,OAAO,CAACE,YAAR,CAAqB,UAArB,MAAqC,OAAhF;EACD,CAdD;;EAgBA,MAAM6D,cAAc,GAAG/D,OAAO,IAAI;EAChC,MAAI,CAACH,QAAQ,CAACmE,eAAT,CAAyBC,YAA9B,EAA4C;EAC1C,WAAO,IAAP;EACD,GAH+B;;;EAMhC,MAAI,OAAOjE,OAAO,CAACkE,WAAf,KAA+B,UAAnC,EAA+C;EAC7C,UAAMC,IAAI,GAAGnE,OAAO,CAACkE,WAAR,EAAb;EACA,WAAOC,IAAI,YAAYC,UAAhB,GAA6BD,IAA7B,GAAoC,IAA3C;EACD;;EAED,MAAInE,OAAO,YAAYoE,UAAvB,EAAmC;EACjC,WAAOpE,OAAP;EACD,GAb+B;;;EAgBhC,MAAI,CAACA,OAAO,CAACmD,UAAb,EAAyB;EACvB,WAAO,IAAP;EACD;;EAED,SAAOY,cAAc,CAAC/D,OAAO,CAACmD,UAAT,CAArB;EACD,CArBD;;EAuBA,MAAMkB,IAAI,GAAG,MAAM,YAAY,EAA/B;;EAEA,MAAMC,MAAM,GAAGtE,OAAO,IAAIA,OAAO,CAACuE,YAAlC;;EAEA,MAAMC,SAAS,GAAG,MAAM;EACtB,QAAM;EAAEC,IAAAA;EAAF,MAAa3D,MAAnB;;EAEA,MAAI2D,MAAM,IAAI,CAAC5E,QAAQ,CAAC6E,IAAT,CAAcZ,YAAd,CAA2B,mBAA3B,CAAf,EAAgE;EAC9D,WAAOW,MAAP;EACD;;EAED,SAAO,IAAP;EACD,CARD;;EAUA,MAAME,kBAAkB,GAAGC,QAAQ,IAAI;EACrC,MAAI/E,QAAQ,CAACgF,UAAT,KAAwB,SAA5B,EAAuC;EACrChF,IAAAA,QAAQ,CAACmC,gBAAT,CAA0B,kBAA1B,EAA8C4C,QAA9C;EACD,GAFD,MAEO;EACLA,IAAAA,QAAQ;EACT;EACF,CAND;;EAQA,MAAME,KAAK,GAAG,MAAMjF,QAAQ,CAACmE,eAAT,CAAyBe,GAAzB,KAAiC,KAArD;;EAEA,MAAMC,kBAAkB,GAAG,CAACC,IAAD,EAAOC,MAAP,KAAkB;EAC3CP,EAAAA,kBAAkB,CAAC,MAAM;EACvB,UAAMQ,CAAC,GAAGX,SAAS,EAAnB;EACA;;EACA,QAAIW,CAAJ,EAAO;EACL,YAAMC,kBAAkB,GAAGD,CAAC,CAACE,EAAF,CAAKJ,IAAL,CAA3B;EACAE,MAAAA,CAAC,CAACE,EAAF,CAAKJ,IAAL,IAAaC,MAAM,CAACI,eAApB;EACAH,MAAAA,CAAC,CAACE,EAAF,CAAKJ,IAAL,EAAWM,WAAX,GAAyBL,MAAzB;;EACAC,MAAAA,CAAC,CAACE,EAAF,CAAKJ,IAAL,EAAWO,UAAX,GAAwB,MAAM;EAC5BL,QAAAA,CAAC,CAACE,EAAF,CAAKJ,IAAL,IAAaG,kBAAb;EACA,eAAOF,MAAM,CAACI,eAAd;EACD,OAHD;EAID;EACF,GAZiB,CAAlB;EAaD,CAdD;;EC1NA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EAEA,MAAMG,UAAU,GAAG,IAAIC,GAAJ,EAAnB;AAEA,aAAe;EACbC,EAAAA,GAAG,CAAC3F,OAAD,EAAU4F,GAAV,EAAeC,QAAf,EAAyB;EAC1B,QAAI,CAACJ,UAAU,CAACK,GAAX,CAAe9F,OAAf,CAAL,EAA8B;EAC5ByF,MAAAA,UAAU,CAACE,GAAX,CAAe3F,OAAf,EAAwB,IAAI0F,GAAJ,EAAxB;EACD;;EAED,UAAMK,WAAW,GAAGN,UAAU,CAACO,GAAX,CAAehG,OAAf,CAApB,CAL0B;EAQ1B;;EACA,QAAI,CAAC+F,WAAW,CAACD,GAAZ,CAAgBF,GAAhB,CAAD,IAAyBG,WAAW,CAACE,IAAZ,KAAqB,CAAlD,EAAqD;EACnD;EACAC,MAAAA,OAAO,CAACC,KAAR,CAAe,+EAA8EC,KAAK,CAACC,IAAN,CAAWN,WAAW,CAACxD,IAAZ,EAAX,EAA+B,CAA/B,CAAkC,GAA/H;EACA;EACD;;EAEDwD,IAAAA,WAAW,CAACJ,GAAZ,CAAgBC,GAAhB,EAAqBC,QAArB;EACD,GAjBY;;EAmBbG,EAAAA,GAAG,CAAChG,OAAD,EAAU4F,GAAV,EAAe;EAChB,QAAIH,UAAU,CAACK,GAAX,CAAe9F,OAAf,CAAJ,EAA6B;EAC3B,aAAOyF,UAAU,CAACO,GAAX,CAAehG,OAAf,EAAwBgG,GAAxB,CAA4BJ,GAA5B,KAAoC,IAA3C;EACD;;EAED,WAAO,IAAP;EACD,GAzBY;;EA2BbU,EAAAA,MAAM,CAACtG,OAAD,EAAU4F,GAAV,EAAe;EACnB,QAAI,CAACH,UAAU,CAACK,GAAX,CAAe9F,OAAf,CAAL,EAA8B;EAC5B;EACD;;EAED,UAAM+F,WAAW,GAAGN,UAAU,CAACO,GAAX,CAAehG,OAAf,CAApB;EAEA+F,IAAAA,WAAW,CAACQ,MAAZ,CAAmBX,GAAnB,EAPmB;;EAUnB,QAAIG,WAAW,CAACE,IAAZ,KAAqB,CAAzB,EAA4B;EAC1BR,MAAAA,UAAU,CAACc,MAAX,CAAkBvG,OAAlB;EACD;EACF;;EAxCY,CAAf;;ECfA;EACA;EACA;EACA;EACA;EACA;EAIA;EACA;EACA;EACA;EACA;;EAEA,MAAMwG,cAAc,GAAG,oBAAvB;EACA,MAAMC,cAAc,GAAG,MAAvB;EACA,MAAMC,aAAa,GAAG,QAAtB;EACA,MAAMC,aAAa,GAAG,EAAtB;;EACA,IAAIC,QAAQ,GAAG,CAAf;EACA,MAAMC,YAAY,GAAG;EACnBC,EAAAA,UAAU,EAAE,WADO;EAEnBC,EAAAA,UAAU,EAAE;EAFO,CAArB;EAIA,MAAMC,YAAY,GAAG,IAAIC,GAAJ,CAAQ,CAC3B,OAD2B,EAE3B,UAF2B,EAG3B,SAH2B,EAI3B,WAJ2B,EAK3B,aAL2B,EAM3B,YAN2B,EAO3B,gBAP2B,EAQ3B,WAR2B,EAS3B,UAT2B,EAU3B,WAV2B,EAW3B,aAX2B,EAY3B,WAZ2B,EAa3B,SAb2B,EAc3B,UAd2B,EAe3B,OAf2B,EAgB3B,mBAhB2B,EAiB3B,YAjB2B,EAkB3B,WAlB2B,EAmB3B,UAnB2B,EAoB3B,aApB2B,EAqB3B,aArB2B,EAsB3B,aAtB2B,EAuB3B,WAvB2B,EAwB3B,cAxB2B,EAyB3B,eAzB2B,EA0B3B,cA1B2B,EA2B3B,eA3B2B,EA4B3B,YA5B2B,EA6B3B,OA7B2B,EA8B3B,MA9B2B,EA+B3B,QA/B2B,EAgC3B,OAhC2B,EAiC3B,QAjC2B,EAkC3B,QAlC2B,EAmC3B,SAnC2B,EAoC3B,UApC2B,EAqC3B,MArC2B,EAsC3B,QAtC2B,EAuC3B,cAvC2B,EAwC3B,QAxC2B,EAyC3B,MAzC2B,EA0C3B,kBA1C2B,EA2C3B,kBA3C2B,EA4C3B,OA5C2B,EA6C3B,OA7C2B,EA8C3B,QA9C2B,CAAR,CAArB;EAiDA;EACA;EACA;EACA;EACA;;EAEA,SAASC,WAAT,CAAqBlH,OAArB,EAA8BmH,GAA9B,EAAmC;EACjC,SAAQA,GAAG,IAAK,GAAEA,GAAI,KAAIP,QAAQ,EAAG,EAA9B,IAAoC5G,OAAO,CAAC4G,QAA5C,IAAwDA,QAAQ,EAAvE;EACD;;EAED,SAASQ,QAAT,CAAkBpH,OAAlB,EAA2B;EACzB,QAAMmH,GAAG,GAAGD,WAAW,CAAClH,OAAD,CAAvB;EAEAA,EAAAA,OAAO,CAAC4G,QAAR,GAAmBO,GAAnB;EACAR,EAAAA,aAAa,CAACQ,GAAD,CAAb,GAAqBR,aAAa,CAACQ,GAAD,CAAb,IAAsB,EAA3C;EAEA,SAAOR,aAAa,CAACQ,GAAD,CAApB;EACD;;EAED,SAASE,gBAAT,CAA0BrH,OAA1B,EAAmCqF,EAAnC,EAAuC;EACrC,SAAO,SAASiC,OAAT,CAAiBC,KAAjB,EAAwB;EAC7BA,IAAAA,KAAK,CAACC,cAAN,GAAuBxH,OAAvB;;EAEA,QAAIsH,OAAO,CAACG,MAAZ,EAAoB;EAClBC,MAAAA,YAAY,CAACC,GAAb,CAAiB3H,OAAjB,EAA0BuH,KAAK,CAACK,IAAhC,EAAsCvC,EAAtC;EACD;;EAED,WAAOA,EAAE,CAACwC,KAAH,CAAS7H,OAAT,EAAkB,CAACuH,KAAD,CAAlB,CAAP;EACD,GARD;EASD;;EAED,SAASO,0BAAT,CAAoC9H,OAApC,EAA6CC,QAA7C,EAAuDoF,EAAvD,EAA2D;EACzD,SAAO,SAASiC,OAAT,CAAiBC,KAAjB,EAAwB;EAC7B,UAAMQ,WAAW,GAAG/H,OAAO,CAACgI,gBAAR,CAAyB/H,QAAzB,CAApB;;EAEA,SAAK,IAAI;EAAEgI,MAAAA;EAAF,QAAaV,KAAtB,EAA6BU,MAAM,IAAIA,MAAM,KAAK,IAAlD,EAAwDA,MAAM,GAAGA,MAAM,CAAC9E,UAAxE,EAAoF;EAClF,WAAK,IAAI+E,CAAC,GAAGH,WAAW,CAACI,MAAzB,EAAiCD,CAAC,EAAlC,GAAuC;EACrC,YAAIH,WAAW,CAACG,CAAD,CAAX,KAAmBD,MAAvB,EAA+B;EAC7BV,UAAAA,KAAK,CAACC,cAAN,GAAuBS,MAAvB;;EAEA,cAAIX,OAAO,CAACG,MAAZ,EAAoB;EAClB;EACAC,YAAAA,YAAY,CAACC,GAAb,CAAiB3H,OAAjB,EAA0BuH,KAAK,CAACK,IAAhC,EAAsCvC,EAAtC;EACD;;EAED,iBAAOA,EAAE,CAACwC,KAAH,CAASI,MAAT,EAAiB,CAACV,KAAD,CAAjB,CAAP;EACD;EACF;EACF,KAhB4B;;;EAmB7B,WAAO,IAAP;EACD,GApBD;EAqBD;;EAED,SAASa,WAAT,CAAqBC,MAArB,EAA6Bf,OAA7B,EAAsCgB,kBAAkB,GAAG,IAA3D,EAAiE;EAC/D,QAAMC,YAAY,GAAGjG,MAAM,CAACC,IAAP,CAAY8F,MAAZ,CAArB;;EAEA,OAAK,IAAIH,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAGD,YAAY,CAACJ,MAAnC,EAA2CD,CAAC,GAAGM,GAA/C,EAAoDN,CAAC,EAArD,EAAyD;EACvD,UAAMX,KAAK,GAAGc,MAAM,CAACE,YAAY,CAACL,CAAD,CAAb,CAApB;;EAEA,QAAIX,KAAK,CAACkB,eAAN,KAA0BnB,OAA1B,IAAqCC,KAAK,CAACe,kBAAN,KAA6BA,kBAAtE,EAA0F;EACxF,aAAOf,KAAP;EACD;EACF;;EAED,SAAO,IAAP;EACD;;EAED,SAASmB,eAAT,CAAyBC,iBAAzB,EAA4CrB,OAA5C,EAAqDsB,YAArD,EAAmE;EACjE,QAAMC,UAAU,GAAG,OAAOvB,OAAP,KAAmB,QAAtC;EACA,QAAMmB,eAAe,GAAGI,UAAU,GAAGD,YAAH,GAAkBtB,OAApD,CAFiE;;EAKjE,MAAIwB,SAAS,GAAGH,iBAAiB,CAACI,OAAlB,CAA0BtC,cAA1B,EAA0C,EAA1C,CAAhB;EACA,QAAMuC,MAAM,GAAGnC,YAAY,CAACiC,SAAD,CAA3B;;EAEA,MAAIE,MAAJ,EAAY;EACVF,IAAAA,SAAS,GAAGE,MAAZ;EACD;;EAED,QAAMC,QAAQ,GAAGjC,YAAY,CAAClB,GAAb,CAAiBgD,SAAjB,CAAjB;;EAEA,MAAI,CAACG,QAAL,EAAe;EACbH,IAAAA,SAAS,GAAGH,iBAAZ;EACD;;EAED,SAAO,CAACE,UAAD,EAAaJ,eAAb,EAA8BK,SAA9B,CAAP;EACD;;EAED,SAASI,UAAT,CAAoBlJ,OAApB,EAA6B2I,iBAA7B,EAAgDrB,OAAhD,EAAyDsB,YAAzD,EAAuEnB,MAAvE,EAA+E;EAC7E,MAAI,OAAOkB,iBAAP,KAA6B,QAA7B,IAAyC,CAAC3I,OAA9C,EAAuD;EACrD;EACD;;EAED,MAAI,CAACsH,OAAL,EAAc;EACZA,IAAAA,OAAO,GAAGsB,YAAV;EACAA,IAAAA,YAAY,GAAG,IAAf;EACD;;EAED,QAAM,CAACC,UAAD,EAAaJ,eAAb,EAA8BK,SAA9B,IAA2CJ,eAAe,CAACC,iBAAD,EAAoBrB,OAApB,EAA6BsB,YAA7B,CAAhE;EACA,QAAMP,MAAM,GAAGjB,QAAQ,CAACpH,OAAD,CAAvB;EACA,QAAMmJ,QAAQ,GAAGd,MAAM,CAACS,SAAD,CAAN,KAAsBT,MAAM,CAACS,SAAD,CAAN,GAAoB,EAA1C,CAAjB;EACA,QAAMM,UAAU,GAAGhB,WAAW,CAACe,QAAD,EAAWV,eAAX,EAA4BI,UAAU,GAAGvB,OAAH,GAAa,IAAnD,CAA9B;;EAEA,MAAI8B,UAAJ,EAAgB;EACdA,IAAAA,UAAU,CAAC3B,MAAX,GAAoB2B,UAAU,CAAC3B,MAAX,IAAqBA,MAAzC;EAEA;EACD;;EAED,QAAMN,GAAG,GAAGD,WAAW,CAACuB,eAAD,EAAkBE,iBAAiB,CAACI,OAAlB,CAA0BvC,cAA1B,EAA0C,EAA1C,CAAlB,CAAvB;EACA,QAAMnB,EAAE,GAAGwD,UAAU,GACnBf,0BAA0B,CAAC9H,OAAD,EAAUsH,OAAV,EAAmBsB,YAAnB,CADP,GAEnBvB,gBAAgB,CAACrH,OAAD,EAAUsH,OAAV,CAFlB;EAIAjC,EAAAA,EAAE,CAACiD,kBAAH,GAAwBO,UAAU,GAAGvB,OAAH,GAAa,IAA/C;EACAjC,EAAAA,EAAE,CAACoD,eAAH,GAAqBA,eAArB;EACApD,EAAAA,EAAE,CAACoC,MAAH,GAAYA,MAAZ;EACApC,EAAAA,EAAE,CAACuB,QAAH,GAAcO,GAAd;EACAgC,EAAAA,QAAQ,CAAChC,GAAD,CAAR,GAAgB9B,EAAhB;EAEArF,EAAAA,OAAO,CAACgC,gBAAR,CAAyB8G,SAAzB,EAAoCzD,EAApC,EAAwCwD,UAAxC;EACD;;EAED,SAASQ,aAAT,CAAuBrJ,OAAvB,EAAgCqI,MAAhC,EAAwCS,SAAxC,EAAmDxB,OAAnD,EAA4DgB,kBAA5D,EAAgF;EAC9E,QAAMjD,EAAE,GAAG+C,WAAW,CAACC,MAAM,CAACS,SAAD,CAAP,EAAoBxB,OAApB,EAA6BgB,kBAA7B,CAAtB;;EAEA,MAAI,CAACjD,EAAL,EAAS;EACP;EACD;;EAEDrF,EAAAA,OAAO,CAAC+B,mBAAR,CAA4B+G,SAA5B,EAAuCzD,EAAvC,EAA2CiE,OAAO,CAAChB,kBAAD,CAAlD;EACA,SAAOD,MAAM,CAACS,SAAD,CAAN,CAAkBzD,EAAE,CAACuB,QAArB,CAAP;EACD;;EAED,SAAS2C,wBAAT,CAAkCvJ,OAAlC,EAA2CqI,MAA3C,EAAmDS,SAAnD,EAA8DU,SAA9D,EAAyE;EACvE,QAAMC,iBAAiB,GAAGpB,MAAM,CAACS,SAAD,CAAN,IAAqB,EAA/C;EAEAxG,EAAAA,MAAM,CAACC,IAAP,CAAYkH,iBAAZ,EAA+BjH,OAA/B,CAAuCkH,UAAU,IAAI;EACnD,QAAIA,UAAU,CAACtJ,QAAX,CAAoBoJ,SAApB,CAAJ,EAAoC;EAClC,YAAMjC,KAAK,GAAGkC,iBAAiB,CAACC,UAAD,CAA/B;EAEAL,MAAAA,aAAa,CAACrJ,OAAD,EAAUqI,MAAV,EAAkBS,SAAlB,EAA6BvB,KAAK,CAACkB,eAAnC,EAAoDlB,KAAK,CAACe,kBAA1D,CAAb;EACD;EACF,GAND;EAOD;;EAED,MAAMZ,YAAY,GAAG;EACnBiC,EAAAA,EAAE,CAAC3J,OAAD,EAAUuH,KAAV,EAAiBD,OAAjB,EAA0BsB,YAA1B,EAAwC;EACxCM,IAAAA,UAAU,CAAClJ,OAAD,EAAUuH,KAAV,EAAiBD,OAAjB,EAA0BsB,YAA1B,EAAwC,KAAxC,CAAV;EACD,GAHkB;;EAKnBgB,EAAAA,GAAG,CAAC5J,OAAD,EAAUuH,KAAV,EAAiBD,OAAjB,EAA0BsB,YAA1B,EAAwC;EACzCM,IAAAA,UAAU,CAAClJ,OAAD,EAAUuH,KAAV,EAAiBD,OAAjB,EAA0BsB,YAA1B,EAAwC,IAAxC,CAAV;EACD,GAPkB;;EASnBjB,EAAAA,GAAG,CAAC3H,OAAD,EAAU2I,iBAAV,EAA6BrB,OAA7B,EAAsCsB,YAAtC,EAAoD;EACrD,QAAI,OAAOD,iBAAP,KAA6B,QAA7B,IAAyC,CAAC3I,OAA9C,EAAuD;EACrD;EACD;;EAED,UAAM,CAAC6I,UAAD,EAAaJ,eAAb,EAA8BK,SAA9B,IAA2CJ,eAAe,CAACC,iBAAD,EAAoBrB,OAApB,EAA6BsB,YAA7B,CAAhE;EACA,UAAMiB,WAAW,GAAGf,SAAS,KAAKH,iBAAlC;EACA,UAAMN,MAAM,GAAGjB,QAAQ,CAACpH,OAAD,CAAvB;EACA,UAAM8J,WAAW,GAAGnB,iBAAiB,CAACtI,UAAlB,CAA6B,GAA7B,CAApB;;EAEA,QAAI,OAAOoI,eAAP,KAA2B,WAA/B,EAA4C;EAC1C;EACA,UAAI,CAACJ,MAAD,IAAW,CAACA,MAAM,CAACS,SAAD,CAAtB,EAAmC;EACjC;EACD;;EAEDO,MAAAA,aAAa,CAACrJ,OAAD,EAAUqI,MAAV,EAAkBS,SAAlB,EAA6BL,eAA7B,EAA8CI,UAAU,GAAGvB,OAAH,GAAa,IAArE,CAAb;EACA;EACD;;EAED,QAAIwC,WAAJ,EAAiB;EACfxH,MAAAA,MAAM,CAACC,IAAP,CAAY8F,MAAZ,EAAoB7F,OAApB,CAA4BuH,YAAY,IAAI;EAC1CR,QAAAA,wBAAwB,CAACvJ,OAAD,EAAUqI,MAAV,EAAkB0B,YAAlB,EAAgCpB,iBAAiB,CAACqB,KAAlB,CAAwB,CAAxB,CAAhC,CAAxB;EACD,OAFD;EAGD;;EAED,UAAMP,iBAAiB,GAAGpB,MAAM,CAACS,SAAD,CAAN,IAAqB,EAA/C;EACAxG,IAAAA,MAAM,CAACC,IAAP,CAAYkH,iBAAZ,EAA+BjH,OAA/B,CAAuCyH,WAAW,IAAI;EACpD,YAAMP,UAAU,GAAGO,WAAW,CAAClB,OAAZ,CAAoBrC,aAApB,EAAmC,EAAnC,CAAnB;;EAEA,UAAI,CAACmD,WAAD,IAAgBlB,iBAAiB,CAACvI,QAAlB,CAA2BsJ,UAA3B,CAApB,EAA4D;EAC1D,cAAMnC,KAAK,GAAGkC,iBAAiB,CAACQ,WAAD,CAA/B;EAEAZ,QAAAA,aAAa,CAACrJ,OAAD,EAAUqI,MAAV,EAAkBS,SAAlB,EAA6BvB,KAAK,CAACkB,eAAnC,EAAoDlB,KAAK,CAACe,kBAA1D,CAAb;EACD;EACF,KARD;EASD,GA7CkB;;EA+CnB4B,EAAAA,OAAO,CAAClK,OAAD,EAAUuH,KAAV,EAAiB4C,IAAjB,EAAuB;EAC5B,QAAI,OAAO5C,KAAP,KAAiB,QAAjB,IAA6B,CAACvH,OAAlC,EAA2C;EACzC,aAAO,IAAP;EACD;;EAED,UAAMmF,CAAC,GAAGX,SAAS,EAAnB;EACA,UAAMsE,SAAS,GAAGvB,KAAK,CAACwB,OAAN,CAActC,cAAd,EAA8B,EAA9B,CAAlB;EACA,UAAMoD,WAAW,GAAGtC,KAAK,KAAKuB,SAA9B;EACA,UAAMG,QAAQ,GAAGjC,YAAY,CAAClB,GAAb,CAAiBgD,SAAjB,CAAjB;EAEA,QAAIsB,WAAJ;EACA,QAAIC,OAAO,GAAG,IAAd;EACA,QAAIC,cAAc,GAAG,IAArB;EACA,QAAIC,gBAAgB,GAAG,KAAvB;EACA,QAAIC,GAAG,GAAG,IAAV;;EAEA,QAAIX,WAAW,IAAI1E,CAAnB,EAAsB;EACpBiF,MAAAA,WAAW,GAAGjF,CAAC,CAAC7D,KAAF,CAAQiG,KAAR,EAAe4C,IAAf,CAAd;EAEAhF,MAAAA,CAAC,CAACnF,OAAD,CAAD,CAAWkK,OAAX,CAAmBE,WAAnB;EACAC,MAAAA,OAAO,GAAG,CAACD,WAAW,CAACK,oBAAZ,EAAX;EACAH,MAAAA,cAAc,GAAG,CAACF,WAAW,CAACM,6BAAZ,EAAlB;EACAH,MAAAA,gBAAgB,GAAGH,WAAW,CAACO,kBAAZ,EAAnB;EACD;;EAED,QAAI1B,QAAJ,EAAc;EACZuB,MAAAA,GAAG,GAAG3K,QAAQ,CAAC+K,WAAT,CAAqB,YAArB,CAAN;EACAJ,MAAAA,GAAG,CAACK,SAAJ,CAAc/B,SAAd,EAAyBuB,OAAzB,EAAkC,IAAlC;EACD,KAHD,MAGO;EACLG,MAAAA,GAAG,GAAG,IAAIM,WAAJ,CAAgBvD,KAAhB,EAAuB;EAC3B8C,QAAAA,OAD2B;EAE3BU,QAAAA,UAAU,EAAE;EAFe,OAAvB,CAAN;EAID,KAjC2B;;;EAoC5B,QAAI,OAAOZ,IAAP,KAAgB,WAApB,EAAiC;EAC/B7H,MAAAA,MAAM,CAACC,IAAP,CAAY4H,IAAZ,EAAkB3H,OAAlB,CAA0BoD,GAAG,IAAI;EAC/BtD,QAAAA,MAAM,CAAC0I,cAAP,CAAsBR,GAAtB,EAA2B5E,GAA3B,EAAgC;EAC9BI,UAAAA,GAAG,GAAG;EACJ,mBAAOmE,IAAI,CAACvE,GAAD,CAAX;EACD;;EAH6B,SAAhC;EAKD,OAND;EAOD;;EAED,QAAI2E,gBAAJ,EAAsB;EACpBC,MAAAA,GAAG,CAACS,cAAJ;EACD;;EAED,QAAIX,cAAJ,EAAoB;EAClBtK,MAAAA,OAAO,CAACqB,aAAR,CAAsBmJ,GAAtB;EACD;;EAED,QAAIA,GAAG,CAACD,gBAAJ,IAAwB,OAAOH,WAAP,KAAuB,WAAnD,EAAgE;EAC9DA,MAAAA,WAAW,CAACa,cAAZ;EACD;;EAED,WAAOT,GAAP;EACD;;EA1GkB,CAArB;;EC7NA;EACA;EACA;EACA;EACA;EACA;EAIA;EACA;EACA;EACA;EACA;;EAEA,MAAMU,OAAO,GAAG,aAAhB;;EAEA,MAAMC,aAAN,CAAoB;EAClBC,EAAAA,WAAW,CAACpL,OAAD,EAAU;EACnBA,IAAAA,OAAO,GAAG,OAAOA,OAAP,KAAmB,QAAnB,GAA8BH,QAAQ,CAACY,aAAT,CAAuBT,OAAvB,CAA9B,GAAgEA,OAA1E;;EAEA,QAAI,CAACA,OAAL,EAAc;EACZ;EACD;;EAED,SAAKqL,QAAL,GAAgBrL,OAAhB;EACAsL,IAAAA,IAAI,CAAC3F,GAAL,CAAS,KAAK0F,QAAd,EAAwB,KAAKD,WAAL,CAAiBG,QAAzC,EAAmD,IAAnD;EACD;;EAEDC,EAAAA,OAAO,GAAG;EACRF,IAAAA,IAAI,CAAChF,MAAL,CAAY,KAAK+E,QAAjB,EAA2B,KAAKD,WAAL,CAAiBG,QAA5C;EACA,SAAKF,QAAL,GAAgB,IAAhB;EACD;EAED;;;EAEkB,SAAXI,WAAW,CAACzL,OAAD,EAAU;EAC1B,WAAOsL,IAAI,CAACtF,GAAL,CAAShG,OAAT,EAAkB,KAAKuL,QAAvB,CAAP;EACD;;EAEiB,aAAPL,OAAO,GAAG;EACnB,WAAOA,OAAP;EACD;;EAzBiB;;ECjBpB;EACA;EACA;EACA;EACA;EACA;EAYA;EACA;EACA;EACA;EACA;;EAEA,MAAMQ,MAAI,GAAG,OAAb;EACA,MAAMH,UAAQ,GAAG,UAAjB;EACA,MAAMI,WAAS,GAAI,IAAGJ,UAAS,EAA/B;EACA,MAAMK,cAAY,GAAG,WAArB;EAEA,MAAMC,gBAAgB,GAAG,2BAAzB;EAEA,MAAMC,WAAW,GAAI,QAAOH,WAAU,EAAtC;EACA,MAAMI,YAAY,GAAI,SAAQJ,WAAU,EAAxC;EACA,MAAMK,sBAAoB,GAAI,QAAOL,WAAU,GAAEC,cAAa,EAA9D;EAEA,MAAMK,gBAAgB,GAAG,OAAzB;EACA,MAAMC,iBAAe,GAAG,MAAxB;EACA,MAAMC,iBAAe,GAAG,MAAxB;EAEA;EACA;EACA;EACA;EACA;;EAEA,MAAMC,KAAN,SAAoBjB,aAApB,CAAkC;EAChC;EAEmB,aAARI,QAAQ,GAAG;EACpB,WAAOA,UAAP;EACD,GAL+B;;;EAShCc,EAAAA,KAAK,CAACrM,OAAD,EAAU;EACb,UAAMsM,WAAW,GAAGtM,OAAO,GAAG,KAAKuM,eAAL,CAAqBvM,OAArB,CAAH,GAAmC,KAAKqL,QAAnE;;EACA,UAAMmB,WAAW,GAAG,KAAKC,kBAAL,CAAwBH,WAAxB,CAApB;;EAEA,QAAIE,WAAW,KAAK,IAAhB,IAAwBA,WAAW,CAACjC,gBAAxC,EAA0D;EACxD;EACD;;EAED,SAAKmC,cAAL,CAAoBJ,WAApB;EACD,GAlB+B;;;EAsBhCC,EAAAA,eAAe,CAACvM,OAAD,EAAU;EACvB,WAAOU,sBAAsB,CAACV,OAAD,CAAtB,IAAmCA,OAAO,CAAC2M,OAAR,CAAiB,IAAGV,gBAAiB,EAArC,CAA1C;EACD;;EAEDQ,EAAAA,kBAAkB,CAACzM,OAAD,EAAU;EAC1B,WAAO0H,YAAY,CAACwC,OAAb,CAAqBlK,OAArB,EAA8B8L,WAA9B,CAAP;EACD;;EAEDY,EAAAA,cAAc,CAAC1M,OAAD,EAAU;EACtBA,IAAAA,OAAO,CAAC2D,SAAR,CAAkB2C,MAAlB,CAAyB6F,iBAAzB;;EAEA,QAAI,CAACnM,OAAO,CAAC2D,SAAR,CAAkBC,QAAlB,CAA2BsI,iBAA3B,CAAL,EAAkD;EAChD,WAAKU,eAAL,CAAqB5M,OAArB;;EACA;EACD;;EAED,UAAMY,kBAAkB,GAAGD,gCAAgC,CAACX,OAAD,CAA3D;EAEA0H,IAAAA,YAAY,CAACkC,GAAb,CAAiB5J,OAAjB,EAA0B,eAA1B,EAA2C,MAAM,KAAK4M,eAAL,CAAqB5M,OAArB,CAAjD;EACAyB,IAAAA,oBAAoB,CAACzB,OAAD,EAAUY,kBAAV,CAApB;EACD;;EAEDgM,EAAAA,eAAe,CAAC5M,OAAD,EAAU;EACvB,QAAIA,OAAO,CAACmD,UAAZ,EAAwB;EACtBnD,MAAAA,OAAO,CAACmD,UAAR,CAAmB0J,WAAnB,CAA+B7M,OAA/B;EACD;;EAED0H,IAAAA,YAAY,CAACwC,OAAb,CAAqBlK,OAArB,EAA8B+L,YAA9B;EACD,GAlD+B;;;EAsDV,SAAfzG,eAAe,CAAClD,MAAD,EAAS;EAC7B,WAAO,KAAK0K,IAAL,CAAU,YAAY;EAC3B,UAAIC,IAAI,GAAGzB,IAAI,CAACtF,GAAL,CAAS,IAAT,EAAeuF,UAAf,CAAX;;EAEA,UAAI,CAACwB,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAIX,KAAJ,CAAU,IAAV,CAAP;EACD;;EAED,UAAIhK,MAAM,KAAK,OAAf,EAAwB;EACtB2K,QAAAA,IAAI,CAAC3K,MAAD,CAAJ,CAAa,IAAb;EACD;EACF,KAVM,CAAP;EAWD;;EAEmB,SAAb4K,aAAa,CAACC,aAAD,EAAgB;EAClC,WAAO,UAAU1F,KAAV,EAAiB;EACtB,UAAIA,KAAJ,EAAW;EACTA,QAAAA,KAAK,CAAC0D,cAAN;EACD;;EAEDgC,MAAAA,aAAa,CAACZ,KAAd,CAAoB,IAApB;EACD,KAND;EAOD;;EA5E+B;EA+ElC;EACA;EACA;EACA;EACA;;;EAEA3E,YAAY,CAACiC,EAAb,CAAgB9J,QAAhB,EAA0BmM,sBAA1B,EAAgDH,gBAAhD,EAAkEO,KAAK,CAACY,aAAN,CAAoB,IAAIZ,KAAJ,EAApB,CAAlE;EAEA;EACA;EACA;EACA;EACA;EACA;;EAEApH,kBAAkB,CAAC0G,MAAD,EAAOU,KAAP,CAAlB;;EC1IA;EACA;EACA;EACA;EACA;EACA;EAOA;EACA;EACA;EACA;EACA;;EAEA,MAAMV,MAAI,GAAG,QAAb;EACA,MAAMH,UAAQ,GAAG,WAAjB;EACA,MAAMI,WAAS,GAAI,IAAGJ,UAAS,EAA/B;EACA,MAAMK,cAAY,GAAG,WAArB;EAEA,MAAMsB,mBAAiB,GAAG,QAA1B;EAEA,MAAMC,sBAAoB,GAAG,2BAA7B;EAEA,MAAMnB,sBAAoB,GAAI,QAAOL,WAAU,GAAEC,cAAa,EAA9D;EAEA;EACA;EACA;EACA;EACA;;EAEA,MAAMwB,MAAN,SAAqBjC,aAArB,CAAmC;EACjC;EAEmB,aAARI,QAAQ,GAAG;EACpB,WAAOA,UAAP;EACD,GALgC;;;EASjC8B,EAAAA,MAAM,GAAG;EACP;EACA,SAAKhC,QAAL,CAAciC,YAAd,CAA2B,cAA3B,EAA2C,KAAKjC,QAAL,CAAc1H,SAAd,CAAwB0J,MAAxB,CAA+BH,mBAA/B,CAA3C;EACD,GAZgC;;;EAgBX,SAAf5H,eAAe,CAAClD,MAAD,EAAS;EAC7B,WAAO,KAAK0K,IAAL,CAAU,YAAY;EAC3B,UAAIC,IAAI,GAAGzB,IAAI,CAACtF,GAAL,CAAS,IAAT,EAAeuF,UAAf,CAAX;;EAEA,UAAI,CAACwB,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAIK,MAAJ,CAAW,IAAX,CAAP;EACD;;EAED,UAAIhL,MAAM,KAAK,QAAf,EAAyB;EACvB2K,QAAAA,IAAI,CAAC3K,MAAD,CAAJ;EACD;EACF,KAVM,CAAP;EAWD;;EA5BgC;EA+BnC;EACA;EACA;EACA;EACA;;;EAEAsF,YAAY,CAACiC,EAAb,CAAgB9J,QAAhB,EAA0BmM,sBAA1B,EAAgDmB,sBAAhD,EAAsE5F,KAAK,IAAI;EAC7EA,EAAAA,KAAK,CAAC0D,cAAN;EAEA,QAAMsC,MAAM,GAAGhG,KAAK,CAACU,MAAN,CAAa0E,OAAb,CAAqBQ,sBAArB,CAAf;EAEA,MAAIJ,IAAI,GAAGzB,IAAI,CAACtF,GAAL,CAASuH,MAAT,EAAiBhC,UAAjB,CAAX;;EACA,MAAI,CAACwB,IAAL,EAAW;EACTA,IAAAA,IAAI,GAAG,IAAIK,MAAJ,CAAWG,MAAX,CAAP;EACD;;EAEDR,EAAAA,IAAI,CAACM,MAAL;EACD,CAXD;EAaA;EACA;EACA;EACA;EACA;EACA;;EAEArI,kBAAkB,CAAC0G,MAAD,EAAO0B,MAAP,CAAlB;;EC5FA;EACA;EACA;EACA;EACA;EACA;EAEA,SAASI,aAAT,CAAuBC,GAAvB,EAA4B;EAC1B,MAAIA,GAAG,KAAK,MAAZ,EAAoB;EAClB,WAAO,IAAP;EACD;;EAED,MAAIA,GAAG,KAAK,OAAZ,EAAqB;EACnB,WAAO,KAAP;EACD;;EAED,MAAIA,GAAG,KAAKxM,MAAM,CAACwM,GAAD,CAAN,CAAYrO,QAAZ,EAAZ,EAAoC;EAClC,WAAO6B,MAAM,CAACwM,GAAD,CAAb;EACD;;EAED,MAAIA,GAAG,KAAK,EAAR,IAAcA,GAAG,KAAK,MAA1B,EAAkC;EAChC,WAAO,IAAP;EACD;;EAED,SAAOA,GAAP;EACD;;EAED,SAASC,gBAAT,CAA0B9H,GAA1B,EAA+B;EAC7B,SAAOA,GAAG,CAACmD,OAAJ,CAAY,QAAZ,EAAsB4E,GAAG,IAAK,IAAGA,GAAG,CAACpO,WAAJ,EAAkB,EAAnD,CAAP;EACD;;EAED,MAAMqO,WAAW,GAAG;EAClBC,EAAAA,gBAAgB,CAAC7N,OAAD,EAAU4F,GAAV,EAAejD,KAAf,EAAsB;EACpC3C,IAAAA,OAAO,CAACsN,YAAR,CAAsB,WAAUI,gBAAgB,CAAC9H,GAAD,CAAM,EAAtD,EAAyDjD,KAAzD;EACD,GAHiB;;EAKlBmL,EAAAA,mBAAmB,CAAC9N,OAAD,EAAU4F,GAAV,EAAe;EAChC5F,IAAAA,OAAO,CAAC+N,eAAR,CAAyB,WAAUL,gBAAgB,CAAC9H,GAAD,CAAM,EAAzD;EACD,GAPiB;;EASlBoI,EAAAA,iBAAiB,CAAChO,OAAD,EAAU;EACzB,QAAI,CAACA,OAAL,EAAc;EACZ,aAAO,EAAP;EACD;;EAED,UAAMiO,UAAU,GAAG,EAAnB;EAEA3L,IAAAA,MAAM,CAACC,IAAP,CAAYvC,OAAO,CAACkO,OAApB,EACGC,MADH,CACUvI,GAAG,IAAIA,GAAG,CAACvF,UAAJ,CAAe,IAAf,CADjB,EAEGmC,OAFH,CAEWoD,GAAG,IAAI;EACd,UAAIwI,OAAO,GAAGxI,GAAG,CAACmD,OAAJ,CAAY,KAAZ,EAAmB,EAAnB,CAAd;EACAqF,MAAAA,OAAO,GAAGA,OAAO,CAACC,MAAR,CAAe,CAAf,EAAkB9O,WAAlB,KAAkC6O,OAAO,CAACpE,KAAR,CAAc,CAAd,EAAiBoE,OAAO,CAACjG,MAAzB,CAA5C;EACA8F,MAAAA,UAAU,CAACG,OAAD,CAAV,GAAsBZ,aAAa,CAACxN,OAAO,CAACkO,OAAR,CAAgBtI,GAAhB,CAAD,CAAnC;EACD,KANH;EAQA,WAAOqI,UAAP;EACD,GAzBiB;;EA2BlBK,EAAAA,gBAAgB,CAACtO,OAAD,EAAU4F,GAAV,EAAe;EAC7B,WAAO4H,aAAa,CAACxN,OAAO,CAACE,YAAR,CAAsB,WAAUwN,gBAAgB,CAAC9H,GAAD,CAAM,EAAtD,CAAD,CAApB;EACD,GA7BiB;;EA+BlB2I,EAAAA,MAAM,CAACvO,OAAD,EAAU;EACd,UAAMwO,IAAI,GAAGxO,OAAO,CAACyO,qBAAR,EAAb;EAEA,WAAO;EACLC,MAAAA,GAAG,EAAEF,IAAI,CAACE,GAAL,GAAW7O,QAAQ,CAAC6E,IAAT,CAAciK,SADzB;EAELC,MAAAA,IAAI,EAAEJ,IAAI,CAACI,IAAL,GAAY/O,QAAQ,CAAC6E,IAAT,CAAcmK;EAF3B,KAAP;EAID,GAtCiB;;EAwClBC,EAAAA,QAAQ,CAAC9O,OAAD,EAAU;EAChB,WAAO;EACL0O,MAAAA,GAAG,EAAE1O,OAAO,CAAC+O,SADR;EAELH,MAAAA,IAAI,EAAE5O,OAAO,CAACgP;EAFT,KAAP;EAID;;EA7CiB,CAApB;;EC/BA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EAEA,MAAMC,SAAS,GAAG,CAAlB;EAEA,MAAMC,cAAc,GAAG;EACrBC,EAAAA,IAAI,CAAClP,QAAD,EAAWD,OAAO,GAAGH,QAAQ,CAACmE,eAA9B,EAA+C;EACjD,WAAO,GAAGoL,MAAH,CAAU,GAAGC,OAAO,CAACC,SAAR,CAAkBtH,gBAAlB,CAAmC3I,IAAnC,CAAwCW,OAAxC,EAAiDC,QAAjD,CAAb,CAAP;EACD,GAHoB;;EAKrBsP,EAAAA,OAAO,CAACtP,QAAD,EAAWD,OAAO,GAAGH,QAAQ,CAACmE,eAA9B,EAA+C;EACpD,WAAOqL,OAAO,CAACC,SAAR,CAAkB7O,aAAlB,CAAgCpB,IAAhC,CAAqCW,OAArC,EAA8CC,QAA9C,CAAP;EACD,GAPoB;;EASrBuP,EAAAA,QAAQ,CAACxP,OAAD,EAAUC,QAAV,EAAoB;EAC1B,WAAO,GAAGmP,MAAH,CAAU,GAAGpP,OAAO,CAACwP,QAArB,EACJrB,MADI,CACGsB,KAAK,IAAIA,KAAK,CAACC,OAAN,CAAczP,QAAd,CADZ,CAAP;EAED,GAZoB;;EAcrB0P,EAAAA,OAAO,CAAC3P,OAAD,EAAUC,QAAV,EAAoB;EACzB,UAAM0P,OAAO,GAAG,EAAhB;EAEA,QAAIC,QAAQ,GAAG5P,OAAO,CAACmD,UAAvB;;EAEA,WAAOyM,QAAQ,IAAIA,QAAQ,CAACpO,QAAT,KAAsBiC,IAAI,CAACC,YAAvC,IAAuDkM,QAAQ,CAACpO,QAAT,KAAsByN,SAApF,EAA+F;EAC7F,UAAIW,QAAQ,CAACF,OAAT,CAAiBzP,QAAjB,CAAJ,EAAgC;EAC9B0P,QAAAA,OAAO,CAACE,IAAR,CAAaD,QAAb;EACD;;EAEDA,MAAAA,QAAQ,GAAGA,QAAQ,CAACzM,UAApB;EACD;;EAED,WAAOwM,OAAP;EACD,GA5BoB;;EA8BrBG,EAAAA,IAAI,CAAC9P,OAAD,EAAUC,QAAV,EAAoB;EACtB,QAAI8P,QAAQ,GAAG/P,OAAO,CAACgQ,sBAAvB;;EAEA,WAAOD,QAAP,EAAiB;EACf,UAAIA,QAAQ,CAACL,OAAT,CAAiBzP,QAAjB,CAAJ,EAAgC;EAC9B,eAAO,CAAC8P,QAAD,CAAP;EACD;;EAEDA,MAAAA,QAAQ,GAAGA,QAAQ,CAACC,sBAApB;EACD;;EAED,WAAO,EAAP;EACD,GA1CoB;;EA4CrBC,EAAAA,IAAI,CAACjQ,OAAD,EAAUC,QAAV,EAAoB;EACtB,QAAIgQ,IAAI,GAAGjQ,OAAO,CAACkQ,kBAAnB;;EAEA,WAAOD,IAAP,EAAa;EACX,UAAIA,IAAI,CAACP,OAAL,CAAazP,QAAb,CAAJ,EAA4B;EAC1B,eAAO,CAACgQ,IAAD,CAAP;EACD;;EAEDA,MAAAA,IAAI,GAAGA,IAAI,CAACC,kBAAZ;EACD;;EAED,WAAO,EAAP;EACD;;EAxDoB,CAAvB;;ECfA;EACA;EACA;EACA;EACA;EACA;EAmBA;EACA;EACA;EACA;EACA;;EAEA,MAAMxE,MAAI,GAAG,UAAb;EACA,MAAMH,UAAQ,GAAG,aAAjB;EACA,MAAMI,WAAS,GAAI,IAAGJ,UAAS,EAA/B;EACA,MAAMK,cAAY,GAAG,WAArB;EAEA,MAAMuE,cAAc,GAAG,WAAvB;EACA,MAAMC,eAAe,GAAG,YAAxB;EACA,MAAMC,sBAAsB,GAAG,GAA/B;;EACA,MAAMC,eAAe,GAAG,EAAxB;EAEA,MAAMC,SAAO,GAAG;EACdC,EAAAA,QAAQ,EAAE,IADI;EAEdC,EAAAA,QAAQ,EAAE,IAFI;EAGdC,EAAAA,KAAK,EAAE,KAHO;EAIdC,EAAAA,KAAK,EAAE,OAJO;EAKdC,EAAAA,IAAI,EAAE,IALQ;EAMdC,EAAAA,KAAK,EAAE;EANO,CAAhB;EASA,MAAMC,aAAW,GAAG;EAClBN,EAAAA,QAAQ,EAAE,kBADQ;EAElBC,EAAAA,QAAQ,EAAE,SAFQ;EAGlBC,EAAAA,KAAK,EAAE,kBAHW;EAIlBC,EAAAA,KAAK,EAAE,kBAJW;EAKlBC,EAAAA,IAAI,EAAE,SALY;EAMlBC,EAAAA,KAAK,EAAE;EANW,CAApB;EASA,MAAME,UAAU,GAAG,MAAnB;EACA,MAAMC,UAAU,GAAG,MAAnB;EACA,MAAMC,cAAc,GAAG,MAAvB;EACA,MAAMC,eAAe,GAAG,OAAxB;EAEA,MAAMC,WAAW,GAAI,QAAOxF,WAAU,EAAtC;EACA,MAAMyF,UAAU,GAAI,OAAMzF,WAAU,EAApC;EACA,MAAM0F,aAAa,GAAI,UAAS1F,WAAU,EAA1C;EACA,MAAM2F,gBAAgB,GAAI,aAAY3F,WAAU,EAAhD;EACA,MAAM4F,gBAAgB,GAAI,aAAY5F,WAAU,EAAhD;EACA,MAAM6F,gBAAgB,GAAI,aAAY7F,WAAU,EAAhD;EACA,MAAM8F,eAAe,GAAI,YAAW9F,WAAU,EAA9C;EACA,MAAM+F,cAAc,GAAI,WAAU/F,WAAU,EAA5C;EACA,MAAMgG,iBAAiB,GAAI,cAAahG,WAAU,EAAlD;EACA,MAAMiG,eAAe,GAAI,YAAWjG,WAAU,EAA9C;EACA,MAAMkG,gBAAgB,GAAI,YAAWlG,WAAU,EAA/C;EACA,MAAMmG,qBAAmB,GAAI,OAAMnG,WAAU,GAAEC,cAAa,EAA5D;EACA,MAAMI,sBAAoB,GAAI,QAAOL,WAAU,GAAEC,cAAa,EAA9D;EAEA,MAAMmG,mBAAmB,GAAG,UAA5B;EACA,MAAM7E,mBAAiB,GAAG,QAA1B;EACA,MAAM8E,gBAAgB,GAAG,OAAzB;EACA,MAAMC,cAAc,GAAG,mBAAvB;EACA,MAAMC,gBAAgB,GAAG,qBAAzB;EACA,MAAMC,eAAe,GAAG,oBAAxB;EACA,MAAMC,eAAe,GAAG,oBAAxB;EACA,MAAMC,wBAAwB,GAAG,eAAjC;EAEA,MAAMC,iBAAe,GAAG,SAAxB;EACA,MAAMC,oBAAoB,GAAG,uBAA7B;EACA,MAAMC,aAAa,GAAG,gBAAtB;EACA,MAAMC,iBAAiB,GAAG,oBAA1B;EACA,MAAMC,kBAAkB,GAAG,0CAA3B;EACA,MAAMC,mBAAmB,GAAG,sBAA5B;EACA,MAAMC,kBAAkB,GAAG,kBAA3B;EACA,MAAMC,mBAAmB,GAAG,qCAA5B;EACA,MAAMC,kBAAkB,GAAG,2BAA3B;EAEA,MAAMC,kBAAkB,GAAG,OAA3B;EACA,MAAMC,gBAAgB,GAAG,KAAzB;EAEA;EACA;EACA;EACA;EACA;;EACA,MAAMC,QAAN,SAAuB9H,aAAvB,CAAqC;EACnCC,EAAAA,WAAW,CAACpL,OAAD,EAAUoC,MAAV,EAAkB;EAC3B,UAAMpC,OAAN;EAEA,SAAKkT,MAAL,GAAc,IAAd;EACA,SAAKC,SAAL,GAAiB,IAAjB;EACA,SAAKC,cAAL,GAAsB,IAAtB;EACA,SAAKC,SAAL,GAAiB,KAAjB;EACA,SAAKC,UAAL,GAAkB,KAAlB;EACA,SAAKC,YAAL,GAAoB,IAApB;EACA,SAAKC,WAAL,GAAmB,CAAnB;EACA,SAAKC,WAAL,GAAmB,CAAnB;EAEA,SAAKC,OAAL,GAAe,KAAKC,UAAL,CAAgBvR,MAAhB,CAAf;EACA,SAAKwR,kBAAL,GAA0B1E,cAAc,CAACK,OAAf,CAAuBoD,mBAAvB,EAA4C,KAAKtH,QAAjD,CAA1B;EACA,SAAKwI,eAAL,GAAuB,kBAAkBhU,QAAQ,CAACmE,eAA3B,IAA8C8P,SAAS,CAACC,cAAV,GAA2B,CAAhG;EACA,SAAKC,aAAL,GAAqB1K,OAAO,CAACxI,MAAM,CAACmT,YAAR,CAA5B;;EAEA,SAAKC,kBAAL;EACD,GAnBkC;;;EAuBjB,aAAP3D,OAAO,GAAG;EACnB,WAAOA,SAAP;EACD;;EAEkB,aAARhF,QAAQ,GAAG;EACpB,WAAOA,UAAP;EACD,GA7BkC;;;EAiCnC0E,EAAAA,IAAI,GAAG;EACL,QAAI,CAAC,KAAKqD,UAAV,EAAsB;EACpB,WAAKa,MAAL,CAAYpD,UAAZ;EACD;EACF;;EAEDqD,EAAAA,eAAe,GAAG;EAChB;EACA;EACA,QAAI,CAACvU,QAAQ,CAACwU,MAAV,IAAoBpR,SAAS,CAAC,KAAKoI,QAAN,CAAjC,EAAkD;EAChD,WAAK4E,IAAL;EACD;EACF;;EAEDH,EAAAA,IAAI,GAAG;EACL,QAAI,CAAC,KAAKwD,UAAV,EAAsB;EACpB,WAAKa,MAAL,CAAYnD,UAAZ;EACD;EACF;;EAEDL,EAAAA,KAAK,CAACpJ,KAAD,EAAQ;EACX,QAAI,CAACA,KAAL,EAAY;EACV,WAAK8L,SAAL,GAAiB,IAAjB;EACD;;EAED,QAAInE,cAAc,CAACK,OAAf,CAAuBmD,kBAAvB,EAA2C,KAAKrH,QAAhD,CAAJ,EAA+D;EAC7DjK,MAAAA,oBAAoB,CAAC,KAAKiK,QAAN,CAApB;EACA,WAAKiJ,KAAL,CAAW,IAAX;EACD;;EAEDC,IAAAA,aAAa,CAAC,KAAKpB,SAAN,CAAb;EACA,SAAKA,SAAL,GAAiB,IAAjB;EACD;;EAEDmB,EAAAA,KAAK,CAAC/M,KAAD,EAAQ;EACX,QAAI,CAACA,KAAL,EAAY;EACV,WAAK8L,SAAL,GAAiB,KAAjB;EACD;;EAED,QAAI,KAAKF,SAAT,EAAoB;EAClBoB,MAAAA,aAAa,CAAC,KAAKpB,SAAN,CAAb;EACA,WAAKA,SAAL,GAAiB,IAAjB;EACD;;EAED,QAAI,KAAKO,OAAL,IAAgB,KAAKA,OAAL,CAAalD,QAA7B,IAAyC,CAAC,KAAK6C,SAAnD,EAA8D;EAC5D,WAAKmB,eAAL;;EAEA,WAAKrB,SAAL,GAAiBsB,WAAW,CAC1B,CAAC5U,QAAQ,CAAC6U,eAAT,GAA2B,KAAKN,eAAhC,GAAkD,KAAKnE,IAAxD,EAA8D0E,IAA9D,CAAmE,IAAnE,CAD0B,EAE1B,KAAKjB,OAAL,CAAalD,QAFa,CAA5B;EAID;EACF;;EAEDoE,EAAAA,EAAE,CAACC,KAAD,EAAQ;EACR,SAAKzB,cAAL,GAAsBlE,cAAc,CAACK,OAAf,CAAuBgD,oBAAvB,EAA6C,KAAKlH,QAAlD,CAAtB;;EACA,UAAMyJ,WAAW,GAAG,KAAKC,aAAL,CAAmB,KAAK3B,cAAxB,CAApB;;EAEA,QAAIyB,KAAK,GAAG,KAAK3B,MAAL,CAAY/K,MAAZ,GAAqB,CAA7B,IAAkC0M,KAAK,GAAG,CAA9C,EAAiD;EAC/C;EACD;;EAED,QAAI,KAAKvB,UAAT,EAAqB;EACnB5L,MAAAA,YAAY,CAACkC,GAAb,CAAiB,KAAKyB,QAAtB,EAAgC+F,UAAhC,EAA4C,MAAM,KAAKwD,EAAL,CAAQC,KAAR,CAAlD;EACA;EACD;;EAED,QAAIC,WAAW,KAAKD,KAApB,EAA2B;EACzB,WAAKlE,KAAL;EACA,WAAK2D,KAAL;EACA;EACD;;EAED,UAAMU,KAAK,GAAGH,KAAK,GAAGC,WAAR,GACZ/D,UADY,GAEZC,UAFF;;EAIA,SAAKmD,MAAL,CAAYa,KAAZ,EAAmB,KAAK9B,MAAL,CAAY2B,KAAZ,CAAnB;EACD;;EAEDrJ,EAAAA,OAAO,GAAG;EACR9D,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK0D,QAAtB,EAAgCM,WAAhC;EAEA,SAAKuH,MAAL,GAAc,IAAd;EACA,SAAKQ,OAAL,GAAe,IAAf;EACA,SAAKP,SAAL,GAAiB,IAAjB;EACA,SAAKE,SAAL,GAAiB,IAAjB;EACA,SAAKC,UAAL,GAAkB,IAAlB;EACA,SAAKF,cAAL,GAAsB,IAAtB;EACA,SAAKQ,kBAAL,GAA0B,IAA1B;EAEA,UAAMpI,OAAN;EACD,GA7HkC;;;EAiInCmI,EAAAA,UAAU,CAACvR,MAAD,EAAS;EACjBA,IAAAA,MAAM,GAAG,EACP,GAAGmO,SADI;EAEP,SAAGnO;EAFI,KAAT;EAIAF,IAAAA,eAAe,CAACwJ,MAAD,EAAOtJ,MAAP,EAAe0O,aAAf,CAAf;EACA,WAAO1O,MAAP;EACD;;EAED6S,EAAAA,YAAY,GAAG;EACb,UAAMC,SAAS,GAAGxV,IAAI,CAACyV,GAAL,CAAS,KAAK1B,WAAd,CAAlB;;EAEA,QAAIyB,SAAS,IAAI5E,eAAjB,EAAkC;EAChC;EACD;;EAED,UAAM8E,SAAS,GAAGF,SAAS,GAAG,KAAKzB,WAAnC;EAEA,SAAKA,WAAL,GAAmB,CAAnB;;EAEA,QAAI,CAAC2B,SAAL,EAAgB;EACd;EACD;;EAED,SAAKjB,MAAL,CAAYiB,SAAS,GAAG,CAAZ,GAAgBlE,eAAhB,GAAkCD,cAA9C;EACD;;EAEDiD,EAAAA,kBAAkB,GAAG;EACnB,QAAI,KAAKR,OAAL,CAAajD,QAAjB,EAA2B;EACzB/I,MAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+BgG,aAA/B,EAA8C9J,KAAK,IAAI,KAAK8N,QAAL,CAAc9N,KAAd,CAAvD;EACD;;EAED,QAAI,KAAKmM,OAAL,CAAa/C,KAAb,KAAuB,OAA3B,EAAoC;EAClCjJ,MAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+BiG,gBAA/B,EAAiD/J,KAAK,IAAI,KAAKoJ,KAAL,CAAWpJ,KAAX,CAA1D;EACAG,MAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+BkG,gBAA/B,EAAiDhK,KAAK,IAAI,KAAK+M,KAAL,CAAW/M,KAAX,CAA1D;EACD;;EAED,QAAI,KAAKmM,OAAL,CAAa7C,KAAb,IAAsB,KAAKgD,eAA/B,EAAgD;EAC9C,WAAKyB,uBAAL;EACD;EACF;;EAEDA,EAAAA,uBAAuB,GAAG;EACxB,UAAMC,KAAK,GAAGhO,KAAK,IAAI;EACrB,UAAI,KAAKyM,aAAL,KAAuBzM,KAAK,CAACiO,WAAN,KAAsBxC,gBAAtB,IAA0CzL,KAAK,CAACiO,WAAN,KAAsBzC,kBAAvF,CAAJ,EAAgH;EAC9G,aAAKS,WAAL,GAAmBjM,KAAK,CAACkO,OAAzB;EACD,OAFD,MAEO,IAAI,CAAC,KAAKzB,aAAV,EAAyB;EAC9B,aAAKR,WAAL,GAAmBjM,KAAK,CAACmO,OAAN,CAAc,CAAd,EAAiBD,OAApC;EACD;EACF,KAND;;EAQA,UAAME,IAAI,GAAGpO,KAAK,IAAI;EACpB;EACA,WAAKkM,WAAL,GAAmBlM,KAAK,CAACmO,OAAN,IAAiBnO,KAAK,CAACmO,OAAN,CAAcvN,MAAd,GAAuB,CAAxC,GACjB,CADiB,GAEjBZ,KAAK,CAACmO,OAAN,CAAc,CAAd,EAAiBD,OAAjB,GAA2B,KAAKjC,WAFlC;EAGD,KALD;;EAOA,UAAMoC,GAAG,GAAGrO,KAAK,IAAI;EACnB,UAAI,KAAKyM,aAAL,KAAuBzM,KAAK,CAACiO,WAAN,KAAsBxC,gBAAtB,IAA0CzL,KAAK,CAACiO,WAAN,KAAsBzC,kBAAvF,CAAJ,EAAgH;EAC9G,aAAKU,WAAL,GAAmBlM,KAAK,CAACkO,OAAN,GAAgB,KAAKjC,WAAxC;EACD;;EAED,WAAKyB,YAAL;;EACA,UAAI,KAAKvB,OAAL,CAAa/C,KAAb,KAAuB,OAA3B,EAAoC;EAClC;EACA;EACA;EACA;EACA;EACA;EACA;EAEA,aAAKA,KAAL;;EACA,YAAI,KAAK4C,YAAT,EAAuB;EACrBsC,UAAAA,YAAY,CAAC,KAAKtC,YAAN,CAAZ;EACD;;EAED,aAAKA,YAAL,GAAoBtR,UAAU,CAACsF,KAAK,IAAI,KAAK+M,KAAL,CAAW/M,KAAX,CAAV,EAA6B8I,sBAAsB,GAAG,KAAKqD,OAAL,CAAalD,QAAnE,CAA9B;EACD;EACF,KAtBD;;EAwBAtB,IAAAA,cAAc,CAACC,IAAf,CAAoBsD,iBAApB,EAAuC,KAAKpH,QAA5C,EAAsD7I,OAAtD,CAA8DsT,OAAO,IAAI;EACvEpO,MAAAA,YAAY,CAACiC,EAAb,CAAgBmM,OAAhB,EAAyBjE,gBAAzB,EAA2CkE,CAAC,IAAIA,CAAC,CAAC9K,cAAF,EAAhD;EACD,KAFD;;EAIA,QAAI,KAAK+I,aAAT,EAAwB;EACtBtM,MAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+BsG,iBAA/B,EAAkDpK,KAAK,IAAIgO,KAAK,CAAChO,KAAD,CAAhE;EACAG,MAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+BuG,eAA/B,EAAgDrK,KAAK,IAAIqO,GAAG,CAACrO,KAAD,CAA5D;;EAEA,WAAK8D,QAAL,CAAc1H,SAAd,CAAwBqS,GAAxB,CAA4B3D,wBAA5B;EACD,KALD,MAKO;EACL3K,MAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+BmG,gBAA/B,EAAiDjK,KAAK,IAAIgO,KAAK,CAAChO,KAAD,CAA/D;EACAG,MAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+BoG,eAA/B,EAAgDlK,KAAK,IAAIoO,IAAI,CAACpO,KAAD,CAA7D;EACAG,MAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+BqG,cAA/B,EAA+CnK,KAAK,IAAIqO,GAAG,CAACrO,KAAD,CAA3D;EACD;EACF;;EAED8N,EAAAA,QAAQ,CAAC9N,KAAD,EAAQ;EACd,QAAI,kBAAkBzE,IAAlB,CAAuByE,KAAK,CAACU,MAAN,CAAagO,OAApC,CAAJ,EAAkD;EAChD;EACD;;EAED,QAAI1O,KAAK,CAAC3B,GAAN,KAAcuK,cAAlB,EAAkC;EAChC5I,MAAAA,KAAK,CAAC0D,cAAN;;EACA,WAAKkJ,MAAL,CAAYlD,cAAZ;EACD,KAHD,MAGO,IAAI1J,KAAK,CAAC3B,GAAN,KAAcwK,eAAlB,EAAmC;EACxC7I,MAAAA,KAAK,CAAC0D,cAAN;;EACA,WAAKkJ,MAAL,CAAYjD,eAAZ;EACD;EACF;;EAED6D,EAAAA,aAAa,CAAC/U,OAAD,EAAU;EACrB,SAAKkT,MAAL,GAAclT,OAAO,IAAIA,OAAO,CAACmD,UAAnB,GACZ+L,cAAc,CAACC,IAAf,CAAoBqD,aAApB,EAAmCxS,OAAO,CAACmD,UAA3C,CADY,GAEZ,EAFF;EAIA,WAAO,KAAK+P,MAAL,CAAYgD,OAAZ,CAAoBlW,OAApB,CAAP;EACD;;EAEDmW,EAAAA,eAAe,CAACnB,KAAD,EAAQoB,aAAR,EAAuB;EACpC,UAAMC,MAAM,GAAGrB,KAAK,KAAKjE,UAAzB;EACA,UAAMuF,MAAM,GAAGtB,KAAK,KAAKhE,UAAzB;;EACA,UAAM8D,WAAW,GAAG,KAAKC,aAAL,CAAmBqB,aAAnB,CAApB;;EACA,UAAMG,aAAa,GAAG,KAAKrD,MAAL,CAAY/K,MAAZ,GAAqB,CAA3C;EACA,UAAMqO,aAAa,GAAIF,MAAM,IAAIxB,WAAW,KAAK,CAA3B,IAAkCuB,MAAM,IAAIvB,WAAW,KAAKyB,aAAlF;;EAEA,QAAIC,aAAa,IAAI,CAAC,KAAK9C,OAAL,CAAa9C,IAAnC,EAAyC;EACvC,aAAOwF,aAAP;EACD;;EAED,UAAMK,KAAK,GAAGH,MAAM,GAAG,CAAC,CAAJ,GAAQ,CAA5B;EACA,UAAMI,SAAS,GAAG,CAAC5B,WAAW,GAAG2B,KAAf,IAAwB,KAAKvD,MAAL,CAAY/K,MAAtD;EAEA,WAAOuO,SAAS,KAAK,CAAC,CAAf,GACL,KAAKxD,MAAL,CAAY,KAAKA,MAAL,CAAY/K,MAAZ,GAAqB,CAAjC,CADK,GAEL,KAAK+K,MAAL,CAAYwD,SAAZ,CAFF;EAGD;;EAEDC,EAAAA,kBAAkB,CAACC,aAAD,EAAgBC,kBAAhB,EAAoC;EACpD,UAAMC,WAAW,GAAG,KAAK/B,aAAL,CAAmB6B,aAAnB,CAApB;;EACA,UAAMG,SAAS,GAAG,KAAKhC,aAAL,CAAmB7F,cAAc,CAACK,OAAf,CAAuBgD,oBAAvB,EAA6C,KAAKlH,QAAlD,CAAnB,CAAlB;;EAEA,WAAO3D,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoC8F,WAApC,EAAiD;EACtDyF,MAAAA,aADsD;EAEtDxB,MAAAA,SAAS,EAAEyB,kBAF2C;EAGtDxQ,MAAAA,IAAI,EAAE0Q,SAHgD;EAItDnC,MAAAA,EAAE,EAAEkC;EAJkD,KAAjD,CAAP;EAMD;;EAEDE,EAAAA,0BAA0B,CAAChX,OAAD,EAAU;EAClC,QAAI,KAAK4T,kBAAT,EAA6B;EAC3B,YAAMqD,eAAe,GAAG/H,cAAc,CAACK,OAAf,CAAuB+C,iBAAvB,EAAwC,KAAKsB,kBAA7C,CAAxB;EAEAqD,MAAAA,eAAe,CAACtT,SAAhB,CAA0B2C,MAA1B,CAAiC4G,mBAAjC;EACA+J,MAAAA,eAAe,CAAClJ,eAAhB,CAAgC,cAAhC;EAEA,YAAMmJ,UAAU,GAAGhI,cAAc,CAACC,IAAf,CAAoByD,kBAApB,EAAwC,KAAKgB,kBAA7C,CAAnB;;EAEA,WAAK,IAAI1L,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGgP,UAAU,CAAC/O,MAA/B,EAAuCD,CAAC,EAAxC,EAA4C;EAC1C,YAAIjH,MAAM,CAACkW,QAAP,CAAgBD,UAAU,CAAChP,CAAD,CAAV,CAAchI,YAAd,CAA2B,kBAA3B,CAAhB,EAAgE,EAAhE,MAAwE,KAAK6U,aAAL,CAAmB/U,OAAnB,CAA5E,EAAyG;EACvGkX,UAAAA,UAAU,CAAChP,CAAD,CAAV,CAAcvE,SAAd,CAAwBqS,GAAxB,CAA4B9I,mBAA5B;EACAgK,UAAAA,UAAU,CAAChP,CAAD,CAAV,CAAcoF,YAAd,CAA2B,cAA3B,EAA2C,MAA3C;EACA;EACD;EACF;EACF;EACF;;EAEDkH,EAAAA,eAAe,GAAG;EAChB,UAAMxU,OAAO,GAAG,KAAKoT,cAAL,IAAuBlE,cAAc,CAACK,OAAf,CAAuBgD,oBAAvB,EAA6C,KAAKlH,QAAlD,CAAvC;;EAEA,QAAI,CAACrL,OAAL,EAAc;EACZ;EACD;;EAED,UAAMoX,eAAe,GAAGnW,MAAM,CAACkW,QAAP,CAAgBnX,OAAO,CAACE,YAAR,CAAqB,kBAArB,CAAhB,EAA0D,EAA1D,CAAxB;;EAEA,QAAIkX,eAAJ,EAAqB;EACnB,WAAK1D,OAAL,CAAa2D,eAAb,GAA+B,KAAK3D,OAAL,CAAa2D,eAAb,IAAgC,KAAK3D,OAAL,CAAalD,QAA5E;EACA,WAAKkD,OAAL,CAAalD,QAAb,GAAwB4G,eAAxB;EACD,KAHD,MAGO;EACL,WAAK1D,OAAL,CAAalD,QAAb,GAAwB,KAAKkD,OAAL,CAAa2D,eAAb,IAAgC,KAAK3D,OAAL,CAAalD,QAArE;EACD;EACF;;EAED2D,EAAAA,MAAM,CAACmD,gBAAD,EAAmBtX,OAAnB,EAA4B;EAChC,UAAMgV,KAAK,GAAG,KAAKuC,iBAAL,CAAuBD,gBAAvB,CAAd;;EACA,UAAMlB,aAAa,GAAGlH,cAAc,CAACK,OAAf,CAAuBgD,oBAAvB,EAA6C,KAAKlH,QAAlD,CAAtB;;EACA,UAAMmM,kBAAkB,GAAG,KAAKzC,aAAL,CAAmBqB,aAAnB,CAA3B;;EACA,UAAMqB,WAAW,GAAGzX,OAAO,IAAI,KAAKmW,eAAL,CAAqBnB,KAArB,EAA4BoB,aAA5B,CAA/B;;EAEA,UAAMsB,gBAAgB,GAAG,KAAK3C,aAAL,CAAmB0C,WAAnB,CAAzB;;EACA,UAAME,SAAS,GAAGrO,OAAO,CAAC,KAAK6J,SAAN,CAAzB;EAEA,UAAMkD,MAAM,GAAGrB,KAAK,KAAKjE,UAAzB;EACA,UAAM6G,oBAAoB,GAAGvB,MAAM,GAAGnE,gBAAH,GAAsBD,cAAzD;EACA,UAAM4F,cAAc,GAAGxB,MAAM,GAAGlE,eAAH,GAAqBC,eAAlD;;EACA,UAAMyE,kBAAkB,GAAG,KAAKiB,iBAAL,CAAuB9C,KAAvB,CAA3B;;EAEA,QAAIyC,WAAW,IAAIA,WAAW,CAAC9T,SAAZ,CAAsBC,QAAtB,CAA+BsJ,mBAA/B,CAAnB,EAAsE;EACpE,WAAKoG,UAAL,GAAkB,KAAlB;EACA;EACD;;EAED,UAAMyE,UAAU,GAAG,KAAKpB,kBAAL,CAAwBc,WAAxB,EAAqCZ,kBAArC,CAAnB;;EACA,QAAIkB,UAAU,CAACxN,gBAAf,EAAiC;EAC/B;EACD;;EAED,QAAI,CAAC6L,aAAD,IAAkB,CAACqB,WAAvB,EAAoC;EAClC;EACA;EACD;;EAED,SAAKnE,UAAL,GAAkB,IAAlB;;EAEA,QAAIqE,SAAJ,EAAe;EACb,WAAKhH,KAAL;EACD;;EAED,SAAKqG,0BAAL,CAAgCS,WAAhC;;EACA,SAAKrE,cAAL,GAAsBqE,WAAtB;;EAEA,QAAI,KAAKpM,QAAL,CAAc1H,SAAd,CAAwBC,QAAxB,CAAiCoO,gBAAjC,CAAJ,EAAwD;EACtDyF,MAAAA,WAAW,CAAC9T,SAAZ,CAAsBqS,GAAtB,CAA0B6B,cAA1B;EAEAvT,MAAAA,MAAM,CAACmT,WAAD,CAAN;EAEArB,MAAAA,aAAa,CAACzS,SAAd,CAAwBqS,GAAxB,CAA4B4B,oBAA5B;EACAH,MAAAA,WAAW,CAAC9T,SAAZ,CAAsBqS,GAAtB,CAA0B4B,oBAA1B;EAEA,YAAMhX,kBAAkB,GAAGD,gCAAgC,CAACyV,aAAD,CAA3D;EAEA1O,MAAAA,YAAY,CAACkC,GAAb,CAAiBwM,aAAjB,EAAgC,eAAhC,EAAiD,MAAM;EACrDqB,QAAAA,WAAW,CAAC9T,SAAZ,CAAsB2C,MAAtB,CAA6BsR,oBAA7B,EAAmDC,cAAnD;EACAJ,QAAAA,WAAW,CAAC9T,SAAZ,CAAsBqS,GAAtB,CAA0B9I,mBAA1B;EAEAkJ,QAAAA,aAAa,CAACzS,SAAd,CAAwB2C,MAAxB,CAA+B4G,mBAA/B,EAAkD2K,cAAlD,EAAkED,oBAAlE;EAEA,aAAKtE,UAAL,GAAkB,KAAlB;EAEArR,QAAAA,UAAU,CAAC,MAAM;EACfyF,UAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoC+F,UAApC,EAAgD;EAC9CwF,YAAAA,aAAa,EAAEa,WAD+B;EAE9CrC,YAAAA,SAAS,EAAEyB,kBAFmC;EAG9CxQ,YAAAA,IAAI,EAAEmR,kBAHwC;EAI9C5C,YAAAA,EAAE,EAAE8C;EAJ0C,WAAhD;EAMD,SAPS,EAOP,CAPO,CAAV;EAQD,OAhBD;EAkBAjW,MAAAA,oBAAoB,CAAC2U,aAAD,EAAgBxV,kBAAhB,CAApB;EACD,KA7BD,MA6BO;EACLwV,MAAAA,aAAa,CAACzS,SAAd,CAAwB2C,MAAxB,CAA+B4G,mBAA/B;EACAuK,MAAAA,WAAW,CAAC9T,SAAZ,CAAsBqS,GAAtB,CAA0B9I,mBAA1B;EAEA,WAAKoG,UAAL,GAAkB,KAAlB;EACA5L,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoC+F,UAApC,EAAgD;EAC9CwF,QAAAA,aAAa,EAAEa,WAD+B;EAE9CrC,QAAAA,SAAS,EAAEyB,kBAFmC;EAG9CxQ,QAAAA,IAAI,EAAEmR,kBAHwC;EAI9C5C,QAAAA,EAAE,EAAE8C;EAJ0C,OAAhD;EAMD;;EAED,QAAIC,SAAJ,EAAe;EACb,WAAKrD,KAAL;EACD;EACF;;EAEDiD,EAAAA,iBAAiB,CAACnC,SAAD,EAAY;EAC3B,QAAI,CAAC,CAAClE,eAAD,EAAkBD,cAAlB,EAAkC7Q,QAAlC,CAA2CgV,SAA3C,CAAL,EAA4D;EAC1D,aAAOA,SAAP;EACD;;EAED,QAAItQ,KAAK,EAAT,EAAa;EACX,aAAOsQ,SAAS,KAAKlE,eAAd,GAAgCF,UAAhC,GAA6CD,UAApD;EACD;;EAED,WAAOqE,SAAS,KAAKlE,eAAd,GAAgCH,UAAhC,GAA6CC,UAApD;EACD;;EAED8G,EAAAA,iBAAiB,CAAC9C,KAAD,EAAQ;EACvB,QAAI,CAAC,CAACjE,UAAD,EAAaC,UAAb,EAAyB5Q,QAAzB,CAAkC4U,KAAlC,CAAL,EAA+C;EAC7C,aAAOA,KAAP;EACD;;EAED,QAAIlQ,KAAK,EAAT,EAAa;EACX,aAAOkQ,KAAK,KAAKjE,UAAV,GAAuBE,cAAvB,GAAwCC,eAA/C;EACD;;EAED,WAAO8D,KAAK,KAAKjE,UAAV,GAAuBG,eAAvB,GAAyCD,cAAhD;EACD,GAvakC;;;EA2aX,SAAjB+G,iBAAiB,CAAChY,OAAD,EAAUoC,MAAV,EAAkB;EACxC,QAAI2K,IAAI,GAAGzB,IAAI,CAACtF,GAAL,CAAShG,OAAT,EAAkBuL,UAAlB,CAAX;EACA,QAAImI,OAAO,GAAG,EACZ,GAAGnD,SADS;EAEZ,SAAG3C,WAAW,CAACI,iBAAZ,CAA8BhO,OAA9B;EAFS,KAAd;;EAKA,QAAI,OAAOoC,MAAP,KAAkB,QAAtB,EAAgC;EAC9BsR,MAAAA,OAAO,GAAG,EACR,GAAGA,OADK;EAER,WAAGtR;EAFK,OAAV;EAID;;EAED,UAAM6V,MAAM,GAAG,OAAO7V,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsCsR,OAAO,CAAChD,KAA7D;;EAEA,QAAI,CAAC3D,IAAL,EAAW;EACTA,MAAAA,IAAI,GAAG,IAAIkG,QAAJ,CAAajT,OAAb,EAAsB0T,OAAtB,CAAP;EACD;;EAED,QAAI,OAAOtR,MAAP,KAAkB,QAAtB,EAAgC;EAC9B2K,MAAAA,IAAI,CAAC6H,EAAL,CAAQxS,MAAR;EACD,KAFD,MAEO,IAAI,OAAO6V,MAAP,KAAkB,QAAtB,EAAgC;EACrC,UAAI,OAAOlL,IAAI,CAACkL,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,cAAM,IAAIlV,SAAJ,CAAe,oBAAmBkV,MAAO,GAAzC,CAAN;EACD;;EAEDlL,MAAAA,IAAI,CAACkL,MAAD,CAAJ;EACD,KANM,MAMA,IAAIvE,OAAO,CAAClD,QAAR,IAAoBkD,OAAO,CAACwE,IAAhC,EAAsC;EAC3CnL,MAAAA,IAAI,CAAC4D,KAAL;EACA5D,MAAAA,IAAI,CAACuH,KAAL;EACD;EACF;;EAEqB,SAAfhP,eAAe,CAAClD,MAAD,EAAS;EAC7B,WAAO,KAAK0K,IAAL,CAAU,YAAY;EAC3BmG,MAAAA,QAAQ,CAAC+E,iBAAT,CAA2B,IAA3B,EAAiC5V,MAAjC;EACD,KAFM,CAAP;EAGD;;EAEyB,SAAnB+V,mBAAmB,CAAC5Q,KAAD,EAAQ;EAChC,UAAMU,MAAM,GAAGvH,sBAAsB,CAAC,IAAD,CAArC;;EAEA,QAAI,CAACuH,MAAD,IAAW,CAACA,MAAM,CAACtE,SAAP,CAAiBC,QAAjB,CAA0BmO,mBAA1B,CAAhB,EAAgE;EAC9D;EACD;;EAED,UAAM3P,MAAM,GAAG,EACb,GAAGwL,WAAW,CAACI,iBAAZ,CAA8B/F,MAA9B,CADU;EAEb,SAAG2F,WAAW,CAACI,iBAAZ,CAA8B,IAA9B;EAFU,KAAf;EAIA,UAAMoK,UAAU,GAAG,KAAKlY,YAAL,CAAkB,kBAAlB,CAAnB;;EAEA,QAAIkY,UAAJ,EAAgB;EACdhW,MAAAA,MAAM,CAACoO,QAAP,GAAkB,KAAlB;EACD;;EAEDyC,IAAAA,QAAQ,CAAC+E,iBAAT,CAA2B/P,MAA3B,EAAmC7F,MAAnC;;EAEA,QAAIgW,UAAJ,EAAgB;EACd9M,MAAAA,IAAI,CAACtF,GAAL,CAASiC,MAAT,EAAiBsD,UAAjB,EAA2BqJ,EAA3B,CAA8BwD,UAA9B;EACD;;EAED7Q,IAAAA,KAAK,CAAC0D,cAAN;EACD;;EA3ekC;EA8erC;EACA;EACA;EACA;EACA;;;EAEAvD,YAAY,CAACiC,EAAb,CAAgB9J,QAAhB,EAA0BmM,sBAA1B,EAAgD6G,mBAAhD,EAAqEI,QAAQ,CAACkF,mBAA9E;EAEAzQ,YAAY,CAACiC,EAAb,CAAgB7I,MAAhB,EAAwBgR,qBAAxB,EAA6C,MAAM;EACjD,QAAMuG,SAAS,GAAGnJ,cAAc,CAACC,IAAf,CAAoB2D,kBAApB,CAAlB;;EAEA,OAAK,IAAI5K,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAG6P,SAAS,CAAClQ,MAAhC,EAAwCD,CAAC,GAAGM,GAA5C,EAAiDN,CAAC,EAAlD,EAAsD;EACpD+K,IAAAA,QAAQ,CAAC+E,iBAAT,CAA2BK,SAAS,CAACnQ,CAAD,CAApC,EAAyCoD,IAAI,CAACtF,GAAL,CAASqS,SAAS,CAACnQ,CAAD,CAAlB,EAAuBqD,UAAvB,CAAzC;EACD;EACF,CAND;EAQA;EACA;EACA;EACA;EACA;EACA;;EAEAvG,kBAAkB,CAAC0G,MAAD,EAAOuH,QAAP,CAAlB;;EC7mBA;EACA;EACA;EACA;EACA;EACA;EAkBA;EACA;EACA;EACA;EACA;;EAEA,MAAMvH,MAAI,GAAG,UAAb;EACA,MAAMH,UAAQ,GAAG,aAAjB;EACA,MAAMI,WAAS,GAAI,IAAGJ,UAAS,EAA/B;EACA,MAAMK,cAAY,GAAG,WAArB;EAEA,MAAM2E,SAAO,GAAG;EACdlD,EAAAA,MAAM,EAAE,IADM;EAEdiL,EAAAA,MAAM,EAAE;EAFM,CAAhB;EAKA,MAAMxH,aAAW,GAAG;EAClBzD,EAAAA,MAAM,EAAE,SADU;EAElBiL,EAAAA,MAAM,EAAE;EAFU,CAApB;EAKA,MAAMC,YAAU,GAAI,OAAM5M,WAAU,EAApC;EACA,MAAM6M,aAAW,GAAI,QAAO7M,WAAU,EAAtC;EACA,MAAM8M,YAAU,GAAI,OAAM9M,WAAU,EAApC;EACA,MAAM+M,cAAY,GAAI,SAAQ/M,WAAU,EAAxC;EACA,MAAMK,sBAAoB,GAAI,QAAOL,WAAU,GAAEC,cAAa,EAA9D;EAEA,MAAMO,iBAAe,GAAG,MAAxB;EACA,MAAMwM,mBAAmB,GAAG,UAA5B;EACA,MAAMC,qBAAqB,GAAG,YAA9B;EACA,MAAMC,oBAAoB,GAAG,WAA7B;EAEA,MAAMC,KAAK,GAAG,OAAd;EACA,MAAMC,MAAM,GAAG,QAAf;EAEA,MAAMC,gBAAgB,GAAG,oBAAzB;EACA,MAAM7L,sBAAoB,GAAG,6BAA7B;EAEA;EACA;EACA;EACA;EACA;;EAEA,MAAM8L,QAAN,SAAuB9N,aAAvB,CAAqC;EACnCC,EAAAA,WAAW,CAACpL,OAAD,EAAUoC,MAAV,EAAkB;EAC3B,UAAMpC,OAAN;EAEA,SAAKkZ,gBAAL,GAAwB,KAAxB;EACA,SAAKxF,OAAL,GAAe,KAAKC,UAAL,CAAgBvR,MAAhB,CAAf;EACA,SAAK+W,aAAL,GAAqBjK,cAAc,CAACC,IAAf,CAClB,GAAEhC,sBAAqB,WAAU,KAAK9B,QAAL,CAAc+N,EAAG,KAAnD,GACC,GAAEjM,sBAAqB,qBAAoB,KAAK9B,QAAL,CAAc+N,EAAG,IAF1C,CAArB;EAKA,UAAMC,UAAU,GAAGnK,cAAc,CAACC,IAAf,CAAoBhC,sBAApB,CAAnB;;EAEA,SAAK,IAAIjF,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAG6Q,UAAU,CAAClR,MAAjC,EAAyCD,CAAC,GAAGM,GAA7C,EAAkDN,CAAC,EAAnD,EAAuD;EACrD,YAAMoR,IAAI,GAAGD,UAAU,CAACnR,CAAD,CAAvB;EACA,YAAMjI,QAAQ,GAAGO,sBAAsB,CAAC8Y,IAAD,CAAvC;EACA,YAAMC,aAAa,GAAGrK,cAAc,CAACC,IAAf,CAAoBlP,QAApB,EACnBkO,MADmB,CACZqL,SAAS,IAAIA,SAAS,KAAK,KAAKnO,QADpB,CAAtB;;EAGA,UAAIpL,QAAQ,KAAK,IAAb,IAAqBsZ,aAAa,CAACpR,MAAvC,EAA+C;EAC7C,aAAKsR,SAAL,GAAiBxZ,QAAjB;;EACA,aAAKkZ,aAAL,CAAmBtJ,IAAnB,CAAwByJ,IAAxB;EACD;EACF;;EAED,SAAKI,OAAL,GAAe,KAAKhG,OAAL,CAAa4E,MAAb,GAAsB,KAAKqB,UAAL,EAAtB,GAA0C,IAAzD;;EAEA,QAAI,CAAC,KAAKjG,OAAL,CAAa4E,MAAlB,EAA0B;EACxB,WAAKsB,yBAAL,CAA+B,KAAKvO,QAApC,EAA8C,KAAK8N,aAAnD;EACD;;EAED,QAAI,KAAKzF,OAAL,CAAarG,MAAjB,EAAyB;EACvB,WAAKA,MAAL;EACD;EACF,GAlCkC;;;EAsCjB,aAAPkD,OAAO,GAAG;EACnB,WAAOA,SAAP;EACD;;EAEkB,aAARhF,QAAQ,GAAG;EACpB,WAAOA,UAAP;EACD,GA5CkC;;;EAgDnC8B,EAAAA,MAAM,GAAG;EACP,QAAI,KAAKhC,QAAL,CAAc1H,SAAd,CAAwBC,QAAxB,CAAiCuI,iBAAjC,CAAJ,EAAuD;EACrD,WAAK0N,IAAL;EACD,KAFD,MAEO;EACL,WAAKC,IAAL;EACD;EACF;;EAEDA,EAAAA,IAAI,GAAG;EACL,QAAI,KAAKZ,gBAAL,IAAyB,KAAK7N,QAAL,CAAc1H,SAAd,CAAwBC,QAAxB,CAAiCuI,iBAAjC,CAA7B,EAAgF;EAC9E;EACD;;EAED,QAAI4N,OAAJ;EACA,QAAIC,WAAJ;;EAEA,QAAI,KAAKN,OAAT,EAAkB;EAChBK,MAAAA,OAAO,GAAG7K,cAAc,CAACC,IAAf,CAAoB6J,gBAApB,EAAsC,KAAKU,OAA3C,EACPvL,MADO,CACAmL,IAAI,IAAI;EACd,YAAI,OAAO,KAAK5F,OAAL,CAAa4E,MAApB,KAA+B,QAAnC,EAA6C;EAC3C,iBAAOgB,IAAI,CAACpZ,YAAL,CAAkB,gBAAlB,MAAwC,KAAKwT,OAAL,CAAa4E,MAA5D;EACD;;EAED,eAAOgB,IAAI,CAAC3V,SAAL,CAAeC,QAAf,CAAwB+U,mBAAxB,CAAP;EACD,OAPO,CAAV;;EASA,UAAIoB,OAAO,CAAC5R,MAAR,KAAmB,CAAvB,EAA0B;EACxB4R,QAAAA,OAAO,GAAG,IAAV;EACD;EACF;;EAED,UAAME,SAAS,GAAG/K,cAAc,CAACK,OAAf,CAAuB,KAAKkK,SAA5B,CAAlB;;EACA,QAAIM,OAAJ,EAAa;EACX,YAAMG,cAAc,GAAGH,OAAO,CAAC5K,IAAR,CAAamK,IAAI,IAAIW,SAAS,KAAKX,IAAnC,CAAvB;EACAU,MAAAA,WAAW,GAAGE,cAAc,GAAG5O,IAAI,CAACtF,GAAL,CAASkU,cAAT,EAAyB3O,UAAzB,CAAH,GAAwC,IAApE;;EAEA,UAAIyO,WAAW,IAAIA,WAAW,CAACd,gBAA/B,EAAiD;EAC/C;EACD;EACF;;EAED,UAAMiB,UAAU,GAAGzS,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCkN,YAApC,CAAnB;;EACA,QAAI4B,UAAU,CAAC5P,gBAAf,EAAiC;EAC/B;EACD;;EAED,QAAIwP,OAAJ,EAAa;EACXA,MAAAA,OAAO,CAACvX,OAAR,CAAgB4X,UAAU,IAAI;EAC5B,YAAIH,SAAS,KAAKG,UAAlB,EAA8B;EAC5BnB,UAAAA,QAAQ,CAACoB,iBAAT,CAA2BD,UAA3B,EAAuC,MAAvC;EACD;;EAED,YAAI,CAACJ,WAAL,EAAkB;EAChB1O,UAAAA,IAAI,CAAC3F,GAAL,CAASyU,UAAT,EAAqB7O,UAArB,EAA+B,IAA/B;EACD;EACF,OARD;EASD;;EAED,UAAM+O,SAAS,GAAG,KAAKC,aAAL,EAAlB;;EAEA,SAAKlP,QAAL,CAAc1H,SAAd,CAAwB2C,MAAxB,CAA+BqS,mBAA/B;;EACA,SAAKtN,QAAL,CAAc1H,SAAd,CAAwBqS,GAAxB,CAA4B4C,qBAA5B;;EAEA,SAAKvN,QAAL,CAAcnI,KAAd,CAAoBoX,SAApB,IAAiC,CAAjC;;EAEA,QAAI,KAAKnB,aAAL,CAAmBhR,MAAvB,EAA+B;EAC7B,WAAKgR,aAAL,CAAmB3W,OAAnB,CAA2BxC,OAAO,IAAI;EACpCA,QAAAA,OAAO,CAAC2D,SAAR,CAAkB2C,MAAlB,CAAyBuS,oBAAzB;EACA7Y,QAAAA,OAAO,CAACsN,YAAR,CAAqB,eAArB,EAAsC,IAAtC;EACD,OAHD;EAID;;EAED,SAAKkN,gBAAL,CAAsB,IAAtB;;EAEA,UAAMC,QAAQ,GAAG,MAAM;EACrB,WAAKpP,QAAL,CAAc1H,SAAd,CAAwB2C,MAAxB,CAA+BsS,qBAA/B;;EACA,WAAKvN,QAAL,CAAc1H,SAAd,CAAwBqS,GAAxB,CAA4B2C,mBAA5B,EAAiDxM,iBAAjD;;EAEA,WAAKd,QAAL,CAAcnI,KAAd,CAAoBoX,SAApB,IAAiC,EAAjC;EAEA,WAAKE,gBAAL,CAAsB,KAAtB;EAEA9S,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCmN,aAApC;EACD,KATD;;EAWA,UAAMkC,oBAAoB,GAAGJ,SAAS,CAAC,CAAD,CAAT,CAAatX,WAAb,KAA6BsX,SAAS,CAACtQ,KAAV,CAAgB,CAAhB,CAA1D;EACA,UAAM2Q,UAAU,GAAI,SAAQD,oBAAqB,EAAjD;EACA,UAAM9Z,kBAAkB,GAAGD,gCAAgC,CAAC,KAAK0K,QAAN,CAA3D;EAEA3D,IAAAA,YAAY,CAACkC,GAAb,CAAiB,KAAKyB,QAAtB,EAAgC,eAAhC,EAAiDoP,QAAjD;EAEAhZ,IAAAA,oBAAoB,CAAC,KAAK4J,QAAN,EAAgBzK,kBAAhB,CAApB;EACA,SAAKyK,QAAL,CAAcnI,KAAd,CAAoBoX,SAApB,IAAkC,GAAE,KAAKjP,QAAL,CAAcsP,UAAd,CAA0B,IAA9D;EACD;;EAEDd,EAAAA,IAAI,GAAG;EACL,QAAI,KAAKX,gBAAL,IAAyB,CAAC,KAAK7N,QAAL,CAAc1H,SAAd,CAAwBC,QAAxB,CAAiCuI,iBAAjC,CAA9B,EAAiF;EAC/E;EACD;;EAED,UAAMgO,UAAU,GAAGzS,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCoN,YAApC,CAAnB;;EACA,QAAI0B,UAAU,CAAC5P,gBAAf,EAAiC;EAC/B;EACD;;EAED,UAAM+P,SAAS,GAAG,KAAKC,aAAL,EAAlB;;EAEA,SAAKlP,QAAL,CAAcnI,KAAd,CAAoBoX,SAApB,IAAkC,GAAE,KAAKjP,QAAL,CAAcoD,qBAAd,GAAsC6L,SAAtC,CAAiD,IAArF;EAEAhW,IAAAA,MAAM,CAAC,KAAK+G,QAAN,CAAN;;EAEA,SAAKA,QAAL,CAAc1H,SAAd,CAAwBqS,GAAxB,CAA4B4C,qBAA5B;;EACA,SAAKvN,QAAL,CAAc1H,SAAd,CAAwB2C,MAAxB,CAA+BqS,mBAA/B,EAAoDxM,iBAApD;;EAEA,UAAMyO,kBAAkB,GAAG,KAAKzB,aAAL,CAAmBhR,MAA9C;;EACA,QAAIyS,kBAAkB,GAAG,CAAzB,EAA4B;EAC1B,WAAK,IAAI1S,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG0S,kBAApB,EAAwC1S,CAAC,EAAzC,EAA6C;EAC3C,cAAMgC,OAAO,GAAG,KAAKiP,aAAL,CAAmBjR,CAAnB,CAAhB;EACA,cAAMoR,IAAI,GAAG5Y,sBAAsB,CAACwJ,OAAD,CAAnC;;EAEA,YAAIoP,IAAI,IAAI,CAACA,IAAI,CAAC3V,SAAL,CAAeC,QAAf,CAAwBuI,iBAAxB,CAAb,EAAuD;EACrDjC,UAAAA,OAAO,CAACvG,SAAR,CAAkBqS,GAAlB,CAAsB6C,oBAAtB;EACA3O,UAAAA,OAAO,CAACoD,YAAR,CAAqB,eAArB,EAAsC,KAAtC;EACD;EACF;EACF;;EAED,SAAKkN,gBAAL,CAAsB,IAAtB;;EAEA,UAAMC,QAAQ,GAAG,MAAM;EACrB,WAAKD,gBAAL,CAAsB,KAAtB;;EACA,WAAKnP,QAAL,CAAc1H,SAAd,CAAwB2C,MAAxB,CAA+BsS,qBAA/B;;EACA,WAAKvN,QAAL,CAAc1H,SAAd,CAAwBqS,GAAxB,CAA4B2C,mBAA5B;;EACAjR,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCqN,cAApC;EACD,KALD;;EAOA,SAAKrN,QAAL,CAAcnI,KAAd,CAAoBoX,SAApB,IAAiC,EAAjC;EACA,UAAM1Z,kBAAkB,GAAGD,gCAAgC,CAAC,KAAK0K,QAAN,CAA3D;EAEA3D,IAAAA,YAAY,CAACkC,GAAb,CAAiB,KAAKyB,QAAtB,EAAgC,eAAhC,EAAiDoP,QAAjD;EACAhZ,IAAAA,oBAAoB,CAAC,KAAK4J,QAAN,EAAgBzK,kBAAhB,CAApB;EACD;;EAED4Z,EAAAA,gBAAgB,CAACK,eAAD,EAAkB;EAChC,SAAK3B,gBAAL,GAAwB2B,eAAxB;EACD;;EAEDrP,EAAAA,OAAO,GAAG;EACR,UAAMA,OAAN;EACA,SAAKkI,OAAL,GAAe,IAAf;EACA,SAAKgG,OAAL,GAAe,IAAf;EACA,SAAKP,aAAL,GAAqB,IAArB;EACA,SAAKD,gBAAL,GAAwB,IAAxB;EACD,GAzMkC;;;EA6MnCvF,EAAAA,UAAU,CAACvR,MAAD,EAAS;EACjBA,IAAAA,MAAM,GAAG,EACP,GAAGmO,SADI;EAEP,SAAGnO;EAFI,KAAT;EAIAA,IAAAA,MAAM,CAACiL,MAAP,GAAgB/D,OAAO,CAAClH,MAAM,CAACiL,MAAR,CAAvB,CALiB;;EAMjBnL,IAAAA,eAAe,CAACwJ,MAAD,EAAOtJ,MAAP,EAAe0O,aAAf,CAAf;EACA,WAAO1O,MAAP;EACD;;EAEDmY,EAAAA,aAAa,GAAG;EACd,WAAO,KAAKlP,QAAL,CAAc1H,SAAd,CAAwBC,QAAxB,CAAiCkV,KAAjC,IAA0CA,KAA1C,GAAkDC,MAAzD;EACD;;EAEDY,EAAAA,UAAU,GAAG;EACX,QAAI;EAAErB,MAAAA;EAAF,QAAa,KAAK5E,OAAtB;;EAEA,QAAInS,WAAS,CAAC+W,MAAD,CAAb,EAAuB;EACrB;EACA,UAAI,OAAOA,MAAM,CAACwC,MAAd,KAAyB,WAAzB,IAAwC,OAAOxC,MAAM,CAAC,CAAD,CAAb,KAAqB,WAAjE,EAA8E;EAC5EA,QAAAA,MAAM,GAAGA,MAAM,CAAC,CAAD,CAAf;EACD;EACF,KALD,MAKO;EACLA,MAAAA,MAAM,GAAGpJ,cAAc,CAACK,OAAf,CAAuB+I,MAAvB,CAAT;EACD;;EAED,UAAMrY,QAAQ,GAAI,GAAEkN,sBAAqB,oBAAmBmL,MAAO,IAAnE;EAEApJ,IAAAA,cAAc,CAACC,IAAf,CAAoBlP,QAApB,EAA8BqY,MAA9B,EACG9V,OADH,CACWxC,OAAO,IAAI;EAClB,YAAM+a,QAAQ,GAAGra,sBAAsB,CAACV,OAAD,CAAvC;;EAEA,WAAK4Z,yBAAL,CACEmB,QADF,EAEE,CAAC/a,OAAD,CAFF;EAID,KARH;EAUA,WAAOsY,MAAP;EACD;;EAEDsB,EAAAA,yBAAyB,CAAC5Z,OAAD,EAAUgb,YAAV,EAAwB;EAC/C,QAAI,CAAChb,OAAD,IAAY,CAACgb,YAAY,CAAC7S,MAA9B,EAAsC;EACpC;EACD;;EAED,UAAM8S,MAAM,GAAGjb,OAAO,CAAC2D,SAAR,CAAkBC,QAAlB,CAA2BuI,iBAA3B,CAAf;EAEA6O,IAAAA,YAAY,CAACxY,OAAb,CAAqB8W,IAAI,IAAI;EAC3B,UAAI2B,MAAJ,EAAY;EACV3B,QAAAA,IAAI,CAAC3V,SAAL,CAAe2C,MAAf,CAAsBuS,oBAAtB;EACD,OAFD,MAEO;EACLS,QAAAA,IAAI,CAAC3V,SAAL,CAAeqS,GAAf,CAAmB6C,oBAAnB;EACD;;EAEDS,MAAAA,IAAI,CAAChM,YAAL,CAAkB,eAAlB,EAAmC2N,MAAnC;EACD,KARD;EASD,GAtQkC;;;EA0QX,SAAjBZ,iBAAiB,CAACra,OAAD,EAAUoC,MAAV,EAAkB;EACxC,QAAI2K,IAAI,GAAGzB,IAAI,CAACtF,GAAL,CAAShG,OAAT,EAAkBuL,UAAlB,CAAX;EACA,UAAMmI,OAAO,GAAG,EACd,GAAGnD,SADW;EAEd,SAAG3C,WAAW,CAACI,iBAAZ,CAA8BhO,OAA9B,CAFW;EAGd,UAAI,OAAOoC,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAApD;EAHc,KAAhB;;EAMA,QAAI,CAAC2K,IAAD,IAAS2G,OAAO,CAACrG,MAAjB,IAA2B,OAAOjL,MAAP,KAAkB,QAA7C,IAAyD,YAAYU,IAAZ,CAAiBV,MAAjB,CAA7D,EAAuF;EACrFsR,MAAAA,OAAO,CAACrG,MAAR,GAAiB,KAAjB;EACD;;EAED,QAAI,CAACN,IAAL,EAAW;EACTA,MAAAA,IAAI,GAAG,IAAIkM,QAAJ,CAAajZ,OAAb,EAAsB0T,OAAtB,CAAP;EACD;;EAED,QAAI,OAAOtR,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,UAAI,OAAO2K,IAAI,CAAC3K,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,cAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;EACD;;EAED2K,MAAAA,IAAI,CAAC3K,MAAD,CAAJ;EACD;EACF;;EAEqB,SAAfkD,eAAe,CAAClD,MAAD,EAAS;EAC7B,WAAO,KAAK0K,IAAL,CAAU,YAAY;EAC3BmM,MAAAA,QAAQ,CAACoB,iBAAT,CAA2B,IAA3B,EAAiCjY,MAAjC;EACD,KAFM,CAAP;EAGD;;EAvSkC;EA0SrC;EACA;EACA;EACA;EACA;;;EAEAsF,YAAY,CAACiC,EAAb,CAAgB9J,QAAhB,EAA0BmM,sBAA1B,EAAgDmB,sBAAhD,EAAsE,UAAU5F,KAAV,EAAiB;EACrF;EACA,MAAIA,KAAK,CAACU,MAAN,CAAagO,OAAb,KAAyB,GAAzB,IAAiC1O,KAAK,CAACC,cAAN,IAAwBD,KAAK,CAACC,cAAN,CAAqByO,OAArB,KAAiC,GAA9F,EAAoG;EAClG1O,IAAAA,KAAK,CAAC0D,cAAN;EACD;;EAED,QAAMiQ,WAAW,GAAGtN,WAAW,CAACI,iBAAZ,CAA8B,IAA9B,CAApB;EACA,QAAM/N,QAAQ,GAAGO,sBAAsB,CAAC,IAAD,CAAvC;EACA,QAAM2a,gBAAgB,GAAGjM,cAAc,CAACC,IAAf,CAAoBlP,QAApB,CAAzB;EAEAkb,EAAAA,gBAAgB,CAAC3Y,OAAjB,CAAyBxC,OAAO,IAAI;EAClC,UAAM+M,IAAI,GAAGzB,IAAI,CAACtF,GAAL,CAAShG,OAAT,EAAkBuL,UAAlB,CAAb;EACA,QAAInJ,MAAJ;;EACA,QAAI2K,IAAJ,EAAU;EACR;EACA,UAAIA,IAAI,CAAC2M,OAAL,KAAiB,IAAjB,IAAyB,OAAOwB,WAAW,CAAC5C,MAAnB,KAA8B,QAA3D,EAAqE;EACnEvL,QAAAA,IAAI,CAAC2G,OAAL,CAAa4E,MAAb,GAAsB4C,WAAW,CAAC5C,MAAlC;EACAvL,QAAAA,IAAI,CAAC2M,OAAL,GAAe3M,IAAI,CAAC4M,UAAL,EAAf;EACD;;EAEDvX,MAAAA,MAAM,GAAG,QAAT;EACD,KARD,MAQO;EACLA,MAAAA,MAAM,GAAG8Y,WAAT;EACD;;EAEDjC,IAAAA,QAAQ,CAACoB,iBAAT,CAA2Bra,OAA3B,EAAoCoC,MAApC;EACD,GAhBD;EAiBD,CA3BD;EA6BA;EACA;EACA;EACA;EACA;EACA;;EAEA4C,kBAAkB,CAAC0G,MAAD,EAAOuN,QAAP,CAAlB;;ECvZO,IAAI,GAAG,GAAG,KAAK,CAAC;EAChB,IAAI,MAAM,GAAG,QAAQ,CAAC;EACtB,IAAI,KAAK,GAAG,OAAO,CAAC;EACpB,IAAI,IAAI,GAAG,MAAM,CAAC;EAClB,IAAI,IAAI,GAAG,MAAM,CAAC;EAClB,IAAI,cAAc,GAAG,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;EAChD,IAAI,KAAK,GAAG,OAAO,CAAC;EACpB,IAAI,GAAG,GAAG,KAAK,CAAC;EAChB,IAAI,eAAe,GAAG,iBAAiB,CAAC;EACxC,IAAI,QAAQ,GAAG,UAAU,CAAC;EAC1B,IAAI,MAAM,GAAG,QAAQ,CAAC;EACtB,IAAI,SAAS,GAAG,WAAW,CAAC;EAC5B,IAAI,mBAAmB,gBAAgB,cAAc,CAAC,MAAM,CAAC,UAAU,GAAG,EAAE,SAAS,EAAE;EAC9F,EAAE,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,SAAS,GAAG,GAAG,GAAG,KAAK,EAAE,SAAS,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;EACtE,CAAC,EAAE,EAAE,CAAC,CAAC;EACA,IAAI,UAAU,gBAAgB,EAAE,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,GAAG,EAAE,SAAS,EAAE;EACxG,EAAE,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,SAAS,GAAG,GAAG,GAAG,KAAK,EAAE,SAAS,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;EACjF,CAAC,EAAE,EAAE,CAAC,CAAC;AACP;EACO,IAAI,UAAU,GAAG,YAAY,CAAC;EAC9B,IAAI,IAAI,GAAG,MAAM,CAAC;EAClB,IAAI,SAAS,GAAG,WAAW,CAAC;AACnC;EACO,IAAI,UAAU,GAAG,YAAY,CAAC;EAC9B,IAAI,IAAI,GAAG,MAAM,CAAC;EAClB,IAAI,SAAS,GAAG,WAAW,CAAC;AACnC;EACO,IAAI,WAAW,GAAG,aAAa,CAAC;EAChC,IAAI,KAAK,GAAG,OAAO,CAAC;EACpB,IAAI,UAAU,GAAG,YAAY,CAAC;EAC9B,IAAI,cAAc,GAAG,CAAC,UAAU,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,EAAE,WAAW,EAAE,KAAK,EAAE,UAAU,CAAC;;EC9BvG,SAAS,WAAW,CAAC,OAAO,EAAE;EAC7C,EAAE,OAAO,OAAO,GAAG,CAAC,OAAO,CAAC,QAAQ,IAAI,EAAE,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC;EACjE;;ECFe,SAAS,SAAS,CAAC,IAAI,EAAE;EACxC,EAAE,IAAI,IAAI,IAAI,IAAI,EAAE;EACpB,IAAI,OAAO,MAAM,CAAC;EAClB,GAAG;AACH;EACA,EAAE,IAAI,IAAI,CAAC,QAAQ,EAAE,KAAK,iBAAiB,EAAE;EAC7C,IAAI,IAAI,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;EAC3C,IAAI,OAAO,aAAa,GAAG,aAAa,CAAC,WAAW,IAAI,MAAM,GAAG,MAAM,CAAC;EACxE,GAAG;AACH;EACA,EAAE,OAAO,IAAI,CAAC;EACd;;ECTA,SAAS,SAAS,CAAC,IAAI,EAAE;EACzB,EAAE,IAAI,UAAU,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;EAC3C,EAAE,OAAO,IAAI,YAAY,UAAU,IAAI,IAAI,YAAY,OAAO,CAAC;EAC/D,CAAC;AACD;EACA,SAAS,aAAa,CAAC,IAAI,EAAE;EAC7B,EAAE,IAAI,UAAU,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC;EAC/C,EAAE,OAAO,IAAI,YAAY,UAAU,IAAI,IAAI,YAAY,WAAW,CAAC;EACnE,CAAC;AACD;EACA,SAAS,YAAY,CAAC,IAAI,EAAE;EAC5B;EACA,EAAE,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;EACzC,IAAI,OAAO,KAAK,CAAC;EACjB,GAAG;AACH;EACA,EAAE,IAAI,UAAU,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC;EAC9C,EAAE,OAAO,IAAI,YAAY,UAAU,IAAI,IAAI,YAAY,UAAU,CAAC;EAClE;;EClBA;AACA;EACA,SAAS,WAAW,CAAC,IAAI,EAAE;EAC3B,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;EACzB,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;EACtD,IAAI,IAAI,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;EACzC,IAAI,IAAI,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;EAClD,IAAI,IAAI,OAAO,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AACvC;EACA,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE;EAC1D,MAAM,OAAO;EACb,KAAK;EACL;EACA;AACA;AACA;EACA,IAAI,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;EACxC,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;EACpD,MAAM,IAAI,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;AACnC;EACA,MAAM,IAAI,KAAK,KAAK,KAAK,EAAE;EAC3B,QAAQ,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;EACtC,OAAO,MAAM;EACb,QAAQ,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,KAAK,IAAI,GAAG,EAAE,GAAG,KAAK,CAAC,CAAC;EAChE,OAAO;EACP,KAAK,CAAC,CAAC;EACP,GAAG,CAAC,CAAC;EACL,CAAC;AACD;EACA,SAASmC,QAAM,CAAC,KAAK,EAAE;EACvB,EAAE,IAAI,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;EAC1B,EAAE,IAAI,aAAa,GAAG;EACtB,IAAI,MAAM,EAAE;EACZ,MAAM,QAAQ,EAAE,KAAK,CAAC,OAAO,CAAC,QAAQ;EACtC,MAAM,IAAI,EAAE,GAAG;EACf,MAAM,GAAG,EAAE,GAAG;EACd,MAAM,MAAM,EAAE,GAAG;EACjB,KAAK;EACL,IAAI,KAAK,EAAE;EACX,MAAM,QAAQ,EAAE,UAAU;EAC1B,KAAK;EACL,IAAI,SAAS,EAAE,EAAE;EACjB,GAAG,CAAC;EACJ,EAAE,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC;EACnE,EAAE,KAAK,CAAC,MAAM,GAAG,aAAa,CAAC;AAC/B;EACA,EAAE,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,EAAE;EAC5B,IAAI,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,aAAa,CAAC,KAAK,CAAC,CAAC;EACnE,GAAG;AACH;EACA,EAAE,OAAO,YAAY;EACrB,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;EACxD,MAAM,IAAI,OAAO,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;EACzC,MAAM,IAAI,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;EACpD,MAAM,IAAI,eAAe,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;AACtH;EACA,MAAM,IAAI,KAAK,GAAG,eAAe,CAAC,MAAM,CAAC,UAAU,KAAK,EAAE,QAAQ,EAAE;EACpE,QAAQ,KAAK,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;EAC7B,QAAQ,OAAO,KAAK,CAAC;EACrB,OAAO,EAAE,EAAE,CAAC,CAAC;AACb;EACA,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE;EAC5D,QAAQ,OAAO;EACf,OAAO;AACP;EACA,MAAM,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;EAC1C,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,UAAU,SAAS,EAAE;EAC3D,QAAQ,OAAO,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;EAC3C,OAAO,CAAC,CAAC;EACT,KAAK,CAAC,CAAC;EACP,GAAG,CAAC;EACJ,CAAC;AACD;AACA;AACA,sBAAe;EACf,EAAE,IAAI,EAAE,aAAa;EACrB,EAAE,OAAO,EAAE,IAAI;EACf,EAAE,KAAK,EAAE,OAAO;EAChB,EAAE,EAAE,EAAE,WAAW;EACjB,EAAE,MAAM,EAAEA,QAAM;EAChB,EAAE,QAAQ,EAAE,CAAC,eAAe,CAAC;EAC7B,CAAC;;EClFc,SAAS,gBAAgB,CAAC,SAAS,EAAE;EACpD,EAAE,OAAO,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EACjC;;ECHe,SAAS,qBAAqB,CAAC,OAAO,EAAE;EACvD,EAAE,IAAI,IAAI,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;EAC7C,EAAE,OAAO;EACT,IAAI,KAAK,EAAE,IAAI,CAAC,KAAK;EACrB,IAAI,MAAM,EAAE,IAAI,CAAC,MAAM;EACvB,IAAI,GAAG,EAAE,IAAI,CAAC,GAAG;EACjB,IAAI,KAAK,EAAE,IAAI,CAAC,KAAK;EACrB,IAAI,MAAM,EAAE,IAAI,CAAC,MAAM;EACvB,IAAI,IAAI,EAAE,IAAI,CAAC,IAAI;EACnB,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI;EAChB,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG;EACf,GAAG,CAAC;EACJ;;ECXA;AACA;EACe,SAAS,aAAa,CAAC,OAAO,EAAE;EAC/C,EAAE,IAAI,UAAU,GAAG,qBAAqB,CAAC,OAAO,CAAC,CAAC;EAClD;AACA;EACA,EAAE,IAAI,KAAK,GAAG,OAAO,CAAC,WAAW,CAAC;EAClC,EAAE,IAAI,MAAM,GAAG,OAAO,CAAC,YAAY,CAAC;AACpC;EACA,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;EAC/C,IAAI,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC;EAC7B,GAAG;AACH;EACA,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE;EACjD,IAAI,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;EAC/B,GAAG;AACH;EACA,EAAE,OAAO;EACT,IAAI,CAAC,EAAE,OAAO,CAAC,UAAU;EACzB,IAAI,CAAC,EAAE,OAAO,CAAC,SAAS;EACxB,IAAI,KAAK,EAAE,KAAK;EAChB,IAAI,MAAM,EAAE,MAAM;EAClB,GAAG,CAAC;EACJ;;ECvBe,SAAS,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE;EAChD,EAAE,IAAI,QAAQ,GAAG,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC;AAC1D;EACA,EAAE,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;EAC9B,IAAI,OAAO,IAAI,CAAC;EAChB,GAAG;EACH,OAAO,IAAI,QAAQ,IAAI,YAAY,CAAC,QAAQ,CAAC,EAAE;EAC/C,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC;AACvB;EACA,MAAM,GAAG;EACT,QAAQ,IAAI,IAAI,IAAI,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;EAC7C,UAAU,OAAO,IAAI,CAAC;EACtB,SAAS;AACT;AACA;EACA,QAAQ,IAAI,GAAG,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,IAAI,CAAC;EAC5C,OAAO,QAAQ,IAAI,EAAE;EACrB,KAAK;AACL;AACA;EACA,EAAE,OAAO,KAAK,CAAC;EACf;;ECrBe,SAASra,kBAAgB,CAAC,OAAO,EAAE;EAClD,EAAE,OAAO,SAAS,CAAC,OAAO,CAAC,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;EACtD;;ECFe,SAAS,cAAc,CAAC,OAAO,EAAE;EAChD,EAAE,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC;EAClE;;ECFe,SAAS,kBAAkB,CAAC,OAAO,EAAE;EACpD;EACA,EAAE,OAAO,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,aAAa;EACrD,EAAE,OAAO,CAAC,QAAQ,KAAK,MAAM,CAAC,QAAQ,EAAE,eAAe,CAAC;EACxD;;ECFe,SAAS,aAAa,CAAC,OAAO,EAAE;EAC/C,EAAE,IAAI,WAAW,CAAC,OAAO,CAAC,KAAK,MAAM,EAAE;EACvC,IAAI,OAAO,OAAO,CAAC;EACnB,GAAG;AACH;EACA,EAAE;EACF;EACA;EACA,IAAI,OAAO,CAAC,YAAY;EACxB,IAAI,OAAO,CAAC,UAAU;EACtB,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;EAChD;EACA,IAAI,kBAAkB,CAAC,OAAO,CAAC;AAC/B;EACA,IAAI;EACJ;;ECXA,SAAS,mBAAmB,CAAC,OAAO,EAAE;EACtC,EAAE,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;EAC7B,EAAEA,kBAAgB,CAAC,OAAO,CAAC,CAAC,QAAQ,KAAK,OAAO,EAAE;EAClD,IAAI,OAAO,IAAI,CAAC;EAChB,GAAG;AACH;EACA,EAAE,OAAO,OAAO,CAAC,YAAY,CAAC;EAC9B,CAAC;EACD;AACA;AACA;EACA,SAAS,kBAAkB,CAAC,OAAO,EAAE;EACrC,EAAE,IAAI,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;EAC9E,EAAE,IAAI,WAAW,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC;AAC3C;EACA,EAAE,OAAO,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,EAAE;EAC/F,IAAI,IAAI,GAAG,GAAGA,kBAAgB,CAAC,WAAW,CAAC,CAAC;EAC5C;EACA;AACA;EACA,IAAI,IAAI,GAAG,CAAC,SAAS,KAAK,MAAM,IAAI,GAAG,CAAC,WAAW,KAAK,MAAM,IAAI,GAAG,CAAC,OAAO,KAAK,OAAO,IAAI,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,SAAS,IAAI,GAAG,CAAC,UAAU,KAAK,QAAQ,IAAI,SAAS,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,KAAK,MAAM,EAAE;EAC1P,MAAM,OAAO,WAAW,CAAC;EACzB,KAAK,MAAM;EACX,MAAM,WAAW,GAAG,WAAW,CAAC,UAAU,CAAC;EAC3C,KAAK;EACL,GAAG;AACH;EACA,EAAE,OAAO,IAAI,CAAC;EACd,CAAC;EACD;AACA;AACA;EACe,SAAS,eAAe,CAAC,OAAO,EAAE;EACjD,EAAE,IAAI,MAAM,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC;EAClC,EAAE,IAAI,YAAY,GAAG,mBAAmB,CAAC,OAAO,CAAC,CAAC;AAClD;EACA,EAAE,OAAO,YAAY,IAAI,cAAc,CAAC,YAAY,CAAC,IAAIA,kBAAgB,CAAC,YAAY,CAAC,CAAC,QAAQ,KAAK,QAAQ,EAAE;EAC/G,IAAI,YAAY,GAAG,mBAAmB,CAAC,YAAY,CAAC,CAAC;EACrD,GAAG;AACH;EACA,EAAE,IAAI,YAAY,KAAK,WAAW,CAAC,YAAY,CAAC,KAAK,MAAM,IAAI,WAAW,CAAC,YAAY,CAAC,KAAK,MAAM,IAAIA,kBAAgB,CAAC,YAAY,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,EAAE;EAC9J,IAAI,OAAO,MAAM,CAAC;EAClB,GAAG;AACH;EACA,EAAE,OAAO,YAAY,IAAI,kBAAkB,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC;EAC/D;;ECpDe,SAAS,wBAAwB,CAAC,SAAS,EAAE;EAC5D,EAAE,OAAO,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;EAC/D;;ECFO,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;EACnB,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;EACnB,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK;;ECDd,SAAS,MAAM,CAACsa,KAAG,EAAE,KAAK,EAAEC,KAAG,EAAE;EAChD,EAAE,OAAOC,GAAO,CAACF,KAAG,EAAEG,GAAO,CAAC,KAAK,EAAEF,KAAG,CAAC,CAAC,CAAC;EAC3C;;ECHe,SAAS,kBAAkB,GAAG;EAC7C,EAAE,OAAO;EACT,IAAI,GAAG,EAAE,CAAC;EACV,IAAI,KAAK,EAAE,CAAC;EACZ,IAAI,MAAM,EAAE,CAAC;EACb,IAAI,IAAI,EAAE,CAAC;EACX,GAAG,CAAC;EACJ;;ECNe,SAAS,kBAAkB,CAAC,aAAa,EAAE;EAC1D,EAAE,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,kBAAkB,EAAE,EAAE,aAAa,CAAC,CAAC;EAChE;;ECHe,SAAS,eAAe,CAAC,KAAK,EAAE,IAAI,EAAE;EACrD,EAAE,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,OAAO,EAAE,GAAG,EAAE;EAC7C,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;EACzB,IAAI,OAAO,OAAO,CAAC;EACnB,GAAG,EAAE,EAAE,CAAC,CAAC;EACT;;ECMA,IAAI,eAAe,GAAG,SAAS,eAAe,CAAC,OAAO,EAAE,KAAK,EAAE;EAC/D,EAAE,OAAO,GAAG,OAAO,OAAO,KAAK,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,KAAK,EAAE;EACnF,IAAI,SAAS,EAAE,KAAK,CAAC,SAAS;EAC9B,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC;EAChB,EAAE,OAAO,kBAAkB,CAAC,OAAO,OAAO,KAAK,QAAQ,GAAG,OAAO,GAAG,eAAe,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC,CAAC;EAC9G,CAAC,CAAC;AACF;EACA,SAAS,KAAK,CAAC,IAAI,EAAE;EACrB,EAAE,IAAI,qBAAqB,CAAC;AAC5B;EACA,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK;EACxB,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI;EACtB,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;EAC7B,EAAE,IAAI,YAAY,GAAG,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC;EAC1C,EAAE,IAAI,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC,aAAa,CAAC;EACxD,EAAE,IAAI,aAAa,GAAG,gBAAgB,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;EACxD,EAAE,IAAI,IAAI,GAAG,wBAAwB,CAAC,aAAa,CAAC,CAAC;EACrD,EAAE,IAAI,UAAU,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;EAC7D,EAAE,IAAI,GAAG,GAAG,UAAU,GAAG,QAAQ,GAAG,OAAO,CAAC;AAC5C;EACA,EAAE,IAAI,CAAC,YAAY,IAAI,CAAC,aAAa,EAAE;EACvC,IAAI,OAAO;EACX,GAAG;AACH;EACA,EAAE,IAAI,aAAa,GAAG,eAAe,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;EAC9D,EAAE,IAAI,SAAS,GAAG,aAAa,CAAC,YAAY,CAAC,CAAC;EAC9C,EAAE,IAAI,OAAO,GAAG,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC;EAC1C,EAAE,IAAI,OAAO,GAAG,IAAI,KAAK,GAAG,GAAG,MAAM,GAAG,KAAK,CAAC;EAC9C,EAAE,IAAI,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;EACzH,EAAE,IAAI,SAAS,GAAG,aAAa,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;EACpE,EAAE,IAAI,iBAAiB,GAAG,eAAe,CAAC,YAAY,CAAC,CAAC;EACxD,EAAE,IAAI,UAAU,GAAG,iBAAiB,GAAG,IAAI,KAAK,GAAG,GAAG,iBAAiB,CAAC,YAAY,IAAI,CAAC,GAAG,iBAAiB,CAAC,WAAW,IAAI,CAAC,GAAG,CAAC,CAAC;EACnI,EAAE,IAAI,iBAAiB,GAAG,OAAO,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC,CAAC;EACtD;AACA;EACA,EAAE,IAAI,GAAG,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC;EACnC,EAAE,IAAI,GAAG,GAAG,UAAU,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC;EACjE,EAAE,IAAI,MAAM,GAAG,UAAU,GAAG,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,iBAAiB,CAAC;EACvE,EAAE,IAAI,MAAM,GAAG,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;AACxC;EACA,EAAE,IAAI,QAAQ,GAAG,IAAI,CAAC;EACtB,EAAE,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,qBAAqB,GAAG,EAAE,EAAE,qBAAqB,CAAC,QAAQ,CAAC,GAAG,MAAM,EAAE,qBAAqB,CAAC,YAAY,GAAG,MAAM,GAAG,MAAM,EAAE,qBAAqB,CAAC,CAAC;EAClL,CAAC;AACD;EACA,SAASF,QAAM,CAAC,KAAK,EAAE;EACvB,EAAE,IAAI,KAAK,GAAG,KAAK,CAAC,KAAK;EACzB,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;EAC9B,EAAE,IAAI,gBAAgB,GAAG,OAAO,CAAC,OAAO;EACxC,MAAM,YAAY,GAAG,gBAAgB,KAAK,KAAK,CAAC,GAAG,qBAAqB,GAAG,gBAAgB,CAAC;AAC5F;EACA,EAAE,IAAI,YAAY,IAAI,IAAI,EAAE;EAC5B,IAAI,OAAO;EACX,GAAG;AACH;AACA;EACA,EAAE,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE;EACxC,IAAI,YAAY,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;AACrE;EACA,IAAI,IAAI,CAAC,YAAY,EAAE;EACvB,MAAM,OAAO;EACb,KAAK;EACL,GAAG;AAOH;EACA,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,EAAE,YAAY,CAAC,EAAE;AAItD;EACA,IAAI,OAAO;EACX,GAAG;AACH;EACA,EAAE,KAAK,CAAC,QAAQ,CAAC,KAAK,GAAG,YAAY,CAAC;EACtC,CAAC;AACD;AACA;AACA,gBAAe;EACf,EAAE,IAAI,EAAE,OAAO;EACf,EAAE,OAAO,EAAE,IAAI;EACf,EAAE,KAAK,EAAE,MAAM;EACf,EAAE,EAAE,EAAE,KAAK;EACX,EAAE,MAAM,EAAEA,QAAM;EAChB,EAAE,QAAQ,EAAE,CAAC,eAAe,CAAC;EAC7B,EAAE,gBAAgB,EAAE,CAAC,iBAAiB,CAAC;EACvC,CAAC;;EC5FD,IAAI,UAAU,GAAG;EACjB,EAAE,GAAG,EAAE,MAAM;EACb,EAAE,KAAK,EAAE,MAAM;EACf,EAAE,MAAM,EAAE,MAAM;EAChB,EAAE,IAAI,EAAE,MAAM;EACd,CAAC,CAAC;EACF;EACA;AACA;EACA,SAAS,iBAAiB,CAAC,IAAI,EAAE;EACjC,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;EAChB,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;EACjB,EAAE,IAAI,GAAG,GAAG,MAAM,CAAC;EACnB,EAAE,IAAI,GAAG,GAAG,GAAG,CAAC,gBAAgB,IAAI,CAAC,CAAC;EACtC,EAAE,OAAO;EACT,IAAI,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC;EACvC,IAAI,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC;EACvC,GAAG,CAAC;EACJ,CAAC;AACD;EACO,SAAS,WAAW,CAAC,KAAK,EAAE;EACnC,EAAE,IAAI,eAAe,CAAC;AACtB;EACA,EAAE,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM;EAC3B,MAAM,UAAU,GAAG,KAAK,CAAC,UAAU;EACnC,MAAM,SAAS,GAAG,KAAK,CAAC,SAAS;EACjC,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO;EAC7B,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ;EAC/B,MAAM,eAAe,GAAG,KAAK,CAAC,eAAe;EAC7C,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ;EAC/B,MAAM,YAAY,GAAG,KAAK,CAAC,YAAY,CAAC;AACxC;EACA,EAAE,IAAI,KAAK,GAAG,YAAY,KAAK,IAAI,GAAG,iBAAiB,CAAC,OAAO,CAAC,GAAG,OAAO,YAAY,KAAK,UAAU,GAAG,YAAY,CAAC,OAAO,CAAC,GAAG,OAAO;EACvI,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC;EACvB,MAAM,CAAC,GAAG,OAAO,KAAK,KAAK,CAAC,GAAG,CAAC,GAAG,OAAO;EAC1C,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC;EACvB,MAAM,CAAC,GAAG,OAAO,KAAK,KAAK,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC;AAC3C;EACA,EAAE,IAAI,IAAI,GAAG,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;EACzC,EAAE,IAAI,IAAI,GAAG,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;EACzC,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC;EACnB,EAAE,IAAI,KAAK,GAAG,GAAG,CAAC;EAClB,EAAE,IAAI,GAAG,GAAG,MAAM,CAAC;AACnB;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,IAAI,YAAY,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;EAC/C,IAAI,IAAI,UAAU,GAAG,cAAc,CAAC;EACpC,IAAI,IAAI,SAAS,GAAG,aAAa,CAAC;AAClC;EACA,IAAI,IAAI,YAAY,KAAK,SAAS,CAAC,MAAM,CAAC,EAAE;EAC5C,MAAM,YAAY,GAAG,kBAAkB,CAAC,MAAM,CAAC,CAAC;AAChD;EACA,MAAM,IAAIra,kBAAgB,CAAC,YAAY,CAAC,CAAC,QAAQ,KAAK,QAAQ,EAAE;EAChE,QAAQ,UAAU,GAAG,cAAc,CAAC;EACpC,QAAQ,SAAS,GAAG,aAAa,CAAC;EAClC,OAAO;EACP,KAAK;AACL;AACA;EACA,IAAI,YAAY,GAAG,YAAY,CAAC;AAChC;EACA,IAAI,IAAI,SAAS,KAAK,GAAG,EAAE;EAC3B,MAAM,KAAK,GAAG,MAAM,CAAC;AACrB;EACA,MAAM,CAAC,IAAI,YAAY,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC;EACxD,MAAM,CAAC,IAAI,eAAe,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;EACpC,KAAK;AACL;EACA,IAAI,IAAI,SAAS,KAAK,IAAI,EAAE;EAC5B,MAAM,KAAK,GAAG,KAAK,CAAC;AACpB;EACA,MAAM,CAAC,IAAI,YAAY,CAAC,SAAS,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC;EACtD,MAAM,CAAC,IAAI,eAAe,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;EACpC,KAAK;EACL,GAAG;AACH;EACA,EAAE,IAAI,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC;EACnC,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG,EAAE,QAAQ,IAAI,UAAU,CAAC,CAAC;AAC7B;EACA,EAAE,IAAI,eAAe,EAAE;EACvB,IAAI,IAAI,cAAc,CAAC;AACvB;EACA,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,YAAY,GAAG,cAAc,GAAG,EAAE,EAAE,cAAc,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,GAAG,GAAG,EAAE,EAAE,cAAc,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,GAAG,GAAG,EAAE,EAAE,cAAc,CAAC,SAAS,GAAG,CAAC,GAAG,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,KAAK,GAAG,cAAc,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,QAAQ,EAAE,cAAc,EAAE,CAAC;EACrT,GAAG;AACH;EACA,EAAE,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,YAAY,GAAG,eAAe,GAAG,EAAE,EAAE,eAAe,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,EAAE,EAAE,eAAe,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,EAAE,EAAE,eAAe,CAAC,SAAS,GAAG,EAAE,EAAE,eAAe,EAAE,CAAC;EAChN,CAAC;AACD;EACA,SAAS,aAAa,CAAC,KAAK,EAAE;EAC9B,EAAE,IAAI,KAAK,GAAG,KAAK,CAAC,KAAK;EACzB,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;EAC9B,EAAE,IAAI,qBAAqB,GAAG,OAAO,CAAC,eAAe;EACrD,MAAM,eAAe,GAAG,qBAAqB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,qBAAqB;EACvF,MAAM,iBAAiB,GAAG,OAAO,CAAC,QAAQ;EAC1C,MAAM,QAAQ,GAAG,iBAAiB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,iBAAiB;EACxE,MAAM,qBAAqB,GAAG,OAAO,CAAC,YAAY;EAClD,MAAM,YAAY,GAAG,qBAAqB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,qBAAqB,CAAC;AAWrF;EACA,EAAE,IAAI,YAAY,GAAG;EACrB,IAAI,SAAS,EAAE,gBAAgB,CAAC,KAAK,CAAC,SAAS,CAAC;EAChD,IAAI,MAAM,EAAE,KAAK,CAAC,QAAQ,CAAC,MAAM;EACjC,IAAI,UAAU,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM;EAClC,IAAI,eAAe,EAAE,eAAe;EACpC,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,KAAK,CAAC,aAAa,CAAC,aAAa,IAAI,IAAI,EAAE;EACjD,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,YAAY,EAAE;EAC7G,MAAM,OAAO,EAAE,KAAK,CAAC,aAAa,CAAC,aAAa;EAChD,MAAM,QAAQ,EAAE,KAAK,CAAC,OAAO,CAAC,QAAQ;EACtC,MAAM,QAAQ,EAAE,QAAQ;EACxB,MAAM,YAAY,EAAE,YAAY;EAChC,KAAK,CAAC,CAAC,CAAC,CAAC;EACT,GAAG;AACH;EACA,EAAE,IAAI,KAAK,CAAC,aAAa,CAAC,KAAK,IAAI,IAAI,EAAE;EACzC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,YAAY,EAAE;EAC3G,MAAM,OAAO,EAAE,KAAK,CAAC,aAAa,CAAC,KAAK;EACxC,MAAM,QAAQ,EAAE,UAAU;EAC1B,MAAM,QAAQ,EAAE,KAAK;EACrB,MAAM,YAAY,EAAE,YAAY;EAChC,KAAK,CAAC,CAAC,CAAC,CAAC;EACT,GAAG;AACH;EACA,EAAE,KAAK,CAAC,UAAU,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,UAAU,CAAC,MAAM,EAAE;EACvE,IAAI,uBAAuB,EAAE,KAAK,CAAC,SAAS;EAC5C,GAAG,CAAC,CAAC;EACL,CAAC;AACD;AACA;AACA,wBAAe;EACf,EAAE,IAAI,EAAE,eAAe;EACvB,EAAE,OAAO,EAAE,IAAI;EACf,EAAE,KAAK,EAAE,aAAa;EACtB,EAAE,EAAE,EAAE,aAAa;EACnB,EAAE,IAAI,EAAE,EAAE;EACV,CAAC;;ECxJD,IAAI,OAAO,GAAG;EACd,EAAE,OAAO,EAAE,IAAI;EACf,CAAC,CAAC;AACF;EACA,SAAS,MAAM,CAAC,IAAI,EAAE;EACtB,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK;EACxB,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ;EAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;EAC7B,EAAE,IAAI,eAAe,GAAG,OAAO,CAAC,MAAM;EACtC,MAAM,MAAM,GAAG,eAAe,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,eAAe;EAClE,MAAM,eAAe,GAAG,OAAO,CAAC,MAAM;EACtC,MAAM,MAAM,GAAG,eAAe,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,eAAe,CAAC;EACnE,EAAE,IAAI,MAAM,GAAG,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;EAChD,EAAE,IAAI,aAAa,GAAG,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,SAAS,EAAE,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;AAC3F;EACA,EAAE,IAAI,MAAM,EAAE;EACd,IAAI,aAAa,CAAC,OAAO,CAAC,UAAU,YAAY,EAAE;EAClD,MAAM,YAAY,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;EACxE,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA,EAAE,IAAI,MAAM,EAAE;EACd,IAAI,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;EAChE,GAAG;AACH;EACA,EAAE,OAAO,YAAY;EACrB,IAAI,IAAI,MAAM,EAAE;EAChB,MAAM,aAAa,CAAC,OAAO,CAAC,UAAU,YAAY,EAAE;EACpD,QAAQ,YAAY,CAAC,mBAAmB,CAAC,QAAQ,EAAE,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;EAC7E,OAAO,CAAC,CAAC;EACT,KAAK;AACL;EACA,IAAI,IAAI,MAAM,EAAE;EAChB,MAAM,MAAM,CAAC,mBAAmB,CAAC,QAAQ,EAAE,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;EACrE,KAAK;EACL,GAAG,CAAC;EACJ,CAAC;AACD;AACA;AACA,uBAAe;EACf,EAAE,IAAI,EAAE,gBAAgB;EACxB,EAAE,OAAO,EAAE,IAAI;EACf,EAAE,KAAK,EAAE,OAAO;EAChB,EAAE,EAAE,EAAE,SAAS,EAAE,GAAG,EAAE;EACtB,EAAE,MAAM,EAAE,MAAM;EAChB,EAAE,IAAI,EAAE,EAAE;EACV,CAAC;;EChDD,IAAI0a,MAAI,GAAG;EACX,EAAE,IAAI,EAAE,OAAO;EACf,EAAE,KAAK,EAAE,MAAM;EACf,EAAE,MAAM,EAAE,KAAK;EACf,EAAE,GAAG,EAAE,QAAQ;EACf,CAAC,CAAC;EACa,SAAS,oBAAoB,CAAC,SAAS,EAAE;EACxD,EAAE,OAAO,SAAS,CAAC,OAAO,CAAC,wBAAwB,EAAE,UAAU,OAAO,EAAE;EACxE,IAAI,OAAOA,MAAI,CAAC,OAAO,CAAC,CAAC;EACzB,GAAG,CAAC,CAAC;EACL;;ECVA,IAAI,IAAI,GAAG;EACX,EAAE,KAAK,EAAE,KAAK;EACd,EAAE,GAAG,EAAE,OAAO;EACd,CAAC,CAAC;EACa,SAAS,6BAA6B,CAAC,SAAS,EAAE;EACjE,EAAE,OAAO,SAAS,CAAC,OAAO,CAAC,YAAY,EAAE,UAAU,OAAO,EAAE;EAC5D,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;EACzB,GAAG,CAAC,CAAC;EACL;;ECPe,SAAS,eAAe,CAAC,IAAI,EAAE;EAC9C,EAAE,IAAI,GAAG,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;EAC5B,EAAE,IAAI,UAAU,GAAG,GAAG,CAAC,WAAW,CAAC;EACnC,EAAE,IAAI,SAAS,GAAG,GAAG,CAAC,WAAW,CAAC;EAClC,EAAE,OAAO;EACT,IAAI,UAAU,EAAE,UAAU;EAC1B,IAAI,SAAS,EAAE,SAAS;EACxB,GAAG,CAAC;EACJ;;ECNe,SAAS,mBAAmB,CAAC,OAAO,EAAE;EACrD;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,OAAO,qBAAqB,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,GAAG,eAAe,CAAC,OAAO,CAAC,CAAC,UAAU,CAAC;EACvG;;ECTe,SAAS,eAAe,CAAC,OAAO,EAAE;EACjD,EAAE,IAAI,GAAG,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC;EAC/B,EAAE,IAAI,IAAI,GAAG,kBAAkB,CAAC,OAAO,CAAC,CAAC;EACzC,EAAE,IAAI,cAAc,GAAG,GAAG,CAAC,cAAc,CAAC;EAC1C,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC;EAC/B,EAAE,IAAI,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC;EACjC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;EACZ,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;EACZ;EACA;EACA;EACA;AACA;EACA,EAAE,IAAI,cAAc,EAAE;EACtB,IAAI,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC;EACjC,IAAI,MAAM,GAAG,cAAc,CAAC,MAAM,CAAC;EACnC;EACA;EACA;EACA;EACA;EACA;EACA;AACA;EACA,IAAI,IAAI,CAAC,gCAAgC,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE;EACrE,MAAM,CAAC,GAAG,cAAc,CAAC,UAAU,CAAC;EACpC,MAAM,CAAC,GAAG,cAAc,CAAC,SAAS,CAAC;EACnC,KAAK;EACL,GAAG;AACH;EACA,EAAE,OAAO;EACT,IAAI,KAAK,EAAE,KAAK;EAChB,IAAI,MAAM,EAAE,MAAM;EAClB,IAAI,CAAC,EAAE,CAAC,GAAG,mBAAmB,CAAC,OAAO,CAAC;EACvC,IAAI,CAAC,EAAE,CAAC;EACR,GAAG,CAAC;EACJ;;EClCA;AACA;EACe,SAAS,eAAe,CAAC,OAAO,EAAE;EACjD,EAAE,IAAI,qBAAqB,CAAC;AAC5B;EACA,EAAE,IAAI,IAAI,GAAG,kBAAkB,CAAC,OAAO,CAAC,CAAC;EACzC,EAAE,IAAI,SAAS,GAAG,eAAe,CAAC,OAAO,CAAC,CAAC;EAC3C,EAAE,IAAI,IAAI,GAAG,CAAC,qBAAqB,GAAG,OAAO,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,qBAAqB,CAAC,IAAI,CAAC;EAC3G,EAAE,IAAI,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;EAChH,EAAE,IAAI,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,GAAG,IAAI,CAAC,YAAY,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;EACrH,EAAE,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,UAAU,GAAG,mBAAmB,CAAC,OAAO,CAAC,CAAC;EAC/D,EAAE,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,SAAS,CAAC;AAC/B;EACA,EAAE,IAAI1a,kBAAgB,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,SAAS,KAAK,KAAK,EAAE;EAC1D,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;EACpE,GAAG;AACH;EACA,EAAE,OAAO;EACT,IAAI,KAAK,EAAE,KAAK;EAChB,IAAI,MAAM,EAAE,MAAM;EAClB,IAAI,CAAC,EAAE,CAAC;EACR,IAAI,CAAC,EAAE,CAAC;EACR,GAAG,CAAC;EACJ;;EC3Be,SAAS,cAAc,CAAC,OAAO,EAAE;EAChD;EACA,EAAE,IAAI,iBAAiB,GAAGA,kBAAgB,CAAC,OAAO,CAAC;EACnD,MAAM,QAAQ,GAAG,iBAAiB,CAAC,QAAQ;EAC3C,MAAM,SAAS,GAAG,iBAAiB,CAAC,SAAS;EAC7C,MAAM,SAAS,GAAG,iBAAiB,CAAC,SAAS,CAAC;AAC9C;EACA,EAAE,OAAO,4BAA4B,CAAC,IAAI,CAAC,QAAQ,GAAG,SAAS,GAAG,SAAS,CAAC,CAAC;EAC7E;;ECLe,SAAS,eAAe,CAAC,IAAI,EAAE;EAC9C,EAAE,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE;EACrE;EACA,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;EACnC,GAAG;AACH;EACA,EAAE,IAAI,aAAa,CAAC,IAAI,CAAC,IAAI,cAAc,CAAC,IAAI,CAAC,EAAE;EACnD,IAAI,OAAO,IAAI,CAAC;EAChB,GAAG;AACH;EACA,EAAE,OAAO,eAAe,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;EAC9C;;ECXA;EACA;EACA;EACA;EACA;EACA;AACA;EACe,SAAS,iBAAiB,CAAC,OAAO,EAAE,IAAI,EAAE;EACzD,EAAE,IAAI,qBAAqB,CAAC;AAC5B;EACA,EAAE,IAAI,IAAI,KAAK,KAAK,CAAC,EAAE;EACvB,IAAI,IAAI,GAAG,EAAE,CAAC;EACd,GAAG;AACH;EACA,EAAE,IAAI,YAAY,GAAG,eAAe,CAAC,OAAO,CAAC,CAAC;EAC9C,EAAE,IAAI,MAAM,GAAG,YAAY,MAAM,CAAC,qBAAqB,GAAG,OAAO,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,qBAAqB,CAAC,IAAI,CAAC,CAAC;EAChI,EAAE,IAAI,GAAG,GAAG,SAAS,CAAC,YAAY,CAAC,CAAC;EACpC,EAAE,IAAI,MAAM,GAAG,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,cAAc,IAAI,EAAE,EAAE,cAAc,CAAC,YAAY,CAAC,GAAG,YAAY,GAAG,EAAE,CAAC,GAAG,YAAY,CAAC;EAChI,EAAE,IAAI,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;EACxC,EAAE,OAAO,MAAM,GAAG,WAAW;EAC7B,EAAE,WAAW,CAAC,MAAM,CAAC,iBAAiB,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;EAC/D;;ECzBe,SAAS,gBAAgB,CAAC,IAAI,EAAE;EAC/C,EAAE,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,EAAE;EACjC,IAAI,IAAI,EAAE,IAAI,CAAC,CAAC;EAChB,IAAI,GAAG,EAAE,IAAI,CAAC,CAAC;EACf,IAAI,KAAK,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK;EAC9B,IAAI,MAAM,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM;EAChC,GAAG,CAAC,CAAC;EACL;;ECQA,SAAS,0BAA0B,CAAC,OAAO,EAAE;EAC7C,EAAE,IAAI,IAAI,GAAG,qBAAqB,CAAC,OAAO,CAAC,CAAC;EAC5C,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,SAAS,CAAC;EAC1C,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,UAAU,CAAC;EAC7C,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,YAAY,CAAC;EAChD,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,WAAW,CAAC;EAC/C,EAAE,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,WAAW,CAAC;EACnC,EAAE,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,YAAY,CAAC;EACrC,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;EACrB,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;EACpB,EAAE,OAAO,IAAI,CAAC;EACd,CAAC;AACD;EACA,SAAS,0BAA0B,CAAC,OAAO,EAAE,cAAc,EAAE;EAC7D,EAAE,OAAO,cAAc,KAAK,QAAQ,GAAG,gBAAgB,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,GAAG,aAAa,CAAC,cAAc,CAAC,GAAG,0BAA0B,CAAC,cAAc,CAAC,GAAG,gBAAgB,CAAC,eAAe,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;EAChO,CAAC;EACD;EACA;AACA;AACA;EACA,SAAS,kBAAkB,CAAC,OAAO,EAAE;EACrC,EAAE,IAAI,eAAe,GAAG,iBAAiB,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;EAClE,EAAE,IAAI,iBAAiB,GAAG,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,OAAO,CAACA,kBAAgB,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;EACjG,EAAE,IAAI,cAAc,GAAG,iBAAiB,IAAI,aAAa,CAAC,OAAO,CAAC,GAAG,eAAe,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC;AACxG;EACA,EAAE,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,EAAE;EAClC,IAAI,OAAO,EAAE,CAAC;EACd,GAAG;AACH;AACA;EACA,EAAE,OAAO,eAAe,CAAC,MAAM,CAAC,UAAU,cAAc,EAAE;EAC1D,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,QAAQ,CAAC,cAAc,EAAE,cAAc,CAAC,IAAI,WAAW,CAAC,cAAc,CAAC,KAAK,MAAM,CAAC;EAC3H,GAAG,CAAC,CAAC;EACL,CAAC;EACD;AACA;AACA;EACe,SAAS,eAAe,CAAC,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE;EACzE,EAAE,IAAI,mBAAmB,GAAG,QAAQ,KAAK,iBAAiB,GAAG,kBAAkB,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;EAC/G,EAAE,IAAI,eAAe,GAAG,EAAE,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC;EACvE,EAAE,IAAI,mBAAmB,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;EAC/C,EAAE,IAAI,YAAY,GAAG,eAAe,CAAC,MAAM,CAAC,UAAU,OAAO,EAAE,cAAc,EAAE;EAC/E,IAAI,IAAI,IAAI,GAAG,0BAA0B,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;EACnE,IAAI,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;EAC7C,IAAI,OAAO,CAAC,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;EACnD,IAAI,OAAO,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;EACtD,IAAI,OAAO,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;EAChD,IAAI,OAAO,OAAO,CAAC;EACnB,GAAG,EAAE,0BAA0B,CAAC,OAAO,EAAE,mBAAmB,CAAC,CAAC,CAAC;EAC/D,EAAE,YAAY,CAAC,KAAK,GAAG,YAAY,CAAC,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC;EAC9D,EAAE,YAAY,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,GAAG,YAAY,CAAC,GAAG,CAAC;EAC/D,EAAE,YAAY,CAAC,CAAC,GAAG,YAAY,CAAC,IAAI,CAAC;EACrC,EAAE,YAAY,CAAC,CAAC,GAAG,YAAY,CAAC,GAAG,CAAC;EACpC,EAAE,OAAO,YAAY,CAAC;EACtB;;ECrEe,SAAS,YAAY,CAAC,SAAS,EAAE;EAChD,EAAE,OAAO,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EACjC;;ECEe,SAAS,cAAc,CAAC,IAAI,EAAE;EAC7C,EAAE,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS;EAChC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO;EAC5B,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;EACjC,EAAE,IAAI,aAAa,GAAG,SAAS,GAAG,gBAAgB,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;EACrE,EAAE,IAAI,SAAS,GAAG,SAAS,GAAG,YAAY,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;EAC7D,EAAE,IAAI,OAAO,GAAG,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,KAAK,GAAG,CAAC,GAAG,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC;EACtE,EAAE,IAAI,OAAO,GAAG,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;EACxE,EAAE,IAAI,OAAO,CAAC;AACd;EACA,EAAE,QAAQ,aAAa;EACvB,IAAI,KAAK,GAAG;EACZ,MAAM,OAAO,GAAG;EAChB,QAAQ,CAAC,EAAE,OAAO;EAClB,QAAQ,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM;EACvC,OAAO,CAAC;EACR,MAAM,MAAM;AACZ;EACA,IAAI,KAAK,MAAM;EACf,MAAM,OAAO,GAAG;EAChB,QAAQ,CAAC,EAAE,OAAO;EAClB,QAAQ,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,MAAM;EACzC,OAAO,CAAC;EACR,MAAM,MAAM;AACZ;EACA,IAAI,KAAK,KAAK;EACd,MAAM,OAAO,GAAG;EAChB,QAAQ,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,KAAK;EACxC,QAAQ,CAAC,EAAE,OAAO;EAClB,OAAO,CAAC;EACR,MAAM,MAAM;AACZ;EACA,IAAI,KAAK,IAAI;EACb,MAAM,OAAO,GAAG;EAChB,QAAQ,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,OAAO,CAAC,KAAK;EACtC,QAAQ,CAAC,EAAE,OAAO;EAClB,OAAO,CAAC;EACR,MAAM,MAAM;AACZ;EACA,IAAI;EACJ,MAAM,OAAO,GAAG;EAChB,QAAQ,CAAC,EAAE,SAAS,CAAC,CAAC;EACtB,QAAQ,CAAC,EAAE,SAAS,CAAC,CAAC;EACtB,OAAO,CAAC;EACR,GAAG;AACH;EACA,EAAE,IAAI,QAAQ,GAAG,aAAa,GAAG,wBAAwB,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC;AAChF;EACA,EAAE,IAAI,QAAQ,IAAI,IAAI,EAAE;EACxB,IAAI,IAAI,GAAG,GAAG,QAAQ,KAAK,GAAG,GAAG,QAAQ,GAAG,OAAO,CAAC;AACpD;EACA,IAAI,QAAQ,SAAS;EACrB,MAAM,KAAK,KAAK;EAChB,QAAQ,OAAO,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;EACxF,QAAQ,MAAM;AACd;EACA,MAAM,KAAK,GAAG;EACd,QAAQ,OAAO,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;EACxF,QAAQ,MAAM;EAGd,KAAK;EACL,GAAG;AACH;EACA,EAAE,OAAO,OAAO,CAAC;EACjB;;EC3De,SAAS,cAAc,CAAC,KAAK,EAAE,OAAO,EAAE;EACvD,EAAE,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE;EAC1B,IAAI,OAAO,GAAG,EAAE,CAAC;EACjB,GAAG;AACH;EACA,EAAE,IAAI,QAAQ,GAAG,OAAO;EACxB,MAAM,kBAAkB,GAAG,QAAQ,CAAC,SAAS;EAC7C,MAAM,SAAS,GAAG,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,SAAS,GAAG,kBAAkB;EACtF,MAAM,iBAAiB,GAAG,QAAQ,CAAC,QAAQ;EAC3C,MAAM,QAAQ,GAAG,iBAAiB,KAAK,KAAK,CAAC,GAAG,eAAe,GAAG,iBAAiB;EACnF,MAAM,qBAAqB,GAAG,QAAQ,CAAC,YAAY;EACnD,MAAM,YAAY,GAAG,qBAAqB,KAAK,KAAK,CAAC,GAAG,QAAQ,GAAG,qBAAqB;EACxF,MAAM,qBAAqB,GAAG,QAAQ,CAAC,cAAc;EACrD,MAAM,cAAc,GAAG,qBAAqB,KAAK,KAAK,CAAC,GAAG,MAAM,GAAG,qBAAqB;EACxF,MAAM,oBAAoB,GAAG,QAAQ,CAAC,WAAW;EACjD,MAAM,WAAW,GAAG,oBAAoB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAG,oBAAoB;EAClF,MAAM,gBAAgB,GAAG,QAAQ,CAAC,OAAO;EACzC,MAAM,OAAO,GAAG,gBAAgB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAG,gBAAgB,CAAC;EACnE,EAAE,IAAI,aAAa,GAAG,kBAAkB,CAAC,OAAO,OAAO,KAAK,QAAQ,GAAG,OAAO,GAAG,eAAe,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC,CAAC;EAC3H,EAAE,IAAI,UAAU,GAAG,cAAc,KAAK,MAAM,GAAG,SAAS,GAAG,MAAM,CAAC;EAClE,EAAE,IAAI,gBAAgB,GAAG,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC;EAClD,EAAE,IAAI,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC;EACtC,EAAE,IAAI,OAAO,GAAG,KAAK,CAAC,QAAQ,CAAC,WAAW,GAAG,UAAU,GAAG,cAAc,CAAC,CAAC;EAC1E,EAAE,IAAI,kBAAkB,GAAG,eAAe,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,OAAO,GAAG,OAAO,CAAC,cAAc,IAAI,kBAAkB,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;EACvK,EAAE,IAAI,mBAAmB,GAAG,qBAAqB,CAAC,gBAAgB,CAAC,CAAC;EACpE,EAAE,IAAI,aAAa,GAAG,cAAc,CAAC;EACrC,IAAI,SAAS,EAAE,mBAAmB;EAClC,IAAI,OAAO,EAAE,UAAU;EACvB,IAAI,QAAQ,EAAE,UAAU;EACxB,IAAI,SAAS,EAAE,SAAS;EACxB,GAAG,CAAC,CAAC;EACL,EAAE,IAAI,gBAAgB,GAAG,gBAAgB,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC,CAAC;EACxF,EAAE,IAAI,iBAAiB,GAAG,cAAc,KAAK,MAAM,GAAG,gBAAgB,GAAG,mBAAmB,CAAC;EAC7F;AACA;EACA,EAAE,IAAI,eAAe,GAAG;EACxB,IAAI,GAAG,EAAE,kBAAkB,CAAC,GAAG,GAAG,iBAAiB,CAAC,GAAG,GAAG,aAAa,CAAC,GAAG;EAC3E,IAAI,MAAM,EAAE,iBAAiB,CAAC,MAAM,GAAG,kBAAkB,CAAC,MAAM,GAAG,aAAa,CAAC,MAAM;EACvF,IAAI,IAAI,EAAE,kBAAkB,CAAC,IAAI,GAAG,iBAAiB,CAAC,IAAI,GAAG,aAAa,CAAC,IAAI;EAC/E,IAAI,KAAK,EAAE,iBAAiB,CAAC,KAAK,GAAG,kBAAkB,CAAC,KAAK,GAAG,aAAa,CAAC,KAAK;EACnF,GAAG,CAAC;EACJ,EAAE,IAAI,UAAU,GAAG,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC;AAC9C;EACA,EAAE,IAAI,cAAc,KAAK,MAAM,IAAI,UAAU,EAAE;EAC/C,IAAI,IAAI,MAAM,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC;EACvC,IAAI,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE;EACxD,MAAM,IAAI,QAAQ,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;EAChE,MAAM,IAAI,IAAI,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;EAC7D,MAAM,eAAe,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC;EACtD,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA,EAAE,OAAO,eAAe,CAAC;EACzB;;EC3De,SAAS,oBAAoB,CAAC,KAAK,EAAE,OAAO,EAAE;EAC7D,EAAE,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE;EAC1B,IAAI,OAAO,GAAG,EAAE,CAAC;EACjB,GAAG;AACH;EACA,EAAE,IAAI,QAAQ,GAAG,OAAO;EACxB,MAAM,SAAS,GAAG,QAAQ,CAAC,SAAS;EACpC,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ;EAClC,MAAM,YAAY,GAAG,QAAQ,CAAC,YAAY;EAC1C,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO;EAChC,MAAM,cAAc,GAAG,QAAQ,CAAC,cAAc;EAC9C,MAAM,qBAAqB,GAAG,QAAQ,CAAC,qBAAqB;EAC5D,MAAM,qBAAqB,GAAG,qBAAqB,KAAK,KAAK,CAAC,GAAG2a,UAAa,GAAG,qBAAqB,CAAC;EACvG,EAAE,IAAI,SAAS,GAAG,YAAY,CAAC,SAAS,CAAC,CAAC;EAC1C,EAAE,IAAIC,YAAU,GAAG,SAAS,GAAG,cAAc,GAAG,mBAAmB,GAAG,mBAAmB,CAAC,MAAM,CAAC,UAAU,SAAS,EAAE;EACtH,IAAI,OAAO,YAAY,CAAC,SAAS,CAAC,KAAK,SAAS,CAAC;EACjD,GAAG,CAAC,GAAG,cAAc,CAAC;EACtB,EAAE,IAAI,iBAAiB,GAAGA,YAAU,CAAC,MAAM,CAAC,UAAU,SAAS,EAAE;EACjE,IAAI,OAAO,qBAAqB,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;EACzD,GAAG,CAAC,CAAC;AACL;EACA,EAAE,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE;EACtC,IAAI,iBAAiB,GAAGA,YAAU,CAAC;EAKnC,GAAG;AACH;AACA;EACA,EAAE,IAAI,SAAS,GAAG,iBAAiB,CAAC,MAAM,CAAC,UAAU,GAAG,EAAE,SAAS,EAAE;EACrE,IAAI,GAAG,CAAC,SAAS,CAAC,GAAG,cAAc,CAAC,KAAK,EAAE;EAC3C,MAAM,SAAS,EAAE,SAAS;EAC1B,MAAM,QAAQ,EAAE,QAAQ;EACxB,MAAM,YAAY,EAAE,YAAY;EAChC,MAAM,OAAO,EAAE,OAAO;EACtB,KAAK,CAAC,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,CAAC;EACpC,IAAI,OAAO,GAAG,CAAC;EACf,GAAG,EAAE,EAAE,CAAC,CAAC;EACT,EAAE,OAAO,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE;EACrD,IAAI,OAAO,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;EACvC,GAAG,CAAC,CAAC;EACL;;ECtCA,SAAS,6BAA6B,CAAC,SAAS,EAAE;EAClD,EAAE,IAAI,gBAAgB,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE;EAC5C,IAAI,OAAO,EAAE,CAAC;EACd,GAAG;AACH;EACA,EAAE,IAAI,iBAAiB,GAAG,oBAAoB,CAAC,SAAS,CAAC,CAAC;EAC1D,EAAE,OAAO,CAAC,6BAA6B,CAAC,SAAS,CAAC,EAAE,iBAAiB,EAAE,6BAA6B,CAAC,iBAAiB,CAAC,CAAC,CAAC;EACzH,CAAC;AACD;EACA,SAAS,IAAI,CAAC,IAAI,EAAE;EACpB,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK;EACxB,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO;EAC5B,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACvB;EACA,EAAE,IAAI,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE;EACvC,IAAI,OAAO;EACX,GAAG;AACH;EACA,EAAE,IAAI,iBAAiB,GAAG,OAAO,CAAC,QAAQ;EAC1C,MAAM,aAAa,GAAG,iBAAiB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,iBAAiB;EAC7E,MAAM,gBAAgB,GAAG,OAAO,CAAC,OAAO;EACxC,MAAM,YAAY,GAAG,gBAAgB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,gBAAgB;EAC1E,MAAM,2BAA2B,GAAG,OAAO,CAAC,kBAAkB;EAC9D,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO;EAC/B,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ;EACjC,MAAM,YAAY,GAAG,OAAO,CAAC,YAAY;EACzC,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW;EACvC,MAAM,qBAAqB,GAAG,OAAO,CAAC,cAAc;EACpD,MAAM,cAAc,GAAG,qBAAqB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,qBAAqB;EACtF,MAAM,qBAAqB,GAAG,OAAO,CAAC,qBAAqB,CAAC;EAC5D,EAAE,IAAI,kBAAkB,GAAG,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC;EACnD,EAAE,IAAI,aAAa,GAAG,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;EAC3D,EAAE,IAAI,eAAe,GAAG,aAAa,KAAK,kBAAkB,CAAC;EAC7D,EAAE,IAAI,kBAAkB,GAAG,2BAA2B,KAAK,eAAe,IAAI,CAAC,cAAc,GAAG,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,CAAC,GAAG,6BAA6B,CAAC,kBAAkB,CAAC,CAAC,CAAC;EAChM,EAAE,IAAI,UAAU,GAAG,CAAC,kBAAkB,CAAC,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,MAAM,CAAC,UAAU,GAAG,EAAE,SAAS,EAAE;EACpG,IAAI,OAAO,GAAG,CAAC,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,KAAK,IAAI,GAAG,oBAAoB,CAAC,KAAK,EAAE;EACzF,MAAM,SAAS,EAAE,SAAS;EAC1B,MAAM,QAAQ,EAAE,QAAQ;EACxB,MAAM,YAAY,EAAE,YAAY;EAChC,MAAM,OAAO,EAAE,OAAO;EACtB,MAAM,cAAc,EAAE,cAAc;EACpC,MAAM,qBAAqB,EAAE,qBAAqB;EAClD,KAAK,CAAC,GAAG,SAAS,CAAC,CAAC;EACpB,GAAG,EAAE,EAAE,CAAC,CAAC;EACT,EAAE,IAAI,aAAa,GAAG,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC;EAC5C,EAAE,IAAI,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC;EACtC,EAAE,IAAI,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;EAC5B,EAAE,IAAI,kBAAkB,GAAG,IAAI,CAAC;EAChC,EAAE,IAAI,qBAAqB,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AAC5C;EACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EAC9C,IAAI,IAAI,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AAClC;EACA,IAAI,IAAI,cAAc,GAAG,gBAAgB,CAAC,SAAS,CAAC,CAAC;AACrD;EACA,IAAI,IAAI,gBAAgB,GAAG,YAAY,CAAC,SAAS,CAAC,KAAK,KAAK,CAAC;EAC7D,IAAI,IAAI,UAAU,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;EAChE,IAAI,IAAI,GAAG,GAAG,UAAU,GAAG,OAAO,GAAG,QAAQ,CAAC;EAC9C,IAAI,IAAI,QAAQ,GAAG,cAAc,CAAC,KAAK,EAAE;EACzC,MAAM,SAAS,EAAE,SAAS;EAC1B,MAAM,QAAQ,EAAE,QAAQ;EACxB,MAAM,YAAY,EAAE,YAAY;EAChC,MAAM,WAAW,EAAE,WAAW;EAC9B,MAAM,OAAO,EAAE,OAAO;EACtB,KAAK,CAAC,CAAC;EACP,IAAI,IAAI,iBAAiB,GAAG,UAAU,GAAG,gBAAgB,GAAG,KAAK,GAAG,IAAI,GAAG,gBAAgB,GAAG,MAAM,GAAG,GAAG,CAAC;AAC3G;EACA,IAAI,IAAI,aAAa,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC,EAAE;EAC9C,MAAM,iBAAiB,GAAG,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;EAClE,KAAK;AACL;EACA,IAAI,IAAI,gBAAgB,GAAG,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;EACnE,IAAI,IAAI,MAAM,GAAG,EAAE,CAAC;AACpB;EACA,IAAI,IAAI,aAAa,EAAE;EACvB,MAAM,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;EACjD,KAAK;AACL;EACA,IAAI,IAAI,YAAY,EAAE;EACtB,MAAM,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC;EACrF,KAAK;AACL;EACA,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,UAAU,KAAK,EAAE;EACtC,MAAM,OAAO,KAAK,CAAC;EACnB,KAAK,CAAC,EAAE;EACR,MAAM,qBAAqB,GAAG,SAAS,CAAC;EACxC,MAAM,kBAAkB,GAAG,KAAK,CAAC;EACjC,MAAM,MAAM;EACZ,KAAK;AACL;EACA,IAAI,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;EACrC,GAAG;AACH;EACA,EAAE,IAAI,kBAAkB,EAAE;EAC1B;EACA,IAAI,IAAI,cAAc,GAAG,cAAc,GAAG,CAAC,GAAG,CAAC,CAAC;AAChD;EACA,IAAI,IAAI,KAAK,GAAG,SAAS,KAAK,CAAC,EAAE,EAAE;EACnC,MAAM,IAAI,gBAAgB,GAAG,UAAU,CAAC,IAAI,CAAC,UAAU,SAAS,EAAE;EAClE,QAAQ,IAAI,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AAC9C;EACA,QAAQ,IAAI,MAAM,EAAE;EACpB,UAAU,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,UAAU,KAAK,EAAE;EAC5D,YAAY,OAAO,KAAK,CAAC;EACzB,WAAW,CAAC,CAAC;EACb,SAAS;EACT,OAAO,CAAC,CAAC;AACT;EACA,MAAM,IAAI,gBAAgB,EAAE;EAC5B,QAAQ,qBAAqB,GAAG,gBAAgB,CAAC;EACjD,QAAQ,OAAO,OAAO,CAAC;EACvB,OAAO;EACP,KAAK,CAAC;AACN;EACA,IAAI,KAAK,IAAI,EAAE,GAAG,cAAc,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE;EAChD,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC;AAC3B;EACA,MAAM,IAAI,IAAI,KAAK,OAAO,EAAE,MAAM;EAClC,KAAK;EACL,GAAG;AACH;EACA,EAAE,IAAI,KAAK,CAAC,SAAS,KAAK,qBAAqB,EAAE;EACjD,IAAI,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC;EAC3C,IAAI,KAAK,CAAC,SAAS,GAAG,qBAAqB,CAAC;EAC5C,IAAI,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC;EACvB,GAAG;EACH,CAAC;AACD;AACA;AACA,eAAe;EACf,EAAE,IAAI,EAAE,MAAM;EACd,EAAE,OAAO,EAAE,IAAI;EACf,EAAE,KAAK,EAAE,MAAM;EACf,EAAE,EAAE,EAAE,IAAI;EACV,EAAE,gBAAgB,EAAE,CAAC,QAAQ,CAAC;EAC9B,EAAE,IAAI,EAAE;EACR,IAAI,KAAK,EAAE,KAAK;EAChB,GAAG;EACH,CAAC;;EC/ID,SAAS,cAAc,CAAC,QAAQ,EAAE,IAAI,EAAE,gBAAgB,EAAE;EAC1D,EAAE,IAAI,gBAAgB,KAAK,KAAK,CAAC,EAAE;EACnC,IAAI,gBAAgB,GAAG;EACvB,MAAM,CAAC,EAAE,CAAC;EACV,MAAM,CAAC,EAAE,CAAC;EACV,KAAK,CAAC;EACN,GAAG;AACH;EACA,EAAE,OAAO;EACT,IAAI,GAAG,EAAE,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,GAAG,gBAAgB,CAAC,CAAC;EACxD,IAAI,KAAK,EAAE,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,gBAAgB,CAAC,CAAC;EAC3D,IAAI,MAAM,EAAE,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,gBAAgB,CAAC,CAAC;EAC9D,IAAI,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG,gBAAgB,CAAC,CAAC;EACzD,GAAG,CAAC;EACJ,CAAC;AACD;EACA,SAAS,qBAAqB,CAAC,QAAQ,EAAE;EACzC,EAAE,OAAO,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE;EACzD,IAAI,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EAC/B,GAAG,CAAC,CAAC;EACL,CAAC;AACD;EACA,SAAS9B,MAAI,CAAC,IAAI,EAAE;EACpB,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK;EACxB,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;EACvB,EAAE,IAAI,aAAa,GAAG,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC;EAC5C,EAAE,IAAI,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC;EACtC,EAAE,IAAI,gBAAgB,GAAG,KAAK,CAAC,aAAa,CAAC,eAAe,CAAC;EAC7D,EAAE,IAAI,iBAAiB,GAAG,cAAc,CAAC,KAAK,EAAE;EAChD,IAAI,cAAc,EAAE,WAAW;EAC/B,GAAG,CAAC,CAAC;EACL,EAAE,IAAI,iBAAiB,GAAG,cAAc,CAAC,KAAK,EAAE;EAChD,IAAI,WAAW,EAAE,IAAI;EACrB,GAAG,CAAC,CAAC;EACL,EAAE,IAAI,wBAAwB,GAAG,cAAc,CAAC,iBAAiB,EAAE,aAAa,CAAC,CAAC;EAClF,EAAE,IAAI,mBAAmB,GAAG,cAAc,CAAC,iBAAiB,EAAE,UAAU,EAAE,gBAAgB,CAAC,CAAC;EAC5F,EAAE,IAAI,iBAAiB,GAAG,qBAAqB,CAAC,wBAAwB,CAAC,CAAC;EAC1E,EAAE,IAAI,gBAAgB,GAAG,qBAAqB,CAAC,mBAAmB,CAAC,CAAC;EACpE,EAAE,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG;EAC9B,IAAI,wBAAwB,EAAE,wBAAwB;EACtD,IAAI,mBAAmB,EAAE,mBAAmB;EAC5C,IAAI,iBAAiB,EAAE,iBAAiB;EACxC,IAAI,gBAAgB,EAAE,gBAAgB;EACtC,GAAG,CAAC;EACJ,EAAE,KAAK,CAAC,UAAU,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,UAAU,CAAC,MAAM,EAAE;EACvE,IAAI,8BAA8B,EAAE,iBAAiB;EACrD,IAAI,qBAAqB,EAAE,gBAAgB;EAC3C,GAAG,CAAC,CAAC;EACL,CAAC;AACD;AACA;AACA,eAAe;EACf,EAAE,IAAI,EAAE,MAAM;EACd,EAAE,OAAO,EAAE,IAAI;EACf,EAAE,KAAK,EAAE,MAAM;EACf,EAAE,gBAAgB,EAAE,CAAC,iBAAiB,CAAC;EACvC,EAAE,EAAE,EAAEA,MAAI;EACV,CAAC;;EC1DM,SAAS,uBAAuB,CAAC,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE;EAClE,EAAE,IAAI,aAAa,GAAG,gBAAgB,CAAC,SAAS,CAAC,CAAC;EAClD,EAAE,IAAI,cAAc,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AACxE;EACA,EAAE,IAAI,IAAI,GAAG,OAAO,MAAM,KAAK,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,EAAE;EAC5E,IAAI,SAAS,EAAE,SAAS;EACxB,GAAG,CAAC,CAAC,GAAG,MAAM;EACd,MAAM,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC;EACxB,MAAM,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AACzB;EACA,EAAE,QAAQ,GAAG,QAAQ,IAAI,CAAC,CAAC;EAC3B,EAAE,QAAQ,GAAG,CAAC,QAAQ,IAAI,CAAC,IAAI,cAAc,CAAC;EAC9C,EAAE,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG;EACrD,IAAI,CAAC,EAAE,QAAQ;EACf,IAAI,CAAC,EAAE,QAAQ;EACf,GAAG,GAAG;EACN,IAAI,CAAC,EAAE,QAAQ;EACf,IAAI,CAAC,EAAE,QAAQ;EACf,GAAG,CAAC;EACJ,CAAC;AACD;EACA,SAAS,MAAM,CAAC,KAAK,EAAE;EACvB,EAAE,IAAI,KAAK,GAAG,KAAK,CAAC,KAAK;EACzB,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO;EAC7B,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;EACxB,EAAE,IAAI,eAAe,GAAG,OAAO,CAAC,MAAM;EACtC,MAAM,MAAM,GAAG,eAAe,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,eAAe,CAAC;EACrE,EAAE,IAAI,IAAI,GAAG,UAAU,CAAC,MAAM,CAAC,UAAU,GAAG,EAAE,SAAS,EAAE;EACzD,IAAI,GAAG,CAAC,SAAS,CAAC,GAAG,uBAAuB,CAAC,SAAS,EAAE,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;EAC7E,IAAI,OAAO,GAAG,CAAC;EACf,GAAG,EAAE,EAAE,CAAC,CAAC;EACT,EAAE,IAAI,qBAAqB,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;EACnD,MAAM,CAAC,GAAG,qBAAqB,CAAC,CAAC;EACjC,MAAM,CAAC,GAAG,qBAAqB,CAAC,CAAC,CAAC;AAClC;EACA,EAAE,IAAI,KAAK,CAAC,aAAa,CAAC,aAAa,IAAI,IAAI,EAAE;EACjD,IAAI,KAAK,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC;EAC7C,IAAI,KAAK,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC;EAC7C,GAAG;AACH;EACA,EAAE,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;EACnC,CAAC;AACD;AACA;AACA,iBAAe;EACf,EAAE,IAAI,EAAE,QAAQ;EAChB,EAAE,OAAO,EAAE,IAAI;EACf,EAAE,KAAK,EAAE,MAAM;EACf,EAAE,QAAQ,EAAE,CAAC,eAAe,CAAC;EAC7B,EAAE,EAAE,EAAE,MAAM;EACZ,CAAC;;EClDD,SAAS,aAAa,CAAC,IAAI,EAAE;EAC7B,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK;EACxB,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;EACvB;EACA;EACA;EACA;EACA,EAAE,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC;EAC7C,IAAI,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,SAAS;EACpC,IAAI,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM;EAC/B,IAAI,QAAQ,EAAE,UAAU;EACxB,IAAI,SAAS,EAAE,KAAK,CAAC,SAAS;EAC9B,GAAG,CAAC,CAAC;EACL,CAAC;AACD;AACA;AACA,wBAAe;EACf,EAAE,IAAI,EAAE,eAAe;EACvB,EAAE,OAAO,EAAE,IAAI;EACf,EAAE,KAAK,EAAE,MAAM;EACf,EAAE,EAAE,EAAE,aAAa;EACnB,EAAE,IAAI,EAAE,EAAE;EACV,CAAC;;ECxBc,SAAS,UAAU,CAAC,IAAI,EAAE;EACzC,EAAE,OAAO,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;EAClC;;ECUA,SAAS,eAAe,CAAC,IAAI,EAAE;EAC/B,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK;EACxB,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO;EAC5B,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;EACvB,EAAE,IAAI,iBAAiB,GAAG,OAAO,CAAC,QAAQ;EAC1C,MAAM,aAAa,GAAG,iBAAiB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,iBAAiB;EAC7E,MAAM,gBAAgB,GAAG,OAAO,CAAC,OAAO;EACxC,MAAM,YAAY,GAAG,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAG,gBAAgB;EAC3E,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ;EACjC,MAAM,YAAY,GAAG,OAAO,CAAC,YAAY;EACzC,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW;EACvC,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO;EAC/B,MAAM,eAAe,GAAG,OAAO,CAAC,MAAM;EACtC,MAAM,MAAM,GAAG,eAAe,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,eAAe;EAClE,MAAM,qBAAqB,GAAG,OAAO,CAAC,YAAY;EAClD,MAAM,YAAY,GAAG,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAG,qBAAqB,CAAC;EAClF,EAAE,IAAI,QAAQ,GAAG,cAAc,CAAC,KAAK,EAAE;EACvC,IAAI,QAAQ,EAAE,QAAQ;EACtB,IAAI,YAAY,EAAE,YAAY;EAC9B,IAAI,OAAO,EAAE,OAAO;EACpB,IAAI,WAAW,EAAE,WAAW;EAC5B,GAAG,CAAC,CAAC;EACL,EAAE,IAAI,aAAa,GAAG,gBAAgB,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;EACxD,EAAE,IAAI,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;EAChD,EAAE,IAAI,eAAe,GAAG,CAAC,SAAS,CAAC;EACnC,EAAE,IAAI,QAAQ,GAAG,wBAAwB,CAAC,aAAa,CAAC,CAAC;EACzD,EAAE,IAAI,OAAO,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC;EACrC,EAAE,IAAI,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC,aAAa,CAAC;EACxD,EAAE,IAAI,aAAa,GAAG,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC;EAC5C,EAAE,IAAI,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC;EACtC,EAAE,IAAI,iBAAiB,GAAG,OAAO,YAAY,KAAK,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,KAAK,EAAE;EAC3G,IAAI,SAAS,EAAE,KAAK,CAAC,SAAS;EAC9B,GAAG,CAAC,CAAC,GAAG,YAAY,CAAC;EACrB,EAAE,IAAI,IAAI,GAAG;EACb,IAAI,CAAC,EAAE,CAAC;EACR,IAAI,CAAC,EAAE,CAAC;EACR,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,CAAC,aAAa,EAAE;EACtB,IAAI,OAAO;EACX,GAAG;AACH;EACA,EAAE,IAAI,aAAa,IAAI,YAAY,EAAE;EACrC,IAAI,IAAI,QAAQ,GAAG,QAAQ,KAAK,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC;EACjD,IAAI,IAAI,OAAO,GAAG,QAAQ,KAAK,GAAG,GAAG,MAAM,GAAG,KAAK,CAAC;EACpD,IAAI,IAAI,GAAG,GAAG,QAAQ,KAAK,GAAG,GAAG,QAAQ,GAAG,OAAO,CAAC;EACpD,IAAI,IAAI,MAAM,GAAG,aAAa,CAAC,QAAQ,CAAC,CAAC;EACzC,IAAI,IAAIwB,KAAG,GAAG,aAAa,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;EAC3D,IAAI,IAAIC,KAAG,GAAG,aAAa,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC;EAC1D,IAAI,IAAI,QAAQ,GAAG,MAAM,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACrD,IAAI,IAAI,MAAM,GAAG,SAAS,KAAK,KAAK,GAAG,aAAa,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;EAC5E,IAAI,IAAI,MAAM,GAAG,SAAS,KAAK,KAAK,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;EAC9E;AACA;EACA,IAAI,IAAI,YAAY,GAAG,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC;EAC5C,IAAI,IAAI,SAAS,GAAG,MAAM,IAAI,YAAY,GAAG,aAAa,CAAC,YAAY,CAAC,GAAG;EAC3E,MAAM,KAAK,EAAE,CAAC;EACd,MAAM,MAAM,EAAE,CAAC;EACf,KAAK,CAAC;EACN,IAAI,IAAI,kBAAkB,GAAG,KAAK,CAAC,aAAa,CAAC,kBAAkB,CAAC,GAAG,KAAK,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC,OAAO,GAAG,kBAAkB,EAAE,CAAC;EAC9I,IAAI,IAAI,eAAe,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC;EACvD,IAAI,IAAI,eAAe,GAAG,kBAAkB,CAAC,OAAO,CAAC,CAAC;EACtD;EACA;EACA;EACA;AACA;EACA,IAAI,IAAI,QAAQ,GAAG,MAAM,CAAC,CAAC,EAAE,aAAa,CAAC,GAAG,CAAC,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;EACjE,IAAI,IAAI,SAAS,GAAG,eAAe,GAAG,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,QAAQ,GAAG,QAAQ,GAAG,eAAe,GAAG,iBAAiB,GAAG,MAAM,GAAG,QAAQ,GAAG,eAAe,GAAG,iBAAiB,CAAC;EACnL,IAAI,IAAI,SAAS,GAAG,eAAe,GAAG,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,QAAQ,GAAG,QAAQ,GAAG,eAAe,GAAG,iBAAiB,GAAG,MAAM,GAAG,QAAQ,GAAG,eAAe,GAAG,iBAAiB,CAAC;EACpL,IAAI,IAAI,iBAAiB,GAAG,KAAK,CAAC,QAAQ,CAAC,KAAK,IAAI,eAAe,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;EAC1F,IAAI,IAAI,YAAY,GAAG,iBAAiB,GAAG,QAAQ,KAAK,GAAG,GAAG,iBAAiB,CAAC,SAAS,IAAI,CAAC,GAAG,iBAAiB,CAAC,UAAU,IAAI,CAAC,GAAG,CAAC,CAAC;EACvI,IAAI,IAAI,mBAAmB,GAAG,KAAK,CAAC,aAAa,CAAC,MAAM,GAAG,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;EACrH,IAAI,IAAI,SAAS,GAAG,aAAa,CAAC,QAAQ,CAAC,GAAG,SAAS,GAAG,mBAAmB,GAAG,YAAY,CAAC;EAC7F,IAAI,IAAI,SAAS,GAAG,aAAa,CAAC,QAAQ,CAAC,GAAG,SAAS,GAAG,mBAAmB,CAAC;AAC9E;EACA,IAAI,IAAI,aAAa,EAAE;EACvB,MAAM,IAAI,eAAe,GAAG,MAAM,CAAC,MAAM,GAAGE,GAAO,CAACH,KAAG,EAAE,SAAS,CAAC,GAAGA,KAAG,EAAE,MAAM,EAAE,MAAM,GAAGE,GAAO,CAACD,KAAG,EAAE,SAAS,CAAC,GAAGA,KAAG,CAAC,CAAC;EAC3H,MAAM,aAAa,CAAC,QAAQ,CAAC,GAAG,eAAe,CAAC;EAChD,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,eAAe,GAAG,MAAM,CAAC;EAChD,KAAK;AACL;EACA,IAAI,IAAI,YAAY,EAAE;EACtB,MAAM,IAAI,SAAS,GAAG,QAAQ,KAAK,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC;AACpD;EACA,MAAM,IAAI,QAAQ,GAAG,QAAQ,KAAK,GAAG,GAAG,MAAM,GAAG,KAAK,CAAC;AACvD;EACA,MAAM,IAAI,OAAO,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC;AAC3C;EACA,MAAM,IAAI,IAAI,GAAG,OAAO,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC;AAC/C;EACA,MAAM,IAAI,IAAI,GAAG,OAAO,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAC9C;EACA,MAAM,IAAI,gBAAgB,GAAG,MAAM,CAAC,MAAM,GAAGE,GAAO,CAAC,IAAI,EAAE,SAAS,CAAC,GAAG,IAAI,EAAE,OAAO,EAAE,MAAM,GAAGD,GAAO,CAAC,IAAI,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,CAAC;AACjI;EACA,MAAM,aAAa,CAAC,OAAO,CAAC,GAAG,gBAAgB,CAAC;EAChD,MAAM,IAAI,CAAC,OAAO,CAAC,GAAG,gBAAgB,GAAG,OAAO,CAAC;EACjD,KAAK;EACL,GAAG;AACH;EACA,EAAE,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;EACnC,CAAC;AACD;AACA;AACA,0BAAe;EACf,EAAE,IAAI,EAAE,iBAAiB;EACzB,EAAE,OAAO,EAAE,IAAI;EACf,EAAE,KAAK,EAAE,MAAM;EACf,EAAE,EAAE,EAAE,eAAe;EACrB,EAAE,gBAAgB,EAAE,CAAC,QAAQ,CAAC;EAC9B,CAAC;;EC1Hc,SAAS,oBAAoB,CAAC,OAAO,EAAE;EACtD,EAAE,OAAO;EACT,IAAI,UAAU,EAAE,OAAO,CAAC,UAAU;EAClC,IAAI,SAAS,EAAE,OAAO,CAAC,SAAS;EAChC,GAAG,CAAC;EACJ;;ECDe,SAAS,aAAa,CAAC,IAAI,EAAE;EAC5C,EAAE,IAAI,IAAI,KAAK,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;EACxD,IAAI,OAAO,eAAe,CAAC,IAAI,CAAC,CAAC;EACjC,GAAG,MAAM;EACT,IAAI,OAAO,oBAAoB,CAAC,IAAI,CAAC,CAAC;EACtC,GAAG;EACH;;ECHA;AACA;EACe,SAAS,gBAAgB,CAAC,uBAAuB,EAAE,YAAY,EAAE,OAAO,EAAE;EACzF,EAAE,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE;EAC1B,IAAI,OAAO,GAAG,KAAK,CAAC;EACpB,GAAG;AACH;EACA,EAAE,IAAI,eAAe,GAAG,kBAAkB,CAAC,YAAY,CAAC,CAAC;EACzD,EAAE,IAAI,IAAI,GAAG,qBAAqB,CAAC,uBAAuB,CAAC,CAAC;EAC5D,EAAE,IAAI,uBAAuB,GAAG,aAAa,CAAC,YAAY,CAAC,CAAC;EAC5D,EAAE,IAAI,MAAM,GAAG;EACf,IAAI,UAAU,EAAE,CAAC;EACjB,IAAI,SAAS,EAAE,CAAC;EAChB,GAAG,CAAC;EACJ,EAAE,IAAI,OAAO,GAAG;EAChB,IAAI,CAAC,EAAE,CAAC;EACR,IAAI,CAAC,EAAE,CAAC;EACR,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,uBAAuB,IAAI,CAAC,uBAAuB,IAAI,CAAC,OAAO,EAAE;EACvE,IAAI,IAAI,WAAW,CAAC,YAAY,CAAC,KAAK,MAAM;EAC5C,IAAI,cAAc,CAAC,eAAe,CAAC,EAAE;EACrC,MAAM,MAAM,GAAG,aAAa,CAAC,YAAY,CAAC,CAAC;EAC3C,KAAK;AACL;EACA,IAAI,IAAI,aAAa,CAAC,YAAY,CAAC,EAAE;EACrC,MAAM,OAAO,GAAG,qBAAqB,CAAC,YAAY,CAAC,CAAC;EACpD,MAAM,OAAO,CAAC,CAAC,IAAI,YAAY,CAAC,UAAU,CAAC;EAC3C,MAAM,OAAO,CAAC,CAAC,IAAI,YAAY,CAAC,SAAS,CAAC;EAC1C,KAAK,MAAM,IAAI,eAAe,EAAE;EAChC,MAAM,OAAO,CAAC,CAAC,GAAG,mBAAmB,CAAC,eAAe,CAAC,CAAC;EACvD,KAAK;EACL,GAAG;AACH;EACA,EAAE,OAAO;EACT,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,UAAU,GAAG,OAAO,CAAC,CAAC;EAChD,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,SAAS,GAAG,OAAO,CAAC,CAAC;EAC9C,IAAI,KAAK,EAAE,IAAI,CAAC,KAAK;EACrB,IAAI,MAAM,EAAE,IAAI,CAAC,MAAM;EACvB,GAAG,CAAC;EACJ;;EC7CA,SAAS,KAAK,CAAC,SAAS,EAAE;EAC1B,EAAE,IAAI,GAAG,GAAG,IAAI,GAAG,EAAE,CAAC;EACtB,EAAE,IAAI,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC;EAC1B,EAAE,IAAI,MAAM,GAAG,EAAE,CAAC;EAClB,EAAE,SAAS,CAAC,OAAO,CAAC,UAAU,QAAQ,EAAE;EACxC,IAAI,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;EACrC,GAAG,CAAC,CAAC;AACL;EACA,EAAE,SAAS,IAAI,CAAC,QAAQ,EAAE;EAC1B,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;EAC/B,IAAI,IAAI,QAAQ,GAAG,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,IAAI,EAAE,EAAE,QAAQ,CAAC,gBAAgB,IAAI,EAAE,CAAC,CAAC;EACvF,IAAI,QAAQ,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE;EACpC,MAAM,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;EAC7B,QAAQ,IAAI,WAAW,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACvC;EACA,QAAQ,IAAI,WAAW,EAAE;EACzB,UAAU,IAAI,CAAC,WAAW,CAAC,CAAC;EAC5B,SAAS;EACT,OAAO;EACP,KAAK,CAAC,CAAC;EACP,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAC1B,GAAG;AACH;EACA,EAAE,SAAS,CAAC,OAAO,CAAC,UAAU,QAAQ,EAAE;EACxC,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;EACrC;EACA,MAAM,IAAI,CAAC,QAAQ,CAAC,CAAC;EACrB,KAAK;EACL,GAAG,CAAC,CAAC;EACL,EAAE,OAAO,MAAM,CAAC;EAChB,CAAC;AACD;EACe,SAAS,cAAc,CAAC,SAAS,EAAE;EAClD;EACA,EAAE,IAAI,gBAAgB,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC;AAC1C;EACA,EAAE,OAAO,cAAc,CAAC,MAAM,CAAC,UAAU,GAAG,EAAE,KAAK,EAAE;EACrD,IAAI,OAAO,GAAG,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,UAAU,QAAQ,EAAE;EAClE,MAAM,OAAO,QAAQ,CAAC,KAAK,KAAK,KAAK,CAAC;EACtC,KAAK,CAAC,CAAC,CAAC;EACR,GAAG,EAAE,EAAE,CAAC,CAAC;EACT;;EC3Ce,SAAS,QAAQ,CAAC,EAAE,EAAE;EACrC,EAAE,IAAI,OAAO,CAAC;EACd,EAAE,OAAO,YAAY;EACrB,IAAI,IAAI,CAAC,OAAO,EAAE;EAClB,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,UAAU,OAAO,EAAE;EAC/C,QAAQ,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,YAAY;EAC3C,UAAU,OAAO,GAAG,SAAS,CAAC;EAC9B,UAAU,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;EACxB,SAAS,CAAC,CAAC;EACX,OAAO,CAAC,CAAC;EACT,KAAK;AACL;EACA,IAAI,OAAO,OAAO,CAAC;EACnB,GAAG,CAAC;EACJ;;ECde,SAAS,WAAW,CAAC,SAAS,EAAE;EAC/C,EAAE,IAAI,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,UAAU,MAAM,EAAE,OAAO,EAAE;EAC3D,IAAI,IAAI,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;EACxC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE;EAC3E,MAAM,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC;EACnE,MAAM,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC;EAC1D,KAAK,CAAC,GAAG,OAAO,CAAC;EACjB,IAAI,OAAO,MAAM,CAAC;EAClB,GAAG,EAAE,EAAE,CAAC,CAAC;AACT;EACA,EAAE,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,UAAU,GAAG,EAAE;EAChD,IAAI,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;EACvB,GAAG,CAAC,CAAC;EACL;;ECGA,IAAI,eAAe,GAAG;EACtB,EAAE,SAAS,EAAE,QAAQ;EACrB,EAAE,SAAS,EAAE,EAAE;EACf,EAAE,QAAQ,EAAE,UAAU;EACtB,CAAC,CAAC;AACF;EACA,SAAS,gBAAgB,GAAG;EAC5B,EAAE,KAAK,IAAI,IAAI,GAAG,SAAS,CAAC,MAAM,EAAE,IAAI,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,EAAE,IAAI,EAAE,EAAE;EAC3F,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;EACjC,GAAG;AACH;EACA,EAAE,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,OAAO,EAAE;EACvC,IAAI,OAAO,EAAE,OAAO,IAAI,OAAO,OAAO,CAAC,qBAAqB,KAAK,UAAU,CAAC,CAAC;EAC7E,GAAG,CAAC,CAAC;EACL,CAAC;AACD;EACO,SAAS,eAAe,CAAC,gBAAgB,EAAE;EAClD,EAAE,IAAI,gBAAgB,KAAK,KAAK,CAAC,EAAE;EACnC,IAAI,gBAAgB,GAAG,EAAE,CAAC;EAC1B,GAAG;AACH;EACA,EAAE,IAAI,iBAAiB,GAAG,gBAAgB;EAC1C,MAAM,qBAAqB,GAAG,iBAAiB,CAAC,gBAAgB;EAChE,MAAM,gBAAgB,GAAG,qBAAqB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,qBAAqB;EACtF,MAAM,sBAAsB,GAAG,iBAAiB,CAAC,cAAc;EAC/D,MAAM,cAAc,GAAG,sBAAsB,KAAK,KAAK,CAAC,GAAG,eAAe,GAAG,sBAAsB,CAAC;EACpG,EAAE,OAAO,SAAS,YAAY,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE;EAC3D,IAAI,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE;EAC5B,MAAM,OAAO,GAAG,cAAc,CAAC;EAC/B,KAAK;AACL;EACA,IAAI,IAAI,KAAK,GAAG;EAChB,MAAM,SAAS,EAAE,QAAQ;EACzB,MAAM,gBAAgB,EAAE,EAAE;EAC1B,MAAM,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,eAAe,EAAE,cAAc,CAAC;EACjE,MAAM,aAAa,EAAE,EAAE;EACvB,MAAM,QAAQ,EAAE;EAChB,QAAQ,SAAS,EAAE,SAAS;EAC5B,QAAQ,MAAM,EAAE,MAAM;EACtB,OAAO;EACP,MAAM,UAAU,EAAE,EAAE;EACpB,MAAM,MAAM,EAAE,EAAE;EAChB,KAAK,CAAC;EACN,IAAI,IAAI,gBAAgB,GAAG,EAAE,CAAC;EAC9B,IAAI,IAAI,WAAW,GAAG,KAAK,CAAC;EAC5B,IAAI,IAAI,QAAQ,GAAG;EACnB,MAAM,KAAK,EAAE,KAAK;EAClB,MAAM,UAAU,EAAE,SAAS,UAAU,CAAC,OAAO,EAAE;EAC/C,QAAQ,sBAAsB,EAAE,CAAC;EACjC,QAAQ,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,cAAc,EAAE,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;EAClF,QAAQ,KAAK,CAAC,aAAa,GAAG;EAC9B,UAAU,SAAS,EAAE,SAAS,CAAC,SAAS,CAAC,GAAG,iBAAiB,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC,cAAc,GAAG,iBAAiB,CAAC,SAAS,CAAC,cAAc,CAAC,GAAG,EAAE;EACtJ,UAAU,MAAM,EAAE,iBAAiB,CAAC,MAAM,CAAC;EAC3C,SAAS,CAAC;EACV;AACA;EACA,QAAQ,IAAI,gBAAgB,GAAG,cAAc,CAAC,WAAW,CAAC,EAAE,CAAC,MAAM,CAAC,gBAAgB,EAAE,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AACjH;EACA,QAAQ,KAAK,CAAC,gBAAgB,GAAG,gBAAgB,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE;EACtE,UAAU,OAAO,CAAC,CAAC,OAAO,CAAC;EAC3B,SAAS,CAAC,CAAC;AAmCX;EACA,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,OAAO,QAAQ,CAAC,MAAM,EAAE,CAAC;EACjC,OAAO;EACP;EACA;EACA;EACA;EACA;EACA,MAAM,WAAW,EAAE,SAAS,WAAW,GAAG;EAC1C,QAAQ,IAAI,WAAW,EAAE;EACzB,UAAU,OAAO;EACjB,SAAS;AACT;EACA,QAAQ,IAAI,eAAe,GAAG,KAAK,CAAC,QAAQ;EAC5C,YAAY,SAAS,GAAG,eAAe,CAAC,SAAS;EACjD,YAAY,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC;EAC5C;AACA;EACA,QAAQ,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,MAAM,CAAC,EAAE;AAIlD;EACA,UAAU,OAAO;EACjB,SAAS;AACT;AACA;EACA,QAAQ,KAAK,CAAC,KAAK,GAAG;EACtB,UAAU,SAAS,EAAE,gBAAgB,CAAC,SAAS,EAAE,eAAe,CAAC,MAAM,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC;EAC7G,UAAU,MAAM,EAAE,aAAa,CAAC,MAAM,CAAC;EACvC,SAAS,CAAC;EACV;EACA;EACA;EACA;AACA;EACA,QAAQ,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;EAC5B,QAAQ,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC;EAClD;EACA;EACA;AACA;EACA,QAAQ,KAAK,CAAC,gBAAgB,CAAC,OAAO,CAAC,UAAU,QAAQ,EAAE;EAC3D,UAAU,OAAO,KAAK,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;EACvF,SAAS,CAAC,CAAC;AAEX;EACA,QAAQ,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,gBAAgB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;AAS5E;EACA,UAAU,IAAI,KAAK,CAAC,KAAK,KAAK,IAAI,EAAE;EACpC,YAAY,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;EAChC,YAAY,KAAK,GAAG,CAAC,CAAC,CAAC;EACvB,YAAY,SAAS;EACrB,WAAW;AACX;EACA,UAAU,IAAI,qBAAqB,GAAG,KAAK,CAAC,gBAAgB,CAAC,KAAK,CAAC;EACnE,cAAc,EAAE,GAAG,qBAAqB,CAAC,EAAE;EAC3C,cAAc,sBAAsB,GAAG,qBAAqB,CAAC,OAAO;EACpE,cAAc,QAAQ,GAAG,sBAAsB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,sBAAsB;EACxF,cAAc,IAAI,GAAG,qBAAqB,CAAC,IAAI,CAAC;AAChD;EACA,UAAU,IAAI,OAAO,EAAE,KAAK,UAAU,EAAE;EACxC,YAAY,KAAK,GAAG,EAAE,CAAC;EACvB,cAAc,KAAK,EAAE,KAAK;EAC1B,cAAc,OAAO,EAAE,QAAQ;EAC/B,cAAc,IAAI,EAAE,IAAI;EACxB,cAAc,QAAQ,EAAE,QAAQ;EAChC,aAAa,CAAC,IAAI,KAAK,CAAC;EACxB,WAAW;EACX,SAAS;EACT,OAAO;EACP;EACA;EACA,MAAM,MAAM,EAAE,QAAQ,CAAC,YAAY;EACnC,QAAQ,OAAO,IAAI,OAAO,CAAC,UAAU,OAAO,EAAE;EAC9C,UAAU,QAAQ,CAAC,WAAW,EAAE,CAAC;EACjC,UAAU,OAAO,CAAC,KAAK,CAAC,CAAC;EACzB,SAAS,CAAC,CAAC;EACX,OAAO,CAAC;EACR,MAAM,OAAO,EAAE,SAAS,OAAO,GAAG;EAClC,QAAQ,sBAAsB,EAAE,CAAC;EACjC,QAAQ,WAAW,GAAG,IAAI,CAAC;EAC3B,OAAO;EACP,KAAK,CAAC;AACN;EACA,IAAI,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,MAAM,CAAC,EAAE;AAI9C;EACA,MAAM,OAAO,QAAQ,CAAC;EACtB,KAAK;AACL;EACA,IAAI,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,UAAU,KAAK,EAAE;EACvD,MAAM,IAAI,CAAC,WAAW,IAAI,OAAO,CAAC,aAAa,EAAE;EACjD,QAAQ,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;EACrC,OAAO;EACP,KAAK,CAAC,CAAC;EACP;EACA;EACA;EACA;AACA;EACA,IAAI,SAAS,kBAAkB,GAAG;EAClC,MAAM,KAAK,CAAC,gBAAgB,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE;EACtD,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI;EAC7B,YAAY,aAAa,GAAG,KAAK,CAAC,OAAO;EACzC,YAAY,OAAO,GAAG,aAAa,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,aAAa;EACnE,YAAY,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;AAClC;EACA,QAAQ,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE;EAC1C,UAAU,IAAI,SAAS,GAAG,MAAM,CAAC;EACjC,YAAY,KAAK,EAAE,KAAK;EACxB,YAAY,IAAI,EAAE,IAAI;EACtB,YAAY,QAAQ,EAAE,QAAQ;EAC9B,YAAY,OAAO,EAAE,OAAO;EAC5B,WAAW,CAAC,CAAC;AACb;EACA,UAAU,IAAI,MAAM,GAAG,SAAS,MAAM,GAAG,EAAE,CAAC;AAC5C;EACA,UAAU,gBAAgB,CAAC,IAAI,CAAC,SAAS,IAAI,MAAM,CAAC,CAAC;EACrD,SAAS;EACT,OAAO,CAAC,CAAC;EACT,KAAK;AACL;EACA,IAAI,SAAS,sBAAsB,GAAG;EACtC,MAAM,gBAAgB,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE;EAC7C,QAAQ,OAAO,EAAE,EAAE,CAAC;EACpB,OAAO,CAAC,CAAC;EACT,MAAM,gBAAgB,GAAG,EAAE,CAAC;EAC5B,KAAK;AACL;EACA,IAAI,OAAO,QAAQ,CAAC;EACpB,GAAG,CAAC;EACJ,CAAC;EACM,IAAIK,cAAY,gBAAgB,eAAe,EAAE,CAAC;;EC1PzD,IAAIC,kBAAgB,GAAG,CAAC,cAAc,EAAEC,eAAa,EAAEC,eAAa,EAAEC,aAAW,CAAC,CAAC;EACnF,IAAIJ,cAAY,gBAAgB,eAAe,CAAC;EAChD,EAAE,gBAAgB,EAAEC,kBAAgB;EACpC,CAAC,CAAC,CAAC;;ECEH,IAAI,gBAAgB,GAAG,CAAC,cAAc,EAAEC,eAAa,EAAEC,eAAa,EAAEC,aAAW,EAAEzN,QAAM,EAAE0N,MAAI,EAAEC,iBAAe,EAAEC,OAAK,EAAEtC,MAAI,CAAC,CAAC;EAC/H,IAAI,YAAY,gBAAgB,eAAe,CAAC;EAChD,EAAE,gBAAgB,EAAE,gBAAgB;EACpC,CAAC,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ECbH;EACA;EACA;EACA;EACA;EACA;EAmBA;EACA;EACA;EACA;EACA;;EAEA,MAAMnO,MAAI,GAAG,UAAb;EACA,MAAMH,UAAQ,GAAG,aAAjB;EACA,MAAMI,WAAS,GAAI,IAAGJ,UAAS,EAA/B;EACA,MAAMK,cAAY,GAAG,WAArB;EAEA,MAAMwQ,YAAU,GAAG,QAAnB;EACA,MAAMC,SAAS,GAAG,OAAlB;EACA,MAAMC,OAAO,GAAG,KAAhB;EACA,MAAMC,YAAY,GAAG,SAArB;EACA,MAAMC,cAAc,GAAG,WAAvB;EACA,MAAMC,kBAAkB,GAAG,CAA3B;;EAEA,MAAMC,cAAc,GAAG,IAAI7Z,MAAJ,CAAY,GAAE0Z,YAAa,IAAGC,cAAe,IAAGJ,YAAW,EAA3D,CAAvB;EAEA,MAAM3D,YAAU,GAAI,OAAM9M,WAAU,EAApC;EACA,MAAM+M,cAAY,GAAI,SAAQ/M,WAAU,EAAxC;EACA,MAAM4M,YAAU,GAAI,OAAM5M,WAAU,EAApC;EACA,MAAM6M,aAAW,GAAI,QAAO7M,WAAU,EAAtC;EACA,MAAMgR,WAAW,GAAI,QAAOhR,WAAU,EAAtC;EACA,MAAMK,sBAAoB,GAAI,QAAOL,WAAU,GAAEC,cAAa,EAA9D;EACA,MAAMgR,sBAAsB,GAAI,UAASjR,WAAU,GAAEC,cAAa,EAAlE;EACA,MAAMiR,oBAAoB,GAAI,QAAOlR,WAAU,GAAEC,cAAa,EAA9D;EAEA,MAAMkR,mBAAmB,GAAG,UAA5B;EACA,MAAM3Q,iBAAe,GAAG,MAAxB;EACA,MAAM4Q,iBAAiB,GAAG,QAA1B;EACA,MAAMC,kBAAkB,GAAG,SAA3B;EACA,MAAMC,oBAAoB,GAAG,WAA7B;EACA,MAAMC,iBAAiB,GAAG,QAA1B;EAEA,MAAM/P,sBAAoB,GAAG,6BAA7B;EACA,MAAMgQ,aAAa,GAAG,gBAAtB;EACA,MAAMC,mBAAmB,GAAG,aAA5B;EACA,MAAMC,sBAAsB,GAAG,6DAA/B;EAEA,MAAMC,aAAa,GAAGxY,KAAK,KAAK,SAAL,GAAiB,WAA5C;EACA,MAAMyY,gBAAgB,GAAGzY,KAAK,KAAK,WAAL,GAAmB,SAAjD;EACA,MAAM0Y,gBAAgB,GAAG1Y,KAAK,KAAK,YAAL,GAAoB,cAAlD;EACA,MAAM2Y,mBAAmB,GAAG3Y,KAAK,KAAK,cAAL,GAAsB,YAAvD;EACA,MAAM4Y,eAAe,GAAG5Y,KAAK,KAAK,YAAL,GAAoB,aAAjD;EACA,MAAM6Y,cAAc,GAAG7Y,KAAK,KAAK,aAAL,GAAqB,YAAjD;EAEA,MAAMyL,SAAO,GAAG;EACdhC,EAAAA,MAAM,EAAE,CAAC,CAAD,EAAI,CAAJ,CADM;EAEdqP,EAAAA,QAAQ,EAAE,iBAFI;EAGdC,EAAAA,SAAS,EAAE,QAHG;EAIdva,EAAAA,OAAO,EAAE,SAJK;EAKdwa,EAAAA,YAAY,EAAE;EALA,CAAhB;EAQA,MAAMhN,aAAW,GAAG;EAClBvC,EAAAA,MAAM,EAAE,yBADU;EAElBqP,EAAAA,QAAQ,EAAE,kBAFQ;EAGlBC,EAAAA,SAAS,EAAE,yBAHO;EAIlBva,EAAAA,OAAO,EAAE,QAJS;EAKlBwa,EAAAA,YAAY,EAAE;EALI,CAApB;EAQA;EACA;EACA;EACA;EACA;;EAEA,MAAMC,QAAN,SAAuB5S,aAAvB,CAAqC;EACnCC,EAAAA,WAAW,CAACpL,OAAD,EAAUoC,MAAV,EAAkB;EAC3B,UAAMpC,OAAN;EAEA,SAAKge,OAAL,GAAe,IAAf;EACA,SAAKtK,OAAL,GAAe,KAAKC,UAAL,CAAgBvR,MAAhB,CAAf;EACA,SAAK6b,KAAL,GAAa,KAAKC,eAAL,EAAb;EACA,SAAKC,SAAL,GAAiB,KAAKC,aAAL,EAAjB;;EAEA,SAAKlK,kBAAL;EACD,GAVkC;;;EAcjB,aAAP3D,OAAO,GAAG;EACnB,WAAOA,SAAP;EACD;;EAEqB,aAAXO,WAAW,GAAG;EACvB,WAAOA,aAAP;EACD;;EAEkB,aAARvF,QAAQ,GAAG;EACpB,WAAOA,UAAP;EACD,GAxBkC;;;EA4BnC8B,EAAAA,MAAM,GAAG;EACP,QAAI,KAAKhC,QAAL,CAAcxH,QAAd,IAA0B,KAAKwH,QAAL,CAAc1H,SAAd,CAAwBC,QAAxB,CAAiCkZ,mBAAjC,CAA9B,EAAqF;EACnF;EACD;;EAED,UAAMuB,QAAQ,GAAG,KAAKhT,QAAL,CAAc1H,SAAd,CAAwBC,QAAxB,CAAiCuI,iBAAjC,CAAjB;;EAEA4R,IAAAA,QAAQ,CAACO,UAAT;;EAEA,QAAID,QAAJ,EAAc;EACZ;EACD;;EAED,SAAKvE,IAAL;EACD;;EAEDA,EAAAA,IAAI,GAAG;EACL,QAAI,KAAKzO,QAAL,CAAcxH,QAAd,IAA0B,KAAKwH,QAAL,CAAc1H,SAAd,CAAwBC,QAAxB,CAAiCkZ,mBAAjC,CAA1B,IAAmF,KAAKmB,KAAL,CAAWta,SAAX,CAAqBC,QAArB,CAA8BuI,iBAA9B,CAAvF,EAAuI;EACrI;EACD;;EAED,UAAMmM,MAAM,GAAGyF,QAAQ,CAACQ,oBAAT,CAA8B,KAAKlT,QAAnC,CAAf;EACA,UAAMuL,aAAa,GAAG;EACpBA,MAAAA,aAAa,EAAE,KAAKvL;EADA,KAAtB;EAIA,UAAMmT,SAAS,GAAG9W,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCkN,YAApC,EAAgD3B,aAAhD,CAAlB;;EAEA,QAAI4H,SAAS,CAACjU,gBAAd,EAAgC;EAC9B;EACD,KAdI;;;EAiBL,QAAI,KAAK4T,SAAT,EAAoB;EAClBvQ,MAAAA,WAAW,CAACC,gBAAZ,CAA6B,KAAKoQ,KAAlC,EAAyC,QAAzC,EAAmD,MAAnD;EACD,KAFD,MAEO;EACL,UAAI,OAAOQ,MAAP,KAAkB,WAAtB,EAAmC;EACjC,cAAM,IAAI1b,SAAJ,CAAc,+DAAd,CAAN;EACD;;EAED,UAAI2b,gBAAgB,GAAG,KAAKrT,QAA5B;;EAEA,UAAI,KAAKqI,OAAL,CAAamK,SAAb,KAA2B,QAA/B,EAAyC;EACvCa,QAAAA,gBAAgB,GAAGpG,MAAnB;EACD,OAFD,MAEO,IAAI/W,WAAS,CAAC,KAAKmS,OAAL,CAAamK,SAAd,CAAb,EAAuC;EAC5Ca,QAAAA,gBAAgB,GAAG,KAAKhL,OAAL,CAAamK,SAAhC,CAD4C;;EAI5C,YAAI,OAAO,KAAKnK,OAAL,CAAamK,SAAb,CAAuB/C,MAA9B,KAAyC,WAA7C,EAA0D;EACxD4D,UAAAA,gBAAgB,GAAG,KAAKhL,OAAL,CAAamK,SAAb,CAAuB,CAAvB,CAAnB;EACD;EACF,OAPM,MAOA,IAAI,OAAO,KAAKnK,OAAL,CAAamK,SAApB,KAAkC,QAAtC,EAAgD;EACrDa,QAAAA,gBAAgB,GAAG,KAAKhL,OAAL,CAAamK,SAAhC;EACD;;EAED,YAAMC,YAAY,GAAG,KAAKa,gBAAL,EAArB;;EACA,YAAMC,eAAe,GAAGd,YAAY,CAACe,SAAb,CAAuB1P,IAAvB,CAA4B2P,QAAQ,IAAIA,QAAQ,CAAC7Z,IAAT,KAAkB,aAAlB,IAAmC6Z,QAAQ,CAACC,OAAT,KAAqB,KAAhG,CAAxB;EAEA,WAAKf,OAAL,GAAeS,YAAA,CAAoBC,gBAApB,EAAsC,KAAKT,KAA3C,EAAkDH,YAAlD,CAAf;;EAEA,UAAIc,eAAJ,EAAqB;EACnBhR,QAAAA,WAAW,CAACC,gBAAZ,CAA6B,KAAKoQ,KAAlC,EAAyC,QAAzC,EAAmD,QAAnD;EACD;EACF,KA/CI;EAkDL;EACA;EACA;;;EACA,QAAI,kBAAkBpe,QAAQ,CAACmE,eAA3B,IACF,CAACsU,MAAM,CAAC3L,OAAP,CAAeyQ,mBAAf,CADH,EACwC;EACtC,SAAGhO,MAAH,CAAU,GAAGvP,QAAQ,CAAC6E,IAAT,CAAc8K,QAA3B,EACGhN,OADH,CACW8W,IAAI,IAAI5R,YAAY,CAACiC,EAAb,CAAgB2P,IAAhB,EAAsB,WAAtB,EAAmC,IAAnC,EAAyCjV,IAAI,EAA7C,CADnB;EAED;;EAED,SAAKgH,QAAL,CAAc2T,KAAd;;EACA,SAAK3T,QAAL,CAAciC,YAAd,CAA2B,eAA3B,EAA4C,IAA5C;;EAEA,SAAK2Q,KAAL,CAAWta,SAAX,CAAqB0J,MAArB,CAA4BlB,iBAA5B;;EACA,SAAKd,QAAL,CAAc1H,SAAd,CAAwB0J,MAAxB,CAA+BlB,iBAA/B;;EACAzE,IAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCmN,aAApC,EAAiD5B,aAAjD;EACD;;EAEDiD,EAAAA,IAAI,GAAG;EACL,QAAI,KAAKxO,QAAL,CAAcxH,QAAd,IAA0B,KAAKwH,QAAL,CAAc1H,SAAd,CAAwBC,QAAxB,CAAiCkZ,mBAAjC,CAA1B,IAAmF,CAAC,KAAKmB,KAAL,CAAWta,SAAX,CAAqBC,QAArB,CAA8BuI,iBAA9B,CAAxF,EAAwI;EACtI;EACD;;EAED,UAAMyK,aAAa,GAAG;EACpBA,MAAAA,aAAa,EAAE,KAAKvL;EADA,KAAtB;EAIA,UAAM4T,SAAS,GAAGvX,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCoN,YAApC,EAAgD7B,aAAhD,CAAlB;;EAEA,QAAIqI,SAAS,CAAC1U,gBAAd,EAAgC;EAC9B;EACD;;EAED,QAAI,KAAKyT,OAAT,EAAkB;EAChB,WAAKA,OAAL,CAAakB,OAAb;EACD;;EAED,SAAKjB,KAAL,CAAWta,SAAX,CAAqB0J,MAArB,CAA4BlB,iBAA5B;;EACA,SAAKd,QAAL,CAAc1H,SAAd,CAAwB0J,MAAxB,CAA+BlB,iBAA/B;;EACAyB,IAAAA,WAAW,CAACE,mBAAZ,CAAgC,KAAKmQ,KAArC,EAA4C,QAA5C;EACAvW,IAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCqN,cAApC,EAAkD9B,aAAlD;EACD;;EAEDpL,EAAAA,OAAO,GAAG;EACR9D,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK0D,QAAtB,EAAgCM,WAAhC;EACA,SAAKsS,KAAL,GAAa,IAAb;;EAEA,QAAI,KAAKD,OAAT,EAAkB;EAChB,WAAKA,OAAL,CAAakB,OAAb;;EACA,WAAKlB,OAAL,GAAe,IAAf;EACD;;EAED,UAAMxS,OAAN;EACD;;EAED2T,EAAAA,MAAM,GAAG;EACP,SAAKhB,SAAL,GAAiB,KAAKC,aAAL,EAAjB;;EACA,QAAI,KAAKJ,OAAT,EAAkB;EAChB,WAAKA,OAAL,CAAamB,MAAb;EACD;EACF,GAzJkC;;;EA6JnCjL,EAAAA,kBAAkB,GAAG;EACnBxM,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+BsR,WAA/B,EAA4CpV,KAAK,IAAI;EACnDA,MAAAA,KAAK,CAAC0D,cAAN;EACA,WAAKoC,MAAL;EACD,KAHD;EAID;;EAEDsG,EAAAA,UAAU,CAACvR,MAAD,EAAS;EACjBA,IAAAA,MAAM,GAAG,EACP,GAAG,KAAKgJ,WAAL,CAAiBmF,OADb;EAEP,SAAG3C,WAAW,CAACI,iBAAZ,CAA8B,KAAK3C,QAAnC,CAFI;EAGP,SAAGjJ;EAHI,KAAT;EAMAF,IAAAA,eAAe,CAACwJ,MAAD,EAAOtJ,MAAP,EAAe,KAAKgJ,WAAL,CAAiB0F,WAAhC,CAAf;;EAEA,QAAI,OAAO1O,MAAM,CAACyb,SAAd,KAA4B,QAA5B,IAAwC,CAACtc,WAAS,CAACa,MAAM,CAACyb,SAAR,CAAlD,IACF,OAAOzb,MAAM,CAACyb,SAAP,CAAiBpP,qBAAxB,KAAkD,UADpD,EAEE;EACA;EACA,YAAM,IAAI1L,SAAJ,CAAe,GAAE2I,MAAI,CAAC1I,WAAL,EAAmB,gGAApC,CAAN;EACD;;EAED,WAAOZ,MAAP;EACD;;EAED8b,EAAAA,eAAe,GAAG;EAChB,WAAOhP,cAAc,CAACe,IAAf,CAAoB,KAAK5E,QAAzB,EAAmC8R,aAAnC,EAAkD,CAAlD,CAAP;EACD;;EAEDiC,EAAAA,aAAa,GAAG;EACd,UAAMC,cAAc,GAAG,KAAKhU,QAAL,CAAclI,UAArC;;EAEA,QAAIkc,cAAc,CAAC1b,SAAf,CAAyBC,QAAzB,CAAkCoZ,kBAAlC,CAAJ,EAA2D;EACzD,aAAOU,eAAP;EACD;;EAED,QAAI2B,cAAc,CAAC1b,SAAf,CAAyBC,QAAzB,CAAkCqZ,oBAAlC,CAAJ,EAA6D;EAC3D,aAAOU,cAAP;EACD,KATa;;;EAYd,UAAM2B,KAAK,GAAGve,gBAAgB,CAAC,KAAKkd,KAAN,CAAhB,CAA6BsB,gBAA7B,CAA8C,eAA9C,EAA+Dhf,IAA/D,OAA0E,KAAxF;;EAEA,QAAI8e,cAAc,CAAC1b,SAAf,CAAyBC,QAAzB,CAAkCmZ,iBAAlC,CAAJ,EAA0D;EACxD,aAAOuC,KAAK,GAAG/B,gBAAH,GAAsBD,aAAlC;EACD;;EAED,WAAOgC,KAAK,GAAG7B,mBAAH,GAAyBD,gBAArC;EACD;;EAEDY,EAAAA,aAAa,GAAG;EACd,WAAO,KAAK/S,QAAL,CAAcsB,OAAd,CAAuB,IAAGuQ,iBAAkB,EAA5C,MAAmD,IAA1D;EACD;;EAEDsC,EAAAA,UAAU,GAAG;EACX,UAAM;EAAEjR,MAAAA;EAAF,QAAa,KAAKmF,OAAxB;;EAEA,QAAI,OAAOnF,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,aAAOA,MAAM,CAACjO,KAAP,CAAa,GAAb,EAAkBmf,GAAlB,CAAsBhS,GAAG,IAAIxM,MAAM,CAACkW,QAAP,CAAgB1J,GAAhB,EAAqB,EAArB,CAA7B,CAAP;EACD;;EAED,QAAI,OAAOc,MAAP,KAAkB,UAAtB,EAAkC;EAChC,aAAOmR,UAAU,IAAInR,MAAM,CAACmR,UAAD,EAAa,KAAKrU,QAAlB,CAA3B;EACD;;EAED,WAAOkD,MAAP;EACD;;EAEDoQ,EAAAA,gBAAgB,GAAG;EACjB,UAAMgB,qBAAqB,GAAG;EAC5BC,MAAAA,SAAS,EAAE,KAAKR,aAAL,EADiB;EAE5BP,MAAAA,SAAS,EAAE,CAAC;EACV5Z,QAAAA,IAAI,EAAE,iBADI;EAEV4a,QAAAA,OAAO,EAAE;EACPjC,UAAAA,QAAQ,EAAE,KAAKlK,OAAL,CAAakK;EADhB;EAFC,OAAD,EAMX;EACE3Y,QAAAA,IAAI,EAAE,QADR;EAEE4a,QAAAA,OAAO,EAAE;EACPtR,UAAAA,MAAM,EAAE,KAAKiR,UAAL;EADD;EAFX,OANW;EAFiB,KAA9B,CADiB;;EAkBjB,QAAI,KAAK9L,OAAL,CAAapQ,OAAb,KAAyB,QAA7B,EAAuC;EACrCqc,MAAAA,qBAAqB,CAACd,SAAtB,GAAkC,CAAC;EACjC5Z,QAAAA,IAAI,EAAE,aAD2B;EAEjC8Z,QAAAA,OAAO,EAAE;EAFwB,OAAD,CAAlC;EAID;;EAED,WAAO,EACL,GAAGY,qBADE;EAEL,UAAI,OAAO,KAAKjM,OAAL,CAAaoK,YAApB,KAAqC,UAArC,GAAkD,KAAKpK,OAAL,CAAaoK,YAAb,CAA0B6B,qBAA1B,CAAlD,GAAqG,KAAKjM,OAAL,CAAaoK,YAAtH;EAFK,KAAP;EAID,GA/PkC;;;EAmQX,SAAjBgC,iBAAiB,CAAC9f,OAAD,EAAUoC,MAAV,EAAkB;EACxC,QAAI2K,IAAI,GAAGzB,IAAI,CAACtF,GAAL,CAAShG,OAAT,EAAkBuL,UAAlB,CAAX;;EACA,UAAMmI,OAAO,GAAG,OAAOtR,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,IAAtD;;EAEA,QAAI,CAAC2K,IAAL,EAAW;EACTA,MAAAA,IAAI,GAAG,IAAIgR,QAAJ,CAAa/d,OAAb,EAAsB0T,OAAtB,CAAP;EACD;;EAED,QAAI,OAAOtR,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,UAAI,OAAO2K,IAAI,CAAC3K,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,cAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;EACD;;EAED2K,MAAAA,IAAI,CAAC3K,MAAD,CAAJ;EACD;EACF;;EAEqB,SAAfkD,eAAe,CAAClD,MAAD,EAAS;EAC7B,WAAO,KAAK0K,IAAL,CAAU,YAAY;EAC3BiR,MAAAA,QAAQ,CAAC+B,iBAAT,CAA2B,IAA3B,EAAiC1d,MAAjC;EACD,KAFM,CAAP;EAGD;;EAEgB,SAAVkc,UAAU,CAAC/W,KAAD,EAAQ;EACvB,QAAIA,KAAJ,EAAW;EACT,UAAIA,KAAK,CAACgG,MAAN,KAAiBkP,kBAAjB,IAAwClV,KAAK,CAACK,IAAN,KAAe,OAAf,IAA0BL,KAAK,CAAC3B,GAAN,KAAc0W,OAApF,EAA8F;EAC5F;EACD;;EAED,UAAI,8BAA8BxZ,IAA9B,CAAmCyE,KAAK,CAACU,MAAN,CAAagO,OAAhD,CAAJ,EAA8D;EAC5D;EACD;EACF;;EAED,UAAM8J,OAAO,GAAG7Q,cAAc,CAACC,IAAf,CAAoBhC,sBAApB,CAAhB;;EAEA,SAAK,IAAIjF,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAGuX,OAAO,CAAC5X,MAA9B,EAAsCD,CAAC,GAAGM,GAA1C,EAA+CN,CAAC,EAAhD,EAAoD;EAClD,YAAM8X,OAAO,GAAG1U,IAAI,CAACtF,GAAL,CAAS+Z,OAAO,CAAC7X,CAAD,CAAhB,EAAqBqD,UAArB,CAAhB;EACA,YAAMqL,aAAa,GAAG;EACpBA,QAAAA,aAAa,EAAEmJ,OAAO,CAAC7X,CAAD;EADF,OAAtB;;EAIA,UAAIX,KAAK,IAAIA,KAAK,CAACK,IAAN,KAAe,OAA5B,EAAqC;EACnCgP,QAAAA,aAAa,CAACqJ,UAAd,GAA2B1Y,KAA3B;EACD;;EAED,UAAI,CAACyY,OAAL,EAAc;EACZ;EACD;;EAED,YAAME,YAAY,GAAGF,OAAO,CAAC/B,KAA7B;;EACA,UAAI,CAAC8B,OAAO,CAAC7X,CAAD,CAAP,CAAWvE,SAAX,CAAqBC,QAArB,CAA8BuI,iBAA9B,CAAL,EAAqD;EACnD;EACD;;EAED,UAAI5E,KAAJ,EAAW;EACT;EACA,YAAI,CAACyY,OAAO,CAAC3U,QAAT,EAAmB8U,IAAnB,CAAwBngB,OAAO,IAAIuH,KAAK,CAAC6Y,YAAN,GAAqBhgB,QAArB,CAA8BJ,OAA9B,CAAnC,CAAJ,EAAgF;EAC9E;EACD,SAJQ;;;EAOT,YAAIuH,KAAK,CAACK,IAAN,KAAe,OAAf,IAA0BL,KAAK,CAAC3B,GAAN,KAAc0W,OAAxC,IAAmD4D,YAAY,CAACtc,QAAb,CAAsB2D,KAAK,CAACU,MAA5B,CAAvD,EAA4F;EAC1F;EACD;EACF;;EAED,YAAMgX,SAAS,GAAGvX,YAAY,CAACwC,OAAb,CAAqB6V,OAAO,CAAC7X,CAAD,CAA5B,EAAiCuQ,YAAjC,EAA6C7B,aAA7C,CAAlB;;EACA,UAAIqI,SAAS,CAAC1U,gBAAd,EAAgC;EAC9B;EACD,OAlCiD;EAqClD;;;EACA,UAAI,kBAAkB1K,QAAQ,CAACmE,eAA/B,EAAgD;EAC9C,WAAGoL,MAAH,CAAU,GAAGvP,QAAQ,CAAC6E,IAAT,CAAc8K,QAA3B,EACGhN,OADH,CACW8W,IAAI,IAAI5R,YAAY,CAACC,GAAb,CAAiB2R,IAAjB,EAAuB,WAAvB,EAAoC,IAApC,EAA0CjV,IAAI,EAA9C,CADnB;EAED;;EAED0b,MAAAA,OAAO,CAAC7X,CAAD,CAAP,CAAWoF,YAAX,CAAwB,eAAxB,EAAyC,OAAzC;;EAEA,UAAI0S,OAAO,CAAChC,OAAZ,EAAqB;EACnBgC,QAAAA,OAAO,CAAChC,OAAR,CAAgBkB,OAAhB;EACD;;EAEDgB,MAAAA,YAAY,CAACvc,SAAb,CAAuB2C,MAAvB,CAA8B6F,iBAA9B;EACA4T,MAAAA,OAAO,CAAC7X,CAAD,CAAP,CAAWvE,SAAX,CAAqB2C,MAArB,CAA4B6F,iBAA5B;EACAyB,MAAAA,WAAW,CAACE,mBAAZ,CAAgCoS,YAAhC,EAA8C,QAA9C;EACAxY,MAAAA,YAAY,CAACwC,OAAb,CAAqB6V,OAAO,CAAC7X,CAAD,CAA5B,EAAiCwQ,cAAjC,EAA+C9B,aAA/C;EACD;EACF;;EAE0B,SAApB2H,oBAAoB,CAACve,OAAD,EAAU;EACnC,WAAOU,sBAAsB,CAACV,OAAD,CAAtB,IAAmCA,OAAO,CAACmD,UAAlD;EACD;;EAE2B,SAArBkd,qBAAqB,CAAC9Y,KAAD,EAAQ;EAClC;EACA;EACA;EACA;EACA;EACA;EACA;EACA,QAAI,kBAAkBzE,IAAlB,CAAuByE,KAAK,CAACU,MAAN,CAAagO,OAApC,IACF1O,KAAK,CAAC3B,GAAN,KAAcyW,SAAd,IAA4B9U,KAAK,CAAC3B,GAAN,KAAcwW,YAAd,KAC1B7U,KAAK,CAAC3B,GAAN,KAAc4W,cAAd,IAAgCjV,KAAK,CAAC3B,GAAN,KAAc2W,YAA/C,IACChV,KAAK,CAACU,MAAN,CAAa0E,OAAb,CAAqBwQ,aAArB,CAF0B,CAD1B,GAIF,CAACT,cAAc,CAAC5Z,IAAf,CAAoByE,KAAK,CAAC3B,GAA1B,CAJH,EAImC;EACjC;EACD;;EAED2B,IAAAA,KAAK,CAAC0D,cAAN;EACA1D,IAAAA,KAAK,CAAC+Y,eAAN;;EAEA,QAAI,KAAKzc,QAAL,IAAiB,KAAKF,SAAL,CAAeC,QAAf,CAAwBkZ,mBAAxB,CAArB,EAAmE;EACjE;EACD;;EAED,UAAMxE,MAAM,GAAGyF,QAAQ,CAACQ,oBAAT,CAA8B,IAA9B,CAAf;EACA,UAAMF,QAAQ,GAAG,KAAK1a,SAAL,CAAeC,QAAf,CAAwBuI,iBAAxB,CAAjB;;EAEA,QAAI5E,KAAK,CAAC3B,GAAN,KAAcwW,YAAlB,EAA8B;EAC5B,YAAM7O,MAAM,GAAG,KAAKmC,OAAL,CAAavC,sBAAb,IAAqC,IAArC,GAA4C+B,cAAc,CAACY,IAAf,CAAoB,IAApB,EAA0B3C,sBAA1B,EAAgD,CAAhD,CAA3D;EACAI,MAAAA,MAAM,CAACyR,KAAP;EACAjB,MAAAA,QAAQ,CAACO,UAAT;EACA;EACD;;EAED,QAAI,CAACD,QAAD,KAAc9W,KAAK,CAAC3B,GAAN,KAAc2W,YAAd,IAA8BhV,KAAK,CAAC3B,GAAN,KAAc4W,cAA1D,CAAJ,EAA+E;EAC7E,YAAMjP,MAAM,GAAG,KAAKmC,OAAL,CAAavC,sBAAb,IAAqC,IAArC,GAA4C+B,cAAc,CAACY,IAAf,CAAoB,IAApB,EAA0B3C,sBAA1B,EAAgD,CAAhD,CAA3D;EACAI,MAAAA,MAAM,CAACgT,KAAP;EACA;EACD;;EAED,QAAI,CAAClC,QAAD,IAAa9W,KAAK,CAAC3B,GAAN,KAAcyW,SAA/B,EAA0C;EACxC0B,MAAAA,QAAQ,CAACO,UAAT;EACA;EACD;;EAED,UAAMkC,KAAK,GAAGtR,cAAc,CAACC,IAAf,CAAoBkO,sBAApB,EAA4C/E,MAA5C,EAAoDnK,MAApD,CAA2DlL,SAA3D,CAAd;;EAEA,QAAI,CAACud,KAAK,CAACrY,MAAX,EAAmB;EACjB;EACD;;EAED,QAAI0M,KAAK,GAAG2L,KAAK,CAACtK,OAAN,CAAc3O,KAAK,CAACU,MAApB,CAAZ,CAlDkC;;EAqDlC,QAAIV,KAAK,CAAC3B,GAAN,KAAc2W,YAAd,IAA8B1H,KAAK,GAAG,CAA1C,EAA6C;EAC3CA,MAAAA,KAAK;EACN,KAvDiC;;;EA0DlC,QAAItN,KAAK,CAAC3B,GAAN,KAAc4W,cAAd,IAAgC3H,KAAK,GAAG2L,KAAK,CAACrY,MAAN,GAAe,CAA3D,EAA8D;EAC5D0M,MAAAA,KAAK;EACN,KA5DiC;;;EA+DlCA,IAAAA,KAAK,GAAGA,KAAK,KAAK,CAAC,CAAX,GAAe,CAAf,GAAmBA,KAA3B;EAEA2L,IAAAA,KAAK,CAAC3L,KAAD,CAAL,CAAamK,KAAb;EACD;;EArakC;EAwarC;EACA;EACA;EACA;EACA;;;EAEAtX,YAAY,CAACiC,EAAb,CAAgB9J,QAAhB,EAA0B+c,sBAA1B,EAAkDzP,sBAAlD,EAAwE4Q,QAAQ,CAACsC,qBAAjF;EACA3Y,YAAY,CAACiC,EAAb,CAAgB9J,QAAhB,EAA0B+c,sBAA1B,EAAkDO,aAAlD,EAAiEY,QAAQ,CAACsC,qBAA1E;EACA3Y,YAAY,CAACiC,EAAb,CAAgB9J,QAAhB,EAA0BmM,sBAA1B,EAAgD+R,QAAQ,CAACO,UAAzD;EACA5W,YAAY,CAACiC,EAAb,CAAgB9J,QAAhB,EAA0Bgd,oBAA1B,EAAgDkB,QAAQ,CAACO,UAAzD;EACA5W,YAAY,CAACiC,EAAb,CAAgB9J,QAAhB,EAA0BmM,sBAA1B,EAAgDmB,sBAAhD,EAAsE,UAAU5F,KAAV,EAAiB;EACrFA,EAAAA,KAAK,CAAC0D,cAAN;EACA8S,EAAAA,QAAQ,CAAC+B,iBAAT,CAA2B,IAA3B;EACD,CAHD;EAKA;EACA;EACA;EACA;EACA;EACA;;EAEA9a,kBAAkB,CAAC0G,MAAD,EAAOqS,QAAP,CAAlB;;EC5hBA;EACA;EACA;EACA;EACA;EACA;EAkBA;EACA;EACA;EACA;EACA;;EAEA,MAAMrS,MAAI,GAAG,OAAb;EACA,MAAMH,UAAQ,GAAG,UAAjB;EACA,MAAMI,WAAS,GAAI,IAAGJ,UAAS,EAA/B;EACA,MAAMK,cAAY,GAAG,WAArB;EACA,MAAMwQ,YAAU,GAAG,QAAnB;EAEA,MAAM7L,SAAO,GAAG;EACdkQ,EAAAA,QAAQ,EAAE,IADI;EAEdhQ,EAAAA,QAAQ,EAAE,IAFI;EAGduO,EAAAA,KAAK,EAAE;EAHO,CAAhB;EAMA,MAAMlO,aAAW,GAAG;EAClB2P,EAAAA,QAAQ,EAAE,kBADQ;EAElBhQ,EAAAA,QAAQ,EAAE,SAFQ;EAGlBuO,EAAAA,KAAK,EAAE;EAHW,CAApB;EAMA,MAAMvG,YAAU,GAAI,OAAM9M,WAAU,EAApC;EACA,MAAM+U,oBAAoB,GAAI,gBAAe/U,WAAU,EAAvD;EACA,MAAM+M,cAAY,GAAI,SAAQ/M,WAAU,EAAxC;EACA,MAAM4M,YAAU,GAAI,OAAM5M,WAAU,EAApC;EACA,MAAM6M,aAAW,GAAI,QAAO7M,WAAU,EAAtC;EACA,MAAMgV,eAAa,GAAI,UAAShV,WAAU,EAA1C;EACA,MAAMiV,YAAY,GAAI,SAAQjV,WAAU,EAAxC;EACA,MAAMkV,qBAAmB,GAAI,gBAAelV,WAAU,EAAtD;EACA,MAAMmV,qBAAqB,GAAI,kBAAiBnV,WAAU,EAA1D;EACA,MAAMoV,qBAAqB,GAAI,kBAAiBpV,WAAU,EAA1D;EACA,MAAMqV,uBAAuB,GAAI,oBAAmBrV,WAAU,EAA9D;EACA,MAAMK,sBAAoB,GAAI,QAAOL,WAAU,GAAEC,cAAa,EAA9D;EAEA,MAAMqV,6BAA6B,GAAG,yBAAtC;EACA,MAAMC,mBAAmB,GAAG,gBAA5B;EACA,MAAMC,eAAe,GAAG,YAAxB;EACA,MAAMjV,iBAAe,GAAG,MAAxB;EACA,MAAMC,iBAAe,GAAG,MAAxB;EACA,MAAMiV,iBAAiB,GAAG,cAA1B;EAEA,MAAMC,eAAe,GAAG,eAAxB;EACA,MAAMC,mBAAmB,GAAG,aAA5B;EACA,MAAMnU,sBAAoB,GAAG,0BAA7B;EACA,MAAMoU,uBAAqB,GAAG,2BAA9B;EACA,MAAMC,wBAAsB,GAAG,mDAA/B;EACA,MAAMC,yBAAuB,GAAG,aAAhC;EAEA;EACA;EACA;EACA;EACA;;EAEA,MAAMC,KAAN,SAAoBvW,aAApB,CAAkC;EAChCC,EAAAA,WAAW,CAACpL,OAAD,EAAUoC,MAAV,EAAkB;EAC3B,UAAMpC,OAAN;EAEA,SAAK0T,OAAL,GAAe,KAAKC,UAAL,CAAgBvR,MAAhB,CAAf;EACA,SAAKuf,OAAL,GAAezS,cAAc,CAACK,OAAf,CAAuB8R,eAAvB,EAAwC,KAAKhW,QAA7C,CAAf;EACA,SAAKuW,SAAL,GAAiB,IAAjB;EACA,SAAKC,QAAL,GAAgB,KAAhB;EACA,SAAKC,kBAAL,GAA0B,KAA1B;EACA,SAAKC,oBAAL,GAA4B,KAA5B;EACA,SAAK7I,gBAAL,GAAwB,KAAxB;EACA,SAAK8I,eAAL,GAAuB,CAAvB;EACD,GAZ+B;;;EAgBd,aAAPzR,OAAO,GAAG;EACnB,WAAOA,SAAP;EACD;;EAEkB,aAARhF,QAAQ,GAAG;EACpB,WAAOA,UAAP;EACD,GAtB+B;;;EA0BhC8B,EAAAA,MAAM,CAACuJ,aAAD,EAAgB;EACpB,WAAO,KAAKiL,QAAL,GAAgB,KAAKhI,IAAL,EAAhB,GAA8B,KAAKC,IAAL,CAAUlD,aAAV,CAArC;EACD;;EAEDkD,EAAAA,IAAI,CAAClD,aAAD,EAAgB;EAClB,QAAI,KAAKiL,QAAL,IAAiB,KAAK3I,gBAA1B,EAA4C;EAC1C;EACD;;EAED,QAAI,KAAK+I,WAAL,EAAJ,EAAwB;EACtB,WAAK/I,gBAAL,GAAwB,IAAxB;EACD;;EAED,UAAMsF,SAAS,GAAG9W,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCkN,YAApC,EAAgD;EAChE3B,MAAAA;EADgE,KAAhD,CAAlB;;EAIA,QAAI,KAAKiL,QAAL,IAAiBrD,SAAS,CAACjU,gBAA/B,EAAiD;EAC/C;EACD;;EAED,SAAKsX,QAAL,GAAgB,IAAhB;;EAEA,SAAKK,eAAL;;EACA,SAAKC,aAAL;;EAEA,SAAKC,aAAL;;EAEA,SAAKC,eAAL;;EACA,SAAKC,eAAL;;EAEA5a,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+BwV,qBAA/B,EAAoDU,uBAApD,EAA2Eha,KAAK,IAAI,KAAKsS,IAAL,CAAUtS,KAAV,CAApF;EAEAG,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAKgY,OAArB,EAA8BX,uBAA9B,EAAuD,MAAM;EAC3DtZ,MAAAA,YAAY,CAACkC,GAAb,CAAiB,KAAKyB,QAAtB,EAAgC0V,qBAAhC,EAAuDxZ,KAAK,IAAI;EAC9D,YAAIA,KAAK,CAACU,MAAN,KAAiB,KAAKoD,QAA1B,EAAoC;EAClC,eAAK0W,oBAAL,GAA4B,IAA5B;EACD;EACF,OAJD;EAKD,KAND;;EAQA,SAAKQ,aAAL,CAAmB,MAAM,KAAKC,YAAL,CAAkB5L,aAAlB,CAAzB;EACD;;EAEDiD,EAAAA,IAAI,CAACtS,KAAD,EAAQ;EACV,QAAIA,KAAJ,EAAW;EACTA,MAAAA,KAAK,CAAC0D,cAAN;EACD;;EAED,QAAI,CAAC,KAAK4W,QAAN,IAAkB,KAAK3I,gBAA3B,EAA6C;EAC3C;EACD;;EAED,UAAM+F,SAAS,GAAGvX,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCoN,YAApC,CAAlB;;EAEA,QAAIwG,SAAS,CAAC1U,gBAAd,EAAgC;EAC9B;EACD;;EAED,SAAKsX,QAAL,GAAgB,KAAhB;;EACA,UAAMY,UAAU,GAAG,KAAKR,WAAL,EAAnB;;EAEA,QAAIQ,UAAJ,EAAgB;EACd,WAAKvJ,gBAAL,GAAwB,IAAxB;EACD;;EAED,SAAKmJ,eAAL;;EACA,SAAKC,eAAL;;EAEA5a,IAAAA,YAAY,CAACC,GAAb,CAAiB9H,QAAjB,EAA2B8gB,eAA3B;;EAEA,SAAKtV,QAAL,CAAc1H,SAAd,CAAwB2C,MAAxB,CAA+B6F,iBAA/B;;EAEAzE,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK0D,QAAtB,EAAgCwV,qBAAhC;EACAnZ,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKga,OAAtB,EAA+BX,uBAA/B;;EAEA,QAAIyB,UAAJ,EAAgB;EACd,YAAM7hB,kBAAkB,GAAGD,gCAAgC,CAAC,KAAK0K,QAAN,CAA3D;EAEA3D,MAAAA,YAAY,CAACkC,GAAb,CAAiB,KAAKyB,QAAtB,EAAgC,eAAhC,EAAiD9D,KAAK,IAAI,KAAKmb,UAAL,CAAgBnb,KAAhB,CAA1D;EACA9F,MAAAA,oBAAoB,CAAC,KAAK4J,QAAN,EAAgBzK,kBAAhB,CAApB;EACD,KALD,MAKO;EACL,WAAK8hB,UAAL;EACD;EACF;;EAEDlX,EAAAA,OAAO,GAAG;EACR,KAAC1K,MAAD,EAAS,KAAKuK,QAAd,EAAwB,KAAKsW,OAA7B,EACGnf,OADH,CACWmgB,WAAW,IAAIjb,YAAY,CAACC,GAAb,CAAiBgb,WAAjB,EAA8BhX,WAA9B,CAD1B;EAGA,UAAMH,OAAN;EAEA;EACJ;EACA;EACA;EACA;;EACI9D,IAAAA,YAAY,CAACC,GAAb,CAAiB9H,QAAjB,EAA2B8gB,eAA3B;EAEA,SAAKjN,OAAL,GAAe,IAAf;EACA,SAAKiO,OAAL,GAAe,IAAf;EACA,SAAKC,SAAL,GAAiB,IAAjB;EACA,SAAKC,QAAL,GAAgB,IAAhB;EACA,SAAKC,kBAAL,GAA0B,IAA1B;EACA,SAAKC,oBAAL,GAA4B,IAA5B;EACA,SAAK7I,gBAAL,GAAwB,IAAxB;EACA,SAAK8I,eAAL,GAAuB,IAAvB;EACD;;EAEDY,EAAAA,YAAY,GAAG;EACb,SAAKR,aAAL;EACD,GAzI+B;;;EA6IhCzO,EAAAA,UAAU,CAACvR,MAAD,EAAS;EACjBA,IAAAA,MAAM,GAAG,EACP,GAAGmO,SADI;EAEP,SAAGnO;EAFI,KAAT;EAIAF,IAAAA,eAAe,CAACwJ,MAAD,EAAOtJ,MAAP,EAAe0O,aAAf,CAAf;EACA,WAAO1O,MAAP;EACD;;EAEDogB,EAAAA,YAAY,CAAC5L,aAAD,EAAgB;EAC1B,UAAM6L,UAAU,GAAG,KAAKR,WAAL,EAAnB;;EACA,UAAMY,SAAS,GAAG3T,cAAc,CAACK,OAAf,CAAuB+R,mBAAvB,EAA4C,KAAKK,OAAjD,CAAlB;;EAEA,QAAI,CAAC,KAAKtW,QAAL,CAAclI,UAAf,IAA6B,KAAKkI,QAAL,CAAclI,UAAd,CAAyB3B,QAAzB,KAAsCiC,IAAI,CAACC,YAA5E,EAA0F;EACxF;EACA7D,MAAAA,QAAQ,CAAC6E,IAAT,CAAcoe,WAAd,CAA0B,KAAKzX,QAA/B;EACD;;EAED,SAAKA,QAAL,CAAcnI,KAAd,CAAoBI,OAApB,GAA8B,OAA9B;;EACA,SAAK+H,QAAL,CAAc0C,eAAd,CAA8B,aAA9B;;EACA,SAAK1C,QAAL,CAAciC,YAAd,CAA2B,YAA3B,EAAyC,IAAzC;;EACA,SAAKjC,QAAL,CAAciC,YAAd,CAA2B,MAA3B,EAAmC,QAAnC;;EACA,SAAKjC,QAAL,CAAcsD,SAAd,GAA0B,CAA1B;;EAEA,QAAIkU,SAAJ,EAAe;EACbA,MAAAA,SAAS,CAAClU,SAAV,GAAsB,CAAtB;EACD;;EAED,QAAI8T,UAAJ,EAAgB;EACdne,MAAAA,MAAM,CAAC,KAAK+G,QAAN,CAAN;EACD;;EAED,SAAKA,QAAL,CAAc1H,SAAd,CAAwBqS,GAAxB,CAA4B7J,iBAA5B;;EAEA,QAAI,KAAKuH,OAAL,CAAasL,KAAjB,EAAwB;EACtB,WAAK+D,aAAL;EACD;;EAED,UAAMC,kBAAkB,GAAG,MAAM;EAC/B,UAAI,KAAKtP,OAAL,CAAasL,KAAjB,EAAwB;EACtB,aAAK3T,QAAL,CAAc2T,KAAd;EACD;;EAED,WAAK9F,gBAAL,GAAwB,KAAxB;EACAxR,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCmN,aAApC,EAAiD;EAC/C5B,QAAAA;EAD+C,OAAjD;EAGD,KATD;;EAWA,QAAI6L,UAAJ,EAAgB;EACd,YAAM7hB,kBAAkB,GAAGD,gCAAgC,CAAC,KAAKghB,OAAN,CAA3D;EAEAja,MAAAA,YAAY,CAACkC,GAAb,CAAiB,KAAK+X,OAAtB,EAA+B,eAA/B,EAAgDqB,kBAAhD;EACAvhB,MAAAA,oBAAoB,CAAC,KAAKkgB,OAAN,EAAe/gB,kBAAf,CAApB;EACD,KALD,MAKO;EACLoiB,MAAAA,kBAAkB;EACnB;EACF;;EAEDD,EAAAA,aAAa,GAAG;EACdrb,IAAAA,YAAY,CAACC,GAAb,CAAiB9H,QAAjB,EAA2B8gB,eAA3B,EADc;;EAEdjZ,IAAAA,YAAY,CAACiC,EAAb,CAAgB9J,QAAhB,EAA0B8gB,eAA1B,EAAyCpZ,KAAK,IAAI;EAChD,UAAI1H,QAAQ,KAAK0H,KAAK,CAACU,MAAnB,IACA,KAAKoD,QAAL,KAAkB9D,KAAK,CAACU,MADxB,IAEA,CAAC,KAAKoD,QAAL,CAAczH,QAAd,CAAuB2D,KAAK,CAACU,MAA7B,CAFL,EAE2C;EACzC,aAAKoD,QAAL,CAAc2T,KAAd;EACD;EACF,KAND;EAOD;;EAEDqD,EAAAA,eAAe,GAAG;EAChB,QAAI,KAAKR,QAAT,EAAmB;EACjBna,MAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+ByV,qBAA/B,EAAsDvZ,KAAK,IAAI;EAC7D,YAAI,KAAKmM,OAAL,CAAajD,QAAb,IAAyBlJ,KAAK,CAAC3B,GAAN,KAAcwW,YAA3C,EAAuD;EACrD7U,UAAAA,KAAK,CAAC0D,cAAN;EACA,eAAK4O,IAAL;EACD,SAHD,MAGO,IAAI,CAAC,KAAKnG,OAAL,CAAajD,QAAd,IAA0BlJ,KAAK,CAAC3B,GAAN,KAAcwW,YAA5C,EAAwD;EAC7D,eAAK6G,0BAAL;EACD;EACF,OAPD;EAQD,KATD,MASO;EACLvb,MAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK0D,QAAtB,EAAgCyV,qBAAhC;EACD;EACF;;EAEDwB,EAAAA,eAAe,GAAG;EAChB,QAAI,KAAKT,QAAT,EAAmB;EACjBna,MAAAA,YAAY,CAACiC,EAAb,CAAgB7I,MAAhB,EAAwB8f,YAAxB,EAAsC,MAAM,KAAKwB,aAAL,EAA5C;EACD,KAFD,MAEO;EACL1a,MAAAA,YAAY,CAACC,GAAb,CAAiB7G,MAAjB,EAAyB8f,YAAzB;EACD;EACF;;EAED8B,EAAAA,UAAU,GAAG;EACX,SAAKrX,QAAL,CAAcnI,KAAd,CAAoBI,OAApB,GAA8B,MAA9B;;EACA,SAAK+H,QAAL,CAAciC,YAAd,CAA2B,aAA3B,EAA0C,IAA1C;;EACA,SAAKjC,QAAL,CAAc0C,eAAd,CAA8B,YAA9B;;EACA,SAAK1C,QAAL,CAAc0C,eAAd,CAA8B,MAA9B;;EACA,SAAKmL,gBAAL,GAAwB,KAAxB;;EACA,SAAKqJ,aAAL,CAAmB,MAAM;EACvB1iB,MAAAA,QAAQ,CAAC6E,IAAT,CAAcf,SAAd,CAAwB2C,MAAxB,CAA+B6a,eAA/B;;EACA,WAAK+B,iBAAL;;EACA,WAAKC,eAAL;;EACAzb,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCqN,cAApC;EACD,KALD;EAMD;;EAED0K,EAAAA,eAAe,GAAG;EAChB,SAAKxB,SAAL,CAAeze,UAAf,CAA0B0J,WAA1B,CAAsC,KAAK+U,SAA3C;;EACA,SAAKA,SAAL,GAAiB,IAAjB;EACD;;EAEDW,EAAAA,aAAa,CAAC3d,QAAD,EAAW;EACtB,UAAM6d,UAAU,GAAG,KAAKR,WAAL,EAAnB;;EACA,QAAI,KAAKJ,QAAL,IAAiB,KAAKnO,OAAL,CAAa+M,QAAlC,EAA4C;EAC1C,WAAKmB,SAAL,GAAiB/hB,QAAQ,CAACwjB,aAAT,CAAuB,KAAvB,CAAjB;EACA,WAAKzB,SAAL,CAAe0B,SAAf,GAA2BpC,mBAA3B;;EAEA,UAAIuB,UAAJ,EAAgB;EACd,aAAKb,SAAL,CAAeje,SAAf,CAAyBqS,GAAzB,CAA6B9J,iBAA7B;EACD;;EAEDrM,MAAAA,QAAQ,CAAC6E,IAAT,CAAcoe,WAAd,CAA0B,KAAKlB,SAA/B;EAEAla,MAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+BwV,qBAA/B,EAAoDtZ,KAAK,IAAI;EAC3D,YAAI,KAAKwa,oBAAT,EAA+B;EAC7B,eAAKA,oBAAL,GAA4B,KAA5B;EACA;EACD;;EAED,YAAIxa,KAAK,CAACU,MAAN,KAAiBV,KAAK,CAACgc,aAA3B,EAA0C;EACxC;EACD;;EAED,YAAI,KAAK7P,OAAL,CAAa+M,QAAb,KAA0B,QAA9B,EAAwC;EACtC,eAAKwC,0BAAL;EACD,SAFD,MAEO;EACL,eAAKpJ,IAAL;EACD;EACF,OAfD;;EAiBA,UAAI4I,UAAJ,EAAgB;EACdne,QAAAA,MAAM,CAAC,KAAKsd,SAAN,CAAN;EACD;;EAED,WAAKA,SAAL,CAAeje,SAAf,CAAyBqS,GAAzB,CAA6B7J,iBAA7B;;EAEA,UAAI,CAACsW,UAAL,EAAiB;EACf7d,QAAAA,QAAQ;EACR;EACD;;EAED,YAAM4e,0BAA0B,GAAG7iB,gCAAgC,CAAC,KAAKihB,SAAN,CAAnE;EAEAla,MAAAA,YAAY,CAACkC,GAAb,CAAiB,KAAKgY,SAAtB,EAAiC,eAAjC,EAAkDhd,QAAlD;EACAnD,MAAAA,oBAAoB,CAAC,KAAKmgB,SAAN,EAAiB4B,0BAAjB,CAApB;EACD,KA1CD,MA0CO,IAAI,CAAC,KAAK3B,QAAN,IAAkB,KAAKD,SAA3B,EAAsC;EAC3C,WAAKA,SAAL,CAAeje,SAAf,CAAyB2C,MAAzB,CAAgC6F,iBAAhC;;EAEA,YAAMsX,cAAc,GAAG,MAAM;EAC3B,aAAKL,eAAL;;EACAxe,QAAAA,QAAQ;EACT,OAHD;;EAKA,UAAI6d,UAAJ,EAAgB;EACd,cAAMe,0BAA0B,GAAG7iB,gCAAgC,CAAC,KAAKihB,SAAN,CAAnE;EACAla,QAAAA,YAAY,CAACkC,GAAb,CAAiB,KAAKgY,SAAtB,EAAiC,eAAjC,EAAkD6B,cAAlD;EACAhiB,QAAAA,oBAAoB,CAAC,KAAKmgB,SAAN,EAAiB4B,0BAAjB,CAApB;EACD,OAJD,MAIO;EACLC,QAAAA,cAAc;EACf;EACF,KAfM,MAeA;EACL7e,MAAAA,QAAQ;EACT;EACF;;EAEDqd,EAAAA,WAAW,GAAG;EACZ,WAAO,KAAK5W,QAAL,CAAc1H,SAAd,CAAwBC,QAAxB,CAAiCsI,iBAAjC,CAAP;EACD;;EAED+W,EAAAA,0BAA0B,GAAG;EAC3B,UAAMhE,SAAS,GAAGvX,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCqV,oBAApC,CAAlB;;EACA,QAAIzB,SAAS,CAAC1U,gBAAd,EAAgC;EAC9B;EACD;;EAED,UAAMmZ,kBAAkB,GAAG,KAAKrY,QAAL,CAAcsY,YAAd,GAA6B9jB,QAAQ,CAACmE,eAAT,CAAyB4f,YAAjF;;EAEA,QAAI,CAACF,kBAAL,EAAyB;EACvB,WAAKrY,QAAL,CAAcnI,KAAd,CAAoB2gB,SAApB,GAAgC,QAAhC;EACD;;EAED,SAAKxY,QAAL,CAAc1H,SAAd,CAAwBqS,GAAxB,CAA4BoL,iBAA5B;;EACA,UAAM0C,uBAAuB,GAAGnjB,gCAAgC,CAAC,KAAKghB,OAAN,CAAhE;EACAja,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK0D,QAAtB,EAAgC,eAAhC;EACA3D,IAAAA,YAAY,CAACkC,GAAb,CAAiB,KAAKyB,QAAtB,EAAgC,eAAhC,EAAiD,MAAM;EACrD,WAAKA,QAAL,CAAc1H,SAAd,CAAwB2C,MAAxB,CAA+B8a,iBAA/B;;EACA,UAAI,CAACsC,kBAAL,EAAyB;EACvBhc,QAAAA,YAAY,CAACkC,GAAb,CAAiB,KAAKyB,QAAtB,EAAgC,eAAhC,EAAiD,MAAM;EACrD,eAAKA,QAAL,CAAcnI,KAAd,CAAoB2gB,SAApB,GAAgC,EAAhC;EACD,SAFD;EAGApiB,QAAAA,oBAAoB,CAAC,KAAK4J,QAAN,EAAgByY,uBAAhB,CAApB;EACD;EACF,KARD;EASAriB,IAAAA,oBAAoB,CAAC,KAAK4J,QAAN,EAAgByY,uBAAhB,CAApB;;EACA,SAAKzY,QAAL,CAAc2T,KAAd;EACD,GA3V+B;EA8VhC;EACA;;;EAEAoD,EAAAA,aAAa,GAAG;EACd,UAAMsB,kBAAkB,GAAG,KAAKrY,QAAL,CAAcsY,YAAd,GAA6B9jB,QAAQ,CAACmE,eAAT,CAAyB4f,YAAjF;;EAEA,QAAK,CAAC,KAAK9B,kBAAN,IAA4B4B,kBAA5B,IAAkD,CAAC5e,KAAK,EAAzD,IAAiE,KAAKgd,kBAAL,IAA2B,CAAC4B,kBAA5B,IAAkD5e,KAAK,EAA5H,EAAiI;EAC/H,WAAKuG,QAAL,CAAcnI,KAAd,CAAoB6gB,WAApB,GAAmC,GAAE,KAAK/B,eAAgB,IAA1D;EACD;;EAED,QAAK,KAAKF,kBAAL,IAA2B,CAAC4B,kBAA5B,IAAkD,CAAC5e,KAAK,EAAzD,IAAiE,CAAC,KAAKgd,kBAAN,IAA4B4B,kBAA5B,IAAkD5e,KAAK,EAA5H,EAAiI;EAC/H,WAAKuG,QAAL,CAAcnI,KAAd,CAAoB8gB,YAApB,GAAoC,GAAE,KAAKhC,eAAgB,IAA3D;EACD;EACF;;EAEDkB,EAAAA,iBAAiB,GAAG;EAClB,SAAK7X,QAAL,CAAcnI,KAAd,CAAoB6gB,WAApB,GAAkC,EAAlC;EACA,SAAK1Y,QAAL,CAAcnI,KAAd,CAAoB8gB,YAApB,GAAmC,EAAnC;EACD;;EAED9B,EAAAA,eAAe,GAAG;EAChB,UAAM1T,IAAI,GAAG3O,QAAQ,CAAC6E,IAAT,CAAc+J,qBAAd,EAAb;EACA,SAAKqT,kBAAL,GAA0BpiB,IAAI,CAACukB,KAAL,CAAWzV,IAAI,CAACI,IAAL,GAAYJ,IAAI,CAAC0V,KAA5B,IAAqCpjB,MAAM,CAACqjB,UAAtE;EACA,SAAKnC,eAAL,GAAuB,KAAKoC,kBAAL,EAAvB;EACD;;EAEDjC,EAAAA,aAAa,GAAG;EACd,QAAI,KAAKL,kBAAT,EAA6B;EAC3B,WAAKuC,qBAAL,CAA2B7C,wBAA3B,EAAmD,cAAnD,EAAmE8C,eAAe,IAAIA,eAAe,GAAG,KAAKtC,eAA7G;;EACA,WAAKqC,qBAAL,CAA2B5C,yBAA3B,EAAoD,aAApD,EAAmE6C,eAAe,IAAIA,eAAe,GAAG,KAAKtC,eAA7G;;EACA,WAAKqC,qBAAL,CAA2B,MAA3B,EAAmC,cAAnC,EAAmDC,eAAe,IAAIA,eAAe,GAAG,KAAKtC,eAA7F;EACD;;EAEDniB,IAAAA,QAAQ,CAAC6E,IAAT,CAAcf,SAAd,CAAwBqS,GAAxB,CAA4BmL,eAA5B;EACD;;EAEDkD,EAAAA,qBAAqB,CAACpkB,QAAD,EAAWskB,SAAX,EAAsB3f,QAAtB,EAAgC;EACnDsK,IAAAA,cAAc,CAACC,IAAf,CAAoBlP,QAApB,EACGuC,OADH,CACWxC,OAAO,IAAI;EAClB,UAAIA,OAAO,KAAKH,QAAQ,CAAC6E,IAArB,IAA6B5D,MAAM,CAACqjB,UAAP,GAAoBnkB,OAAO,CAACwkB,WAAR,GAAsB,KAAKxC,eAAhF,EAAiG;EAC/F;EACD;;EAED,YAAMyC,WAAW,GAAGzkB,OAAO,CAACkD,KAAR,CAAcqhB,SAAd,CAApB;EACA,YAAMD,eAAe,GAAGxjB,MAAM,CAACC,gBAAP,CAAwBf,OAAxB,EAAiCukB,SAAjC,CAAxB;EACA3W,MAAAA,WAAW,CAACC,gBAAZ,CAA6B7N,OAA7B,EAAsCukB,SAAtC,EAAiDE,WAAjD;EACAzkB,MAAAA,OAAO,CAACkD,KAAR,CAAcqhB,SAAd,IAA2B3f,QAAQ,CAAC3D,MAAM,CAACC,UAAP,CAAkBojB,eAAlB,CAAD,CAAR,GAA+C,IAA1E;EACD,KAVH;EAWD;;EAEDnB,EAAAA,eAAe,GAAG;EAChB,SAAKuB,uBAAL,CAA6BlD,wBAA7B,EAAqD,cAArD;;EACA,SAAKkD,uBAAL,CAA6BjD,yBAA7B,EAAsD,aAAtD;;EACA,SAAKiD,uBAAL,CAA6B,MAA7B,EAAqC,cAArC;EACD;;EAEDA,EAAAA,uBAAuB,CAACzkB,QAAD,EAAWskB,SAAX,EAAsB;EAC3CrV,IAAAA,cAAc,CAACC,IAAf,CAAoBlP,QAApB,EAA8BuC,OAA9B,CAAsCxC,OAAO,IAAI;EAC/C,YAAM2C,KAAK,GAAGiL,WAAW,CAACU,gBAAZ,CAA6BtO,OAA7B,EAAsCukB,SAAtC,CAAd;;EACA,UAAI,OAAO5hB,KAAP,KAAiB,WAAjB,IAAgC3C,OAAO,KAAKH,QAAQ,CAAC6E,IAAzD,EAA+D;EAC7D1E,QAAAA,OAAO,CAACkD,KAAR,CAAcqhB,SAAd,IAA2B,EAA3B;EACD,OAFD,MAEO;EACL3W,QAAAA,WAAW,CAACE,mBAAZ,CAAgC9N,OAAhC,EAAyCukB,SAAzC;EACAvkB,QAAAA,OAAO,CAACkD,KAAR,CAAcqhB,SAAd,IAA2B5hB,KAA3B;EACD;EACF,KARD;EASD;;EAEDyhB,EAAAA,kBAAkB,GAAG;EAAE;EACrB,UAAMO,SAAS,GAAG9kB,QAAQ,CAACwjB,aAAT,CAAuB,KAAvB,CAAlB;EACAsB,IAAAA,SAAS,CAACrB,SAAV,GAAsBrC,6BAAtB;EACAphB,IAAAA,QAAQ,CAAC6E,IAAT,CAAcoe,WAAd,CAA0B6B,SAA1B;EACA,UAAMC,cAAc,GAAGD,SAAS,CAAClW,qBAAV,GAAkCoW,KAAlC,GAA0CF,SAAS,CAACH,WAA3E;EACA3kB,IAAAA,QAAQ,CAAC6E,IAAT,CAAcmI,WAAd,CAA0B8X,SAA1B;EACA,WAAOC,cAAP;EACD,GAza+B;;;EA6aV,SAAftf,eAAe,CAAClD,MAAD,EAASwU,aAAT,EAAwB;EAC5C,WAAO,KAAK9J,IAAL,CAAU,YAAY;EAC3B,UAAIC,IAAI,GAAGzB,IAAI,CAACtF,GAAL,CAAS,IAAT,EAAeuF,UAAf,CAAX;EACA,YAAMmI,OAAO,GAAG,EACd,GAAGnD,SADW;EAEd,WAAG3C,WAAW,CAACI,iBAAZ,CAA8B,IAA9B,CAFW;EAGd,YAAI,OAAO5L,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAApD;EAHc,OAAhB;;EAMA,UAAI,CAAC2K,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAI2U,KAAJ,CAAU,IAAV,EAAgBhO,OAAhB,CAAP;EACD;;EAED,UAAI,OAAOtR,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAO2K,IAAI,CAAC3K,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;EACD;;EAED2K,QAAAA,IAAI,CAAC3K,MAAD,CAAJ,CAAawU,aAAb;EACD;EACF,KAnBM,CAAP;EAoBD;;EAlc+B;EAqclC;EACA;EACA;EACA;EACA;;;EAEAlP,YAAY,CAACiC,EAAb,CAAgB9J,QAAhB,EAA0BmM,sBAA1B,EAAgDmB,sBAAhD,EAAsE,UAAU5F,KAAV,EAAiB;EACrF,QAAMU,MAAM,GAAGvH,sBAAsB,CAAC,IAAD,CAArC;;EAEA,MAAI,KAAKuV,OAAL,KAAiB,GAAjB,IAAwB,KAAKA,OAAL,KAAiB,MAA7C,EAAqD;EACnD1O,IAAAA,KAAK,CAAC0D,cAAN;EACD;;EAEDvD,EAAAA,YAAY,CAACkC,GAAb,CAAiB3B,MAAjB,EAAyBsQ,YAAzB,EAAqCiG,SAAS,IAAI;EAChD,QAAIA,SAAS,CAACjU,gBAAd,EAAgC;EAC9B;EACA;EACD;;EAED7C,IAAAA,YAAY,CAACkC,GAAb,CAAiB3B,MAAjB,EAAyByQ,cAAzB,EAAuC,MAAM;EAC3C,UAAIzV,SAAS,CAAC,IAAD,CAAb,EAAqB;EACnB,aAAK+b,KAAL;EACD;EACF,KAJD;EAKD,GAXD;EAaA,MAAIjS,IAAI,GAAGzB,IAAI,CAACtF,GAAL,CAASiC,MAAT,EAAiBsD,UAAjB,CAAX;;EACA,MAAI,CAACwB,IAAL,EAAW;EACT,UAAM3K,MAAM,GAAG,EACb,GAAGwL,WAAW,CAACI,iBAAZ,CAA8B/F,MAA9B,CADU;EAEb,SAAG2F,WAAW,CAACI,iBAAZ,CAA8B,IAA9B;EAFU,KAAf;EAKAjB,IAAAA,IAAI,GAAG,IAAI2U,KAAJ,CAAUzZ,MAAV,EAAkB7F,MAAlB,CAAP;EACD;;EAED2K,EAAAA,IAAI,CAACM,MAAL,CAAY,IAAZ;EACD,CA/BD;EAiCA;EACA;EACA;EACA;EACA;EACA;;EAEArI,kBAAkB,CAAC0G,MAAD,EAAOgW,KAAP,CAAlB;;ECnkBA;EACA;EACA;EACA;EACA;EACA;EAKA,MAAMF,sBAAsB,GAAG,sCAA/B;EACA,MAAMC,uBAAuB,GAAG,aAAhC;;EAEA,MAAMqD,QAAQ,GAAG,MAAM;EACrB;EACA,QAAMC,aAAa,GAAGllB,QAAQ,CAACmE,eAAT,CAAyBwgB,WAA/C;EACA,SAAO9kB,IAAI,CAACyV,GAAL,CAASrU,MAAM,CAACqjB,UAAP,GAAoBY,aAA7B,CAAP;EACD,CAJD;;EAMA,MAAMlL,IAAI,GAAG,CAACgL,KAAK,GAAGC,QAAQ,EAAjB,KAAwB;EACnCjlB,EAAAA,QAAQ,CAAC6E,IAAT,CAAcxB,KAAd,CAAoB8hB,QAApB,GAA+B,QAA/B;;EACAX,EAAAA,qBAAqB,CAAC7C,sBAAD,EAAyB,cAAzB,EAAyC8C,eAAe,IAAIA,eAAe,GAAGO,KAA9E,CAArB;;EACAR,EAAAA,qBAAqB,CAAC5C,uBAAD,EAA0B,aAA1B,EAAyC6C,eAAe,IAAIA,eAAe,GAAGO,KAA9E,CAArB;;EACAR,EAAAA,qBAAqB,CAAC,MAAD,EAAS,cAAT,EAAyBC,eAAe,IAAIA,eAAe,GAAGO,KAA9D,CAArB;EACD,CALD;;EAOA,MAAMR,qBAAqB,GAAG,CAACpkB,QAAD,EAAWskB,SAAX,EAAsB3f,QAAtB,KAAmC;EAC/D,QAAMggB,cAAc,GAAGE,QAAQ,EAA/B;EACA5V,EAAAA,cAAc,CAACC,IAAf,CAAoBlP,QAApB,EACGuC,OADH,CACWxC,OAAO,IAAI;EAClB,QAAIA,OAAO,KAAKH,QAAQ,CAAC6E,IAArB,IAA6B5D,MAAM,CAACqjB,UAAP,GAAoBnkB,OAAO,CAACwkB,WAAR,GAAsBI,cAA3E,EAA2F;EACzF;EACD;;EAED,UAAMH,WAAW,GAAGzkB,OAAO,CAACkD,KAAR,CAAcqhB,SAAd,CAApB;EACA,UAAMD,eAAe,GAAGxjB,MAAM,CAACC,gBAAP,CAAwBf,OAAxB,EAAiCukB,SAAjC,CAAxB;EACA3W,IAAAA,WAAW,CAACC,gBAAZ,CAA6B7N,OAA7B,EAAsCukB,SAAtC,EAAiDE,WAAjD;EACAzkB,IAAAA,OAAO,CAACkD,KAAR,CAAcqhB,SAAd,IAA2B3f,QAAQ,CAAC3D,MAAM,CAACC,UAAP,CAAkBojB,eAAlB,CAAD,CAAR,GAA+C,IAA1E;EACD,GAVH;EAWD,CAbD;;EAeA,MAAMW,KAAK,GAAG,MAAM;EAClBplB,EAAAA,QAAQ,CAAC6E,IAAT,CAAcxB,KAAd,CAAoB8hB,QAApB,GAA+B,MAA/B;;EACAN,EAAAA,uBAAuB,CAAClD,sBAAD,EAAyB,cAAzB,CAAvB;;EACAkD,EAAAA,uBAAuB,CAACjD,uBAAD,EAA0B,aAA1B,CAAvB;;EACAiD,EAAAA,uBAAuB,CAAC,MAAD,EAAS,cAAT,CAAvB;EACD,CALD;;EAOA,MAAMA,uBAAuB,GAAG,CAACzkB,QAAD,EAAWskB,SAAX,KAAyB;EACvDrV,EAAAA,cAAc,CAACC,IAAf,CAAoBlP,QAApB,EAA8BuC,OAA9B,CAAsCxC,OAAO,IAAI;EAC/C,UAAM2C,KAAK,GAAGiL,WAAW,CAACU,gBAAZ,CAA6BtO,OAA7B,EAAsCukB,SAAtC,CAAd;;EACA,QAAI,OAAO5hB,KAAP,KAAiB,WAAjB,IAAgC3C,OAAO,KAAKH,QAAQ,CAAC6E,IAAzD,EAA+D;EAC7D1E,MAAAA,OAAO,CAACkD,KAAR,CAAcgiB,cAAd,CAA6BX,SAA7B;EACD,KAFD,MAEO;EACL3W,MAAAA,WAAW,CAACE,mBAAZ,CAAgC9N,OAAhC,EAAyCukB,SAAzC;EACAvkB,MAAAA,OAAO,CAACkD,KAAR,CAAcqhB,SAAd,IAA2B5hB,KAA3B;EACD;EACF,GARD;EASD,CAVD;;EChDA;EACA;EACA;EACA;EACA;EACA;EAkBA;EACA;EACA;EACA;EACA;;EAEA,MAAM+I,MAAI,GAAG,WAAb;EACA,MAAMH,UAAQ,GAAG,cAAjB;EACA,MAAMI,WAAS,GAAI,IAAGJ,UAAS,EAA/B;EACA,MAAMK,cAAY,GAAG,WAArB;EACA,MAAMkG,qBAAmB,GAAI,OAAMnG,WAAU,GAAEC,cAAa,EAA5D;EACA,MAAMwQ,UAAU,GAAG,QAAnB;EAEA,MAAM7L,SAAO,GAAG;EACdkQ,EAAAA,QAAQ,EAAE,IADI;EAEdhQ,EAAAA,QAAQ,EAAE,IAFI;EAGd0U,EAAAA,MAAM,EAAE;EAHM,CAAhB;EAMA,MAAMrU,aAAW,GAAG;EAClB2P,EAAAA,QAAQ,EAAE,SADQ;EAElBhQ,EAAAA,QAAQ,EAAE,SAFQ;EAGlB0U,EAAAA,MAAM,EAAE;EAHU,CAApB;EAMA,MAAMC,wBAAwB,GAAG,oBAAjC;EACA,MAAMjZ,iBAAe,GAAG,MAAxB;EACA,MAAMkZ,mBAAmB,GAAG,oBAA5B;EACA,MAAMC,aAAa,GAAG,iBAAtB;EACA,MAAMC,eAAe,GAAI,GAAED,aAAc,MAAKD,mBAAoB,EAAlE;EAEA,MAAM9M,YAAU,GAAI,OAAM5M,WAAU,EAApC;EACA,MAAM6M,aAAW,GAAI,QAAO7M,WAAU,EAAtC;EACA,MAAM8M,YAAU,GAAI,OAAM9M,WAAU,EAApC;EACA,MAAM+M,cAAY,GAAI,SAAQ/M,WAAU,EAAxC;EACA,MAAMgV,aAAa,GAAI,UAAShV,WAAU,EAA1C;EACA,MAAMK,sBAAoB,GAAI,QAAOL,WAAU,GAAEC,cAAa,EAA9D;EACA,MAAMiV,qBAAmB,GAAI,gBAAelV,WAAU,EAAtD;EAEA,MAAM4V,uBAAqB,GAAG,+BAA9B;EACA,MAAMpU,sBAAoB,GAAG,8BAA7B;EAEA;EACA;EACA;EACA;EACA;;EAEA,MAAMqY,SAAN,SAAwBra,aAAxB,CAAsC;EACpCC,EAAAA,WAAW,CAACpL,OAAD,EAAUoC,MAAV,EAAkB;EAC3B,UAAMpC,OAAN;EAEA,SAAK0T,OAAL,GAAe,KAAKC,UAAL,CAAgBvR,MAAhB,CAAf;EACA,SAAKyf,QAAL,GAAgB,KAAhB;;EACA,SAAK3N,kBAAL;EACD,GAPmC;;;EAWlB,aAAP3D,OAAO,GAAG;EACnB,WAAOA,SAAP;EACD;;EAEkB,aAARhF,QAAQ,GAAG;EACpB,WAAOA,UAAP;EACD,GAjBmC;;;EAqBpC8B,EAAAA,MAAM,CAACuJ,aAAD,EAAgB;EACpB,WAAO,KAAKiL,QAAL,GAAgB,KAAKhI,IAAL,EAAhB,GAA8B,KAAKC,IAAL,CAAUlD,aAAV,CAArC;EACD;;EAEDkD,EAAAA,IAAI,CAAClD,aAAD,EAAgB;EAClB,QAAI,KAAKiL,QAAT,EAAmB;EACjB;EACD;;EAED,UAAMrD,SAAS,GAAG9W,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCkN,YAApC,EAAgD;EAAE3B,MAAAA;EAAF,KAAhD,CAAlB;;EAEA,QAAI4H,SAAS,CAACjU,gBAAd,EAAgC;EAC9B;EACD;;EAED,SAAKsX,QAAL,GAAgB,IAAhB;EACA,SAAKxW,QAAL,CAAcnI,KAAd,CAAoBK,UAApB,GAAiC,SAAjC;;EAEA,QAAI,KAAKmQ,OAAL,CAAa+M,QAAjB,EAA2B;EACzB5gB,MAAAA,QAAQ,CAAC6E,IAAT,CAAcf,SAAd,CAAwBqS,GAAxB,CAA4BoP,wBAA5B;EACD;;EAED,QAAI,CAAC,KAAK1R,OAAL,CAAayR,MAAlB,EAA0B;EACxBM,MAAAA,IAAa;EACd;;EAED,SAAKpa,QAAL,CAAc1H,SAAd,CAAwBqS,GAAxB,CAA4BqP,mBAA5B;;EACA,SAAKha,QAAL,CAAc0C,eAAd,CAA8B,aAA9B;;EACA,SAAK1C,QAAL,CAAciC,YAAd,CAA2B,YAA3B,EAAyC,IAAzC;;EACA,SAAKjC,QAAL,CAAciC,YAAd,CAA2B,MAA3B,EAAmC,QAAnC;;EACA,SAAKjC,QAAL,CAAc1H,SAAd,CAAwBqS,GAAxB,CAA4B7J,iBAA5B;;EAEA,UAAMuZ,gBAAgB,GAAG,MAAM;EAC7B,WAAKra,QAAL,CAAc1H,SAAd,CAAwB2C,MAAxB,CAA+B+e,mBAA/B;;EACA3d,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCmN,aAApC,EAAiD;EAAE5B,QAAAA;EAAF,OAAjD;;EACA,WAAK+O,sBAAL,CAA4B,KAAKta,QAAjC;EACD,KAJD;;EAMApJ,IAAAA,UAAU,CAACyjB,gBAAD,EAAmB/kB,gCAAgC,CAAC,KAAK0K,QAAN,CAAnD,CAAV;EACD;;EAEDwO,EAAAA,IAAI,GAAG;EACL,QAAI,CAAC,KAAKgI,QAAV,EAAoB;EAClB;EACD;;EAED,UAAM5C,SAAS,GAAGvX,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCoN,YAApC,CAAlB;;EAEA,QAAIwG,SAAS,CAAC1U,gBAAd,EAAgC;EAC9B;EACD;;EAED,SAAKc,QAAL,CAAc1H,SAAd,CAAwBqS,GAAxB,CAA4BqP,mBAA5B;;EACA3d,IAAAA,YAAY,CAACC,GAAb,CAAiB9H,QAAjB,EAA2B8gB,aAA3B;;EACA,SAAKtV,QAAL,CAAcua,IAAd;;EACA,SAAK/D,QAAL,GAAgB,KAAhB;;EACA,SAAKxW,QAAL,CAAc1H,SAAd,CAAwB2C,MAAxB,CAA+B6F,iBAA/B;;EAEA,UAAM0Z,gBAAgB,GAAG,MAAM;EAC7B,WAAKxa,QAAL,CAAciC,YAAd,CAA2B,aAA3B,EAA0C,IAA1C;;EACA,WAAKjC,QAAL,CAAc0C,eAAd,CAA8B,YAA9B;;EACA,WAAK1C,QAAL,CAAc0C,eAAd,CAA8B,MAA9B;;EACA,WAAK1C,QAAL,CAAcnI,KAAd,CAAoBK,UAApB,GAAiC,QAAjC;;EAEA,UAAI,KAAKmQ,OAAL,CAAa+M,QAAjB,EAA2B;EACzB5gB,QAAAA,QAAQ,CAAC6E,IAAT,CAAcf,SAAd,CAAwB2C,MAAxB,CAA+B8e,wBAA/B;EACD;;EAED,UAAI,CAAC,KAAK1R,OAAL,CAAayR,MAAlB,EAA0B;EACxBW,QAAAA,KAAc;EACf;;EAEDpe,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCqN,cAApC;;EACA,WAAKrN,QAAL,CAAc1H,SAAd,CAAwB2C,MAAxB,CAA+B+e,mBAA/B;EACD,KAhBD;;EAkBApjB,IAAAA,UAAU,CAAC4jB,gBAAD,EAAmBllB,gCAAgC,CAAC,KAAK0K,QAAN,CAAnD,CAAV;EACD,GAlGmC;;;EAsGpCsI,EAAAA,UAAU,CAACvR,MAAD,EAAS;EACjBA,IAAAA,MAAM,GAAG,EACP,GAAGmO,SADI;EAEP,SAAG3C,WAAW,CAACI,iBAAZ,CAA8B,KAAK3C,QAAnC,CAFI;EAGP,UAAI,OAAOjJ,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,EAA1C;EAHO,KAAT;EAKAF,IAAAA,eAAe,CAACwJ,MAAD,EAAOtJ,MAAP,EAAe0O,aAAf,CAAf;EACA,WAAO1O,MAAP;EACD;;EAEDujB,EAAAA,sBAAsB,CAAC3lB,OAAD,EAAU;EAC9B0H,IAAAA,YAAY,CAACC,GAAb,CAAiB9H,QAAjB,EAA2B8gB,aAA3B,EAD8B;;EAE9BjZ,IAAAA,YAAY,CAACiC,EAAb,CAAgB9J,QAAhB,EAA0B8gB,aAA1B,EAAyCpZ,KAAK,IAAI;EAChD,UAAI1H,QAAQ,KAAK0H,KAAK,CAACU,MAAnB,IACFjI,OAAO,KAAKuH,KAAK,CAACU,MADhB,IAEF,CAACjI,OAAO,CAAC4D,QAAR,CAAiB2D,KAAK,CAACU,MAAvB,CAFH,EAEmC;EACjCjI,QAAAA,OAAO,CAACgf,KAAR;EACD;EACF,KAND;EAOAhf,IAAAA,OAAO,CAACgf,KAAR;EACD;;EAED9K,EAAAA,kBAAkB,GAAG;EACnBxM,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+BwV,qBAA/B,EAAoDU,uBAApD,EAA2E,MAAM,KAAK1H,IAAL,EAAjF;EAEAnS,IAAAA,YAAY,CAACiC,EAAb,CAAgB9J,QAAhB,EAA0B,SAA1B,EAAqC0H,KAAK,IAAI;EAC5C,UAAI,KAAKmM,OAAL,CAAajD,QAAb,IAAyBlJ,KAAK,CAAC3B,GAAN,KAAcwW,UAA3C,EAAuD;EACrD,aAAKvC,IAAL;EACD;EACF,KAJD;EAMAnS,IAAAA,YAAY,CAACiC,EAAb,CAAgB9J,QAAhB,EAA0BmM,sBAA1B,EAAgDzE,KAAK,IAAI;EACvD,YAAMU,MAAM,GAAGiH,cAAc,CAACK,OAAf,CAAuB/O,sBAAsB,CAAC+G,KAAK,CAACU,MAAP,CAA7C,CAAf;;EACA,UAAI,CAAC,KAAKoD,QAAL,CAAczH,QAAd,CAAuB2D,KAAK,CAACU,MAA7B,CAAD,IAAyCA,MAAM,KAAK,KAAKoD,QAA7D,EAAuE;EACrE,aAAKwO,IAAL;EACD;EACF,KALD;EAMD,GA3ImC;;;EA+Id,SAAfvU,eAAe,CAAClD,MAAD,EAAS;EAC7B,WAAO,KAAK0K,IAAL,CAAU,YAAY;EAC3B,YAAMC,IAAI,GAAGzB,IAAI,CAACtF,GAAL,CAAS,IAAT,EAAeuF,UAAf,KAA4B,IAAIia,SAAJ,CAAc,IAAd,EAAoB,OAAOpjB,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,EAA1D,CAAzC;;EAEA,UAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;EAC9B;EACD;;EAED,UAAI2K,IAAI,CAAC3K,MAAD,CAAJ,KAAiBjD,SAAjB,IAA8BiD,MAAM,CAAC/B,UAAP,CAAkB,GAAlB,CAA9B,IAAwD+B,MAAM,KAAK,aAAvE,EAAsF;EACpF,cAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;EACD;;EAED2K,MAAAA,IAAI,CAAC3K,MAAD,CAAJ,CAAa,IAAb;EACD,KAZM,CAAP;EAaD;;EA7JmC;EAgKtC;EACA;EACA;EACA;EACA;;;EAEAsF,YAAY,CAACiC,EAAb,CAAgB9J,QAAhB,EAA0BmM,sBAA1B,EAAgDmB,sBAAhD,EAAsE,UAAU5F,KAAV,EAAiB;EACrF,QAAMU,MAAM,GAAGvH,sBAAsB,CAAC,IAAD,CAArC;;EAEA,MAAI,CAAC,GAAD,EAAM,MAAN,EAAcN,QAAd,CAAuB,KAAK6V,OAA5B,CAAJ,EAA0C;EACxC1O,IAAAA,KAAK,CAAC0D,cAAN;EACD;;EAED,MAAIzH,UAAU,CAAC,IAAD,CAAd,EAAsB;EACpB;EACD;;EAEDkE,EAAAA,YAAY,CAACkC,GAAb,CAAiB3B,MAAjB,EAAyByQ,cAAzB,EAAuC,MAAM;EAC3C;EACA,QAAIzV,SAAS,CAAC,IAAD,CAAb,EAAqB;EACnB,WAAK+b,KAAL;EACD;EACF,GALD,EAXqF;;EAmBrF,QAAM+G,YAAY,GAAG7W,cAAc,CAACK,OAAf,CAAuBgW,eAAvB,CAArB;;EACA,MAAIQ,YAAY,IAAIA,YAAY,KAAK9d,MAArC,EAA6C;EAC3C;EACD;;EAED,QAAM8E,IAAI,GAAGzB,IAAI,CAACtF,GAAL,CAASiC,MAAT,EAAiBsD,UAAjB,KAA8B,IAAIia,SAAJ,CAAcvd,MAAd,CAA3C;EAEA8E,EAAAA,IAAI,CAACM,MAAL,CAAY,IAAZ;EACD,CA3BD;EA6BA3F,YAAY,CAACiC,EAAb,CAAgB7I,MAAhB,EAAwBgR,qBAAxB,EAA6C,MAAM;EACjD5C,EAAAA,cAAc,CAACC,IAAf,CAAoBmW,aAApB,EAAmC9iB,OAAnC,CAA2CwjB,EAAE,IAAI,CAAC1a,IAAI,CAACtF,GAAL,CAASggB,EAAT,EAAaza,UAAb,KAA0B,IAAIia,SAAJ,CAAcQ,EAAd,CAA3B,EAA8ClM,IAA9C,EAAjD;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;;EAEA9U,kBAAkB,CAAC0G,MAAD,EAAO8Z,SAAP,CAAlB;;ECpRA;EACA;EACA;EACA;EACA;EACA;EAEA,MAAMS,QAAQ,GAAG,IAAIhf,GAAJ,CAAQ,CACvB,YADuB,EAEvB,MAFuB,EAGvB,MAHuB,EAIvB,UAJuB,EAKvB,UALuB,EAMvB,QANuB,EAOvB,KAPuB,EAQvB,YARuB,CAAR,CAAjB;EAWA,MAAMif,sBAAsB,GAAG,gBAA/B;EAEA;EACA;EACA;EACA;EACA;;EACA,MAAMC,gBAAgB,GAAG,4DAAzB;EAEA;EACA;EACA;EACA;EACA;;EACA,MAAMC,gBAAgB,GAAG,oIAAzB;;EAEA,MAAMC,gBAAgB,GAAG,CAACC,IAAD,EAAOC,oBAAP,KAAgC;EACvD,QAAMC,QAAQ,GAAGF,IAAI,CAACG,QAAL,CAAclnB,WAAd,EAAjB;;EAEA,MAAIgnB,oBAAoB,CAACnmB,QAArB,CAA8BomB,QAA9B,CAAJ,EAA6C;EAC3C,QAAIP,QAAQ,CAACngB,GAAT,CAAa0gB,QAAb,CAAJ,EAA4B;EAC1B,aAAOld,OAAO,CAAC6c,gBAAgB,CAACrjB,IAAjB,CAAsBwjB,IAAI,CAACI,SAA3B,KAAyCN,gBAAgB,CAACtjB,IAAjB,CAAsBwjB,IAAI,CAACI,SAA3B,CAA1C,CAAd;EACD;;EAED,WAAO,IAAP;EACD;;EAED,QAAMC,MAAM,GAAGJ,oBAAoB,CAACpY,MAArB,CAA4ByY,SAAS,IAAIA,SAAS,YAAY/jB,MAA9D,CAAf,CAXuD;;EAcvD,OAAK,IAAIqF,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAGme,MAAM,CAACxe,MAA7B,EAAqCD,CAAC,GAAGM,GAAzC,EAA8CN,CAAC,EAA/C,EAAmD;EACjD,QAAIye,MAAM,CAACze,CAAD,CAAN,CAAUpF,IAAV,CAAe0jB,QAAf,CAAJ,EAA8B;EAC5B,aAAO,IAAP;EACD;EACF;;EAED,SAAO,KAAP;EACD,CArBD;;EAuBO,MAAMK,gBAAgB,GAAG;EAC9B;EACA,OAAK,CAAC,OAAD,EAAU,KAAV,EAAiB,IAAjB,EAAuB,MAAvB,EAA+B,MAA/B,EAAuCX,sBAAvC,CAFyB;EAG9BY,EAAAA,CAAC,EAAE,CAAC,QAAD,EAAW,MAAX,EAAmB,OAAnB,EAA4B,KAA5B,CAH2B;EAI9BC,EAAAA,IAAI,EAAE,EAJwB;EAK9BC,EAAAA,CAAC,EAAE,EAL2B;EAM9BC,EAAAA,EAAE,EAAE,EAN0B;EAO9BC,EAAAA,GAAG,EAAE,EAPyB;EAQ9BC,EAAAA,IAAI,EAAE,EARwB;EAS9BC,EAAAA,GAAG,EAAE,EATyB;EAU9BC,EAAAA,EAAE,EAAE,EAV0B;EAW9BC,EAAAA,EAAE,EAAE,EAX0B;EAY9BC,EAAAA,EAAE,EAAE,EAZ0B;EAa9BC,EAAAA,EAAE,EAAE,EAb0B;EAc9BC,EAAAA,EAAE,EAAE,EAd0B;EAe9BC,EAAAA,EAAE,EAAE,EAf0B;EAgB9BC,EAAAA,EAAE,EAAE,EAhB0B;EAiB9BC,EAAAA,EAAE,EAAE,EAjB0B;EAkB9B1f,EAAAA,CAAC,EAAE,EAlB2B;EAmB9B2f,EAAAA,GAAG,EAAE,CAAC,KAAD,EAAQ,QAAR,EAAkB,KAAlB,EAAyB,OAAzB,EAAkC,OAAlC,EAA2C,QAA3C,CAnByB;EAoB9BC,EAAAA,EAAE,EAAE,EApB0B;EAqB9BC,EAAAA,EAAE,EAAE,EArB0B;EAsB9BC,EAAAA,CAAC,EAAE,EAtB2B;EAuB9BC,EAAAA,GAAG,EAAE,EAvByB;EAwB9BC,EAAAA,CAAC,EAAE,EAxB2B;EAyB9BC,EAAAA,KAAK,EAAE,EAzBuB;EA0B9BC,EAAAA,IAAI,EAAE,EA1BwB;EA2B9BC,EAAAA,GAAG,EAAE,EA3ByB;EA4B9BC,EAAAA,GAAG,EAAE,EA5ByB;EA6B9BC,EAAAA,MAAM,EAAE,EA7BsB;EA8B9BC,EAAAA,CAAC,EAAE,EA9B2B;EA+B9BC,EAAAA,EAAE,EAAE;EA/B0B,CAAzB;EAkCA,SAASC,YAAT,CAAsBC,UAAtB,EAAkCC,SAAlC,EAA6CC,UAA7C,EAAyD;EAC9D,MAAI,CAACF,UAAU,CAACxgB,MAAhB,EAAwB;EACtB,WAAOwgB,UAAP;EACD;;EAED,MAAIE,UAAU,IAAI,OAAOA,UAAP,KAAsB,UAAxC,EAAoD;EAClD,WAAOA,UAAU,CAACF,UAAD,CAAjB;EACD;;EAED,QAAMG,SAAS,GAAG,IAAIhoB,MAAM,CAACioB,SAAX,EAAlB;EACA,QAAMC,eAAe,GAAGF,SAAS,CAACG,eAAV,CAA0BN,UAA1B,EAAsC,WAAtC,CAAxB;EACA,QAAMO,aAAa,GAAG5mB,MAAM,CAACC,IAAP,CAAYqmB,SAAZ,CAAtB;EACA,QAAMO,QAAQ,GAAG,GAAG/Z,MAAH,CAAU,GAAG4Z,eAAe,CAACtkB,IAAhB,CAAqBsD,gBAArB,CAAsC,GAAtC,CAAb,CAAjB;;EAEA,OAAK,IAAIE,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAG2gB,QAAQ,CAAChhB,MAA/B,EAAuCD,CAAC,GAAGM,GAA3C,EAAgDN,CAAC,EAAjD,EAAqD;EACnD,UAAM8d,EAAE,GAAGmD,QAAQ,CAACjhB,CAAD,CAAnB;EACA,UAAMkhB,MAAM,GAAGpD,EAAE,CAACS,QAAH,CAAYlnB,WAAZ,EAAf;;EAEA,QAAI,CAAC2pB,aAAa,CAAC9oB,QAAd,CAAuBgpB,MAAvB,CAAL,EAAqC;EACnCpD,MAAAA,EAAE,CAAC7iB,UAAH,CAAc0J,WAAd,CAA0BmZ,EAA1B;EAEA;EACD;;EAED,UAAMqD,aAAa,GAAG,GAAGja,MAAH,CAAU,GAAG4W,EAAE,CAAC/X,UAAhB,CAAtB;EACA,UAAMqb,iBAAiB,GAAG,GAAGla,MAAH,CAAUwZ,SAAS,CAAC,GAAD,CAAT,IAAkB,EAA5B,EAAgCA,SAAS,CAACQ,MAAD,CAAT,IAAqB,EAArD,CAA1B;EAEAC,IAAAA,aAAa,CAAC7mB,OAAd,CAAsB8jB,IAAI,IAAI;EAC5B,UAAI,CAACD,gBAAgB,CAACC,IAAD,EAAOgD,iBAAP,CAArB,EAAgD;EAC9CtD,QAAAA,EAAE,CAACjY,eAAH,CAAmBuY,IAAI,CAACG,QAAxB;EACD;EACF,KAJD;EAKD;;EAED,SAAOuC,eAAe,CAACtkB,IAAhB,CAAqB6kB,SAA5B;EACD;;EC9HD;EACA;EACA;EACA;EACA;EACA;EAyBA;EACA;EACA;EACA;EACA;;EAEA,MAAM7d,MAAI,GAAG,SAAb;EACA,MAAMH,UAAQ,GAAG,YAAjB;EACA,MAAMI,WAAS,GAAI,IAAGJ,UAAS,EAA/B;EACA,MAAMie,cAAY,GAAG,YAArB;EACA,MAAMC,oBAAkB,GAAG,IAAI5mB,MAAJ,CAAY,UAAS2mB,cAAa,MAAlC,EAAyC,GAAzC,CAA3B;EACA,MAAME,qBAAqB,GAAG,IAAIziB,GAAJ,CAAQ,CAAC,UAAD,EAAa,WAAb,EAA0B,YAA1B,CAAR,CAA9B;EAEA,MAAM6J,aAAW,GAAG;EAClB6Y,EAAAA,SAAS,EAAE,SADO;EAElBC,EAAAA,QAAQ,EAAE,QAFQ;EAGlBC,EAAAA,KAAK,EAAE,2BAHW;EAIlB3f,EAAAA,OAAO,EAAE,QAJS;EAKlB4f,EAAAA,KAAK,EAAE,iBALW;EAMlBC,EAAAA,IAAI,EAAE,SANY;EAOlB9pB,EAAAA,QAAQ,EAAE,kBAPQ;EAQlB2f,EAAAA,SAAS,EAAE,mBARO;EASlBrR,EAAAA,MAAM,EAAE,yBATU;EAUlB0L,EAAAA,SAAS,EAAE,0BAVO;EAWlB+P,EAAAA,kBAAkB,EAAE,OAXF;EAYlBpM,EAAAA,QAAQ,EAAE,kBAZQ;EAalBqM,EAAAA,WAAW,EAAE,mBAbK;EAclBC,EAAAA,QAAQ,EAAE,SAdQ;EAelBrB,EAAAA,UAAU,EAAE,iBAfM;EAgBlBD,EAAAA,SAAS,EAAE,QAhBO;EAiBlB9K,EAAAA,YAAY,EAAE;EAjBI,CAApB;EAoBA,MAAMqM,aAAa,GAAG;EACpBC,EAAAA,IAAI,EAAE,MADc;EAEpBC,EAAAA,GAAG,EAAE,KAFe;EAGpBC,EAAAA,KAAK,EAAExlB,KAAK,KAAK,MAAL,GAAc,OAHN;EAIpBylB,EAAAA,MAAM,EAAE,QAJY;EAKpBC,EAAAA,IAAI,EAAE1lB,KAAK,KAAK,OAAL,GAAe;EALN,CAAtB;EAQA,MAAMyL,SAAO,GAAG;EACdoZ,EAAAA,SAAS,EAAE,IADG;EAEdC,EAAAA,QAAQ,EAAE,yCACE,mCADF,GAEE,mCAFF,GAGA,QALI;EAMd1f,EAAAA,OAAO,EAAE,aANK;EAOd2f,EAAAA,KAAK,EAAE,EAPO;EAQdC,EAAAA,KAAK,EAAE,CARO;EASdC,EAAAA,IAAI,EAAE,KATQ;EAUd9pB,EAAAA,QAAQ,EAAE,KAVI;EAWd2f,EAAAA,SAAS,EAAE,KAXG;EAYdrR,EAAAA,MAAM,EAAE,CAAC,CAAD,EAAI,CAAJ,CAZM;EAad0L,EAAAA,SAAS,EAAE,KAbG;EAcd+P,EAAAA,kBAAkB,EAAE,CAAC,KAAD,EAAQ,OAAR,EAAiB,QAAjB,EAA2B,MAA3B,CAdN;EAedpM,EAAAA,QAAQ,EAAE,iBAfI;EAgBdqM,EAAAA,WAAW,EAAE,EAhBC;EAiBdC,EAAAA,QAAQ,EAAE,IAjBI;EAkBdrB,EAAAA,UAAU,EAAE,IAlBE;EAmBdD,EAAAA,SAAS,EAAE/B,gBAnBG;EAoBd/I,EAAAA,YAAY,EAAE;EApBA,CAAhB;EAuBA,MAAMxc,OAAK,GAAG;EACZmpB,EAAAA,IAAI,EAAG,OAAM9e,WAAU,EADX;EAEZ+e,EAAAA,MAAM,EAAG,SAAQ/e,WAAU,EAFf;EAGZgf,EAAAA,IAAI,EAAG,OAAMhf,WAAU,EAHX;EAIZif,EAAAA,KAAK,EAAG,QAAOjf,WAAU,EAJb;EAKZkf,EAAAA,QAAQ,EAAG,WAAUlf,WAAU,EALnB;EAMZmf,EAAAA,KAAK,EAAG,QAAOnf,WAAU,EANb;EAOZof,EAAAA,OAAO,EAAG,UAASpf,WAAU,EAPjB;EAQZqf,EAAAA,QAAQ,EAAG,WAAUrf,WAAU,EARnB;EASZsf,EAAAA,UAAU,EAAG,aAAYtf,WAAU,EATvB;EAUZuf,EAAAA,UAAU,EAAG,aAAYvf,WAAU;EAVvB,CAAd;EAaA,MAAMO,iBAAe,GAAG,MAAxB;EACA,MAAMif,gBAAgB,GAAG,OAAzB;EACA,MAAMhf,iBAAe,GAAG,MAAxB;EAEA,MAAMif,gBAAgB,GAAG,MAAzB;EACA,MAAMC,eAAe,GAAG,KAAxB;EAEA,MAAMC,sBAAsB,GAAG,gBAA/B;EAEA,MAAMC,aAAa,GAAG,OAAtB;EACA,MAAMC,aAAa,GAAG,OAAtB;EACA,MAAMC,aAAa,GAAG,OAAtB;EACA,MAAMC,cAAc,GAAG,QAAvB;EAEA;EACA;EACA;EACA;EACA;;EAEA,MAAMC,OAAN,SAAsBxgB,aAAtB,CAAoC;EAClCC,EAAAA,WAAW,CAACpL,OAAD,EAAUoC,MAAV,EAAkB;EAC3B,QAAI,OAAOqc,MAAP,KAAkB,WAAtB,EAAmC;EACjC,YAAM,IAAI1b,SAAJ,CAAc,8DAAd,CAAN;EACD;;EAED,UAAM/C,OAAN,EAL2B;;EAQ3B,SAAK4rB,UAAL,GAAkB,IAAlB;EACA,SAAKC,QAAL,GAAgB,CAAhB;EACA,SAAKC,WAAL,GAAmB,EAAnB;EACA,SAAKC,cAAL,GAAsB,EAAtB;EACA,SAAK/N,OAAL,GAAe,IAAf,CAZ2B;;EAe3B,SAAK5b,MAAL,GAAc,KAAKuR,UAAL,CAAgBvR,MAAhB,CAAd;EACA,SAAK4pB,GAAL,GAAW,IAAX;;EAEA,SAAKC,aAAL;EACD,GApBiC;;;EAwBhB,aAAP1b,OAAO,GAAG;EACnB,WAAOA,SAAP;EACD;;EAEc,aAAJ7E,IAAI,GAAG;EAChB,WAAOA,MAAP;EACD;;EAEkB,aAARH,QAAQ,GAAG;EACpB,WAAOA,UAAP;EACD;;EAEe,aAALjK,KAAK,GAAG;EACjB,WAAOA,OAAP;EACD;;EAEmB,aAATqK,SAAS,GAAG;EACrB,WAAOA,WAAP;EACD;;EAEqB,aAAXmF,WAAW,GAAG;EACvB,WAAOA,aAAP;EACD,GA9CiC;;;EAkDlCob,EAAAA,MAAM,GAAG;EACP,SAAKN,UAAL,GAAkB,IAAlB;EACD;;EAEDO,EAAAA,OAAO,GAAG;EACR,SAAKP,UAAL,GAAkB,KAAlB;EACD;;EAEDQ,EAAAA,aAAa,GAAG;EACd,SAAKR,UAAL,GAAkB,CAAC,KAAKA,UAAxB;EACD;;EAEDve,EAAAA,MAAM,CAAC9F,KAAD,EAAQ;EACZ,QAAI,CAAC,KAAKqkB,UAAV,EAAsB;EACpB;EACD;;EAED,QAAIrkB,KAAJ,EAAW;EACT,YAAMyY,OAAO,GAAG,KAAKqM,4BAAL,CAAkC9kB,KAAlC,CAAhB;;EAEAyY,MAAAA,OAAO,CAAC+L,cAAR,CAAuBxL,KAAvB,GAA+B,CAACP,OAAO,CAAC+L,cAAR,CAAuBxL,KAAvD;;EAEA,UAAIP,OAAO,CAACsM,oBAAR,EAAJ,EAAoC;EAClCtM,QAAAA,OAAO,CAACuM,MAAR,CAAe,IAAf,EAAqBvM,OAArB;EACD,OAFD,MAEO;EACLA,QAAAA,OAAO,CAACwM,MAAR,CAAe,IAAf,EAAqBxM,OAArB;EACD;EACF,KAVD,MAUO;EACL,UAAI,KAAKyM,aAAL,GAAqB9oB,SAArB,CAA+BC,QAA/B,CAAwCuI,iBAAxC,CAAJ,EAA8D;EAC5D,aAAKqgB,MAAL,CAAY,IAAZ,EAAkB,IAAlB;;EACA;EACD;;EAED,WAAKD,MAAL,CAAY,IAAZ,EAAkB,IAAlB;EACD;EACF;;EAED/gB,EAAAA,OAAO,GAAG;EACRqK,IAAAA,YAAY,CAAC,KAAKgW,QAAN,CAAZ;EAEAnkB,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK0D,QAAtB,EAAgC,KAAKD,WAAL,CAAiBO,SAAjD;EACAjE,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK0D,QAAL,CAAcsB,OAAd,CAAuB,IAAGwe,gBAAiB,EAA3C,CAAjB,EAAgE,eAAhE,EAAiF,KAAKuB,iBAAtF;;EAEA,QAAI,KAAKV,GAAL,IAAY,KAAKA,GAAL,CAAS7oB,UAAzB,EAAqC;EACnC,WAAK6oB,GAAL,CAAS7oB,UAAT,CAAoB0J,WAApB,CAAgC,KAAKmf,GAArC;EACD;;EAED,SAAKJ,UAAL,GAAkB,IAAlB;EACA,SAAKC,QAAL,GAAgB,IAAhB;EACA,SAAKC,WAAL,GAAmB,IAAnB;EACA,SAAKC,cAAL,GAAsB,IAAtB;;EACA,QAAI,KAAK/N,OAAT,EAAkB;EAChB,WAAKA,OAAL,CAAakB,OAAb;EACD;;EAED,SAAKlB,OAAL,GAAe,IAAf;EACA,SAAK5b,MAAL,GAAc,IAAd;EACA,SAAK4pB,GAAL,GAAW,IAAX;EACA,UAAMxgB,OAAN;EACD;;EAEDsO,EAAAA,IAAI,GAAG;EACL,QAAI,KAAKzO,QAAL,CAAcnI,KAAd,CAAoBI,OAApB,KAAgC,MAApC,EAA4C;EAC1C,YAAM,IAAIqpB,KAAJ,CAAU,qCAAV,CAAN;EACD;;EAED,QAAI,EAAE,KAAKC,aAAL,MAAwB,KAAKhB,UAA/B,CAAJ,EAAgD;EAC9C;EACD;;EAED,UAAMpN,SAAS,GAAG9W,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoC,KAAKD,WAAL,CAAiB9J,KAAjB,CAAuBqpB,IAA3D,CAAlB;EACA,UAAMkC,UAAU,GAAG9oB,cAAc,CAAC,KAAKsH,QAAN,CAAjC;EACA,UAAMyhB,UAAU,GAAGD,UAAU,KAAK,IAAf,GACjB,KAAKxhB,QAAL,CAAc0hB,aAAd,CAA4B/oB,eAA5B,CAA4CJ,QAA5C,CAAqD,KAAKyH,QAA1D,CADiB,GAEjBwhB,UAAU,CAACjpB,QAAX,CAAoB,KAAKyH,QAAzB,CAFF;;EAIA,QAAImT,SAAS,CAACjU,gBAAV,IAA8B,CAACuiB,UAAnC,EAA+C;EAC7C;EACD;;EAED,UAAMd,GAAG,GAAG,KAAKS,aAAL,EAAZ;EACA,UAAMO,KAAK,GAAGxtB,MAAM,CAAC,KAAK4L,WAAL,CAAiBM,IAAlB,CAApB;EAEAsgB,IAAAA,GAAG,CAAC1e,YAAJ,CAAiB,IAAjB,EAAuB0f,KAAvB;;EACA,SAAK3hB,QAAL,CAAciC,YAAd,CAA2B,kBAA3B,EAA+C0f,KAA/C;;EAEA,SAAKC,UAAL;;EAEA,QAAI,KAAK7qB,MAAL,CAAYunB,SAAhB,EAA2B;EACzBqC,MAAAA,GAAG,CAACroB,SAAJ,CAAcqS,GAAd,CAAkB9J,iBAAlB;EACD;;EAED,UAAM0T,SAAS,GAAG,OAAO,KAAKxd,MAAL,CAAYwd,SAAnB,KAAiC,UAAjC,GAChB,KAAKxd,MAAL,CAAYwd,SAAZ,CAAsBvgB,IAAtB,CAA2B,IAA3B,EAAiC2sB,GAAjC,EAAsC,KAAK3gB,QAA3C,CADgB,GAEhB,KAAKjJ,MAAL,CAAYwd,SAFd;;EAIA,UAAMsN,UAAU,GAAG,KAAKC,cAAL,CAAoBvN,SAApB,CAAnB;;EACA,SAAKwN,mBAAL,CAAyBF,UAAzB;;EAEA,UAAMjT,SAAS,GAAG,KAAKoT,aAAL,EAAlB;;EACA/hB,IAAAA,IAAI,CAAC3F,GAAL,CAASqmB,GAAT,EAAc,KAAK5gB,WAAL,CAAiBG,QAA/B,EAAyC,IAAzC;;EAEA,QAAI,CAAC,KAAKF,QAAL,CAAc0hB,aAAd,CAA4B/oB,eAA5B,CAA4CJ,QAA5C,CAAqD,KAAKooB,GAA1D,CAAL,EAAqE;EACnE/R,MAAAA,SAAS,CAAC6I,WAAV,CAAsBkJ,GAAtB;EACAtkB,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoC,KAAKD,WAAL,CAAiB9J,KAAjB,CAAuBupB,QAA3D;EACD;;EAED,QAAI,KAAK7M,OAAT,EAAkB;EAChB,WAAKA,OAAL,CAAamB,MAAb;EACD,KAFD,MAEO;EACL,WAAKnB,OAAL,GAAeS,YAAA,CAAoB,KAAKpT,QAAzB,EAAmC2gB,GAAnC,EAAwC,KAAKrN,gBAAL,CAAsBuO,UAAtB,CAAxC,CAAf;EACD;;EAEDlB,IAAAA,GAAG,CAACroB,SAAJ,CAAcqS,GAAd,CAAkB7J,iBAAlB;EAEA,UAAM8d,WAAW,GAAG,OAAO,KAAK7nB,MAAL,CAAY6nB,WAAnB,KAAmC,UAAnC,GAAgD,KAAK7nB,MAAL,CAAY6nB,WAAZ,EAAhD,GAA4E,KAAK7nB,MAAL,CAAY6nB,WAA5G;;EACA,QAAIA,WAAJ,EAAiB;EACf+B,MAAAA,GAAG,CAACroB,SAAJ,CAAcqS,GAAd,CAAkB,GAAGiU,WAAW,CAAC3pB,KAAZ,CAAkB,GAAlB,CAArB;EACD,KAzDI;EA4DL;EACA;EACA;;;EACA,QAAI,kBAAkBT,QAAQ,CAACmE,eAA/B,EAAgD;EAC9C,SAAGoL,MAAH,CAAU,GAAGvP,QAAQ,CAAC6E,IAAT,CAAc8K,QAA3B,EAAqChN,OAArC,CAA6CxC,OAAO,IAAI;EACtD0H,QAAAA,YAAY,CAACiC,EAAb,CAAgB3J,OAAhB,EAAyB,WAAzB,EAAsCqE,IAAI,EAA1C;EACD,OAFD;EAGD;;EAED,UAAMoW,QAAQ,GAAG,MAAM;EACrB,YAAM6S,cAAc,GAAG,KAAKxB,WAA5B;EAEA,WAAKA,WAAL,GAAmB,IAAnB;EACApkB,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoC,KAAKD,WAAL,CAAiB9J,KAAjB,CAAuBspB,KAA3D;;EAEA,UAAI0C,cAAc,KAAKjC,eAAvB,EAAwC;EACtC,aAAKmB,MAAL,CAAY,IAAZ,EAAkB,IAAlB;EACD;EACF,KATD;;EAWA,QAAI,KAAKR,GAAL,CAASroB,SAAT,CAAmBC,QAAnB,CAA4BsI,iBAA5B,CAAJ,EAAkD;EAChD,YAAMtL,kBAAkB,GAAGD,gCAAgC,CAAC,KAAKqrB,GAAN,CAA3D;EACAtkB,MAAAA,YAAY,CAACkC,GAAb,CAAiB,KAAKoiB,GAAtB,EAA2B,eAA3B,EAA4CvR,QAA5C;EACAhZ,MAAAA,oBAAoB,CAAC,KAAKuqB,GAAN,EAAWprB,kBAAX,CAApB;EACD,KAJD,MAIO;EACL6Z,MAAAA,QAAQ;EACT;EACF;;EAEDZ,EAAAA,IAAI,GAAG;EACL,QAAI,CAAC,KAAKmE,OAAV,EAAmB;EACjB;EACD;;EAED,UAAMgO,GAAG,GAAG,KAAKS,aAAL,EAAZ;;EACA,UAAMhS,QAAQ,GAAG,MAAM;EACrB,UAAI,KAAK6R,oBAAL,EAAJ,EAAiC;EAC/B;EACD;;EAED,UAAI,KAAKR,WAAL,KAAqBV,gBAArB,IAAyCY,GAAG,CAAC7oB,UAAjD,EAA6D;EAC3D6oB,QAAAA,GAAG,CAAC7oB,UAAJ,CAAe0J,WAAf,CAA2Bmf,GAA3B;EACD;;EAED,WAAKuB,cAAL;;EACA,WAAKliB,QAAL,CAAc0C,eAAd,CAA8B,kBAA9B;;EACArG,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoC,KAAKD,WAAL,CAAiB9J,KAAjB,CAAuBopB,MAA3D;;EAEA,UAAI,KAAK1M,OAAT,EAAkB;EAChB,aAAKA,OAAL,CAAakB,OAAb;;EACA,aAAKlB,OAAL,GAAe,IAAf;EACD;EACF,KAjBD;;EAmBA,UAAMiB,SAAS,GAAGvX,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoC,KAAKD,WAAL,CAAiB9J,KAAjB,CAAuBmpB,IAA3D,CAAlB;;EACA,QAAIxL,SAAS,CAAC1U,gBAAd,EAAgC;EAC9B;EACD;;EAEDyhB,IAAAA,GAAG,CAACroB,SAAJ,CAAc2C,MAAd,CAAqB6F,iBAArB,EA9BK;EAiCL;;EACA,QAAI,kBAAkBtM,QAAQ,CAACmE,eAA/B,EAAgD;EAC9C,SAAGoL,MAAH,CAAU,GAAGvP,QAAQ,CAAC6E,IAAT,CAAc8K,QAA3B,EACGhN,OADH,CACWxC,OAAO,IAAI0H,YAAY,CAACC,GAAb,CAAiB3H,OAAjB,EAA0B,WAA1B,EAAuCqE,IAAvC,CADtB;EAED;;EAED,SAAK0nB,cAAL,CAAoBN,aAApB,IAAqC,KAArC;EACA,SAAKM,cAAL,CAAoBP,aAApB,IAAqC,KAArC;EACA,SAAKO,cAAL,CAAoBR,aAApB,IAAqC,KAArC;;EAEA,QAAI,KAAKS,GAAL,CAASroB,SAAT,CAAmBC,QAAnB,CAA4BsI,iBAA5B,CAAJ,EAAkD;EAChD,YAAMtL,kBAAkB,GAAGD,gCAAgC,CAACqrB,GAAD,CAA3D;EAEAtkB,MAAAA,YAAY,CAACkC,GAAb,CAAiBoiB,GAAjB,EAAsB,eAAtB,EAAuCvR,QAAvC;EACAhZ,MAAAA,oBAAoB,CAACuqB,GAAD,EAAMprB,kBAAN,CAApB;EACD,KALD,MAKO;EACL6Z,MAAAA,QAAQ;EACT;;EAED,SAAKqR,WAAL,GAAmB,EAAnB;EACD;;EAED3M,EAAAA,MAAM,GAAG;EACP,QAAI,KAAKnB,OAAL,KAAiB,IAArB,EAA2B;EACzB,WAAKA,OAAL,CAAamB,MAAb;EACD;EACF,GAnQiC;;;EAuQlCyN,EAAAA,aAAa,GAAG;EACd,WAAOtjB,OAAO,CAAC,KAAKkkB,QAAL,EAAD,CAAd;EACD;;EAEDf,EAAAA,aAAa,GAAG;EACd,QAAI,KAAKT,GAAT,EAAc;EACZ,aAAO,KAAKA,GAAZ;EACD;;EAED,UAAMhsB,OAAO,GAAGH,QAAQ,CAACwjB,aAAT,CAAuB,KAAvB,CAAhB;EACArjB,IAAAA,OAAO,CAACupB,SAAR,GAAoB,KAAKnnB,MAAL,CAAYwnB,QAAhC;EAEA,SAAKoC,GAAL,GAAWhsB,OAAO,CAACwP,QAAR,CAAiB,CAAjB,CAAX;EACA,WAAO,KAAKwc,GAAZ;EACD;;EAEDiB,EAAAA,UAAU,GAAG;EACX,UAAMjB,GAAG,GAAG,KAAKS,aAAL,EAAZ;EACA,SAAKgB,iBAAL,CAAuBve,cAAc,CAACK,OAAf,CAAuB+b,sBAAvB,EAA+CU,GAA/C,CAAvB,EAA4E,KAAKwB,QAAL,EAA5E;EACAxB,IAAAA,GAAG,CAACroB,SAAJ,CAAc2C,MAAd,CAAqB4F,iBAArB,EAAsCC,iBAAtC;EACD;;EAEDshB,EAAAA,iBAAiB,CAACztB,OAAD,EAAU0tB,OAAV,EAAmB;EAClC,QAAI1tB,OAAO,KAAK,IAAhB,EAAsB;EACpB;EACD;;EAED,QAAI,OAAO0tB,OAAP,KAAmB,QAAnB,IAA+BnsB,WAAS,CAACmsB,OAAD,CAA5C,EAAuD;EACrD,UAAIA,OAAO,CAAC5S,MAAZ,EAAoB;EAClB4S,QAAAA,OAAO,GAAGA,OAAO,CAAC,CAAD,CAAjB;EACD,OAHoD;;;EAMrD,UAAI,KAAKtrB,MAAL,CAAY2nB,IAAhB,EAAsB;EACpB,YAAI2D,OAAO,CAACvqB,UAAR,KAAuBnD,OAA3B,EAAoC;EAClCA,UAAAA,OAAO,CAACupB,SAAR,GAAoB,EAApB;EACAvpB,UAAAA,OAAO,CAAC8iB,WAAR,CAAoB4K,OAApB;EACD;EACF,OALD,MAKO;EACL1tB,QAAAA,OAAO,CAAC2tB,WAAR,GAAsBD,OAAO,CAACC,WAA9B;EACD;;EAED;EACD;;EAED,QAAI,KAAKvrB,MAAL,CAAY2nB,IAAhB,EAAsB;EACpB,UAAI,KAAK3nB,MAAL,CAAY8nB,QAAhB,EAA0B;EACxBwD,QAAAA,OAAO,GAAGhF,YAAY,CAACgF,OAAD,EAAU,KAAKtrB,MAAL,CAAYwmB,SAAtB,EAAiC,KAAKxmB,MAAL,CAAYymB,UAA7C,CAAtB;EACD;;EAED7oB,MAAAA,OAAO,CAACupB,SAAR,GAAoBmE,OAApB;EACD,KAND,MAMO;EACL1tB,MAAAA,OAAO,CAAC2tB,WAAR,GAAsBD,OAAtB;EACD;EACF;;EAEDF,EAAAA,QAAQ,GAAG;EACT,QAAI3D,KAAK,GAAG,KAAKxe,QAAL,CAAcnL,YAAd,CAA2B,wBAA3B,CAAZ;;EAEA,QAAI,CAAC2pB,KAAL,EAAY;EACVA,MAAAA,KAAK,GAAG,OAAO,KAAKznB,MAAL,CAAYynB,KAAnB,KAA6B,UAA7B,GACN,KAAKznB,MAAL,CAAYynB,KAAZ,CAAkBxqB,IAAlB,CAAuB,KAAKgM,QAA5B,CADM,GAEN,KAAKjJ,MAAL,CAAYynB,KAFd;EAGD;;EAED,WAAOA,KAAP;EACD;;EAED+D,EAAAA,gBAAgB,CAACV,UAAD,EAAa;EAC3B,QAAIA,UAAU,KAAK,OAAnB,EAA4B;EAC1B,aAAO,KAAP;EACD;;EAED,QAAIA,UAAU,KAAK,MAAnB,EAA2B;EACzB,aAAO,OAAP;EACD;;EAED,WAAOA,UAAP;EACD,GArViC;;;EAyVlCb,EAAAA,4BAA4B,CAAC9kB,KAAD,EAAQyY,OAAR,EAAiB;EAC3C,UAAM6N,OAAO,GAAG,KAAKziB,WAAL,CAAiBG,QAAjC;EACAyU,IAAAA,OAAO,GAAGA,OAAO,IAAI1U,IAAI,CAACtF,GAAL,CAASuB,KAAK,CAACC,cAAf,EAA+BqmB,OAA/B,CAArB;;EAEA,QAAI,CAAC7N,OAAL,EAAc;EACZA,MAAAA,OAAO,GAAG,IAAI,KAAK5U,WAAT,CAAqB7D,KAAK,CAACC,cAA3B,EAA2C,KAAKsmB,kBAAL,EAA3C,CAAV;EACAxiB,MAAAA,IAAI,CAAC3F,GAAL,CAAS4B,KAAK,CAACC,cAAf,EAA+BqmB,OAA/B,EAAwC7N,OAAxC;EACD;;EAED,WAAOA,OAAP;EACD;;EAEDR,EAAAA,UAAU,GAAG;EACX,UAAM;EAAEjR,MAAAA;EAAF,QAAa,KAAKnM,MAAxB;;EAEA,QAAI,OAAOmM,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,aAAOA,MAAM,CAACjO,KAAP,CAAa,GAAb,EAAkBmf,GAAlB,CAAsBhS,GAAG,IAAIxM,MAAM,CAACkW,QAAP,CAAgB1J,GAAhB,EAAqB,EAArB,CAA7B,CAAP;EACD;;EAED,QAAI,OAAOc,MAAP,KAAkB,UAAtB,EAAkC;EAChC,aAAOmR,UAAU,IAAInR,MAAM,CAACmR,UAAD,EAAa,KAAKrU,QAAlB,CAA3B;EACD;;EAED,WAAOkD,MAAP;EACD;;EAEDoQ,EAAAA,gBAAgB,CAACuO,UAAD,EAAa;EAC3B,UAAMvN,qBAAqB,GAAG;EAC5BC,MAAAA,SAAS,EAAEsN,UADiB;EAE5BrO,MAAAA,SAAS,EAAE,CACT;EACE5Z,QAAAA,IAAI,EAAE,MADR;EAEE4a,QAAAA,OAAO,EAAE;EACPkO,UAAAA,WAAW,EAAE,IADN;EAEP/D,UAAAA,kBAAkB,EAAE,KAAK5nB,MAAL,CAAY4nB;EAFzB;EAFX,OADS,EAQT;EACE/kB,QAAAA,IAAI,EAAE,QADR;EAEE4a,QAAAA,OAAO,EAAE;EACPtR,UAAAA,MAAM,EAAE,KAAKiR,UAAL;EADD;EAFX,OARS,EAcT;EACEva,QAAAA,IAAI,EAAE,iBADR;EAEE4a,QAAAA,OAAO,EAAE;EACPjC,UAAAA,QAAQ,EAAE,KAAKxb,MAAL,CAAYwb;EADf;EAFX,OAdS,EAoBT;EACE3Y,QAAAA,IAAI,EAAE,OADR;EAEE4a,QAAAA,OAAO,EAAE;EACP7f,UAAAA,OAAO,EAAG,IAAG,KAAKoL,WAAL,CAAiBM,IAAK;EAD5B;EAFX,OApBS,EA0BT;EACEzG,QAAAA,IAAI,EAAE,UADR;EAEE8Z,QAAAA,OAAO,EAAE,IAFX;EAGEiP,QAAAA,KAAK,EAAE,YAHT;EAIE3oB,QAAAA,EAAE,EAAE0H,IAAI,IAAI,KAAKkhB,4BAAL,CAAkClhB,IAAlC;EAJd,OA1BS,CAFiB;EAmC5BmhB,MAAAA,aAAa,EAAEnhB,IAAI,IAAI;EACrB,YAAIA,IAAI,CAAC8S,OAAL,CAAaD,SAAb,KAA2B7S,IAAI,CAAC6S,SAApC,EAA+C;EAC7C,eAAKqO,4BAAL,CAAkClhB,IAAlC;EACD;EACF;EAvC2B,KAA9B;EA0CA,WAAO,EACL,GAAG4S,qBADE;EAEL,UAAI,OAAO,KAAKvd,MAAL,CAAY0b,YAAnB,KAAoC,UAApC,GAAiD,KAAK1b,MAAL,CAAY0b,YAAZ,CAAyB6B,qBAAzB,CAAjD,GAAmG,KAAKvd,MAAL,CAAY0b,YAAnH;EAFK,KAAP;EAID;;EAEDsP,EAAAA,mBAAmB,CAACF,UAAD,EAAa;EAC9B,SAAKT,aAAL,GAAqB9oB,SAArB,CAA+BqS,GAA/B,CAAoC,GAAEwT,cAAa,IAAG,KAAKoE,gBAAL,CAAsBV,UAAtB,CAAkC,EAAxF;EACD;;EAEDG,EAAAA,aAAa,GAAG;EACd,QAAI,KAAKjrB,MAAL,CAAY6X,SAAZ,KAA0B,KAA9B,EAAqC;EACnC,aAAOpa,QAAQ,CAAC6E,IAAhB;EACD;;EAED,QAAInD,WAAS,CAAC,KAAKa,MAAL,CAAY6X,SAAb,CAAb,EAAsC;EACpC,aAAO,KAAK7X,MAAL,CAAY6X,SAAnB;EACD;;EAED,WAAO/K,cAAc,CAACK,OAAf,CAAuB,KAAKnN,MAAL,CAAY6X,SAAnC,CAAP;EACD;;EAEDkT,EAAAA,cAAc,CAACvN,SAAD,EAAY;EACxB,WAAOuK,aAAa,CAACvK,SAAS,CAAC5c,WAAV,EAAD,CAApB;EACD;;EAEDipB,EAAAA,aAAa,GAAG;EACd,UAAMkC,QAAQ,GAAG,KAAK/rB,MAAL,CAAY8H,OAAZ,CAAoB5J,KAApB,CAA0B,GAA1B,CAAjB;EAEA6tB,IAAAA,QAAQ,CAAC3rB,OAAT,CAAiB0H,OAAO,IAAI;EAC1B,UAAIA,OAAO,KAAK,OAAhB,EAAyB;EACvBxC,QAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+B,KAAKD,WAAL,CAAiB9J,KAAjB,CAAuBwpB,KAAtD,EAA6D,KAAK1oB,MAAL,CAAYnC,QAAzE,EAAmFsH,KAAK,IAAI,KAAK8F,MAAL,CAAY9F,KAAZ,CAA5F;EACD,OAFD,MAEO,IAAI2C,OAAO,KAAKwhB,cAAhB,EAAgC;EACrC,cAAM0C,OAAO,GAAGlkB,OAAO,KAAKqhB,aAAZ,GACd,KAAKngB,WAAL,CAAiB9J,KAAjB,CAAuB2pB,UADT,GAEd,KAAK7f,WAAL,CAAiB9J,KAAjB,CAAuBypB,OAFzB;EAGA,cAAMsD,QAAQ,GAAGnkB,OAAO,KAAKqhB,aAAZ,GACf,KAAKngB,WAAL,CAAiB9J,KAAjB,CAAuB4pB,UADR,GAEf,KAAK9f,WAAL,CAAiB9J,KAAjB,CAAuB0pB,QAFzB;EAIAtjB,QAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+B+iB,OAA/B,EAAwC,KAAKhsB,MAAL,CAAYnC,QAApD,EAA8DsH,KAAK,IAAI,KAAKglB,MAAL,CAAYhlB,KAAZ,CAAvE;EACAG,QAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+BgjB,QAA/B,EAAyC,KAAKjsB,MAAL,CAAYnC,QAArD,EAA+DsH,KAAK,IAAI,KAAKilB,MAAL,CAAYjlB,KAAZ,CAAxE;EACD;EACF,KAdD;;EAgBA,SAAKmlB,iBAAL,GAAyB,MAAM;EAC7B,UAAI,KAAKrhB,QAAT,EAAmB;EACjB,aAAKwO,IAAL;EACD;EACF,KAJD;;EAMAnS,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAAL,CAAcsB,OAAd,CAAuB,IAAGwe,gBAAiB,EAA3C,CAAhB,EAA+D,eAA/D,EAAgF,KAAKuB,iBAArF;;EAEA,QAAI,KAAKtqB,MAAL,CAAYnC,QAAhB,EAA0B;EACxB,WAAKmC,MAAL,GAAc,EACZ,GAAG,KAAKA,MADI;EAEZ8H,QAAAA,OAAO,EAAE,QAFG;EAGZjK,QAAAA,QAAQ,EAAE;EAHE,OAAd;EAKD,KAND,MAMO;EACL,WAAKquB,SAAL;EACD;EACF;;EAEDA,EAAAA,SAAS,GAAG;EACV,UAAMzE,KAAK,GAAG,KAAKxe,QAAL,CAAcnL,YAAd,CAA2B,OAA3B,CAAd;;EACA,UAAMquB,iBAAiB,GAAG,OAAO,KAAKljB,QAAL,CAAcnL,YAAd,CAA2B,wBAA3B,CAAjC;;EAEA,QAAI2pB,KAAK,IAAI0E,iBAAiB,KAAK,QAAnC,EAA6C;EAC3C,WAAKljB,QAAL,CAAciC,YAAd,CAA2B,wBAA3B,EAAqDuc,KAAK,IAAI,EAA9D;;EACA,UAAIA,KAAK,IAAI,CAAC,KAAKxe,QAAL,CAAcnL,YAAd,CAA2B,YAA3B,CAAV,IAAsD,CAAC,KAAKmL,QAAL,CAAcsiB,WAAzE,EAAsF;EACpF,aAAKtiB,QAAL,CAAciC,YAAd,CAA2B,YAA3B,EAAyCuc,KAAzC;EACD;;EAED,WAAKxe,QAAL,CAAciC,YAAd,CAA2B,OAA3B,EAAoC,EAApC;EACD;EACF;;EAEDif,EAAAA,MAAM,CAAChlB,KAAD,EAAQyY,OAAR,EAAiB;EACrBA,IAAAA,OAAO,GAAG,KAAKqM,4BAAL,CAAkC9kB,KAAlC,EAAyCyY,OAAzC,CAAV;;EAEA,QAAIzY,KAAJ,EAAW;EACTyY,MAAAA,OAAO,CAAC+L,cAAR,CACExkB,KAAK,CAACK,IAAN,KAAe,SAAf,GAA2B4jB,aAA3B,GAA2CD,aAD7C,IAEI,IAFJ;EAGD;;EAED,QAAIvL,OAAO,CAACyM,aAAR,GAAwB9oB,SAAxB,CAAkCC,QAAlC,CAA2CuI,iBAA3C,KAA+D6T,OAAO,CAAC8L,WAAR,KAAwBV,gBAA3F,EAA6G;EAC3GpL,MAAAA,OAAO,CAAC8L,WAAR,GAAsBV,gBAAtB;EACA;EACD;;EAEDvV,IAAAA,YAAY,CAACmK,OAAO,CAAC6L,QAAT,CAAZ;EAEA7L,IAAAA,OAAO,CAAC8L,WAAR,GAAsBV,gBAAtB;;EAEA,QAAI,CAACpL,OAAO,CAAC5d,MAAR,CAAe0nB,KAAhB,IAAyB,CAAC9J,OAAO,CAAC5d,MAAR,CAAe0nB,KAAf,CAAqBhQ,IAAnD,EAAyD;EACvDkG,MAAAA,OAAO,CAAClG,IAAR;EACA;EACD;;EAEDkG,IAAAA,OAAO,CAAC6L,QAAR,GAAmB5pB,UAAU,CAAC,MAAM;EAClC,UAAI+d,OAAO,CAAC8L,WAAR,KAAwBV,gBAA5B,EAA8C;EAC5CpL,QAAAA,OAAO,CAAClG,IAAR;EACD;EACF,KAJ4B,EAI1BkG,OAAO,CAAC5d,MAAR,CAAe0nB,KAAf,CAAqBhQ,IAJK,CAA7B;EAKD;;EAED0S,EAAAA,MAAM,CAACjlB,KAAD,EAAQyY,OAAR,EAAiB;EACrBA,IAAAA,OAAO,GAAG,KAAKqM,4BAAL,CAAkC9kB,KAAlC,EAAyCyY,OAAzC,CAAV;;EAEA,QAAIzY,KAAJ,EAAW;EACTyY,MAAAA,OAAO,CAAC+L,cAAR,CACExkB,KAAK,CAACK,IAAN,KAAe,UAAf,GAA4B4jB,aAA5B,GAA4CD,aAD9C,IAEIvL,OAAO,CAAC3U,QAAR,CAAiBzH,QAAjB,CAA0B2D,KAAK,CAACqP,aAAhC,CAFJ;EAGD;;EAED,QAAIoJ,OAAO,CAACsM,oBAAR,EAAJ,EAAoC;EAClC;EACD;;EAEDzW,IAAAA,YAAY,CAACmK,OAAO,CAAC6L,QAAT,CAAZ;EAEA7L,IAAAA,OAAO,CAAC8L,WAAR,GAAsBT,eAAtB;;EAEA,QAAI,CAACrL,OAAO,CAAC5d,MAAR,CAAe0nB,KAAhB,IAAyB,CAAC9J,OAAO,CAAC5d,MAAR,CAAe0nB,KAAf,CAAqBjQ,IAAnD,EAAyD;EACvDmG,MAAAA,OAAO,CAACnG,IAAR;EACA;EACD;;EAEDmG,IAAAA,OAAO,CAAC6L,QAAR,GAAmB5pB,UAAU,CAAC,MAAM;EAClC,UAAI+d,OAAO,CAAC8L,WAAR,KAAwBT,eAA5B,EAA6C;EAC3CrL,QAAAA,OAAO,CAACnG,IAAR;EACD;EACF,KAJ4B,EAI1BmG,OAAO,CAAC5d,MAAR,CAAe0nB,KAAf,CAAqBjQ,IAJK,CAA7B;EAKD;;EAEDyS,EAAAA,oBAAoB,GAAG;EACrB,SAAK,MAAMpiB,OAAX,IAAsB,KAAK6hB,cAA3B,EAA2C;EACzC,UAAI,KAAKA,cAAL,CAAoB7hB,OAApB,CAAJ,EAAkC;EAChC,eAAO,IAAP;EACD;EACF;;EAED,WAAO,KAAP;EACD;;EAEDyJ,EAAAA,UAAU,CAACvR,MAAD,EAAS;EACjB,UAAMosB,cAAc,GAAG5gB,WAAW,CAACI,iBAAZ,CAA8B,KAAK3C,QAAnC,CAAvB;EAEA/I,IAAAA,MAAM,CAACC,IAAP,CAAYisB,cAAZ,EAA4BhsB,OAA5B,CAAoCisB,QAAQ,IAAI;EAC9C,UAAI/E,qBAAqB,CAAC5jB,GAAtB,CAA0B2oB,QAA1B,CAAJ,EAAyC;EACvC,eAAOD,cAAc,CAACC,QAAD,CAArB;EACD;EACF,KAJD;;EAMA,QAAIrsB,MAAM,IAAI,OAAOA,MAAM,CAAC6X,SAAd,KAA4B,QAAtC,IAAkD7X,MAAM,CAAC6X,SAAP,CAAiBa,MAAvE,EAA+E;EAC7E1Y,MAAAA,MAAM,CAAC6X,SAAP,GAAmB7X,MAAM,CAAC6X,SAAP,CAAiB,CAAjB,CAAnB;EACD;;EAED7X,IAAAA,MAAM,GAAG,EACP,GAAG,KAAKgJ,WAAL,CAAiBmF,OADb;EAEP,SAAGie,cAFI;EAGP,UAAI,OAAOpsB,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAApD;EAHO,KAAT;;EAMA,QAAI,OAAOA,MAAM,CAAC0nB,KAAd,KAAwB,QAA5B,EAAsC;EACpC1nB,MAAAA,MAAM,CAAC0nB,KAAP,GAAe;EACbhQ,QAAAA,IAAI,EAAE1X,MAAM,CAAC0nB,KADA;EAEbjQ,QAAAA,IAAI,EAAEzX,MAAM,CAAC0nB;EAFA,OAAf;EAID;;EAED,QAAI,OAAO1nB,MAAM,CAACynB,KAAd,KAAwB,QAA5B,EAAsC;EACpCznB,MAAAA,MAAM,CAACynB,KAAP,GAAeznB,MAAM,CAACynB,KAAP,CAAazqB,QAAb,EAAf;EACD;;EAED,QAAI,OAAOgD,MAAM,CAACsrB,OAAd,KAA0B,QAA9B,EAAwC;EACtCtrB,MAAAA,MAAM,CAACsrB,OAAP,GAAiBtrB,MAAM,CAACsrB,OAAP,CAAetuB,QAAf,EAAjB;EACD;;EAED8C,IAAAA,eAAe,CAACwJ,MAAD,EAAOtJ,MAAP,EAAe,KAAKgJ,WAAL,CAAiB0F,WAAhC,CAAf;;EAEA,QAAI1O,MAAM,CAAC8nB,QAAX,EAAqB;EACnB9nB,MAAAA,MAAM,CAACwnB,QAAP,GAAkBlB,YAAY,CAACtmB,MAAM,CAACwnB,QAAR,EAAkBxnB,MAAM,CAACwmB,SAAzB,EAAoCxmB,MAAM,CAACymB,UAA3C,CAA9B;EACD;;EAED,WAAOzmB,MAAP;EACD;;EAED0rB,EAAAA,kBAAkB,GAAG;EACnB,UAAM1rB,MAAM,GAAG,EAAf;;EAEA,QAAI,KAAKA,MAAT,EAAiB;EACf,WAAK,MAAMwD,GAAX,IAAkB,KAAKxD,MAAvB,EAA+B;EAC7B,YAAI,KAAKgJ,WAAL,CAAiBmF,OAAjB,CAAyB3K,GAAzB,MAAkC,KAAKxD,MAAL,CAAYwD,GAAZ,CAAtC,EAAwD;EACtDxD,UAAAA,MAAM,CAACwD,GAAD,CAAN,GAAc,KAAKxD,MAAL,CAAYwD,GAAZ,CAAd;EACD;EACF;EACF;;EAED,WAAOxD,MAAP;EACD;;EAEDmrB,EAAAA,cAAc,GAAG;EACf,UAAMvB,GAAG,GAAG,KAAKS,aAAL,EAAZ;EACA,UAAMiC,QAAQ,GAAG1C,GAAG,CAAC9rB,YAAJ,CAAiB,OAAjB,EAA0BZ,KAA1B,CAAgCmqB,oBAAhC,CAAjB;;EACA,QAAIiF,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,CAACvmB,MAAT,GAAkB,CAA3C,EAA8C;EAC5CumB,MAAAA,QAAQ,CAACjP,GAAT,CAAakP,KAAK,IAAIA,KAAK,CAACpuB,IAAN,EAAtB,EACGiC,OADH,CACWosB,MAAM,IAAI5C,GAAG,CAACroB,SAAJ,CAAc2C,MAAd,CAAqBsoB,MAArB,CADrB;EAED;EACF;;EAEDX,EAAAA,4BAA4B,CAACvO,UAAD,EAAa;EACvC,UAAM;EAAEmP,MAAAA;EAAF,QAAYnP,UAAlB;;EAEA,QAAI,CAACmP,KAAL,EAAY;EACV;EACD;;EAED,SAAK7C,GAAL,GAAW6C,KAAK,CAAC1F,QAAN,CAAe2F,MAA1B;;EACA,SAAKvB,cAAL;;EACA,SAAKH,mBAAL,CAAyB,KAAKD,cAAL,CAAoB0B,KAAK,CAACjP,SAA1B,CAAzB;EACD,GA7nBiC;;;EAioBZ,SAAfta,eAAe,CAAClD,MAAD,EAAS;EAC7B,WAAO,KAAK0K,IAAL,CAAU,YAAY;EAC3B,UAAIC,IAAI,GAAGzB,IAAI,CAACtF,GAAL,CAAS,IAAT,EAAeuF,UAAf,CAAX;;EACA,YAAMmI,OAAO,GAAG,OAAOtR,MAAP,KAAkB,QAAlB,IAA8BA,MAA9C;;EAEA,UAAI,CAAC2K,IAAD,IAAS,eAAejK,IAAf,CAAoBV,MAApB,CAAb,EAA0C;EACxC;EACD;;EAED,UAAI,CAAC2K,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAI4e,OAAJ,CAAY,IAAZ,EAAkBjY,OAAlB,CAAP;EACD;;EAED,UAAI,OAAOtR,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAO2K,IAAI,CAAC3K,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;EACD;;EAED2K,QAAAA,IAAI,CAAC3K,MAAD,CAAJ;EACD;EACF,KAnBM,CAAP;EAoBD;;EAtpBiC;EAypBpC;EACA;EACA;EACA;EACA;EACA;;;EAEA4C,kBAAkB,CAAC0G,MAAD,EAAOigB,OAAP,CAAlB;;EC/xBA;EACA;EACA;EACA;EACA;EACA;EAOA;EACA;EACA;EACA;EACA;;EAEA,MAAMjgB,MAAI,GAAG,SAAb;EACA,MAAMH,UAAQ,GAAG,YAAjB;EACA,MAAMI,WAAS,GAAI,IAAGJ,UAAS,EAA/B;EACA,MAAMie,YAAY,GAAG,YAArB;EACA,MAAMC,kBAAkB,GAAG,IAAI5mB,MAAJ,CAAY,UAAS2mB,YAAa,MAAlC,EAAyC,GAAzC,CAA3B;EAEA,MAAMjZ,SAAO,GAAG,EACd,GAAGob,OAAO,CAACpb,OADG;EAEdqP,EAAAA,SAAS,EAAE,OAFG;EAGdrR,EAAAA,MAAM,EAAE,CAAC,CAAD,EAAI,CAAJ,CAHM;EAIdrE,EAAAA,OAAO,EAAE,OAJK;EAKdwjB,EAAAA,OAAO,EAAE,EALK;EAMd9D,EAAAA,QAAQ,EAAE,yCACE,mCADF,GAEI,kCAFJ,GAGE,kCAHF,GAIA;EAVI,CAAhB;EAaA,MAAM9Y,aAAW,GAAG,EAClB,GAAG6a,OAAO,CAAC7a,WADO;EAElB4c,EAAAA,OAAO,EAAE;EAFS,CAApB;EAKA,MAAMpsB,OAAK,GAAG;EACZmpB,EAAAA,IAAI,EAAG,OAAM9e,WAAU,EADX;EAEZ+e,EAAAA,MAAM,EAAG,SAAQ/e,WAAU,EAFf;EAGZgf,EAAAA,IAAI,EAAG,OAAMhf,WAAU,EAHX;EAIZif,EAAAA,KAAK,EAAG,QAAOjf,WAAU,EAJb;EAKZkf,EAAAA,QAAQ,EAAG,WAAUlf,WAAU,EALnB;EAMZmf,EAAAA,KAAK,EAAG,QAAOnf,WAAU,EANb;EAOZof,EAAAA,OAAO,EAAG,UAASpf,WAAU,EAPjB;EAQZqf,EAAAA,QAAQ,EAAG,WAAUrf,WAAU,EARnB;EASZsf,EAAAA,UAAU,EAAG,aAAYtf,WAAU,EATvB;EAUZuf,EAAAA,UAAU,EAAG,aAAYvf,WAAU;EAVvB,CAAd;EAaA,MAAMO,iBAAe,GAAG,MAAxB;EACA,MAAMC,iBAAe,GAAG,MAAxB;EAEA,MAAM4iB,cAAc,GAAG,iBAAvB;EACA,MAAMC,gBAAgB,GAAG,eAAzB;EAEA;EACA;EACA;EACA;EACA;;EAEA,MAAMC,OAAN,SAAsBtD,OAAtB,CAA8B;EAC5B;EAEkB,aAAPpb,OAAO,GAAG;EACnB,WAAOA,SAAP;EACD;;EAEc,aAAJ7E,IAAI,GAAG;EAChB,WAAOA,MAAP;EACD;;EAEkB,aAARH,QAAQ,GAAG;EACpB,WAAOA,UAAP;EACD;;EAEe,aAALjK,KAAK,GAAG;EACjB,WAAOA,OAAP;EACD;;EAEmB,aAATqK,SAAS,GAAG;EACrB,WAAOA,WAAP;EACD;;EAEqB,aAAXmF,WAAW,GAAG;EACvB,WAAOA,aAAP;EACD,GAzB2B;;;EA6B5B8b,EAAAA,aAAa,GAAG;EACd,WAAO,KAAKY,QAAL,MAAmB,KAAK0B,WAAL,EAA1B;EACD;;EAEDjC,EAAAA,UAAU,GAAG;EACX,UAAMjB,GAAG,GAAG,KAAKS,aAAL,EAAZ,CADW;;EAIX,SAAKgB,iBAAL,CAAuBve,cAAc,CAACK,OAAf,CAAuBwf,cAAvB,EAAuC/C,GAAvC,CAAvB,EAAoE,KAAKwB,QAAL,EAApE;;EACA,QAAIE,OAAO,GAAG,KAAKwB,WAAL,EAAd;;EACA,QAAI,OAAOxB,OAAP,KAAmB,UAAvB,EAAmC;EACjCA,MAAAA,OAAO,GAAGA,OAAO,CAACruB,IAAR,CAAa,KAAKgM,QAAlB,CAAV;EACD;;EAED,SAAKoiB,iBAAL,CAAuBve,cAAc,CAACK,OAAf,CAAuByf,gBAAvB,EAAyChD,GAAzC,CAAvB,EAAsE0B,OAAtE;EAEA1B,IAAAA,GAAG,CAACroB,SAAJ,CAAc2C,MAAd,CAAqB4F,iBAArB,EAAsCC,iBAAtC;EACD,GA9C2B;;;EAkD5BihB,EAAAA,mBAAmB,CAACF,UAAD,EAAa;EAC9B,SAAKT,aAAL,GAAqB9oB,SAArB,CAA+BqS,GAA/B,CAAoC,GAAEwT,YAAa,IAAG,KAAKoE,gBAAL,CAAsBV,UAAtB,CAAkC,EAAxF;EACD;;EAEDgC,EAAAA,WAAW,GAAG;EACZ,WAAO,KAAK7jB,QAAL,CAAcnL,YAAd,CAA2B,iBAA3B,KAAiD,KAAKkC,MAAL,CAAYsrB,OAApE;EACD;;EAEDH,EAAAA,cAAc,GAAG;EACf,UAAMvB,GAAG,GAAG,KAAKS,aAAL,EAAZ;EACA,UAAMiC,QAAQ,GAAG1C,GAAG,CAAC9rB,YAAJ,CAAiB,OAAjB,EAA0BZ,KAA1B,CAAgCmqB,kBAAhC,CAAjB;;EACA,QAAIiF,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,CAACvmB,MAAT,GAAkB,CAA3C,EAA8C;EAC5CumB,MAAAA,QAAQ,CAACjP,GAAT,CAAakP,KAAK,IAAIA,KAAK,CAACpuB,IAAN,EAAtB,EACGiC,OADH,CACWosB,MAAM,IAAI5C,GAAG,CAACroB,SAAJ,CAAc2C,MAAd,CAAqBsoB,MAArB,CADrB;EAED;EACF,GAjE2B;;;EAqEN,SAAftpB,eAAe,CAAClD,MAAD,EAAS;EAC7B,WAAO,KAAK0K,IAAL,CAAU,YAAY;EAC3B,UAAIC,IAAI,GAAGzB,IAAI,CAACtF,GAAL,CAAS,IAAT,EAAeuF,UAAf,CAAX;;EACA,YAAMmI,OAAO,GAAG,OAAOtR,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,IAAtD;;EAEA,UAAI,CAAC2K,IAAD,IAAS,eAAejK,IAAf,CAAoBV,MAApB,CAAb,EAA0C;EACxC;EACD;;EAED,UAAI,CAAC2K,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAIkiB,OAAJ,CAAY,IAAZ,EAAkBvb,OAAlB,CAAP;EACApI,QAAAA,IAAI,CAAC3F,GAAL,CAAS,IAAT,EAAe4F,UAAf,EAAyBwB,IAAzB;EACD;;EAED,UAAI,OAAO3K,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAO2K,IAAI,CAAC3K,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;EACD;;EAED2K,QAAAA,IAAI,CAAC3K,MAAD,CAAJ;EACD;EACF,KApBM,CAAP;EAqBD;;EA3F2B;EA8F9B;EACA;EACA;EACA;EACA;EACA;;;EAEA4C,kBAAkB,CAAC0G,MAAD,EAAOujB,OAAP,CAAlB;;ECxKA;EACA;EACA;EACA;EACA;EACA;EAeA;EACA;EACA;EACA;EACA;;EAEA,MAAMvjB,MAAI,GAAG,WAAb;EACA,MAAMH,UAAQ,GAAG,cAAjB;EACA,MAAMI,WAAS,GAAI,IAAGJ,UAAS,EAA/B;EACA,MAAMK,cAAY,GAAG,WAArB;EAEA,MAAM2E,SAAO,GAAG;EACdhC,EAAAA,MAAM,EAAE,EADM;EAEd4gB,EAAAA,MAAM,EAAE,MAFM;EAGdlnB,EAAAA,MAAM,EAAE;EAHM,CAAhB;EAMA,MAAM6I,aAAW,GAAG;EAClBvC,EAAAA,MAAM,EAAE,QADU;EAElB4gB,EAAAA,MAAM,EAAE,QAFU;EAGlBlnB,EAAAA,MAAM,EAAE;EAHU,CAApB;EAMA,MAAMmnB,cAAc,GAAI,WAAUzjB,WAAU,EAA5C;EACA,MAAM0jB,YAAY,GAAI,SAAQ1jB,WAAU,EAAxC;EACA,MAAMmG,mBAAmB,GAAI,OAAMnG,WAAU,GAAEC,cAAa,EAA5D;EAEA,MAAM0jB,wBAAwB,GAAG,eAAjC;EACA,MAAMpiB,mBAAiB,GAAG,QAA1B;EAEA,MAAMqiB,iBAAiB,GAAG,wBAA1B;EACA,MAAMC,yBAAuB,GAAG,mBAAhC;EACA,MAAMC,kBAAkB,GAAG,WAA3B;EACA,MAAMC,kBAAkB,GAAG,WAA3B;EACA,MAAMC,mBAAmB,GAAG,kBAA5B;EACA,MAAMC,mBAAiB,GAAG,WAA1B;EACA,MAAMC,0BAAwB,GAAG,kBAAjC;EAEA,MAAMC,aAAa,GAAG,QAAtB;EACA,MAAMC,eAAe,GAAG,UAAxB;EAEA;EACA;EACA;EACA;EACA;;EAEA,MAAMC,SAAN,SAAwB7kB,aAAxB,CAAsC;EACpCC,EAAAA,WAAW,CAACpL,OAAD,EAAUoC,MAAV,EAAkB;EAC3B,UAAMpC,OAAN;EACA,SAAKiwB,cAAL,GAAsB,KAAK5kB,QAAL,CAAc4K,OAAd,KAA0B,MAA1B,GAAmCnV,MAAnC,GAA4C,KAAKuK,QAAvE;EACA,SAAKqI,OAAL,GAAe,KAAKC,UAAL,CAAgBvR,MAAhB,CAAf;EACA,SAAKqX,SAAL,GAAkB,GAAE,KAAK/F,OAAL,CAAazL,MAAO,IAAGwnB,kBAAmB,KAAI,KAAK/b,OAAL,CAAazL,MAAO,IAAG0nB,mBAAoB,KAAI,KAAKjc,OAAL,CAAazL,MAAO,KAAIqnB,wBAAyB,EAAlK;EACA,SAAKY,QAAL,GAAgB,EAAhB;EACA,SAAKC,QAAL,GAAgB,EAAhB;EACA,SAAKC,aAAL,GAAqB,IAArB;EACA,SAAKC,aAAL,GAAqB,CAArB;EAEA3oB,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAKsmB,cAArB,EAAqCZ,YAArC,EAAmD,MAAM,KAAKiB,QAAL,EAAzD;EAEA,SAAKC,OAAL;;EACA,SAAKD,QAAL;EACD,GAfmC;;;EAmBlB,aAAP/f,OAAO,GAAG;EACnB,WAAOA,SAAP;EACD;;EAEkB,aAARhF,QAAQ,GAAG;EACpB,WAAOA,UAAP;EACD,GAzBmC;;;EA6BpCglB,EAAAA,OAAO,GAAG;EACR,UAAMC,UAAU,GAAG,KAAKP,cAAL,KAAwB,KAAKA,cAAL,CAAoBnvB,MAA5C,GACjBgvB,aADiB,GAEjBC,eAFF;EAIA,UAAMU,YAAY,GAAG,KAAK/c,OAAL,CAAayb,MAAb,KAAwB,MAAxB,GACnBqB,UADmB,GAEnB,KAAK9c,OAAL,CAAayb,MAFf;EAIA,UAAMuB,UAAU,GAAGD,YAAY,KAAKV,eAAjB,GACjB,KAAKY,aAAL,EADiB,GAEjB,CAFF;EAIA,SAAKT,QAAL,GAAgB,EAAhB;EACA,SAAKC,QAAL,GAAgB,EAAhB;EACA,SAAKE,aAAL,GAAqB,KAAKO,gBAAL,EAArB;EAEA,UAAMC,OAAO,GAAG3hB,cAAc,CAACC,IAAf,CAAoB,KAAKsK,SAAzB,CAAhB;EAEAoX,IAAAA,OAAO,CAACpR,GAAR,CAAYzf,OAAO,IAAI;EACrB,YAAM8wB,cAAc,GAAGtwB,sBAAsB,CAACR,OAAD,CAA7C;EACA,YAAMiI,MAAM,GAAG6oB,cAAc,GAAG5hB,cAAc,CAACK,OAAf,CAAuBuhB,cAAvB,CAAH,GAA4C,IAAzE;;EAEA,UAAI7oB,MAAJ,EAAY;EACV,cAAM8oB,SAAS,GAAG9oB,MAAM,CAACwG,qBAAP,EAAlB;;EACA,YAAIsiB,SAAS,CAAClM,KAAV,IAAmBkM,SAAS,CAACC,MAAjC,EAAyC;EACvC,iBAAO,CACLpjB,WAAW,CAAC6iB,YAAD,CAAX,CAA0BxoB,MAA1B,EAAkCyG,GAAlC,GAAwCgiB,UADnC,EAELI,cAFK,CAAP;EAID;EACF;;EAED,aAAO,IAAP;EACD,KAfD,EAgBG3iB,MAhBH,CAgBU8iB,IAAI,IAAIA,IAhBlB,EAiBGC,IAjBH,CAiBQ,CAACpK,CAAD,EAAIE,CAAJ,KAAUF,CAAC,CAAC,CAAD,CAAD,GAAOE,CAAC,CAAC,CAAD,CAjB1B,EAkBGxkB,OAlBH,CAkBWyuB,IAAI,IAAI;EACf,WAAKf,QAAL,CAAcrgB,IAAd,CAAmBohB,IAAI,CAAC,CAAD,CAAvB;;EACA,WAAKd,QAAL,CAActgB,IAAd,CAAmBohB,IAAI,CAAC,CAAD,CAAvB;EACD,KArBH;EAsBD;;EAEDzlB,EAAAA,OAAO,GAAG;EACR,UAAMA,OAAN;EACA9D,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKsoB,cAAtB,EAAsCtkB,WAAtC;EAEA,SAAKskB,cAAL,GAAsB,IAAtB;EACA,SAAKvc,OAAL,GAAe,IAAf;EACA,SAAK+F,SAAL,GAAiB,IAAjB;EACA,SAAKyW,QAAL,GAAgB,IAAhB;EACA,SAAKC,QAAL,GAAgB,IAAhB;EACA,SAAKC,aAAL,GAAqB,IAArB;EACA,SAAKC,aAAL,GAAqB,IAArB;EACD,GAnFmC;;;EAuFpC1c,EAAAA,UAAU,CAACvR,MAAD,EAAS;EACjBA,IAAAA,MAAM,GAAG,EACP,GAAGmO,SADI;EAEP,UAAI,OAAOnO,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAApD;EAFO,KAAT;;EAKA,QAAI,OAAOA,MAAM,CAAC6F,MAAd,KAAyB,QAAzB,IAAqC1G,WAAS,CAACa,MAAM,CAAC6F,MAAR,CAAlD,EAAmE;EACjE,UAAI;EAAEmR,QAAAA;EAAF,UAAShX,MAAM,CAAC6F,MAApB;;EACA,UAAI,CAACmR,EAAL,EAAS;EACPA,QAAAA,EAAE,GAAG5Z,MAAM,CAACkM,MAAD,CAAX;EACAtJ,QAAAA,MAAM,CAAC6F,MAAP,CAAcmR,EAAd,GAAmBA,EAAnB;EACD;;EAEDhX,MAAAA,MAAM,CAAC6F,MAAP,GAAiB,IAAGmR,EAAG,EAAvB;EACD;;EAEDlX,IAAAA,eAAe,CAACwJ,MAAD,EAAOtJ,MAAP,EAAe0O,aAAf,CAAf;EAEA,WAAO1O,MAAP;EACD;;EAEDuuB,EAAAA,aAAa,GAAG;EACd,WAAO,KAAKV,cAAL,KAAwBnvB,MAAxB,GACL,KAAKmvB,cAAL,CAAoBkB,WADf,GAEL,KAAKlB,cAAL,CAAoBthB,SAFtB;EAGD;;EAEDiiB,EAAAA,gBAAgB,GAAG;EACjB,WAAO,KAAKX,cAAL,CAAoBtM,YAApB,IAAoCjkB,IAAI,CAAC4b,GAAL,CACzCzb,QAAQ,CAAC6E,IAAT,CAAcif,YAD2B,EAEzC9jB,QAAQ,CAACmE,eAAT,CAAyB2f,YAFgB,CAA3C;EAID;;EAEDyN,EAAAA,gBAAgB,GAAG;EACjB,WAAO,KAAKnB,cAAL,KAAwBnvB,MAAxB,GACLA,MAAM,CAACuwB,WADF,GAEL,KAAKpB,cAAL,CAAoBxhB,qBAApB,GAA4CuiB,MAF9C;EAGD;;EAEDV,EAAAA,QAAQ,GAAG;EACT,UAAM3hB,SAAS,GAAG,KAAKgiB,aAAL,KAAuB,KAAKjd,OAAL,CAAanF,MAAtD;;EACA,UAAMoV,YAAY,GAAG,KAAKiN,gBAAL,EAArB;;EACA,UAAMU,SAAS,GAAG,KAAK5d,OAAL,CAAanF,MAAb,GAAsBoV,YAAtB,GAAqC,KAAKyN,gBAAL,EAAvD;;EAEA,QAAI,KAAKf,aAAL,KAAuB1M,YAA3B,EAAyC;EACvC,WAAK4M,OAAL;EACD;;EAED,QAAI5hB,SAAS,IAAI2iB,SAAjB,EAA4B;EAC1B,YAAMrpB,MAAM,GAAG,KAAKkoB,QAAL,CAAc,KAAKA,QAAL,CAAchoB,MAAd,GAAuB,CAArC,CAAf;;EAEA,UAAI,KAAKioB,aAAL,KAAuBnoB,MAA3B,EAAmC;EACjC,aAAKspB,SAAL,CAAetpB,MAAf;EACD;;EAED;EACD;;EAED,QAAI,KAAKmoB,aAAL,IAAsBzhB,SAAS,GAAG,KAAKuhB,QAAL,CAAc,CAAd,CAAlC,IAAsD,KAAKA,QAAL,CAAc,CAAd,IAAmB,CAA7E,EAAgF;EAC9E,WAAKE,aAAL,GAAqB,IAArB;;EACA,WAAKoB,MAAL;;EACA;EACD;;EAED,SAAK,IAAItpB,CAAC,GAAG,KAAKgoB,QAAL,CAAc/nB,MAA3B,EAAmCD,CAAC,EAApC,GAAyC;EACvC,YAAMupB,cAAc,GAAG,KAAKrB,aAAL,KAAuB,KAAKD,QAAL,CAAcjoB,CAAd,CAAvB,IACnByG,SAAS,IAAI,KAAKuhB,QAAL,CAAchoB,CAAd,CADM,KAElB,OAAO,KAAKgoB,QAAL,CAAchoB,CAAC,GAAG,CAAlB,CAAP,KAAgC,WAAhC,IAA+CyG,SAAS,GAAG,KAAKuhB,QAAL,CAAchoB,CAAC,GAAG,CAAlB,CAFzC,CAAvB;;EAIA,UAAIupB,cAAJ,EAAoB;EAClB,aAAKF,SAAL,CAAe,KAAKpB,QAAL,CAAcjoB,CAAd,CAAf;EACD;EACF;EACF;;EAEDqpB,EAAAA,SAAS,CAACtpB,MAAD,EAAS;EAChB,SAAKmoB,aAAL,GAAqBnoB,MAArB;;EAEA,SAAKupB,MAAL;;EAEA,UAAME,OAAO,GAAG,KAAKjY,SAAL,CAAenZ,KAAf,CAAqB,GAArB,EACbmf,GADa,CACTxf,QAAQ,IAAK,GAAEA,QAAS,oBAAmBgI,MAAO,MAAKhI,QAAS,UAASgI,MAAO,IADvE,CAAhB;;EAGA,UAAM0pB,IAAI,GAAGziB,cAAc,CAACK,OAAf,CAAuBmiB,OAAO,CAACE,IAAR,CAAa,GAAb,CAAvB,CAAb;;EAEA,QAAID,IAAI,CAAChuB,SAAL,CAAeC,QAAf,CAAwB0rB,wBAAxB,CAAJ,EAAuD;EACrDpgB,MAAAA,cAAc,CAACK,OAAf,CAAuBsgB,0BAAvB,EAAiD8B,IAAI,CAAChlB,OAAL,CAAaijB,mBAAb,CAAjD,EACGjsB,SADH,CACaqS,GADb,CACiB9I,mBADjB;EAGAykB,MAAAA,IAAI,CAAChuB,SAAL,CAAeqS,GAAf,CAAmB9I,mBAAnB;EACD,KALD,MAKO;EACL;EACAykB,MAAAA,IAAI,CAAChuB,SAAL,CAAeqS,GAAf,CAAmB9I,mBAAnB;EAEAgC,MAAAA,cAAc,CAACS,OAAf,CAAuBgiB,IAAvB,EAA6BnC,yBAA7B,EACGhtB,OADH,CACWqvB,SAAS,IAAI;EACpB;EACA;EACA3iB,QAAAA,cAAc,CAACY,IAAf,CAAoB+hB,SAApB,EAAgC,GAAEpC,kBAAmB,KAAIE,mBAAoB,EAA7E,EACGntB,OADH,CACWyuB,IAAI,IAAIA,IAAI,CAACttB,SAAL,CAAeqS,GAAf,CAAmB9I,mBAAnB,CADnB,EAHoB;;EAOpBgC,QAAAA,cAAc,CAACY,IAAf,CAAoB+hB,SAApB,EAA+BnC,kBAA/B,EACGltB,OADH,CACWsvB,OAAO,IAAI;EAClB5iB,UAAAA,cAAc,CAACM,QAAf,CAAwBsiB,OAAxB,EAAiCrC,kBAAjC,EACGjtB,OADH,CACWyuB,IAAI,IAAIA,IAAI,CAACttB,SAAL,CAAeqS,GAAf,CAAmB9I,mBAAnB,CADnB;EAED,SAJH;EAKD,OAbH;EAcD;;EAEDxF,IAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAK+lB,cAA1B,EAA0Cb,cAA1C,EAA0D;EACxDxY,MAAAA,aAAa,EAAE3O;EADyC,KAA1D;EAGD;;EAEDupB,EAAAA,MAAM,GAAG;EACPtiB,IAAAA,cAAc,CAACC,IAAf,CAAoB,KAAKsK,SAAzB,EACGtL,MADH,CACU4jB,IAAI,IAAIA,IAAI,CAACpuB,SAAL,CAAeC,QAAf,CAAwBsJ,mBAAxB,CADlB,EAEG1K,OAFH,CAEWuvB,IAAI,IAAIA,IAAI,CAACpuB,SAAL,CAAe2C,MAAf,CAAsB4G,mBAAtB,CAFnB;EAGD,GA/MmC;;;EAmNd,SAAf5H,eAAe,CAAClD,MAAD,EAAS;EAC7B,WAAO,KAAK0K,IAAL,CAAU,YAAY;EAC3B,UAAIC,IAAI,GAAGzB,IAAI,CAACtF,GAAL,CAAS,IAAT,EAAeuF,UAAf,CAAX;;EACA,YAAMmI,OAAO,GAAG,OAAOtR,MAAP,KAAkB,QAAlB,IAA8BA,MAA9C;;EAEA,UAAI,CAAC2K,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAIijB,SAAJ,CAAc,IAAd,EAAoBtc,OAApB,CAAP;EACD;;EAED,UAAI,OAAOtR,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAO2K,IAAI,CAAC3K,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;EACD;;EAED2K,QAAAA,IAAI,CAAC3K,MAAD,CAAJ;EACD;EACF,KAfM,CAAP;EAgBD;;EApOmC;EAuOtC;EACA;EACA;EACA;EACA;;;EAEAsF,YAAY,CAACiC,EAAb,CAAgB7I,MAAhB,EAAwBgR,mBAAxB,EAA6C,MAAM;EACjD5C,EAAAA,cAAc,CAACC,IAAf,CAAoBogB,iBAApB,EACG/sB,OADH,CACWwvB,GAAG,IAAI,IAAIhC,SAAJ,CAAcgC,GAAd,EAAmBpkB,WAAW,CAACI,iBAAZ,CAA8BgkB,GAA9B,CAAnB,CADlB;EAED,CAHD;EAKA;EACA;EACA;EACA;EACA;EACA;;EAEAhtB,kBAAkB,CAAC0G,MAAD,EAAOskB,SAAP,CAAlB;;EC5TA;EACA;EACA;EACA;EACA;EACA;EAeA;EACA;EACA;EACA;EACA;;EAEA,MAAMtkB,MAAI,GAAG,KAAb;EACA,MAAMH,UAAQ,GAAG,QAAjB;EACA,MAAMI,WAAS,GAAI,IAAGJ,UAAS,EAA/B;EACA,MAAMK,YAAY,GAAG,WAArB;EAEA,MAAM6M,YAAU,GAAI,OAAM9M,WAAU,EAApC;EACA,MAAM+M,cAAY,GAAI,SAAQ/M,WAAU,EAAxC;EACA,MAAM4M,YAAU,GAAI,OAAM5M,WAAU,EAApC;EACA,MAAM6M,aAAW,GAAI,QAAO7M,WAAU,EAAtC;EACA,MAAMK,oBAAoB,GAAI,QAAOL,WAAU,GAAEC,YAAa,EAA9D;EAEA,MAAMqmB,wBAAwB,GAAG,eAAjC;EACA,MAAM/kB,iBAAiB,GAAG,QAA1B;EACA,MAAMhB,iBAAe,GAAG,MAAxB;EACA,MAAMC,iBAAe,GAAG,MAAxB;EAEA,MAAMyjB,iBAAiB,GAAG,WAA1B;EACA,MAAMJ,uBAAuB,GAAG,mBAAhC;EACA,MAAMld,eAAe,GAAG,SAAxB;EACA,MAAM4f,kBAAkB,GAAG,uBAA3B;EACA,MAAM/kB,oBAAoB,GAAG,0EAA7B;EACA,MAAM0iB,wBAAwB,GAAG,kBAAjC;EACA,MAAMsC,8BAA8B,GAAG,iCAAvC;EAEA;EACA;EACA;EACA;EACA;;EAEA,MAAMC,GAAN,SAAkBjnB,aAAlB,CAAgC;EAC9B;EAEmB,aAARI,QAAQ,GAAG;EACpB,WAAOA,UAAP;EACD,GAL6B;;;EAS9BuO,EAAAA,IAAI,GAAG;EACL,QAAK,KAAKzO,QAAL,CAAclI,UAAd,IACH,KAAKkI,QAAL,CAAclI,UAAd,CAAyB3B,QAAzB,KAAsCiC,IAAI,CAACC,YADxC,IAEH,KAAK2H,QAAL,CAAc1H,SAAd,CAAwBC,QAAxB,CAAiCsJ,iBAAjC,CAFE,IAGF1J,UAAU,CAAC,KAAK6H,QAAN,CAHZ,EAG6B;EAC3B;EACD;;EAED,QAAI0E,QAAJ;EACA,UAAM9H,MAAM,GAAGvH,sBAAsB,CAAC,KAAK2K,QAAN,CAArC;;EACA,UAAMgnB,WAAW,GAAG,KAAKhnB,QAAL,CAAcsB,OAAd,CAAsB6iB,uBAAtB,CAApB;;EAEA,QAAI6C,WAAJ,EAAiB;EACf,YAAMC,YAAY,GAAGD,WAAW,CAAC5L,QAAZ,KAAyB,IAAzB,IAAiC4L,WAAW,CAAC5L,QAAZ,KAAyB,IAA1D,GAAiEyL,kBAAjE,GAAsF5f,eAA3G;EACAvC,MAAAA,QAAQ,GAAGb,cAAc,CAACC,IAAf,CAAoBmjB,YAApB,EAAkCD,WAAlC,CAAX;EACAtiB,MAAAA,QAAQ,GAAGA,QAAQ,CAACA,QAAQ,CAAC5H,MAAT,GAAkB,CAAnB,CAAnB;EACD;;EAED,UAAM8W,SAAS,GAAGlP,QAAQ,GACxBrI,YAAY,CAACwC,OAAb,CAAqB6F,QAArB,EAA+B0I,YAA/B,EAA2C;EACzC7B,MAAAA,aAAa,EAAE,KAAKvL;EADqB,KAA3C,CADwB,GAIxB,IAJF;EAMA,UAAMmT,SAAS,GAAG9W,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCkN,YAApC,EAAgD;EAChE3B,MAAAA,aAAa,EAAE7G;EADiD,KAAhD,CAAlB;;EAIA,QAAIyO,SAAS,CAACjU,gBAAV,IAA+B0U,SAAS,KAAK,IAAd,IAAsBA,SAAS,CAAC1U,gBAAnE,EAAsF;EACpF;EACD;;EAED,SAAKgnB,SAAL,CAAe,KAAKlmB,QAApB,EAA8BgnB,WAA9B;;EAEA,UAAM5X,QAAQ,GAAG,MAAM;EACrB/S,MAAAA,YAAY,CAACwC,OAAb,CAAqB6F,QAArB,EAA+B2I,cAA/B,EAA6C;EAC3C9B,QAAAA,aAAa,EAAE,KAAKvL;EADuB,OAA7C;EAGA3D,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCmN,aAApC,EAAiD;EAC/C5B,QAAAA,aAAa,EAAE7G;EADgC,OAAjD;EAGD,KAPD;;EASA,QAAI9H,MAAJ,EAAY;EACV,WAAKspB,SAAL,CAAetpB,MAAf,EAAuBA,MAAM,CAAC9E,UAA9B,EAA0CsX,QAA1C;EACD,KAFD,MAEO;EACLA,MAAAA,QAAQ;EACT;EACF,GAzD6B;;;EA6D9B8W,EAAAA,SAAS,CAACvxB,OAAD,EAAUia,SAAV,EAAqBrV,QAArB,EAA+B;EACtC,UAAM2tB,cAAc,GAAGtY,SAAS,KAAKA,SAAS,CAACwM,QAAV,KAAuB,IAAvB,IAA+BxM,SAAS,CAACwM,QAAV,KAAuB,IAA3D,CAAT,GACrBvX,cAAc,CAACC,IAAf,CAAoB+iB,kBAApB,EAAwCjY,SAAxC,CADqB,GAErB/K,cAAc,CAACM,QAAf,CAAwByK,SAAxB,EAAmC3H,eAAnC,CAFF;EAIA,UAAMkgB,MAAM,GAAGD,cAAc,CAAC,CAAD,CAA7B;EACA,UAAM1X,eAAe,GAAGjW,QAAQ,IAAK4tB,MAAM,IAAIA,MAAM,CAAC7uB,SAAP,CAAiBC,QAAjB,CAA0BsI,iBAA1B,CAA/C;;EAEA,UAAMuO,QAAQ,GAAG,MAAM,KAAKgY,mBAAL,CAAyBzyB,OAAzB,EAAkCwyB,MAAlC,EAA0C5tB,QAA1C,CAAvB;;EAEA,QAAI4tB,MAAM,IAAI3X,eAAd,EAA+B;EAC7B,YAAMja,kBAAkB,GAAGD,gCAAgC,CAAC6xB,MAAD,CAA3D;EACAA,MAAAA,MAAM,CAAC7uB,SAAP,CAAiB2C,MAAjB,CAAwB6F,iBAAxB;EAEAzE,MAAAA,YAAY,CAACkC,GAAb,CAAiB4oB,MAAjB,EAAyB,eAAzB,EAA0C/X,QAA1C;EACAhZ,MAAAA,oBAAoB,CAAC+wB,MAAD,EAAS5xB,kBAAT,CAApB;EACD,KAND,MAMO;EACL6Z,MAAAA,QAAQ;EACT;EACF;;EAEDgY,EAAAA,mBAAmB,CAACzyB,OAAD,EAAUwyB,MAAV,EAAkB5tB,QAAlB,EAA4B;EAC7C,QAAI4tB,MAAJ,EAAY;EACVA,MAAAA,MAAM,CAAC7uB,SAAP,CAAiB2C,MAAjB,CAAwB4G,iBAAxB;EAEA,YAAMwlB,aAAa,GAAGxjB,cAAc,CAACK,OAAf,CAAuB4iB,8BAAvB,EAAuDK,MAAM,CAACrvB,UAA9D,CAAtB;;EAEA,UAAIuvB,aAAJ,EAAmB;EACjBA,QAAAA,aAAa,CAAC/uB,SAAd,CAAwB2C,MAAxB,CAA+B4G,iBAA/B;EACD;;EAED,UAAIslB,MAAM,CAACtyB,YAAP,CAAoB,MAApB,MAAgC,KAApC,EAA2C;EACzCsyB,QAAAA,MAAM,CAACllB,YAAP,CAAoB,eAApB,EAAqC,KAArC;EACD;EACF;;EAEDtN,IAAAA,OAAO,CAAC2D,SAAR,CAAkBqS,GAAlB,CAAsB9I,iBAAtB;;EACA,QAAIlN,OAAO,CAACE,YAAR,CAAqB,MAArB,MAAiC,KAArC,EAA4C;EAC1CF,MAAAA,OAAO,CAACsN,YAAR,CAAqB,eAArB,EAAsC,IAAtC;EACD;;EAEDhJ,IAAAA,MAAM,CAACtE,OAAD,CAAN;;EAEA,QAAIA,OAAO,CAAC2D,SAAR,CAAkBC,QAAlB,CAA2BsI,iBAA3B,CAAJ,EAAiD;EAC/ClM,MAAAA,OAAO,CAAC2D,SAAR,CAAkBqS,GAAlB,CAAsB7J,iBAAtB;EACD;;EAED,QAAInM,OAAO,CAACmD,UAAR,IAAsBnD,OAAO,CAACmD,UAAR,CAAmBQ,SAAnB,CAA6BC,QAA7B,CAAsCquB,wBAAtC,CAA1B,EAA2F;EACzF,YAAMU,eAAe,GAAG3yB,OAAO,CAAC2M,OAAR,CAAgBijB,iBAAhB,CAAxB;;EAEA,UAAI+C,eAAJ,EAAqB;EACnBzjB,QAAAA,cAAc,CAACC,IAAf,CAAoB0gB,wBAApB,EACGrtB,OADH,CACWowB,QAAQ,IAAIA,QAAQ,CAACjvB,SAAT,CAAmBqS,GAAnB,CAAuB9I,iBAAvB,CADvB;EAED;;EAEDlN,MAAAA,OAAO,CAACsN,YAAR,CAAqB,eAArB,EAAsC,IAAtC;EACD;;EAED,QAAI1I,QAAJ,EAAc;EACZA,MAAAA,QAAQ;EACT;EACF,GA1H6B;;;EA8HR,SAAfU,eAAe,CAAClD,MAAD,EAAS;EAC7B,WAAO,KAAK0K,IAAL,CAAU,YAAY;EAC3B,YAAMC,IAAI,GAAGzB,IAAI,CAACtF,GAAL,CAAS,IAAT,EAAeuF,UAAf,KAA4B,IAAI6mB,GAAJ,CAAQ,IAAR,CAAzC;;EAEA,UAAI,OAAOhwB,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAO2K,IAAI,CAAC3K,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;EACD;;EAED2K,QAAAA,IAAI,CAAC3K,MAAD,CAAJ;EACD;EACF,KAVM,CAAP;EAWD;;EA1I6B;EA6IhC;EACA;EACA;EACA;EACA;;;EAEAsF,YAAY,CAACiC,EAAb,CAAgB9J,QAAhB,EAA0BmM,oBAA1B,EAAgDmB,oBAAhD,EAAsE,UAAU5F,KAAV,EAAiB;EACrFA,EAAAA,KAAK,CAAC0D,cAAN;EAEA,QAAM8B,IAAI,GAAGzB,IAAI,CAACtF,GAAL,CAAS,IAAT,EAAeuF,UAAf,KAA4B,IAAI6mB,GAAJ,CAAQ,IAAR,CAAzC;EACArlB,EAAAA,IAAI,CAAC+M,IAAL;EACD,CALD;EAOA;EACA;EACA;EACA;EACA;EACA;;EAEA9U,kBAAkB,CAAC0G,MAAD,EAAO0mB,GAAP,CAAlB;;ECzNA;EACA;EACA;EACA;EACA;EACA;EAcA;EACA;EACA;EACA;EACA;;EAEA,MAAM1mB,IAAI,GAAG,OAAb;EACA,MAAMH,QAAQ,GAAG,UAAjB;EACA,MAAMI,SAAS,GAAI,IAAGJ,QAAS,EAA/B;EAEA,MAAMsV,mBAAmB,GAAI,gBAAelV,SAAU,EAAtD;EACA,MAAM8M,UAAU,GAAI,OAAM9M,SAAU,EAApC;EACA,MAAM+M,YAAY,GAAI,SAAQ/M,SAAU,EAAxC;EACA,MAAM4M,UAAU,GAAI,OAAM5M,SAAU,EAApC;EACA,MAAM6M,WAAW,GAAI,QAAO7M,SAAU,EAAtC;EAEA,MAAMO,eAAe,GAAG,MAAxB;EACA,MAAM2mB,eAAe,GAAG,MAAxB;EACA,MAAM1mB,eAAe,GAAG,MAAxB;EACA,MAAM2mB,kBAAkB,GAAG,SAA3B;EAEA,MAAMhiB,WAAW,GAAG;EAClB6Y,EAAAA,SAAS,EAAE,SADO;EAElBoJ,EAAAA,QAAQ,EAAE,SAFQ;EAGlBjJ,EAAAA,KAAK,EAAE;EAHW,CAApB;EAMA,MAAMvZ,OAAO,GAAG;EACdoZ,EAAAA,SAAS,EAAE,IADG;EAEdoJ,EAAAA,QAAQ,EAAE,IAFI;EAGdjJ,EAAAA,KAAK,EAAE;EAHO,CAAhB;EAMA,MAAMvI,qBAAqB,GAAG,2BAA9B;EAEA;EACA;EACA;EACA;EACA;;EAEA,MAAMyR,KAAN,SAAoB7nB,aAApB,CAAkC;EAChCC,EAAAA,WAAW,CAACpL,OAAD,EAAUoC,MAAV,EAAkB;EAC3B,UAAMpC,OAAN;EAEA,SAAK0T,OAAL,GAAe,KAAKC,UAAL,CAAgBvR,MAAhB,CAAf;EACA,SAAKypB,QAAL,GAAgB,IAAhB;;EACA,SAAKI,aAAL;EACD,GAP+B;;;EAWV,aAAXnb,WAAW,GAAG;EACvB,WAAOA,WAAP;EACD;;EAEiB,aAAPP,OAAO,GAAG;EACnB,WAAOA,OAAP;EACD;;EAEkB,aAARhF,QAAQ,GAAG;EACpB,WAAOA,QAAP;EACD,GArB+B;;;EAyBhCuO,EAAAA,IAAI,GAAG;EACL,UAAM0E,SAAS,GAAG9W,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCkN,UAApC,CAAlB;;EAEA,QAAIiG,SAAS,CAACjU,gBAAd,EAAgC;EAC9B;EACD;;EAED,SAAK0oB,aAAL;;EAEA,QAAI,KAAKvf,OAAL,CAAaiW,SAAjB,EAA4B;EAC1B,WAAKte,QAAL,CAAc1H,SAAd,CAAwBqS,GAAxB,CAA4B9J,eAA5B;EACD;;EAED,UAAMuO,QAAQ,GAAG,MAAM;EACrB,WAAKpP,QAAL,CAAc1H,SAAd,CAAwB2C,MAAxB,CAA+BwsB,kBAA/B;;EACA,WAAKznB,QAAL,CAAc1H,SAAd,CAAwBqS,GAAxB,CAA4B7J,eAA5B;;EAEAzE,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCmN,WAApC;;EAEA,UAAI,KAAK9E,OAAL,CAAaqf,QAAjB,EAA2B;EACzB,aAAKlH,QAAL,GAAgB5pB,UAAU,CAAC,MAAM;EAC/B,eAAK4X,IAAL;EACD,SAFyB,EAEvB,KAAKnG,OAAL,CAAaoW,KAFU,CAA1B;EAGD;EACF,KAXD;;EAaA,SAAKze,QAAL,CAAc1H,SAAd,CAAwB2C,MAAxB,CAA+BusB,eAA/B;;EACAvuB,IAAAA,MAAM,CAAC,KAAK+G,QAAN,CAAN;;EACA,SAAKA,QAAL,CAAc1H,SAAd,CAAwBqS,GAAxB,CAA4B8c,kBAA5B;;EACA,QAAI,KAAKpf,OAAL,CAAaiW,SAAjB,EAA4B;EAC1B,YAAM/oB,kBAAkB,GAAGD,gCAAgC,CAAC,KAAK0K,QAAN,CAA3D;EAEA3D,MAAAA,YAAY,CAACkC,GAAb,CAAiB,KAAKyB,QAAtB,EAAgC,eAAhC,EAAiDoP,QAAjD;EACAhZ,MAAAA,oBAAoB,CAAC,KAAK4J,QAAN,EAAgBzK,kBAAhB,CAApB;EACD,KALD,MAKO;EACL6Z,MAAAA,QAAQ;EACT;EACF;;EAEDZ,EAAAA,IAAI,GAAG;EACL,QAAI,CAAC,KAAKxO,QAAL,CAAc1H,SAAd,CAAwBC,QAAxB,CAAiCuI,eAAjC,CAAL,EAAwD;EACtD;EACD;;EAED,UAAM8S,SAAS,GAAGvX,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCoN,UAApC,CAAlB;;EAEA,QAAIwG,SAAS,CAAC1U,gBAAd,EAAgC;EAC9B;EACD;;EAED,UAAMkQ,QAAQ,GAAG,MAAM;EACrB,WAAKpP,QAAL,CAAc1H,SAAd,CAAwBqS,GAAxB,CAA4B6c,eAA5B;;EACAnrB,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCqN,YAApC;EACD,KAHD;;EAKA,SAAKrN,QAAL,CAAc1H,SAAd,CAAwB2C,MAAxB,CAA+B6F,eAA/B;;EACA,QAAI,KAAKuH,OAAL,CAAaiW,SAAjB,EAA4B;EAC1B,YAAM/oB,kBAAkB,GAAGD,gCAAgC,CAAC,KAAK0K,QAAN,CAA3D;EAEA3D,MAAAA,YAAY,CAACkC,GAAb,CAAiB,KAAKyB,QAAtB,EAAgC,eAAhC,EAAiDoP,QAAjD;EACAhZ,MAAAA,oBAAoB,CAAC,KAAK4J,QAAN,EAAgBzK,kBAAhB,CAApB;EACD,KALD,MAKO;EACL6Z,MAAAA,QAAQ;EACT;EACF;;EAEDjP,EAAAA,OAAO,GAAG;EACR,SAAKynB,aAAL;;EAEA,QAAI,KAAK5nB,QAAL,CAAc1H,SAAd,CAAwBC,QAAxB,CAAiCuI,eAAjC,CAAJ,EAAuD;EACrD,WAAKd,QAAL,CAAc1H,SAAd,CAAwB2C,MAAxB,CAA+B6F,eAA/B;EACD;;EAEDzE,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK0D,QAAtB,EAAgCwV,mBAAhC;EAEA,UAAMrV,OAAN;EACA,SAAKkI,OAAL,GAAe,IAAf;EACD,GAtG+B;;;EA0GhCC,EAAAA,UAAU,CAACvR,MAAD,EAAS;EACjBA,IAAAA,MAAM,GAAG,EACP,GAAGmO,OADI;EAEP,SAAG3C,WAAW,CAACI,iBAAZ,CAA8B,KAAK3C,QAAnC,CAFI;EAGP,UAAI,OAAOjJ,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAApD;EAHO,KAAT;EAMAF,IAAAA,eAAe,CAACwJ,IAAD,EAAOtJ,MAAP,EAAe,KAAKgJ,WAAL,CAAiB0F,WAAhC,CAAf;EAEA,WAAO1O,MAAP;EACD;;EAED6pB,EAAAA,aAAa,GAAG;EACdvkB,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+BwV,mBAA/B,EAAoDU,qBAApD,EAA2E,MAAM,KAAK1H,IAAL,EAAjF;EACD;;EAEDoZ,EAAAA,aAAa,GAAG;EACdpd,IAAAA,YAAY,CAAC,KAAKgW,QAAN,CAAZ;EACA,SAAKA,QAAL,GAAgB,IAAhB;EACD,GA7H+B;;;EAiIV,SAAfvmB,eAAe,CAAClD,MAAD,EAAS;EAC7B,WAAO,KAAK0K,IAAL,CAAU,YAAY;EAC3B,UAAIC,IAAI,GAAGzB,IAAI,CAACtF,GAAL,CAAS,IAAT,EAAeuF,QAAf,CAAX;;EACA,YAAMmI,OAAO,GAAG,OAAOtR,MAAP,KAAkB,QAAlB,IAA8BA,MAA9C;;EAEA,UAAI,CAAC2K,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAIimB,KAAJ,CAAU,IAAV,EAAgBtf,OAAhB,CAAP;EACD;;EAED,UAAI,OAAOtR,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAO2K,IAAI,CAAC3K,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;EACD;;EAED2K,QAAAA,IAAI,CAAC3K,MAAD,CAAJ,CAAa,IAAb;EACD;EACF,KAfM,CAAP;EAgBD;;EAlJ+B;EAqJlC;EACA;EACA;EACA;EACA;EACA;;;EAEA4C,kBAAkB,CAAC0G,IAAD,EAAOsnB,KAAP,CAAlB;;ECxNA;EACA;EACA;EACA;EACA;EACA;AAeA,kBAAe;EACb5mB,EAAAA,KADa;EAEbgB,EAAAA,MAFa;EAGb6F,EAAAA,QAHa;EAIbgG,EAAAA,QAJa;EAKb8E,EAAAA,QALa;EAMb2D,EAAAA,KANa;EAOb8D,EAAAA,SAPa;EAQbyJ,EAAAA,OARa;EASbe,EAAAA,SATa;EAUboC,EAAAA,GAVa;EAWbY,EAAAA,KAXa;EAYbrH,EAAAA;EAZa,CAAf;;;;;;;;"}