<?= $this->extend('layouts/backend/template') ?>
<?= $this->section('content') ?>

<div id="content">
    <div class="container-fluid">
        <!-- Page Heading -->
        <h1 class="h3 mb-2 text-gray-800">Pendaftaran Event</h1>
        <?php if (session('success')): ?>
                <div class="alert alert-success"><?= session('success') ?></div>
            <?php endif ?>
            
            <?php if (session('error')): ?>
                <div class="alert alert-danger"><?= session('error') ?></div>
            <?php endif ?>
        <!-- Daftar Event -->
        <div class="row">
            <?php if (!empty($events)): ?>
                <?php foreach ($events as $event): ?>
                    <div class="col-lg-4">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary"><?= $event['name'] ?></h6>
                            </div>
                            <div class="card-body">
                                <div class="text-center mb-4">
                                    <?php if ($event['event_image']): ?>
                                        <img src="<?= base_url('uploads/file-event/' . strtolower(str_replace(' ', '-', $event['name'])) . '/' . $event['event_image']) ?>" 
                                             alt="Event Image" 
                                             class="img-fluid event-image" 
                                             style="max-height: 200px; width: 100%; object-fit: cover;">
                                    <?php else: ?>
                                        <p>Tidak ada gambar.</p>
                                    <?php endif; ?>
                                </div>
                                <h5 class="card-title text-primary"><?= $event['name'] ?></h5>
                                <p class="card-text text-dark">
                                    <strong>Deskripsi:</strong> <?= $event['description'] ?>
                                </p>
                                <p class="card-text text-dark">
                                    <strong>Tanggal Mulai:</strong> <?= date('d-m-Y', strtotime($event['start_date'])) ?><br>
                                    <strong>Tanggal Selesai:</strong> <?= date('d-m-Y', strtotime($event['end_date'])) ?>
                                </p>
                                <p class="card-text text-dark">
                                    <strong>Biaya Registrasi:</strong> Rp. <?= number_format($event['biaya_registrasi'], 0, ',', '.') ?>,-
                                </p>
                                <p class="card-text text-dark">
                                    <strong>Status:</strong> 
                                    <?php if ($event['status'] === 'active'): ?>
                                        <span class="badge badge-success">Aktif</span>
                                    <?php elseif ($event['status'] === 'completed'): ?>
                                        <span class="badge badge-secondary">Selesai</span>
                                    <?php endif; ?>
                                </p>
                                <?php if ($event['is_registered']): ?>
                                    <?php if ($event['status'] === 'completed' && $event['event_pemenang']): ?>
                                        <!-- Tombol Unduh Pemenang -->
                                        <a href="<?= base_url('uploads/file-event/' . strtolower(str_replace(' ', '-', $event['name'])) . '/' . $event['event_pemenang']) ?>" 
                                           class="btn btn-success btn-block" 
                                           target="_blank">
                                            Unduh Pemenang
                                        </a>
                                    <?php else: ?>
                                        <!-- Tombol Sudah Terdaftar -->
                                        <button class="btn btn-secondary btn-block" disabled>
                                            Sudah Terdaftar
                                        </button>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <?php if ($event['status'] === 'completed' && $event['event_pemenang']): ?>
                                        <a href="<?= base_url('uploads/file-event/' . strtolower(str_replace(' ', '-', $event['name'])) . '/' . $event['event_pemenang']) ?>" 
                                            class="btn btn-success btn-block" 
                                            target="_blank">
                                                Unduh Pemenang
                                        </a>
                                    <?php elseif ($event['status'] === 'completed'): ?>
                                        <button class="btn btn-secondary btn-block" disabled>
                                            Event Selesai
                                        </button>
                                    <?php else: ?>
                                    <button class="btn btn-primary btn-block btn-lihat-detail" data-toggle="modal" data-target="#modalDetail" 
                                        data-event-id="<?= $event['id'] ?>" 
                                        data-event-name="<?= $event['name'] ?>" 
                                        data-event-description="<?= $event['description'] ?>" 
                                        data-event-start-date="<?= $event['start_date'] ?>" 
                                        data-event-end-date="<?= $event['end_date'] ?>" 
                                        data-event-image="<?= $event['event_image'] ?>"
                                        data-event-proposal="<?= base_url('uploads/file-event/' . strtolower(str_replace(' ', '-', $event['name'])) . '/' . $event['event_proposal']) ?>"
                                        data-event-status="<?= $event['status'] ?>"
                                        data-event-pemenang="<?= $event['event_pemenang'] ?>">
                                        Lihat Detail
                                    </button>
                                    <?php endif;?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="col-lg-12">
                    <div class="alert alert-info" role="alert">
                        Tidak ada event aktif yang tersedia.
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Modal Detail Event (Modal Besar) -->
<div class="modal fade" id="modalDetail" tabindex="-1" role="dialog" aria-labelledby="modalDetailLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document"> <!-- Modal besar -->
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalDetailLabel">Detail Event</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-4">
                    <img id="detailImage" src="" alt="Event Image" class="img-fluid" style="max-height: 400px; object-fit: cover;">
                </div>
                <p><strong>Nama Event:</strong> <span id="detailName"></span></p>
                <p><strong>Deskripsi:</strong> <span id="detailDescription"></span></p>
                <p><strong>Tanggal Mulai:</strong> <span id="detailStartDate"></span></p>
                <p><strong>Tanggal Selesai:</strong> <span id="detailEndDate"></span></p>
                <p><strong>Status:</strong> <span id="detailStatus"></span></p>
            </div>
            <div class="modal-footer">
                <!-- Tombol Unduh Proposal di sebelah kiri -->
                <a class="btn btn-primary mr-auto" id="detailProposal" href="#" target="_blank">Unduh Proposal</a>
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
                <button type="button" class="btn btn-primary" id="btnAction">Daftar Sekarang</button>
            </div>
        </div>
    </div>
</div>


<!-- Modal Form Pendaftaran -->
<div class="modal fade" id="modalForm" tabindex="-1" role="dialog" aria-labelledby="modalFormLabel" aria-hidden="true">
    <div class="modal-dialog modal-l" role="document"> <!-- Modal besar -->
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalFormLabel">Pendaftaran Kontingen</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="<?= base_url('pendaftaran-event/store') ?>" method="post">
                <div class="modal-body">
                    <input type="hidden" id="formIdEvent" name="id_event">
                    <input type="hidden" id="formIdKontingen" name="id_kontingen" value="<?= $kontingen['id'] ?? '' ?>">
                    <div class="form-group">
                        <label for="formNamaKontingen">Nama Kontingen</label>
                        <input type="text" class="form-control" id="formNamaKontingen" value="<?= $kontingen['name'] ?? 'Tidak ada kontingen' ?>" readonly>
                    </div>
                    <div class="form-group">
                        <label for="formNegara">Negara</label>
                        <input type="text" class="form-control" id="formNegara" value="<?= $kontingen['negara'] ?? '-' ?>" readonly>
                    </div>
                    <div class="form-group">
                        <label for="formProvinsi">Provinsi</label>
                        <input type="text" class="form-control" id="formProvinsi" value="<?= $kontingen['provinsi'] ?? '-' ?>" readonly>
                    </div>
                    <div class="form-group">
                        <label for="formKabupatenKota">Kabupaten/Kota</label>
                        <input type="text" class="form-control" id="formKabupatenKota" value="<?= $kontingen['kabupaten_kota'] ?? '-' ?>" readonly>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Daftar</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    // Set nilai detail event saat tombol "Lihat Detail" ditekan
    document.querySelectorAll('.btn-lihat-detail').forEach(button => {
        button.addEventListener('click', function () {
            const eventId = this.getAttribute('data-event-id');
            const eventName = this.getAttribute('data-event-name');
            const eventImage = this.getAttribute('data-event-image');
            const eventStatus = this.getAttribute('data-event-status');
            const eventPemenang = this.getAttribute('data-event-pemenang');
            // Isi detail event ke modal
            document.getElementById('detailName').textContent = eventName;
            document.getElementById('detailDescription').textContent = this.getAttribute('data-event-description');
            document.getElementById('detailStartDate').textContent = this.getAttribute('data-event-start-date');
            document.getElementById('detailEndDate').textContent = this.getAttribute('data-event-end-date');
            document.getElementById('detailProposal').href = this.getAttribute('data-event-proposal');
            document.getElementById('detailImage').src = `<?= base_url('uploads/file-event/') ?>${eventName.toLowerCase().replace(/ /g, '-')}/${eventImage}`;
            // Ubah tombol "Daftar Sekarang" menjadi "Unduh Pemenang" jika status event selesai
            const btnAction = document.getElementById('btnAction');
            if (eventStatus === 'completed' && eventPemenang) {
                btnAction.textContent = 'Unduh Pemenang';
                btnAction.onclick = function () {
                    window.location.href = `<?= base_url('uploads/file-event/') ?>${eventName.toLowerCase().replace(/ /g, '-')}/${eventPemenang}`;
                };
            } else {
                // Buka modal form pendaftaran
                btnAction.textContent = 'Daftar Sekarang';
                btnAction.onclick = function () {
                    // Arahkan ke halaman pendaftaran
                    document.getElementById('formIdEvent').value = eventId; // Set ID event di form
                    $('#modalDetail').modal('hide'); // Tutup modal detail
                    $('#modalForm').modal('show'); // Buka modal pendaftaran
                };
            }
        });
    });
</script>

<?= $this->endSection(); ?>