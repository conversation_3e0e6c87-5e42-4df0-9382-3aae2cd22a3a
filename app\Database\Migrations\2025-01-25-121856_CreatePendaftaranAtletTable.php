<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreatePendaftaranAtletTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 5,
                'unsigned' => true,
                'auto_increment' => true
            ],
            'jenis_tanding' => [
                'type' => 'VARCHAR',
                'constraint' => '255',
            ],
            'kategori_umur' => [
                'type' => 'VARCHAR',
                'constraint' => '255',
            ],
            'kelas_tanding' => [
                'type' => 'VARCHAR',
                'constraint' => '255',
            ],
            'status' => [
                'type' => 'ENUM',
                'constraint' => "'pending', 'accepted'",
                'default' => 'pending'
            ],
            'bukti_pembayaran' => [
                'type' => 'VARCHAR',
                'constraint' => '255',
                'null' => true,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'id_pendaftaran_event' => [
                'type' => 'INT',
                'constraint' => 5,
                'unsigned' => true, 
            ],

            ]);
        $this->forge->addKey('id', true);
        $this->forge->addForeignKey('id_pendaftaran_kontingen', 'pendaftaran_kontingen', 'id', 'CASCADE', 'CASCADE');
        $this->forge->createTable('pendaftaran_atlet');
    }

    public function down()
    {
        $this->forge->dropTable('pendaftaran_atlet');
    }
}
