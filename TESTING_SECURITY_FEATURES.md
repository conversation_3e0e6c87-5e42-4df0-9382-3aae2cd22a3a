# 🧪 Testing Fitur Keamanan BAJA

## ✅ Status Implementasi

### 🔐 **Fitur yang Telah Berhasil Diimplementasikan:**

#### Backend (Express.js + TypeScript):
- ✅ **OTP System** - Generate, verify, dan cleanup OTP
- ✅ **Email Service** - <PERSON><PERSON>TP via email (dengan fallback untuk testing)
- ✅ **Google OAuth Service** - Verifikasi Google ID token
- ✅ **Enhanced Auth Controller** - Login/register dengan OTP dan Google
- ✅ **Database Migration** - Tabel users dan otps sudah diupdate
- ✅ **Session Middleware** - Pengelolaan session untuk OTP flow
- ✅ **Security Middleware** - Rate limiting, helmet, CORS

#### Frontend (Next.js + TypeScript):
- ✅ **OTP Input Component** - Input 6 digit dengan auto-focus
- ✅ **Google Login Button** - Tombol login Google dengan loading state
- ✅ **Verify OTP Page** - Halaman verifikasi dengan countdown resend
- ✅ **Enhanced Auth Context** - Fungsi Google login dan OTP
- ✅ **Updated Login/Register** - Terintegrasi dengan fitur baru

#### API Endpoints:
- ✅ `POST /auth/register` - Registrasi dengan OTP
- ✅ `POST /auth/verify-registration` - Verifikasi OTP registrasi
- ✅ `POST /auth/google` - Login dengan Google
- ✅ `POST /auth/resend-otp` - Kirim ulang OTP
- ✅ `POST /auth/2fa/setup` - Setup 2FA
- ✅ `POST /auth/2fa/verify` - Verifikasi 2FA
- ✅ `POST /auth/2fa/disable` - Nonaktifkan 2FA

## 🚀 Cara Menjalankan dan Testing

### 1. **Setup Database** ✅
```bash
cd baja-backend
node run-security-migration.js
```
**Status**: ✅ **SELESAI** - Migration berhasil dijalankan

### 2. **Setup Email (Untuk OTP)**

#### Opsi A: Gmail App Password (Recommended)
1. Buka [Google Account](https://myaccount.google.com/)
2. Security → 2-Step Verification → App passwords
3. Generate password untuk "Mail"
4. Update .env:
```env
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-16-char-app-password
```

#### Opsi B: Testing Mode (Tanpa Email)
Biarkan .env seperti sekarang, OTP akan ditampilkan di console backend:
```env
EMAIL_USER=<EMAIL>
EMAIL_PASS=rengatkota2  # Password biasa (tidak akan kirim email)
```

### 3. **Jalankan Backend**
```bash
cd baja-backend
npm run build
npm start
# atau
npm run dev
```

**Backend akan berjalan di port 5000**

### 4. **Jalankan Frontend**
```bash
cd baja-frontend
npm run dev
```

**Frontend akan berjalan di port 3000**

## 🧪 Skenario Testing

### **Test 1: Registrasi dengan OTP**

1. **Buka**: `http://localhost:3000/auth/register`
2. **Isi form** registrasi dengan data valid
3. **Submit** form
4. **Cek console backend** untuk melihat OTP (jika email tidak dikonfigurasi)
5. **Masukkan OTP** di halaman verifikasi
6. **Verifikasi berhasil** → redirect ke login

**Expected Result**: 
- ✅ OTP muncul di console backend
- ✅ Halaman verifikasi OTP terbuka
- ✅ Setelah verifikasi, akun terbuat di database
- ✅ Redirect ke halaman login

### **Test 2: Login dengan OTP**

1. **Buka**: `http://localhost:3000/auth/login`
2. **Login** dengan email/password yang sudah terdaftar
3. **Sistem kirim OTP** (muncul di console backend)
4. **Masukkan OTP** untuk melanjutkan login
5. **Login berhasil** → redirect ke dashboard

**Expected Result**:
- ✅ OTP muncul di console backend
- ✅ Login berhasil setelah verifikasi OTP

### **Test 3: Google Login (Jika Dikonfigurasi)**

1. **Setup Google OAuth** di Google Cloud Console
2. **Update .env** dengan GOOGLE_CLIENT_ID
3. **Klik "Masuk dengan Google"** di halaman login
4. **Authorize** aplikasi
5. **Login otomatis** → redirect ke dashboard

**Expected Result**:
- ✅ Google OAuth popup muncul
- ✅ Akun dibuat/linked otomatis
- ✅ Login berhasil

### **Test 4: Resend OTP**

1. **Di halaman verifikasi OTP**
2. **Tunggu countdown** 60 detik selesai
3. **Klik "Kirim ulang kode"**
4. **OTP baru** muncul di console backend

**Expected Result**:
- ✅ Countdown berfungsi
- ✅ OTP baru ter-generate
- ✅ OTP lama menjadi invalid

## 🔍 Debugging

### **Backend Logs**
Cek console backend untuk:
```
🔐 OTP for testing (email not configured):
📧 Email: <EMAIL>
🔑 OTP Code: 123456
📝 Type: registration
⚠️  Setup Gmail App Password for actual email delivery
```

### **Frontend Errors**
Cek browser console untuk error JavaScript

### **Database Check**
```sql
-- Cek tabel users
SELECT id, name, email, email_verified, google_id FROM users;

-- Cek tabel otps
SELECT * FROM otps ORDER BY created_at DESC;
```

## 📋 Checklist Testing

### ✅ **Sudah Bisa Ditest**
- [x] Database migration
- [x] Backend build & run
- [x] Frontend build & run
- [x] Registrasi dengan OTP (console mode)
- [x] Login dengan OTP (console mode)
- [x] Resend OTP functionality
- [x] Session management

### 🔧 **Perlu Setup untuk Full Testing**
- [ ] Gmail App Password (untuk email OTP)
- [ ] Google OAuth credentials (untuk Google login)
- [ ] 2FA testing (setelah login berhasil)

### 🚀 **Ready to Use**
- [ ] Production email delivery
- [ ] Google OAuth integration
- [ ] 2FA setup dan verification

## 🎯 Next Steps

1. **Setup Gmail App Password** untuk email OTP yang sesungguhnya
2. **Setup Google OAuth** untuk login Google
3. **Test 2FA functionality** setelah login berhasil
4. **Deploy ke production** dengan environment yang sesuai

## 🔐 Keamanan Features Summary

**Aplikasi BAJA sekarang memiliki:**
- ✅ **OTP Email Verification** - 6 digit code dengan expiry 10 menit
- ✅ **Google OAuth Login** - Login dengan akun Google
- ✅ **Two-Factor Authentication** - TOTP dengan QR code
- ✅ **Enhanced Password Security** - Bcrypt dengan salt rounds 12
- ✅ **Rate Limiting** - Pembatasan request per IP
- ✅ **Session Management** - Secure session handling
- ✅ **Input Validation** - Validasi ketat semua input
- ✅ **Security Headers** - Helmet untuk security headers

**Semua menggunakan teknologi keamanan terbaik yang gratis!** 🚀🔐
