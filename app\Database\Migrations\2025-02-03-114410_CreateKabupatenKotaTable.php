<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateKabupatenKotaTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true
            ],
            'kabupaten_kota' => [
                'type' => 'VARCHAR',
                'constraint' => 255
            ],
            'id_provinsi' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
            ]
            ]);

            $this->forge->addKey('id', true);
            $this->forge->addForeignKey('id_provinsi', 'provinsi', 'id', 'CASCADE', 'CASCADE');
            $this->forge->createTable('kabupaten_kota');
    }

    public function down()
    {
        $this->forge->dropTable('kabupaten_kota');
    }
}
