<?php

namespace App\Controllers;

use App\Models\Event;
use App\Models\Gallery;
use App\Models\Kontingen;
use App\Models\Atlet;
use App\Models\Official;
use App\Models\Paket;

class HomeController extends BaseController
{
    public function index(): string
    {
        $eventModel    = new Event();
        $galleryModel  = new Gallery();
        $kontingenModel = new Kontingen();
        $atletModel    = new Atlet();
        $officialModel = new Official();
        $paketModel    = new Paket();
        
        // Ambil data event dan gallery
        $data['events']  = $eventModel->findAll();
        $data['gallery'] = $galleryModel->findAll();
        $data['paket']   = $paketModel->findAll();
        
        // Hitung semua data
        $data['count_event']     = $eventModel->countAll();
        $data['count_kontingen'] = $kontingenModel->countAll();
        $data['count_atlet']     = $atletModel->countAll();
        $data['count_official']  = $officialModel->countAll();
        
        return view('home/index', $data);
    }
}
