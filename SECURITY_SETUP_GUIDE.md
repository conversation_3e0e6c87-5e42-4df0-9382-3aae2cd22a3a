# 🔐 Panduan Setup Fitur Keamanan BAJA

## ✅ Fitur yang Telah Diimplementasikan

### 🚀 **Fitur Utama**
- ✅ **Google OAuth Login/Register** - Login dengan akun Gmail
- ✅ **OTP Email Verification** - Verifikasi email dengan kode 6 digit
- ✅ **Two-Factor Authentication (2FA)** - Keamanan berlapis dengan TOTP
- ✅ **Enhanced Password Security** - Bcrypt hashing dengan salt rounds 12
- ✅ **Email Templates** - Email HTML yang menarik untuk OTP dan welcome
- ✅ **Session Management** - Pengelolaan session untuk OTP flow
- ✅ **Rate Limiting** - Pembatasan request untuk keamanan
- ✅ **Input Validation** - Validasi ketat untuk semua input

### 🎯 **Teknologi Keamanan Terbaik (Gratis)**
- **bcryptjs** - Password hashing
- **jsonwebtoken** - JWT authentication
- **speakeasy** - TOTP 2FA
- **google-auth-library** - Google OAuth
- **nodemailer** - Email service
- **express-rate-limit** - Rate limiting
- **helmet** - Security headers
- **express-validator** - Input validation

## 🛠️ Setup Cepat

### 1. **Database Migration** ✅
```bash
cd baja-backend
node run-security-migration.js
```
**Status**: ✅ **SELESAI** - Tabel users dan otps sudah diupdate

### 2. **Install Dependencies** ✅
```bash
# Backend
cd baja-backend
npm install

# Frontend  
cd baja-frontend
npm install
```
**Status**: ✅ **SELESAI** - Semua dependencies terinstall

### 3. **Konfigurasi Environment**

#### Backend (.env) - **PERLU DIKONFIGURASI**
```env
# Email Configuration (WAJIB untuk OTP)
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# Google OAuth (OPSIONAL - untuk Google login)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
```

#### Frontend (.env.local) - **PERLU DIKONFIGURASI**
```env
# Google OAuth (OPSIONAL)
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your-google-client-id
```

### 4. **Setup Email Service (WAJIB)**

#### Cara Setup Gmail App Password:
1. Buka [Google Account](https://myaccount.google.com/)
2. Security → 2-Step Verification (aktifkan dulu jika belum)
3. App passwords → Generate password
4. Pilih "Mail" dan "Other (custom name)"
5. Copy password yang dihasilkan
6. Update EMAIL_USER dan EMAIL_PASS di .env

### 5. **Setup Google OAuth (OPSIONAL)**

#### Cara Setup Google OAuth:
1. Buka [Google Cloud Console](https://console.cloud.google.com/)
2. Buat project baru atau pilih yang ada
3. APIs & Services → Enable APIs → Google+ API
4. Credentials → Create OAuth 2.0 Client ID
5. Application type: Web application
6. Authorized origins: `http://localhost:3000`
7. Copy Client ID dan Secret ke .env

## 🚀 Menjalankan Aplikasi

### Backend (Port 5000)
```bash
cd baja-backend
npm run build
npm start
```

### Frontend (Port 3000)
```bash
cd baja-frontend
npm run dev
```

## 🧪 Testing Fitur Keamanan

### 1. **Test Registrasi dengan OTP**
1. Buka `http://localhost:3000/auth/register`
2. Isi form registrasi
3. Cek email untuk kode OTP
4. Masukkan kode di halaman verifikasi
5. ✅ Akun berhasil dibuat

### 2. **Test Google Login** (jika dikonfigurasi)
1. Buka `http://localhost:3000/auth/login`
2. Klik "Masuk dengan Google"
3. Login dengan akun Google
4. ✅ Otomatis masuk ke dashboard

### 3. **Test Login dengan OTP**
1. Login dengan email/password
2. Sistem akan kirim OTP ke email
3. Masukkan OTP untuk melanjutkan
4. ✅ Login berhasil

## 📋 Checklist Setup

### ✅ **Sudah Selesai**
- [x] Database migration
- [x] Dependencies installation
- [x] Code implementation
- [x] Frontend components
- [x] Backend services
- [x] API endpoints

### 🔧 **Perlu Dikonfigurasi**
- [ ] Email service (Gmail App Password)
- [ ] Google OAuth credentials (opsional)
- [ ] Environment variables
- [ ] Test email delivery

### 🚀 **Siap Dijalankan**
- [ ] Backend server (port 5000)
- [ ] Frontend server (port 3000)
- [ ] Test registrasi OTP
- [ ] Test Google login (jika dikonfigurasi)

## 🔍 Troubleshooting

### **Email tidak terkirim**
- Pastikan EMAIL_USER dan EMAIL_PASS benar
- Gunakan App Password, bukan password Gmail biasa
- Cek spam folder

### **Google login tidak berfungsi**
- Pastikan GOOGLE_CLIENT_ID dikonfigurasi
- Cek authorized origins di Google Console
- Pastikan domain localhost:3000 terdaftar

### **OTP expired**
- OTP berlaku 10 menit
- Gunakan tombol "Kirim ulang" jika expired
- Cek waktu sistem

### **Database error**
- Pastikan MySQL/MariaDB berjalan
- Cek koneksi database di .env
- Jalankan migration ulang jika perlu

## 📞 Support

Jika ada masalah:
1. Cek log error di console
2. Pastikan semua environment variables dikonfigurasi
3. Test koneksi database dan email service
4. Restart server jika perlu

## 🎉 Fitur Siap Digunakan!

Setelah setup selesai, aplikasi BAJA akan memiliki:
- 🔐 **Login/Register yang aman** dengan OTP
- 🌐 **Google OAuth** untuk kemudahan akses
- 📧 **Email verification** otomatis
- 🛡️ **Two-factor authentication** untuk keamanan ekstra
- 🔒 **Password hashing** dengan bcrypt
- ⚡ **Rate limiting** untuk mencegah abuse
- 📱 **UI/UX yang responsif** untuk semua fitur keamanan

**Teknologi keamanan terbaik yang gratis dan modern!** 🚀
