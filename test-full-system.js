const http = require('http');

// Test the full system
async function testFullSystem() {
  console.log('🧪 Testing Full System...\n');
  
  // Test 1: Backend Health Check
  console.log('1. Testing Backend Health...');
  await testBackendHealth();
  
  // Test 2: Frontend Health Check
  console.log('\n2. Testing Frontend Health...');
  await testFrontendHealth();
  
  // Test 3: API Endpoints
  console.log('\n3. Testing API Endpoints...');
  await testAPIEndpoints();
  
  console.log('\n✅ All tests completed!');
}

function testBackendHealth() {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 5000,
      path: '/api/v1/health',
      method: 'GET'
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        if (res.statusCode === 200) {
          console.log('   ✅ Backend health check passed');
          console.log('   📊 Response:', JSON.parse(data).message);
        } else {
          console.log('   ❌ Backend health check failed:', res.statusCode);
        }
        resolve();
      });
    });

    req.on('error', (e) => {
      console.log('   ❌ Backend connection error:', e.message);
      resolve();
    });

    req.end();
  });
}

function testFrontendHealth() {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/',
      method: 'GET'
    };

    const req = http.request(options, (res) => {
      if (res.statusCode === 200) {
        console.log('   ✅ Frontend health check passed');
        console.log('   🌐 Frontend is accessible on port 3000');
      } else {
        console.log('   ❌ Frontend health check failed:', res.statusCode);
      }
      resolve();
    });

    req.on('error', (e) => {
      console.log('   ❌ Frontend connection error:', e.message);
      resolve();
    });

    req.end();
  });
}

function testAPIEndpoints() {
  return new Promise((resolve, reject) => {
    console.log('   🔐 Testing authentication endpoint...');
    
    const authData = JSON.stringify({
      email: '<EMAIL>',
      password: 'wrongpassword'
    });

    const options = {
      hostname: 'localhost',
      port: 5000,
      path: '/api/v1/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(authData)
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        if (res.statusCode === 401) {
          console.log('   ✅ Authentication endpoint working (correctly rejected invalid credentials)');
        } else {
          console.log('   ⚠️  Authentication endpoint response:', res.statusCode);
        }
        
        // Test protected endpoint
        testProtectedEndpoint();
      });
    });

    req.on('error', (e) => {
      console.log('   ❌ Auth endpoint error:', e.message);
      resolve();
    });

    req.write(authData);
    req.end();
    
    function testProtectedEndpoint() {
      console.log('   🔒 Testing protected endpoint...');
      
      const protectedOptions = {
        hostname: 'localhost',
        port: 5000,
        path: '/api/v1/kontingen/my',
        method: 'GET'
      };

      const protectedReq = http.request(protectedOptions, (res) => {
        if (res.statusCode === 401) {
          console.log('   ✅ Protected endpoint working (correctly requires authentication)');
        } else {
          console.log('   ⚠️  Protected endpoint response:', res.statusCode);
        }
        resolve();
      });

      protectedReq.on('error', (e) => {
        console.log('   ❌ Protected endpoint error:', e.message);
        resolve();
      });

      protectedReq.end();
    }
  });
}

// Run the tests
testFullSystem().catch(console.error);
