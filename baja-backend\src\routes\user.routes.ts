import { Router } from 'express';
import { authenticate, authorize } from '../middleware/auth.middleware';
import {
  getAllUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser,
  changeUserStatus,
  resetUserPassword
} from '../controllers/user.controller';

const router = Router();

// All user routes require authentication
router.use(authenticate);

// User management routes (admin only)
router.get('/', authorize('admin'), getAllUsers);
router.post('/', authorize('admin'), createUser);
router.get('/:id', authorize('admin'), getUserById);
router.put('/:id', authorize('admin'), updateUser);
router.delete('/:id', authorize('admin'), deleteUser);
router.patch('/:id/status', authorize('admin'), changeUserStatus);
router.patch('/:id/reset-password', authorize('admin'), resetUserPassword);

export default router;
