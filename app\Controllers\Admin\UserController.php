<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\User;

class UserController extends BaseController
{
    public function index()
    {
        $userModel = new User();
        $data['users'] = $userModel->findAll();
        return view('admin/user/index', $data);
    }

    public function create()
    {
        return view('admin/user/create');
    }

    public function store()
    {
        $userModel = new User();
        
        // Aturan validasi
        $rules = [
            'name'     => 'required|min_length[3]',
            'email'    => 'required|valid_email|is_unique[users.email]',
            'no_hp'    => 'required',
            'alamat'   => 'required',
            'password' => 'required|min_length[8]',
            'agama'    => 'required',
            'role'     => 'required',
            'status'   => 'required'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'name'     => $this->request->getPost('name'),
            'email'    => $this->request->getPost('email'),
            'no_hp'    => $this->request->getPost('no_hp'),
            'alamat'   => $this->request->getPost('alamat'),
            'password' => password_hash($this->request->getPost('password'), PASSWORD_DEFAULT),
            'agama'    => $this->request->getPost('agama'),
            'role'     => $this->request->getPost('role'),
            'status'   => $this->request->getPost('status'),
        ];

        if ($userModel->insert($data) === false) {
            return redirect()->back()->withInput()->with('errors', $userModel->errors());
        }

        return redirect()->to('/users')->with('success', 'User berhasil ditambahkan.');
    }

    public function edit($id)
    {
        $userModel = new User();
        $data['users'] = $userModel->find($id);
        if (!$data['users']) {
            return redirect()->to('/users')->with('error', 'User tidak ditemukan.');
        }
        return view('admin/user/edit', $data);
    }

    public function update($id)
    {
        $userModel = new User();
        $existingUser = $userModel->find($id);

        if (!$existingUser) {
            return redirect()->to('/users')->with('error', 'User tidak ditemukan.');
        }

        // Aturan validasi dengan pengecualian untuk email yang sudah ada
        $rules = [
            'name'     => 'required|min_length[3]',
            'email'    => "required|valid_email|is_unique[users.email,id,$id]",
            'no_hp'    => 'required',
            'alamat'   => 'required',
            'agama'    => 'required',
            'role'     => 'required',
            'status'   => 'required'
        ];

        // Validasi password hanya jika diisi
        if ($this->request->getPost('password')) {
            $rules['password'] = 'min_length[8]';
        }

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'name'   => $this->request->getPost('name'),
            'email'  => $this->request->getPost('email'),
            'no_hp'  => $this->request->getPost('no_hp'),
            'alamat' => $this->request->getPost('alamat'),
            'agama'  => $this->request->getPost('agama'),
            'role'   => $this->request->getPost('role'),
            'status' => $this->request->getPost('status'),
        ];

        // Jika password diisi, maka update password-nya
        if ($this->request->getPost('password')) {
            $data['password'] = password_hash($this->request->getPost('password'), PASSWORD_DEFAULT);
        }

        if ($userModel->update($id, $data) === false) {
            return redirect()->back()->withInput()->with('errors', $userModel->errors());
        }

        return redirect()->to('/users')->with('success', 'User berhasil diperbarui.');
    }

    public function editPassword($id)
    {
        $userModel = new User();

        $data['users'] = $userModel->find($id);
        if (!$data['users']) {
            return redirect()->to('/users')->with('error', 'User tidak ditemukan.');
        }

        return view('admin/user/update-password', $data);
    }

    public function updatePassword($id)
    {
        $userModel = new User();

        if ($this->request->getPost('password')) {
            $data['password'] = password_hash($this->request->getPost('password'), PASSWORD_DEFAULT);
        }

        if ($userModel->update($id, $data) === false) {
            return redirect()->back()->withInput()->with('errors', $userModel->errors());
        }

        return redirect()->to('/users')->with('success', 'Password berhasil diperbarui.');
    }

    public function delete($id)
    {
        $userModel = new User();
        if (!$userModel->find($id)) {
            return redirect()->to('/users')->with('error', 'User tidak ditemukan.');
        }

        if (!$userModel->delete($id)) {
            return redirect()->to('/users')->with('error', 'Gagal menghapus user.');
        }

        return redirect()->to('/users')->with('success', 'User berhasil dihapus.');
    }
}
