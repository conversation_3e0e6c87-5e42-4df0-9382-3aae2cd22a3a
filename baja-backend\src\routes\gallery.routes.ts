import { Router } from 'express';
import { authenticate, authorize, optionalAuth } from '../middleware/auth.middleware';
import {
  getAllGallery,
  getGalleryById,
  createGallery,
  updateGallery,
  deleteGallery,
  changeGalleryStatus,
  getActiveGallery,
  upload
} from '../controllers/gallery.controller';

const router = Router();

// Public routes
router.get('/active', getActiveGallery);
router.get('/', optionalAuth, getAllGallery);
router.get('/:id', optionalAuth, getGalleryById);

// Protected routes
router.use(authenticate);

router.post('/', authorize('admin'), upload.single('image'), createGallery);
router.put('/:id', authorize('admin'), upload.single('image'), updateGallery);
router.delete('/:id', authorize('admin'), deleteGallery);
router.patch('/:id/status', authorize('admin'), changeGalleryStatus);

export default router;
