'use client';

import React, { useState, useRef } from 'react';
import { DocumentArrowUpIcon, XMarkIcon, CheckCircleIcon } from '@heroicons/react/24/outline';
import Button from './Button';
import LoadingSpinner from './LoadingSpinner';

interface FileUploadProps {
  label: string;
  fileType: 'rapor' | 'kk_ktp' | 'surat_kesehatan';
  onFileSelect: (file: File, fileType: string) => void;
  currentFileUrl?: string;
  loading?: boolean;
  uploaded?: boolean;
  acceptedFormats?: string[];
  maxSize?: number; // in MB
  disabled?: boolean;
}

const FileUpload: React.FC<FileUploadProps> = ({
  label,
  fileType,
  onFileSelect,
  currentFileUrl,
  loading = false,
  uploaded = false,
  acceptedFormats = ['.pdf', '.jpg', '.jpeg', '.png'],
  maxSize = 5,
  disabled = false
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (file: File) => {
    // Validate file size
    if (file.size > maxSize * 1024 * 1024) {
      alert(`File size must be less than ${maxSize}MB`);
      return;
    }

    // Validate file type
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    if (!acceptedFormats.includes(fileExtension)) {
      alert(`File type not supported. Accepted formats: ${acceptedFormats.join(', ')}`);
      return;
    }

    setSelectedFile(file);
    onFileSelect(file, fileType);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const clearFile = () => {
    setSelectedFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const openFileDialog = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <div className="space-y-2">
      <label className="block text-sm font-medium text-gray-300">
        {label}
      </label>
      
      <div
        className={`
          border-2 border-dashed rounded-lg p-4 transition-colors cursor-pointer
          ${dragOver ? 'border-gold-400 bg-gold-400/10' : 'border-gray-600'}
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:border-gold-400 hover:bg-gold-400/5'}
          ${uploaded ? 'border-green-500 bg-green-500/10' : ''}
        `}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={openFileDialog}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={acceptedFormats.join(',')}
          onChange={handleInputChange}
          className="hidden"
          disabled={disabled}
        />

        <div className="flex flex-col items-center justify-center space-y-2">
          {loading ? (
            <>
              <LoadingSpinner size="sm" />
              <span className="text-sm text-gray-400">Uploading...</span>
            </>
          ) : uploaded ? (
            <>
              <CheckCircleIcon className="h-8 w-8 text-green-400" />
              <span className="text-sm text-green-400">File uploaded successfully</span>
              {currentFileUrl && (
                <a
                  href={currentFileUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-xs text-blue-400 hover:text-blue-300 underline"
                  onClick={(e) => e.stopPropagation()}
                >
                  View file
                </a>
              )}
            </>
          ) : selectedFile ? (
            <>
              <DocumentArrowUpIcon className="h-8 w-8 text-gold-400" />
              <div className="text-center">
                <span className="text-sm text-white">{selectedFile.name}</span>
                <p className="text-xs text-gray-400">
                  {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                </p>
              </div>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  clearFile();
                }}
                className="mt-2"
              >
                <XMarkIcon className="h-4 w-4 mr-1" />
                Remove
              </Button>
            </>
          ) : (
            <>
              <DocumentArrowUpIcon className="h-8 w-8 text-gray-400" />
              <div className="text-center">
                <span className="text-sm text-gray-300">
                  Click to upload or drag and drop
                </span>
                <p className="text-xs text-gray-400">
                  {acceptedFormats.join(', ')} up to {maxSize}MB
                </p>
              </div>
            </>
          )}
        </div>
      </div>

      {currentFileUrl && !selectedFile && !uploaded && (
        <div className="text-xs text-gray-400">
          Current file: 
          <a
            href={currentFileUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-400 hover:text-blue-300 underline ml-1"
          >
            View existing file
          </a>
        </div>
      )}
    </div>
  );
};

export default FileUpload;
