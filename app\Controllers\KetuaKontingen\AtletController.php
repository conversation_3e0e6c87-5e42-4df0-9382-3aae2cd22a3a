<?php

namespace App\Controllers\KetuaKontingen;

use App\Controllers\BaseController;
use App\Models\Atlet;
use App\Models\AtletFile;
use App\Models\Kontingen;

class AtletController extends BaseController
{
    public function index()
    {
        $userId = session()->get('id');
        if (!$userId) {
            return redirect()->to('/login')->with('error', 'Anda harus login terlebih dahulu.');
        }

        // Load model
        $kontingenModel = new Kontingen();
        $atletModel     = new Atlet();
        $atletFileModel = new AtletFile();

        // Ambil data kontingen milik ketua kontingen
        $data['kontingen'] = $kontingenModel->where('id_user', $userId)->first();

        if ($data['kontingen']) {
            // Ambil data atlet berdasarkan ID kontingen beserta nama kontingen
            $data['atlet'] = $atletModel
                ->select('atlet.*, kontingen.name as kontingen_name')
                ->join('kontingen', 'kontingen.id = atlet.id_kontingen', 'left')
                ->where('atlet.id_kontingen', $data['kontingen']['id'])
                ->findAll();

            // Ambil file untuk setiap atlet
            foreach ($data['atlet'] as &$atlet) {
                $atlet['files'] = $atletFileModel
                    ->where('id_atlet', $atlet['id'])
                    ->findAll();
            }
        } else {
            $data['atlet'] = [];
        }

        return view('ketua-kontingen/atlet/index', $data);
    }

    public function create()
    {
        return view('ketua-kontingen/atlet/create');
    }

    public function store()
    {
        $atletModel     = new Atlet();
        $atletFileModel = new AtletFile();
        $kontingenModel = new Kontingen();
        $userId         = session()->get('id');

        if (!$userId) {
            return redirect()->to('/login')->with('error', 'Anda harus login terlebih dahulu.');
        }

        // Ambil data kontingen milik ketua kontingen
        $kontingen = $kontingenModel->where('id_user', $userId)->first();
        if (!$kontingen) {
            return redirect()->back()->with('error', 'Data kontingen tidak ditemukan.');
        }

        // Validasi input
        $validationRules = [
            'nik'             => 'required|numeric',
            'name'            => 'required|min_length[3]',
            'tanggal_lahir'   => 'required|valid_date',
            'no_hp'           => 'required|numeric',
            'alamat'          => 'required',
            'agama'           => 'required',
            'jenis_kelamin'   => 'required',
            'umur'            => 'required|numeric',
            'berat_badan'     => 'required|numeric',
            'tinggi_badan'    => 'required|numeric',
            'rapor'           => 'uploaded[rapor]|max_size[rapor,2048]|ext_in[rapor,pdf,jpg,jpeg,png]',
            'kk_ktp'          => 'uploaded[kk_ktp]|max_size[kk_ktp,2048]|ext_in[kk_ktp,pdf,jpg,jpeg,png]',
            'surat_kesehatan' => 'uploaded[surat_kesehatan]|max_size[surat_kesehatan,2048]|ext_in[surat_kesehatan,pdf,jpg,jpeg,png]',
        ];

        if (!$this->validate($validationRules)) {
            return redirect()->back()
                ->withInput()
                ->with('errors', $this->validator->getErrors());
        }

        // Simpan data ke tabel atlet
        $name = $this->request->getPost('name');
        $atletData = [
            'nik'               => $this->request->getPost('nik'),
            'name'              => strtoupper($name),
            'no_hp'             => $this->request->getPost('no_hp'),
            'tanggal_lahir'     => $this->request->getPost('tanggal_lahir'),
            'jenis_kelamin'     => $this->request->getPost('jenis_kelamin'),
            'agama'             => strtoupper($this->request->getPost('agama')),
            'alamat'            => strtoupper($this->request->getPost('alamat')),
            'umur'              => $this->request->getPost('umur'),
            'berat_badan'       => $this->request->getPost('berat_badan'),
            'tinggi_badan'      => $this->request->getPost('tinggi_badan'),
            'status_verifikasi' => 'pending',
            'id_user'           => $userId,
            'id_kontingen'      => $kontingen['id'],
        ];
        $atletModel->insert($atletData);
        $atletId = $atletModel->getInsertID();

        // Buat folder dinamis berdasarkan nama atlet
        $folderName = strtolower(str_replace(' ', '-', $name));
        $folderPath = ROOTPATH . 'public/uploads/file-atlet/' . $folderName;
        if (!is_dir($folderPath)) {
            mkdir($folderPath, 0755, true);
        }

        // Fungsi untuk upload file dan simpan ke database
        $uploadAndSave = function ($field, $fileType) use ($folderPath, $atletFileModel, $atletId) {
            $file = $this->request->getFile($field);
            if ($file && $file->isValid() && !$file->hasMoved()) {
                $fileName = $file->getRandomName();
                if ($file->move($folderPath, $fileName)) {
                    $atletFileModel->insert([
                        'id_atlet'  => $atletId,
                        'file'      => $fileName,
                        'file_type' => $fileType,
                    ]);
                }
            }
        };

        // Upload file-file yang diperlukan
        $uploadAndSave('rapor', 'rapor');
        $uploadAndSave('kk_ktp', 'kk_ktp');
        $uploadAndSave('surat_kesehatan', 'surat_kesehatan');

        return redirect()->to('/atlet')->with('success', 'Data atlet berhasil disimpan.');
    }

    public function edit($id)
    {
        $atletModel = new Atlet();
        $data['atlet'] = $atletModel->find($id);
        if (!$data['atlet']) {
            return redirect()->to('/atlet')->with('error', 'Data atlet tidak ditemukan.');
        }
        return view('ketua-kontingen/atlet/edit', $data);
    }

    public function update($id)
    {
        $atletModel     = new Atlet();
        $atletFileModel = new AtletFile();
        $kontingenModel = new Kontingen();

        // Ambil data atlet yang akan diperbarui
        $atlet = $atletModel->find($id);
        if (!$atlet) {
            return redirect()->to('/atlet')->with('error', 'Data atlet tidak ditemukan.');
        }

        // Validasi input
        $validationRules = [
            'nik'             => 'required|numeric',
            'name'            => 'required|min_length[3]',
            'tanggal_lahir'   => 'required|valid_date',
            'no_hp'           => 'required|numeric',
            'alamat'          => 'required',
            'agama'           => 'required',
            'jenis_kelamin'   => 'required|in_list[pria,wanita]',
            'umur'            => 'required|numeric',
            'berat_badan'     => 'required|numeric',
            'tinggi_badan'    => 'required|numeric',
            'rapor'           => 'permit_empty|uploaded[rapor]|max_size[rapor,2048]|ext_in[rapor,pdf,jpg,jpeg,png]',
            'kk_ktp'          => 'permit_empty|uploaded[kk_ktp]|max_size[kk_ktp,2048]|ext_in[kk_ktp,pdf,jpg,jpeg,png]',
            'surat_kesehatan' => 'permit_empty|uploaded[surat_kesehatan]|max_size[surat_kesehatan,2048]|ext_in[surat_kesehatan,pdf,jpg,jpeg,png]',
        ];

        if (!$this->validate($validationRules)) {
            return redirect()->back()
                ->withInput()
                ->with('errors', $this->validator->getErrors());
        }

        $name = $this->request->getPost('name');
        $updateData = [
            'nik'           => $this->request->getPost('nik'),
            'name'          => $name,
            'no_hp'         => $this->request->getPost('no_hp'),
            'tanggal_lahir' => $this->request->getPost('tanggal_lahir'),
            'jenis_kelamin' => $this->request->getPost('jenis_kelamin'),
            'agama'         => $this->request->getPost('agama'),
            'alamat'        => $this->request->getPost('alamat'),
            'umur'          => $this->request->getPost('umur'),
            'berat_badan'   => $this->request->getPost('berat_badan'),
            'tinggi_badan'  => $this->request->getPost('tinggi_badan'),
        ];

        $atletModel->update($id, $updateData);

        // Buat folder dinamis berdasarkan nama atlet (update folder jika perlu)
        $folderName = strtolower(str_replace(' ', '-', $name));
        $folderPath = ROOTPATH . 'public/uploads/file-atlet/' . $folderName;
        if (!is_dir($folderPath)) {
            mkdir($folderPath, 0755, true);
        }

        // Fungsi helper untuk memperbarui file
        $updateFile = function ($fileType, $newFile) use ($atletFileModel, $id, $folderPath) {
            $existingFile = $atletFileModel->where(['id_atlet' => $id, 'file_type' => $fileType])->first();
            if ($newFile && $newFile->isValid() && !$newFile->hasMoved()) {
                // Hapus file lama jika ada
                if ($existingFile && file_exists($folderPath . '/' . $existingFile['file'])) {
                    unlink($folderPath . '/' . $existingFile['file']);
                }
                // Upload file baru
                $fileName = $newFile->getRandomName();
                if ($newFile->move($folderPath, $fileName)) {
                    if ($existingFile) {
                        $atletFileModel->update($existingFile['id'], ['file' => $fileName]);
                    } else {
                        $atletFileModel->insert([
                            'id_atlet'  => $id,
                            'file'      => $fileName,
                            'file_type' => $fileType,
                        ]);
                    }
                }
            }
        };

        // Update file rapor, KK/KTP, dan surat kesehatan
        $updateFile('rapor', $this->request->getFile('rapor'));
        $updateFile('kk_ktp', $this->request->getFile('kk_ktp'));
        $updateFile('surat_kesehatan', $this->request->getFile('surat_kesehatan'));

        return redirect()->to('/atlet')->with('success', 'Data atlet berhasil diperbarui.');
    }

    public function delete($id)
    {
        $atletModel     = new Atlet();
        $atletFileModel = new AtletFile();

        // Ambil data atlet yang akan dihapus
        $atlet = $atletModel->find($id);
        if (!$atlet) {
            return redirect()->to('/atlet')->with('error', 'Data atlet tidak ditemukan.');
        }

        // Hapus file terkait atlet
        $files = $atletFileModel->where('id_atlet', $id)->findAll();
        $folderName = strtolower(str_replace(' ', '-', $atlet['name']));
        $folderPath = ROOTPATH . 'public/uploads/file-atlet/' . $folderName;
        foreach ($files as $file) {
            $filePath = $folderPath . '/' . $file['file'];
            if (file_exists($filePath)) {
                unlink($filePath);
            }
        }

        // Hapus folder jika kosong (hanya '.' dan '..' yang tersisa)
        if (is_dir($folderPath) && count(scandir($folderPath)) === 2) {
            rmdir($folderPath);
        }

        // Hapus data file dari database
        $atletFileModel->where('id_atlet', $id)->delete();

        // Hapus data atlet dari database
        $atletModel->delete($id);

        return redirect()->to('/atlet')->with('success', 'Data atlet berhasil dihapus.');
    }
}
