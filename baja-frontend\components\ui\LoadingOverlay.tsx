import React from 'react';
import Spinner from './Spinner';
import { cn } from '@/lib/utils';

interface LoadingOverlayProps {
  isLoading: boolean;
  message?: string;
  className?: string;
  spinnerSize?: 'sm' | 'md' | 'lg' | 'xl';
  backdrop?: boolean;
}

const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  isLoading,
  message = 'Loading...',
  className,
  spinnerSize = 'lg',
  backdrop = true
}) => {
  if (!isLoading) return null;

  return (
    <div
      className={cn(
        'fixed inset-0 z-50 flex items-center justify-center',
        backdrop && 'bg-black/50',
        className
      )}
    >
      <div className="bg-white rounded-lg p-6 shadow-lg flex flex-col items-center space-y-4 min-w-[200px]">
        <Spinner size={spinnerSize} />
        {message && (
          <p className="text-gray-700 text-center font-medium">{message}</p>
        )}
      </div>
    </div>
  );
};

export default LoadingOverlay;
