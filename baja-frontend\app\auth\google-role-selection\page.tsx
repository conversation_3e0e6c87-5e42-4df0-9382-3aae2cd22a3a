'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import toast from 'react-hot-toast';
import Link from 'next/link';

export default function GoogleRoleSelectionPage() {
  const [selectedRole, setSelectedRole] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [googleData, setGoogleData] = useState<any>(null);
  const router = useRouter();
  const searchParams = useSearchParams();
  const { googleRegister } = useAuth();

  useEffect(() => {
    // Check if we have Google data in sessionStorage
    const storedGoogleData = sessionStorage.getItem('pendingGoogleAuth');
    if (storedGoogleData) {
      setGoogleData(JSON.parse(storedGoogleData));
    } else {
      // If no Google data, redirect back to register
      toast.error('Data Google tidak ditemukan. Silakan coba lagi.');
      router.push('/auth/register');
    }
  }, [router]);

  const handleRoleSelection = async () => {
    if (!selectedRole) {
      toast.error('Silakan pilih role terlebih dahulu');
      return;
    }

    if (!googleData?.idToken) {
      toast.error('Data Google tidak valid. Silakan coba lagi.');
      router.push('/auth/register');
      return;
    }

    try {
      setLoading(true);
      await googleRegister(googleData.idToken, selectedRole);
      
      // Clear stored Google data
      sessionStorage.removeItem('pendingGoogleAuth');
      
      toast.success('Registrasi berhasil!');
      router.push('/dashboard');
    } catch (error: any) {
      console.error('Google registration error:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Registrasi gagal';
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    // Clear stored Google data
    sessionStorage.removeItem('pendingGoogleAuth');
    router.push('/auth/register');
  };

  if (!googleData) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-yellow-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <div className="mx-auto h-12 w-auto flex justify-center">
            <img
              className="h-12 w-auto"
              src="/baja.jpeg"
              alt="BAJA"
            />
          </div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-white">
            Pilih Role Anda
          </h2>
          <p className="mt-2 text-center text-sm text-gray-300">
            Selamat datang, {googleData.name}!
          </p>
          <p className="mt-1 text-center text-sm text-gray-400">
            Silakan pilih role yang sesuai dengan kebutuhan Anda
          </p>
        </div>

        <div className="mt-8 space-y-6">
          <div className="space-y-4">
            {/* Ketua Kontingen Option */}
            <div
              onClick={() => setSelectedRole('ketua-kontingen')}
              className={`
                relative cursor-pointer rounded-lg border-2 p-6 transition-all duration-200
                ${selectedRole === 'ketua-kontingen'
                  ? 'border-yellow-500 bg-yellow-500/10'
                  : 'border-gray-600 bg-gray-800/50 hover:border-gray-500'
                }
              `}
            >
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className={`
                    w-4 h-4 rounded-full border-2 transition-all duration-200
                    ${selectedRole === 'ketua-kontingen'
                      ? 'border-yellow-500 bg-yellow-500'
                      : 'border-gray-400'
                    }
                  `}>
                    {selectedRole === 'ketua-kontingen' && (
                      <div className="w-full h-full rounded-full bg-yellow-500 flex items-center justify-center">
                        <div className="w-1.5 h-1.5 rounded-full bg-black"></div>
                      </div>
                    )}
                  </div>
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-medium text-white">
                    Ketua Kontingen
                  </h3>
                  <p className="text-sm text-gray-300">
                    Mengelola kontingen, atlet, dan official untuk mengikuti event
                  </p>
                </div>
              </div>
            </div>

            {/* Admin Event Option */}
            <div
              onClick={() => setSelectedRole('admin-event')}
              className={`
                relative cursor-pointer rounded-lg border-2 p-6 transition-all duration-200
                ${selectedRole === 'admin-event'
                  ? 'border-yellow-500 bg-yellow-500/10'
                  : 'border-gray-600 bg-gray-800/50 hover:border-gray-500'
                }
              `}
            >
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className={`
                    w-4 h-4 rounded-full border-2 transition-all duration-200
                    ${selectedRole === 'admin-event'
                      ? 'border-yellow-500 bg-yellow-500'
                      : 'border-gray-400'
                    }
                  `}>
                    {selectedRole === 'admin-event' && (
                      <div className="w-full h-full rounded-full bg-yellow-500 flex items-center justify-center">
                        <div className="w-1.5 h-1.5 rounded-full bg-black"></div>
                      </div>
                    )}
                  </div>
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-medium text-white">
                    Admin Event
                  </h3>
                  <p className="text-sm text-gray-300">
                    Mengelola dan mengorganisir event, melihat pendaftar event
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="flex space-x-4">
            <button
              onClick={handleCancel}
              disabled={loading}
              className="
                flex-1 flex justify-center py-3 px-4 border border-gray-600 rounded-md shadow-sm
                text-sm font-medium text-gray-300 bg-transparent
                hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500
                disabled:opacity-50 disabled:cursor-not-allowed
                transition-all duration-200
              "
            >
              Batal
            </button>
            
            <button
              onClick={handleRoleSelection}
              disabled={loading || !selectedRole}
              className={`
                flex-1 flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm
                text-sm font-medium text-black font-bold
                focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500
                disabled:opacity-50 disabled:cursor-not-allowed
                transition-all duration-200
                ${selectedRole && !loading
                  ? 'bg-gradient-to-r from-yellow-400 to-yellow-600 hover:from-yellow-500 hover:to-yellow-700'
                  : 'bg-gray-600 cursor-not-allowed'
                }
              `}
            >
              {loading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-black mr-2"></div>
                  Memproses...
                </div>
              ) : (
                'Daftar dengan Google'
              )}
            </button>
          </div>

          <div className="text-center">
            <Link
              href="/auth/login"
              className="text-sm text-yellow-500 hover:text-yellow-400 transition-colors duration-200"
            >
              Sudah punya akun? Masuk di sini
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
