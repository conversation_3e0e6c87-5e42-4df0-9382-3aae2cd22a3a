'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { User, LoginRequest, RegisterRequest } from '@/types';
import { authService } from '@/lib/auth';
import toast from 'react-hot-toast';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (credentials: LoginRequest) => Promise<void>;
  register: (userData: RegisterRequest) => Promise<void>;
  verifyRegistrationOTP: (email: string, otp: string) => Promise<void>;
  googleLogin: (idToken: string) => Promise<void>;
  googleRegister: (idToken: string, role: string) => Promise<void>;
  resendOTP: (email: string, type: 'registration' | 'login' | 'password_reset') => Promise<void>;
  logout: () => Promise<void>;
  updateUser: (userData: Partial<User>) => Promise<void>;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  const isAuthenticated = !!user;

  useEffect(() => {
    const initAuth = async () => {
      try {
        // Try to get profile - if successful, user is authenticated
        const userData = await authService.getProfile();
        setUser(userData);
      } catch (error) {
        // Silently handle auth errors - user is just not authenticated
        // Don't log errors for public pages to avoid console spam
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    initAuth();
  }, []);

  const login = async (credentials: LoginRequest) => {
    try {
      setLoading(true);
      const authData = await authService.login(credentials);
      setUser(authData.user);
      toast.success('Login berhasil!');
    } catch (error: any) {
      const message = error.response?.data?.message || error.message || 'Login gagal';
      toast.error(message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const register = async (userData: RegisterRequest) => {
    try {
      setLoading(true);
      await authService.register(userData);
      toast.success('OTP telah dikirim ke email Anda. Silakan verifikasi untuk menyelesaikan registrasi.');
    } catch (error: any) {
      const message = error.response?.data?.message || error.message || 'Registrasi gagal';
      toast.error(message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const verifyRegistrationOTP = async (email: string, otp: string) => {
    // Prevent multiple simultaneous calls
    if (loading) {
      console.log('🔄 Verification already in progress, skipping...');
      return;
    }

    try {
      setLoading(true);
      console.log('🔐 AuthContext: Verifying OTP for', email);

      await authService.verifyRegistrationOTP(email, otp);

      console.log('✅ AuthContext: OTP verification successful');
      // Don't show toast here, let the page handle it
    } catch (error: any) {
      console.error('❌ AuthContext: OTP verification failed', error);
      const message = error.response?.data?.message || error.message || 'Verifikasi OTP gagal';
      throw new Error(message);
    } finally {
      setLoading(false);
    }
  };

  const googleLogin = async (idToken: string) => {
    try {
      setLoading(true);
      const authData = await authService.googleLogin(idToken);
      setUser(authData.user);
      toast.success('Login dengan Google berhasil!');
    } catch (error: any) {
      const message = error.response?.data?.message || error.message || 'Login dengan Google gagal';
      toast.error(message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const googleRegister = async (idToken: string, role: string) => {
    try {
      setLoading(true);
      const authData = await authService.googleRegister(idToken, role);
      setUser(authData.user);
      toast.success('Registrasi dengan Google berhasil!');
    } catch (error: any) {
      const message = error.response?.data?.message || error.message || 'Registrasi dengan Google gagal';
      toast.error(message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const resendOTP = async (email: string, type: 'registration' | 'login' | 'password_reset') => {
    try {
      await authService.resendOTP(email, type);
      toast.success('OTP baru telah dikirim ke email Anda.');
    } catch (error: any) {
      const message = error.response?.data?.message || error.message || 'Gagal mengirim ulang OTP';
      toast.error(message);
      throw error;
    }
  };

  const logout = async () => {
    try {
      await authService.logout();
      setUser(null);
      toast.success('Logout berhasil!');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const updateUser = async (userData: Partial<User>) => {
    try {
      const updatedUser = await authService.updateProfile(userData);
      setUser(updatedUser);
      toast.success('Profile berhasil diperbarui!');
    } catch (error: any) {
      const message = error.response?.data?.message || error.message || 'Gagal memperbarui profile';
      toast.error(message);
      throw error;
    }
  };

  const value: AuthContextType = {
    user,
    loading,
    login,
    register,
    verifyRegistrationOTP,
    googleLogin,
    googleRegister,
    resendOTP,
    logout,
    updateUser,
    isAuthenticated,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
