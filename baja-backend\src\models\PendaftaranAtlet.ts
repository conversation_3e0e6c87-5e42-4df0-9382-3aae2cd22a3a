import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../config/database';
import PendaftaranEvent from './PendaftaranEvent';

interface PendaftaranAtletInterface {
  id: number;
  jenis_tanding: string;
  kategori_umur?: string;
  kelas_tanding?: string;
  status: 'pending' | 'oncheck' | 'paid';
  bukti_pembayaran?: string;
  id_pendaftaran_event: number;
  created_at: Date;
  updated_at: Date;
}

interface PendaftaranAtletCreationAttributes extends Optional<PendaftaranAtletInterface, 'id' | 'created_at' | 'updated_at'> {}

class PendaftaranAtlet extends Model<PendaftaranAtletInterface, PendaftaranAtletCreationAttributes> implements PendaftaranAtletInterface {
  public id!: number;
  public jenis_tanding!: string;
  public kategori_umur?: string;
  public kelas_tanding?: string;
  public status!: 'pending' | 'oncheck' | 'paid';
  public bukti_pembayaran?: string;
  public id_pendaftaran_event!: number;
  public readonly created_at!: Date;
  public readonly updated_at!: Date;
}

PendaftaranAtlet.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    jenis_tanding: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    kategori_umur: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    kelas_tanding: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    status: {
      type: DataTypes.ENUM('pending', 'oncheck', 'paid'),
      allowNull: false,
      defaultValue: 'pending',
    },
    bukti_pembayaran: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    id_pendaftaran_event: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: PendaftaranEvent,
        key: 'id',
      },
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    tableName: 'pendaftaran_atlet',
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  }
);

export default PendaftaranAtlet;
