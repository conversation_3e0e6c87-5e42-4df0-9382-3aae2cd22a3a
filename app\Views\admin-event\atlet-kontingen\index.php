<?= $this->extend('layouts/backend/template') ?>
<?= $this->section('content') ?>

<div id="content">
    <div class="container-fluid">
        <!-- Page Heading -->
        <h1 class="h3 mb-2 text-gray-800">Atlet</h1>
        <?php if (session('success')): ?>
                        <div class="alert alert-success"><?= session('success') ?></div>
                    <?php endif ?>
                    
                    <?php if (session('error')): ?>
                        <div class="alert alert-danger"><?= session('error') ?></div>
                    <?php endif ?>
        <!-- DataTales Example -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Data Atlet</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>No</th>
                            <th>NIK</th>
                            <th><PERSON><PERSON></th>
                            <th><PERSON><PERSON></th>
                            <th>Kontingen</th>
                            <th>No HP</th>
                            <th>Alamat</th>
                            <th>Agama</th>
                            <th>Jenis Kelamin</th>
                            <th>Berat Badan</th>
                            <th>Tinggi Badan</th>
                            <th>Umur</th>
                            <th>File</th>
                            <th>Status Verifikasi</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (!empty($atlet_kontingen)): ?>
                            <?php $no = 1; foreach ($atlet_kontingen as $row): ?>
                                <tr>
                                    <td><?= $no++ ?></td>
                                    <td><?= esc($row['nik']) ?></td>
                                    <td><?= esc($row['name']) ?></td>
                                    <td><?= esc($row['tanggal_lahir']) ?></td>
                                    <td><?= esc($row['kontingen_name']) ?></td>
                                    <td><?= esc($row['no_hp']) ?></td>
                                    <td><?= esc($row['alamat']) ?></td>
                                    <td><?= esc($row['agama']) ?></td>
                                    <td><?= $row['jenis_kelamin'] == 'M' ? 'Laki-laki' : 'Perempuan' ?></td>
                                    <td><?= esc($row['berat_badan']) ?> kg</td>
                                    <td><?= esc($row['tinggi_badan']) ?> cm</td>
                                    <td><?= esc($row['umur']) ?> tahun</td>
                                    <td>
                                        <?php
                                        $fileLinks = [];
                                        foreach ($row['files'] as $file) {
                                            $filePath = base_url("uploads/file-atlet/" . strtolower(str_replace(' ', '-', $row['name'])) . "/" . $file['file']);
                                            $fileLinks[] = '<a href="' . $filePath . '" target="_blank">' . ucfirst($file['file_type']) . '</a>';
                                        }
                                        echo !empty($fileLinks) ? implode(', ', $fileLinks) : 'Tidak Ada';
                                        ?>
                                    </td>
                                    <td>
                                        <?php
                                        if ($row['status_verifikasi'] === 'verified') {
                                            echo '<span class="badge badge-success">Terverifikasi</span>';
                                        } else {
                                            echo '<span class="badge badge-warning">Belum Terverifikasi</span>';
                                        }
                                        ?>
                                    </td>
                                    <td>
                                        <?php if ($row['status_verifikasi'] === 'verified'): ?>
                                            <!-- Jika sudah diverifikasi -->
                                            <span class="badge badge-success"><i class="fas fa-check-circle"></i> Terverifikasi</span>
                                        <?php else: ?>
                                            <!-- Jika belum diverifikasi -->
                                            <form action="<?= base_url('atlet-kontingen/verify/' . $row['id']) ?>" method="POST" style="display:inline;">
                                                <?= csrf_field() ?>
                                                <button type="submit" class="btn btn-sm btn-success" onclick="return confirm('Apakah Anda yakin ingin memverifikasi atlet ini?')">Verifikasi</button>
                                            </form>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="15" class="text-center">Tidak ada data atlet.</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <!-- /.container-fluid -->
</div>
<!-- End of Main Content -->

<?= $this->endSection() ?>