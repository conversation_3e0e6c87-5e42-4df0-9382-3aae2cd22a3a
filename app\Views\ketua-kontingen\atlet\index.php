<?= $this->extend('layouts/backend/template') ?>
<?= $this->section('content') ?>

<div id="content">
    <div class="container-fluid">
        <!-- Page Heading -->
        <h1 class="h3 mb-2 text-gray-800">Atlet</h1>
        <h6 class="m-0 mb-3 font-weight-bold text-primary">
            <a href="<?= base_url('atlet/create') ?>" class="btn btn-primary">+ Atlet</a>
        </h6>
        <?php if (session('success')): ?>
                        <div class="alert alert-success"><?= session('success') ?></div>
                    <?php endif ?>
                    
                    <?php if (session('error')): ?>
                        <div class="alert alert-danger"><?= session('error') ?></div>
                    <?php endif ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <h6 class="alert-heading"></h6>
            <p>Mohon pastikan data atlet sudah lengkap sebelum melakukan pendaftaran!</p>
        </div>
        <!-- DataTales Example -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Data Atlet</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>NIK</th>
                                <th>Nama Lengkap</th>
                                <th>Tanggal Lahir</th>
                                <th>Kontingen</th>
                                <th>No HP</th>
                                <th>Alamat</th>
                                <th>Agama</th>
                                <th>Jenis Kelamin</th>
                                <th>Berat Badan</th>
                                <th>Tinggi Badan</th>
                                <th>Umur</th>
                                <th>Status</th>
                                <th>File</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($atlet)): ?>
                                <?php $no = 1; foreach ($atlet as $row): ?>
                                    <tr>
                                        <td><?= $no++ ?></td>
                                        <td><?= $row['nik'] ?></td>
                                        <td><?= $row['name'] ?></td>
                                        <td><?= $row['tanggal_lahir'] ?></td>
                                        <td><?= $row['kontingen_name'] ?></td>
                                        <td><?= $row['no_hp'] ?></td>
                                        <td><?= $row['alamat'] ?></td>
                                        <td><?= $row['agama'] ?></td>
                                        <td><?= $row['jenis_kelamin'] == 'M' ? 'Laki-laki' : 'Perempuan' ?></td>
                                        <td><?= $row['berat_badan'] ?> kg</td>
                                        <td><?= $row['tinggi_badan'] ?> cm</td>
                                        <td><?= $row['umur'] ?> tahun</td>
                                        <td>
                                        <?php
                                            if ($row['status_verifikasi'] === 'verified') {
                                                echo '<span class="badge badge-success">Terverifikasi</span>';
                                            } else {
                                                echo '<span class="badge badge-warning">Belum Terverifikasi</span>';
                                            }
                                            ?>
                                        </td>
                                        <td>
                                            <?php
                                            $fileLinks = [];
                                            foreach ($row['files'] as $file) {
                                                $filePath = base_url("uploads/file-atlet/" . strtolower(str_replace(' ', '-', $row['name'])) . "/" . $file['file']);
                                                $fileLinks[] = '<a href="' . $filePath . '" target="_blank">' . ucfirst($file['file_type']) . '</a>';
                                            }
                                            echo !empty($fileLinks) ? implode(', ', $fileLinks) : 'Tidak Ada';
                                            ?>
                                        </td>
                                        <td>
                                            <a href="<?= base_url('atlet/edit/' . $row['id']) ?>" class="btn btn-sm btn-warning"><i class="fa fa-edit"></i></a>
                                            <form action="<?= base_url('atlet/delete/' . $row['id']) ?>" method="POST" style="display:inline;">
                                                <?= csrf_field() ?>
                                                <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Apakah Anda yakin ingin menghapus data ini?')"><i class="fa fa-trash"></i></button>
                                            </form>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="14" class="text-center">Tidak ada data atlet.</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <!-- /.container-fluid -->
</div>
<!-- End of Main Content -->

<?= $this->endSection() ?>