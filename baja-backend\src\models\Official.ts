import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../config/database';
import { Official as OfficialInterface } from '../types';
import Kontingen from './Kontingen';

interface OfficialCreationAttributes extends Optional<OfficialInterface, 'id' | 'created_at' | 'updated_at'> {}

class Official extends Model<OfficialInterface, OfficialCreationAttributes> implements OfficialInterface {
  public id!: number;
  public profile?: string;
  public name!: string;
  public no_hp!: string;
  public alamat!: string;
  public agama!: string;
  public jenis_kelamin!: 'M' | 'F';
  public id_kontingen!: number;
  public readonly created_at!: Date;
  public readonly updated_at!: Date;
}

Official.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    profile: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    no_hp: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    alamat: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    agama: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    jenis_kelamin: {
      type: DataTypes.ENUM('M', 'F'),
      allowNull: false,
    },
    id_kontingen: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: Kontingen,
        key: 'id',
      },
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    tableName: 'official',
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  }
);

// Associations will be set up in models/index.ts

export default Official;
