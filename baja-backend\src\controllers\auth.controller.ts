import { Request, Response } from 'express';
import bcrypt from 'bcryptjs';
const jwt = require('jsonwebtoken');
import crypto from 'crypto';
import { Op } from 'sequelize';
import User from '../models/User';
import Event from '../models/Event';
import OTPService from '../services/otp.service';
import { emailService } from '../services/email.service';
import { googleAuthService } from '../services/google-auth.service';
import { JWTPayload, ApiResponse } from '../types';

// Step 1: Register with OTP verification
export const register = async (req: Request, res: Response): Promise<void> => {
  try {
    const { name, email, password, no_hp, alamat, agama, role } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({ where: { email } });
    if (existingUser) {
      res.status(400).json({
        success: false,
        message: 'User with this email already exists',
      });
      return;
    }

    // Generate and send OTP
    const otpCode = await OTPService.createOTP(email, 'registration');
    await OTPService.sendOTPEmail(email, otpCode, 'registration');

    // Store registration data temporarily (you might want to use Redis for this)
    // For now, we'll store it in a way that can be retrieved during verification
    const tempUserData = {
      name,
      email,
      password,
      no_hp,
      alamat,
      agama,
      role: role || 'ketua-kontingen'
    };

    // Store in session or cache (simplified approach)
    // In production, use Redis or similar
    (req as any).session = (req as any).session || {};
    (req as any).session[`registration_${email}`] = tempUserData;

    res.status(200).json({
      success: true,
      message: 'OTP sent to your email. Please verify to complete registration.',
      data: { email }
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
};

// Step 2: Verify OTP and complete registration
export const verifyRegistrationOTP = async (req: Request, res: Response): Promise<void> => {
  try {
    const { email, otp } = req.body;

    console.log(`🔍 [${new Date().toISOString()}] Verifying OTP for email: ${email}, OTP: ${otp}`);

    // Validate input
    if (!email || !otp) {
      console.log(`❌ Missing required fields: email=${!!email}, otp=${!!otp}`);
      res.status(400).json({
        success: false,
        message: 'Email and OTP are required'
      });
      return;
    }

    // Check if user already exists (prevent duplicate registration)
    const existingUser = await User.findOne({ where: { email } });
    if (existingUser) {
      console.log(`❌ User already exists: ${email}`);
      res.status(400).json({
        success: false,
        message: 'User with this email already exists'
      });
      return;
    }

    // Verify OTP
    const isValidOTP = await OTPService.verifyOTP(email, otp, 'registration');
    console.log(`🔑 OTP verification result: ${isValidOTP}`);

    if (!isValidOTP) {
      res.status(400).json({
        success: false,
        message: 'Invalid or expired OTP'
      });
      return;
    }

    // Get stored registration data
    const tempUserData = (req as any).session?.[`registration_${email}`];
    console.log(`📋 Registration data found: ${!!tempUserData}`);

    if (!tempUserData) {
      res.status(400).json({
        success: false,
        message: 'Registration data not found. Please start registration again.'
      });
      return;
    }



    // Hash password
    const saltRounds = parseInt(process.env.BCRYPT_SALT_ROUNDS || '12');
    const hashedPassword = await bcrypt.hash(tempUserData.password, saltRounds);

    // Create user
    const user = await User.create({
      name: tempUserData.name,
      email: tempUserData.email,
      password: hashedPassword,
      no_hp: tempUserData.no_hp,
      alamat: tempUserData.alamat,
      agama: tempUserData.agama,
      role: tempUserData.role,
      status: '1',
      email_verified: true
    });

    console.log(`✅ User created successfully: ${user.id}`);

    // Clean up session data
    delete (req as any).session[`registration_${email}`];

    // Send welcome email (don't wait for it)
    emailService.sendWelcomeEmail(user.email, user.name).catch(err => {
      console.error('Welcome email failed:', err);
    });

    // Remove password from response
    const userResponse: any = { ...user.toJSON() };
    delete userResponse.password;

    res.status(201).json({
      success: true,
      message: 'Registration completed successfully',
      data: userResponse,
    });
  } catch (error: any) {
    console.error('OTP verification error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

export const login = async (req: Request, res: Response): Promise<void> => {
  try {
    const { email, password } = req.body;

    // Find user by email
    const user = await User.findOne({ where: { email } });
    if (!user) {
      res.status(401).json({
        success: false,
        message: 'Invalid email or password',
      });
      return;
    }

    // Check if user is active
    if (user.status === '0') {
      res.status(401).json({
        success: false,
        message: 'Account is not active',
      });
      return;
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      res.status(401).json({
        success: false,
        message: 'Invalid email or password',
      });
      return;
    }

    // Get event_id for admin-event role
    let event_id: number | undefined = undefined;
    if (user.role === 'admin-event') {
      const event = await Event.findOne({ where: { id_user: user.id } });
      event_id = event ? event.id : undefined;
    }

    // Generate JWT token
    const payload: any = {
      id: user.id,
      email: user.email,
      role: user.role,
    };

    // Only add event_id if it exists
    if (event_id !== undefined) {
      payload.event_id = event_id;
    }

    const token = jwt.sign(payload, process.env.JWT_SECRET || 'fallback-secret-key', { expiresIn: '7d' });

    // Set cookie
    res.cookie('token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    });

    // Remove password from response
    const userResponse: any = { ...user.toJSON() };
    delete userResponse.password;

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user: userResponse,
        token,
        event_id,
      },
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
};

export const logout = async (req: Request, res: Response): Promise<void> => {
  try {
    // Clear cookie with same options as when it was set
    res.clearCookie('token', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
    });

    res.json({
      success: true,
      message: 'Logout successful',
    });
  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
};

export const getProfile = async (req: Request, res: Response): Promise<void> => {
  try {
    const user = (req as any).user;

    // Remove password from response
    const userResponse: any = { ...user.toJSON() };
    delete userResponse.password;

    res.json({
      success: true,
      message: 'Profile retrieved successfully',
      data: userResponse,
    });
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
};

export const updateProfile = async (req: Request, res: Response): Promise<void> => {
  try {
    const user = (req as any).user;
    const { name, no_hp, alamat, agama } = req.body;

    await user.update({
      name: name || user.name,
      no_hp: no_hp || user.no_hp,
      alamat: alamat || user.alamat,
      agama: agama || user.agama,
    });

    // Remove password from response
    const userResponse: any = { ...user.toJSON() };
    delete userResponse.password;

    res.json({
      success: true,
      message: 'Profile updated successfully',
      data: userResponse,
    });
  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
};

export const changePassword = async (req: Request, res: Response): Promise<void> => {
  try {
    const user = (req as any).user;
    const { currentPassword, newPassword } = req.body;

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
      res.status(400).json({
        success: false,
        message: 'Current password is incorrect',
      });
      return;
    }

    // Hash new password
    const saltRounds = parseInt(process.env.BCRYPT_SALT_ROUNDS || '12');
    const hashedNewPassword = await bcrypt.hash(newPassword, saltRounds);

    // Update password
    await user.update({
      password: hashedNewPassword,
    });

    res.json({
      success: true,
      message: 'Password changed successfully',
    });
  } catch (error) {
    console.error('Change password error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
};

// Google OAuth registration with role selection
export const googleRegister = async (req: Request, res: Response): Promise<void> => {
  try {
    const { idToken, role } = req.body;

    console.log('🔐 Google OAuth registration attempt');

    // Validate input
    if (!idToken) {
      res.status(400).json({
        success: false,
        message: 'ID token is required'
      });
      return;
    }

    if (!role || !['ketua-kontingen', 'admin-event'].includes(role)) {
      res.status(400).json({
        success: false,
        message: 'Valid role is required (ketua-kontingen or admin-event)'
      });
      return;
    }

    // Check if Google OAuth is configured
    if (!process.env.GOOGLE_CLIENT_ID ||
        !process.env.GOOGLE_CLIENT_SECRET ||
        process.env.GOOGLE_CLIENT_ID === 'your-google-client-id' ||
        process.env.GOOGLE_CLIENT_SECRET === 'your-google-client-secret') {
      console.log('❌ Google OAuth not properly configured');
      console.log('GOOGLE_CLIENT_ID:', process.env.GOOGLE_CLIENT_ID ? 'Set' : 'Not set');
      console.log('GOOGLE_CLIENT_SECRET:', process.env.GOOGLE_CLIENT_SECRET ? 'Set' : 'Not set');
      res.status(400).json({
        success: false,
        message: 'Google OAuth not configured on server'
      });
      return;
    }

    // Verify Google ID token
    const googleUser = await googleAuthService.verifyIdToken(idToken);

    console.log('✅ Google user verified:', googleUser.email);

    // Check if user exists
    let user = await User.findOne({
      where: {
        [Op.or]: [
          { email: googleUser.email },
          { google_id: googleUser.googleId }
        ]
      }
    });

    if (!user) {
      console.log('👤 Creating new user from Google data with role:', role);
      // Create new user from Google data with selected role
      user = await User.create({
        name: googleUser.name,
        email: googleUser.email,
        password: await bcrypt.hash(crypto.randomBytes(32).toString('hex'), 12), // Random password
        google_id: googleUser.googleId,
        profile: googleUser.picture,
        role: role,
        status: '1',
        email_verified: googleUser.emailVerified || false
      });

      // Send welcome email
      try {
        await emailService.sendWelcomeEmail(user.email, user.name);
      } catch (emailError) {
        console.error('Failed to send welcome email:', emailError);
        // Don't fail the login if email fails
      }
    } else {
      // User already exists
      res.status(400).json({
        success: false,
        message: 'User already exists. Please use Google Sign-In instead.'
      });
      return;
    }

    // Check if user is active
    if (user.status === '0') {
      res.status(401).json({
        success: false,
        message: 'Account is not active'
      });
      return;
    }

    // Get event_id for admin-event role
    let event_id: number | undefined = undefined;
    if (user.role === 'admin-event') {
      const event = await Event.findOne({ where: { id_user: user.id } });
      event_id = event ? event.id : undefined;
    }

    // Generate JWT token
    const payload: any = {
      id: user.id,
      email: user.email,
      role: user.role,
    };

    if (event_id !== undefined) {
      payload.event_id = event_id;
    }

    const token = jwt.sign(payload, process.env.JWT_SECRET || 'fallback-secret-key', { expiresIn: '7d' });

    // Set cookie
    res.cookie('token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    });

    // Remove password from response
    const userResponse: any = { ...user.toJSON() };
    delete userResponse.password;

    console.log('✅ Google registration successful for:', user.email);

    res.json({
      success: true,
      message: 'Google registration successful',
      data: {
        user: userResponse,
        token,
      },
    });
  } catch (error: any) {
    console.error('❌ Google registration error:', error);

    const errorMessage = error.message || 'Google registration failed';
    const statusCode = error.message?.includes('Token') ? 400 : 500;

    res.status(statusCode).json({
      success: false,
      message: errorMessage,
    });
  }
};

// Google OAuth login (for existing users)
export const googleLogin = async (req: Request, res: Response): Promise<void> => {
  try {
    const { idToken } = req.body;

    console.log('🔐 Google OAuth login attempt');

    // Validate input
    if (!idToken) {
      res.status(400).json({
        success: false,
        message: 'ID token is required'
      });
      return;
    }

    // Check if Google OAuth is configured
    if (!process.env.GOOGLE_CLIENT_ID ||
        !process.env.GOOGLE_CLIENT_SECRET ||
        process.env.GOOGLE_CLIENT_ID === 'your-google-client-id' ||
        process.env.GOOGLE_CLIENT_SECRET === 'your-google-client-secret') {
      console.log('❌ Google OAuth not properly configured');
      console.log('GOOGLE_CLIENT_ID:', process.env.GOOGLE_CLIENT_ID ? 'Set' : 'Not set');
      console.log('GOOGLE_CLIENT_SECRET:', process.env.GOOGLE_CLIENT_SECRET ? 'Set' : 'Not set');
      res.status(400).json({
        success: false,
        message: 'Google OAuth not configured on server'
      });
      return;
    }

    // Verify Google ID token
    const googleUser = await googleAuthService.verifyIdToken(idToken);

    console.log('✅ Google user verified:', googleUser.email);

    // Check if user exists
    let user = await User.findOne({
      where: {
        [Op.or]: [
          { email: googleUser.email },
          { google_id: googleUser.googleId }
        ]
      }
    });

    if (!user) {
      // User doesn't exist, suggest registration
      res.status(404).json({
        success: false,
        message: 'User not found. Please register first.',
        data: {
          email: googleUser.email,
          name: googleUser.name,
          picture: googleUser.picture
        }
      });
      return;
    }

    // Link Google account if not already linked
    if (!user.google_id) {
      console.log('🔗 Linking existing account with Google');
      await user.update({
        google_id: googleUser.googleId,
        profile: googleUser.picture,
        email_verified: googleUser.emailVerified || user.email_verified
      });
    }

    // Check if user is active
    if (user.status === '0') {
      res.status(401).json({
        success: false,
        message: 'Account is not active'
      });
      return;
    }

    // Get event_id for admin-event role
    let event_id: number | undefined = undefined;
    if (user.role === 'admin-event') {
      const event = await Event.findOne({ where: { id_user: user.id } });
      event_id = event ? event.id : undefined;
    }

    // Generate JWT token
    const payload: any = {
      id: user.id,
      email: user.email,
      role: user.role,
    };

    if (event_id !== undefined) {
      payload.event_id = event_id;
    }

    const token = jwt.sign(payload, process.env.JWT_SECRET || 'fallback-secret-key', { expiresIn: '7d' });

    // Set cookie
    res.cookie('token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    });

    // Remove password from response
    const userResponse: any = { ...user.toJSON() };
    delete userResponse.password;

    console.log('✅ Google login successful for:', user.email);

    res.json({
      success: true,
      message: 'Google login successful',
      data: {
        user: userResponse,
        token,
      },
    });
  } catch (error: any) {
    console.error('❌ Google login error:', error);

    const errorMessage = error.message || 'Google login failed';
    const statusCode = error.message?.includes('Token') ? 400 : 500;

    res.status(statusCode).json({
      success: false,
      message: errorMessage,
    });
  }
};

// Resend OTP
export const resendOTP = async (req: Request, res: Response): Promise<void> => {
  try {
    const { email, type } = req.body;

    if (!['registration', 'login', 'password_reset'].includes(type)) {
      res.status(400).json({
        success: false,
        message: 'Invalid OTP type'
      });
      return;
    }

    // Generate and send new OTP
    const otpCode = await OTPService.createOTP(email, type);
    await OTPService.sendOTPEmail(email, otpCode, type);

    res.json({
      success: true,
      message: 'OTP resent successfully',
      data: { email }
    });
  } catch (error) {
    console.error('Resend OTP error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to resend OTP',
    });
  }
};

// Setup 2FA
export const setup2FA = async (req: Request, res: Response): Promise<void> => {
  try {
    const user = (req as any).user;

    // Generate 2FA secret
    const { secret, qrCodeUrl } = OTPService.generate2FASecret(user.email);

    // Generate QR code
    const qrCodeDataUrl = await OTPService.generate2FAQRCode(qrCodeUrl);

    // Store secret temporarily (don't enable 2FA until verified)
    await user.update({
      two_factor_secret: secret
    });

    res.json({
      success: true,
      message: '2FA setup initiated',
      data: {
        secret,
        qrCode: qrCodeDataUrl
      }
    });
  } catch (error) {
    console.error('Setup 2FA error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to setup 2FA',
    });
  }
};

// Verify and enable 2FA
export const verify2FA = async (req: Request, res: Response): Promise<void> => {
  try {
    const user = (req as any).user;
    const { token } = req.body;

    if (!user.two_factor_secret) {
      res.status(400).json({
        success: false,
        message: '2FA setup not initiated'
      });
      return;
    }

    // Verify 2FA token
    const isValid = OTPService.verify2FAToken(user.two_factor_secret, token);
    if (!isValid) {
      res.status(400).json({
        success: false,
        message: 'Invalid 2FA token'
      });
      return;
    }

    // Enable 2FA
    await user.update({
      two_factor_enabled: true
    });

    res.json({
      success: true,
      message: '2FA enabled successfully'
    });
  } catch (error) {
    console.error('Verify 2FA error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to verify 2FA',
    });
  }
};

// Disable 2FA
export const disable2FA = async (req: Request, res: Response): Promise<void> => {
  try {
    const user = (req as any).user;
    const { token, password } = req.body;

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      res.status(400).json({
        success: false,
        message: 'Invalid password'
      });
      return;
    }

    // Verify 2FA token if 2FA is enabled
    if (user.two_factor_enabled && user.two_factor_secret) {
      const isValid = OTPService.verify2FAToken(user.two_factor_secret, token);
      if (!isValid) {
        res.status(400).json({
          success: false,
          message: 'Invalid 2FA token'
        });
        return;
      }
    }

    // Disable 2FA
    await user.update({
      two_factor_enabled: false,
      two_factor_secret: null
    });

    res.json({
      success: true,
      message: '2FA disabled successfully'
    });
  } catch (error) {
    console.error('Disable 2FA error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to disable 2FA',
    });
  }
};
