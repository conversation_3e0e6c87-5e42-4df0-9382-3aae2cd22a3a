<?= $this->extend('layouts/backend/template') ?>
<?= $this->section('content') ?>

<div id="content">
    <div class="container-fluid">
        <!-- Page Heading -->
        <h1 class="h3 mb-2 text-gray-800">Edit Official</h1>
        <!-- Form Tambah Users -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Form Edit Official</h6>
            </div>
            <div class="card-body">
                <form action="<?= base_url('official/update') ?>" method="post" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="file">Foto Profil (JPG, PNG)</label>
                        <input type="file" class="form-control" id="file" name="file" required>
                        <?php if(isset(session('errors')['file'])): ?>
                            <small class="text-danger"><?= session('errors')['file'] ?></small>
                        <?php endif; ?>
                    </div>
                    <div class="form-group">
                        <label for="nama_lengkap"><PERSON>a <PERSON></label>
                        <input type="text" class="form-control" id="nama_lengkap" name="name" value="<?= $official['name'] ?>" required>
                        <?php if(isset(session('errors')['name'])): ?>
                            <small class="text-danger"><?= session('errors')['name'] ?></small>
                        <?php endif; ?>
                    </div>
                    <div class="form-group">
                        <label for="no_hp">No HP</label>
                        <input type="text" class="form-control" id="no_hp" name="no_hp" value="<?= $official['no_hp'] ?>" required>
                        <?php if(isset(session('errors')['no_hp'])): ?>
                            <small class="text-danger"><?= session('errors')['no_hp'] ?></small>
                        <?php endif; ?>
                    </div>
                    <div class="form-group">
                        <label for="alamat">Alamat</label>
                        <textarea class="form-control" id="alamat" name="alamat" rows="3" value="<?= $official['alamat'] ?>" required></textarea>
                        <?php if(isset(session('errors')['alamat'])): ?>
                            <small class="text-danger"><?= session('errors')['alamat'] ?></small>
                        <?php endif; ?>
                    </div>
                    <div class="form-group">
                        <label for="agama">Agama</label>
                        <select class="form-control" id="agama" name="agama" required>
                            <option value="">-- Pilih Agama --</option>
                            <option value="Islam">Islam</option>
                            <option value="Kristen">Kristen</option>
                            <option value="Katolik">Katolik</option>
                            <option value="Hindu">Hindu</option>
                            <option value="Buddha">Buddha</option>
                            <option value="Konghucu">Konghucu</option>
                        </select>
                        <?php if(isset(session('errors')['agama'])): ?>
                            <small class="text-danger"><?= session('errors')['agama'] ?></small>
                        <?php endif; ?>
                    </div>
                    <div class="form-group">
                        <label for="jenis_kelamin">Jenis Kelamin</label>
                        <select class="form-control" id="jenis_kelamin" name="jenis_kelamin" required>
                            <option value="">-- Pilih Jenis Kelamin --</option>
                            <option value="M">Laki-laki</option>
                            <option value="F">Perempuan</option>
                        </select>
                        <?php if(isset(session('errors')['jenis_kelamin'])): ?>
                            <small class="text-danger"><?= session('errors')['jenis_kelamin'] ?></small>
                        <?php endif; ?>
                    </div>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                    <a href="<?= base_url('official') ?>" class="btn btn-secondary">Batal</a>
                </form>
            </div>
        </div>
    </div>
    <!-- /.container-fluid -->
</div>
<!-- End of Main Content -->

<?= $this->endSection(); ?>