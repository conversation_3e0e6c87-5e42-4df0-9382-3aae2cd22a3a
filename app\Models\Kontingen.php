<?php

namespace App\Models;

use CodeIgniter\Model;

class Kontingen extends Model
{
    protected $table            = 'kontingen';
    protected $primaryKey       = 'id';

    protected $allowedFields    =  [
        'name', 
        'negara', 
        'provinsi', 
        'kabupaten_kota', 
        'created_at', 
        'updated_at', 
        'id_user'
    ];

    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    public function negara()
    {
        return $this->belongsTo(Negara::class, 'negara', 'id');
    }

    // Relasi ke provinsi
    public function provinsi()
    {
        return $this->belongsTo(Provinsi::class, 'provinsi', 'id');
    }

    // Relasi ke kabupaten_kota
    public function kabupatenKota()
    {
        return $this->belongsTo(KabupatenKota::class, 'kabupaten_kota', 'id');
    }
}
