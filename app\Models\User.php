<?php

namespace App\Models;

use CodeIgniter\Model;

class User extends Model
{
    protected $table = 'users'; 
    protected $primaryKey = 'id'; 

    protected $returnType = 'array';

    protected $allowedFields = [
        'profile',
        'name',
        'email',
        'no_hp',
        'alamat',
        'agama',
        'password',
        'role',
        'status'
    ];

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField  = 'updated_at';

    protected $skipValidation = false;
}
