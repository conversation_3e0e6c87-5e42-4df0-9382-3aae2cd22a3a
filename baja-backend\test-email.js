const nodemailer = require('nodemailer');
require('dotenv').config();

async function testEmail() {
  console.log('🧪 Testing Email Configuration...');
  console.log(`📧 EMAIL_USER: ${process.env.EMAIL_USER}`);
  console.log(`🔑 EMAIL_PASS: ${process.env.EMAIL_PASS ? 'SET' : 'NOT SET'}`);
  
  const transporter = nodemailer.createTransport({
    service: 'gmail',
    host: 'smtp.gmail.com',
    port: 587,
    secure: false,
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS,
    },
    tls: {
      rejectUnauthorized: false
    }
  });

  try {
    // Test connection
    console.log('🔗 Testing SMTP connection...');
    await transporter.verify();
    console.log('✅ SMTP connection successful!');

    // Send test email
    console.log('📧 Sending test email...');
    const testEmail = {
      from: {
        name: 'BAJA Event Organizer',
        address: process.env.EMAIL_USER
      },
      to: '<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com', // Your email
      subject: '[BAJA] Test Email - OTP System',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: linear-gradient(135deg, #000000 0%, #FFD700 100%); padding: 30px; text-align: center;">
            <h1 style="color: white; margin: 0; font-size: 28px;">BAJA Event Organizer</h1>
          </div>
          
          <div style="padding: 30px; background-color: #f9f9f9;">
            <h2 style="color: #333; margin-bottom: 20px;">Test Email Berhasil!</h2>
            
            <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
              Ini adalah email test untuk memastikan sistem OTP BAJA berfungsi dengan baik.
            </p>
            
            <div style="background-color: #f4f4f4; padding: 20px; text-align: center; font-size: 24px; font-weight: bold; letter-spacing: 5px; margin: 20px 0;">
              123456
            </div>
            
            <p style="color: #666; line-height: 1.6;">
              Jika Anda menerima email ini, berarti konfigurasi email sudah benar!
            </p>
          </div>
          
          <div style="background-color: #333; padding: 20px; text-align: center;">
            <p style="color: #ccc; margin: 0; font-size: 14px;">
              © 2024 BAJA Event Organizer. All rights reserved.
            </p>
          </div>
        </div>
      `,
      text: 'Test Email - BAJA Event Organizer\n\nIni adalah email test untuk memastikan sistem OTP BAJA berfungsi dengan baik.\n\nKode Test: 123456\n\nJika Anda menerima email ini, berarti konfigurasi email sudah benar!'
    };

    const result = await transporter.sendMail(testEmail);
    
    console.log('✅ Test email sent successfully!');
    console.log(`📧 Message ID: ${result.messageId}`);
    console.log(`📧 Response: ${result.response}`);
    console.log('📧 Please check your email (including spam folder)');
    
  } catch (error) {
    console.error('❌ Email test failed:', error);
    console.error('❌ Error details:', {
      code: error.code,
      command: error.command,
      response: error.response,
      responseCode: error.responseCode
    });
    
    if (error.code === 'EAUTH') {
      console.log('🔑 Authentication failed. Please check:');
      console.log('   1. EMAIL_USER is correct Gmail address');
      console.log('   2. EMAIL_PASS is App Password (not regular password)');
      console.log('   3. 2-Step Verification is enabled in Gmail');
      console.log('   4. App Password is generated correctly');
    }
  }
}

testEmail();
