{"version": 3, "sources": ["../../scss/bootstrap-grid.scss", "../../scss/_containers.scss", "../../scss/mixins/_container.scss", "bootstrap-grid.css", "../../scss/mixins/_breakpoints.scss", "../../scss/_variables.scss", "../../scss/_grid.scss", "../../scss/mixins/_grid.scss", "../../scss/mixins/_utilities.scss", "../../scss/utilities/_api.scss"], "names": [], "mappings": "AAAA;;;;;EAAA;ACME;;;;;;;ECHA,WAAA;EACA,yCAAA;EACA,0CAAA;EACA,iBAAA;EACA,kBAAA;ACWF;;AC6CI;EH5CE;IACE,gBIoTe;EFjTrB;AACF;ACuCI;EH5CE;IACE,gBIoTe;EF5SrB;AACF;ACkCI;EH5CE;IACE,gBIoTe;EFvSrB;AACF;AC6BI;EH5CE;IACE,iBIoTe;EFlSrB;AACF;ACwBI;EH5CE;IACE,iBIoTe;EF7RrB;AACF;AGvCE;ECAA,qBAAA;EACA,gBAAA;EACA,aAAA;EACA,eAAA;EACA,yCAAA;EACA,0CAAA;EACA,2CAAA;AJ0CF;AG7CI;ECQF,sBAAA;EAIA,cAAA;EACA,WAAA;EACA,eAAA;EACA,0CAAA;EACA,2CAAA;EACA,8BAAA;AJqCF;;AIIM;EACE,YAAA;AJDR;;AIIM;EApCJ,cAAA;EACA,WAAA;AJoCF;;AItBE;EACE,cAAA;EACA,WAAA;AJyBJ;;AI3BE;EACE,cAAA;EACA,UAAA;AJ8BJ;;AIhCE;EACE,cAAA;EACA,qBAAA;AJmCJ;;AIrCE;EACE,cAAA;EACA,UAAA;AJwCJ;;AI1CE;EACE,cAAA;EACA,UAAA;AJ6CJ;;AI/CE;EACE,cAAA;EACA,qBAAA;AJkDJ;;AInBM;EAhDJ,cAAA;EACA,WAAA;AJuEF;;AIlBU;EA3DR,cAAA;EACA,oBAAA;AJiFF;;AIvBU;EA3DR,cAAA;EACA,qBAAA;AJsFF;;AI5BU;EA3DR,cAAA;EACA,UAAA;AJ2FF;;AIjCU;EA3DR,cAAA;EACA,qBAAA;AJgGF;;AItCU;EA3DR,cAAA;EACA,qBAAA;AJqGF;;AI3CU;EA3DR,cAAA;EACA,UAAA;AJ0GF;;AIhDU;EA3DR,cAAA;EACA,qBAAA;AJ+GF;;AIrDU;EA3DR,cAAA;EACA,qBAAA;AJoHF;;AI1DU;EA3DR,cAAA;EACA,UAAA;AJyHF;;AI/DU;EA3DR,cAAA;EACA,qBAAA;AJ8HF;;AIpEU;EA3DR,cAAA;EACA,qBAAA;AJmIF;;AIzEU;EA3DR,cAAA;EACA,WAAA;AJwIF;;AItEY;EAxDV,2BAAA;AJkIF;;AI1EY;EAxDV,4BAAA;AJsIF;;AI9EY;EAxDV,iBAAA;AJ0IF;;AIlFY;EAxDV,4BAAA;AJ8IF;;AItFY;EAxDV,4BAAA;AJkJF;;AI1FY;EAxDV,iBAAA;AJsJF;;AI9FY;EAxDV,4BAAA;AJ0JF;;AIlGY;EAxDV,4BAAA;AJ8JF;;AItGY;EAxDV,iBAAA;AJkKF;;AI1GY;EAxDV,4BAAA;AJsKF;;AI9GY;EAxDV,4BAAA;AJ0KF;;AIvGQ;;EAEE,gBAAA;AJ0GV;;AIvGQ;;EAEE,gBAAA;AJ0GV;;AIjHQ;;EAEE,sBAAA;AJoHV;;AIjHQ;;EAEE,sBAAA;AJoHV;;AI3HQ;;EAEE,qBAAA;AJ8HV;;AI3HQ;;EAEE,qBAAA;AJ8HV;;AIrIQ;;EAEE,mBAAA;AJwIV;;AIrIQ;;EAEE,mBAAA;AJwIV;;AI/IQ;;EAEE,qBAAA;AJkJV;;AI/IQ;;EAEE,qBAAA;AJkJV;;AIzJQ;;EAEE,mBAAA;AJ4JV;;AIzJQ;;EAEE,mBAAA;AJ4JV;;AC/MI;EGGE;IACE,YAAA;EJgNN;;EI7MI;IApCJ,cAAA;IACA,WAAA;EJqPA;;EIvOA;IACE,cAAA;IACA,WAAA;EJ0OF;;EI5OA;IACE,cAAA;IACA,UAAA;EJ+OF;;EIjPA;IACE,cAAA;IACA,qBAAA;EJoPF;;EItPA;IACE,cAAA;IACA,UAAA;EJyPF;;EI3PA;IACE,cAAA;IACA,UAAA;EJ8PF;;EIhQA;IACE,cAAA;IACA,qBAAA;EJmQF;;EIpOI;IAhDJ,cAAA;IACA,WAAA;EJwRA;;EInOQ;IA3DR,cAAA;IACA,oBAAA;EJkSA;;EIxOQ;IA3DR,cAAA;IACA,qBAAA;EJuSA;;EI7OQ;IA3DR,cAAA;IACA,UAAA;EJ4SA;;EIlPQ;IA3DR,cAAA;IACA,qBAAA;EJiTA;;EIvPQ;IA3DR,cAAA;IACA,qBAAA;EJsTA;;EI5PQ;IA3DR,cAAA;IACA,UAAA;EJ2TA;;EIjQQ;IA3DR,cAAA;IACA,qBAAA;EJgUA;;EItQQ;IA3DR,cAAA;IACA,qBAAA;EJqUA;;EI3QQ;IA3DR,cAAA;IACA,UAAA;EJ0UA;;EIhRQ;IA3DR,cAAA;IACA,qBAAA;EJ+UA;;EIrRQ;IA3DR,cAAA;IACA,qBAAA;EJoVA;;EI1RQ;IA3DR,cAAA;IACA,WAAA;EJyVA;;EIvRU;IAxDV,eAAA;EJmVA;;EI3RU;IAxDV,2BAAA;EJuVA;;EI/RU;IAxDV,4BAAA;EJ2VA;;EInSU;IAxDV,iBAAA;EJ+VA;;EIvSU;IAxDV,4BAAA;EJmWA;;EI3SU;IAxDV,4BAAA;EJuWA;;EI/SU;IAxDV,iBAAA;EJ2WA;;EInTU;IAxDV,4BAAA;EJ+WA;;EIvTU;IAxDV,4BAAA;EJmXA;;EI3TU;IAxDV,iBAAA;EJuXA;;EI/TU;IAxDV,4BAAA;EJ2XA;;EInUU;IAxDV,4BAAA;EJ+XA;;EI5TM;;IAEE,gBAAA;EJ+TR;;EI5TM;;IAEE,gBAAA;EJ+TR;;EItUM;;IAEE,sBAAA;EJyUR;;EItUM;;IAEE,sBAAA;EJyUR;;EIhVM;;IAEE,qBAAA;EJmVR;;EIhVM;;IAEE,qBAAA;EJmVR;;EI1VM;;IAEE,mBAAA;EJ6VR;;EI1VM;;IAEE,mBAAA;EJ6VR;;EIpWM;;IAEE,qBAAA;EJuWR;;EIpWM;;IAEE,qBAAA;EJuWR;;EI9WM;;IAEE,mBAAA;EJiXR;;EI9WM;;IAEE,mBAAA;EJiXR;AACF;ACraI;EGGE;IACE,YAAA;EJqaN;;EIlaI;IApCJ,cAAA;IACA,WAAA;EJ0cA;;EI5bA;IACE,cAAA;IACA,WAAA;EJ+bF;;EIjcA;IACE,cAAA;IACA,UAAA;EJocF;;EItcA;IACE,cAAA;IACA,qBAAA;EJycF;;EI3cA;IACE,cAAA;IACA,UAAA;EJ8cF;;EIhdA;IACE,cAAA;IACA,UAAA;EJmdF;;EIrdA;IACE,cAAA;IACA,qBAAA;EJwdF;;EIzbI;IAhDJ,cAAA;IACA,WAAA;EJ6eA;;EIxbQ;IA3DR,cAAA;IACA,oBAAA;EJufA;;EI7bQ;IA3DR,cAAA;IACA,qBAAA;EJ4fA;;EIlcQ;IA3DR,cAAA;IACA,UAAA;EJigBA;;EIvcQ;IA3DR,cAAA;IACA,qBAAA;EJsgBA;;EI5cQ;IA3DR,cAAA;IACA,qBAAA;EJ2gBA;;EIjdQ;IA3DR,cAAA;IACA,UAAA;EJghBA;;EItdQ;IA3DR,cAAA;IACA,qBAAA;EJqhBA;;EI3dQ;IA3DR,cAAA;IACA,qBAAA;EJ0hBA;;EIheQ;IA3DR,cAAA;IACA,UAAA;EJ+hBA;;EIreQ;IA3DR,cAAA;IACA,qBAAA;EJoiBA;;EI1eQ;IA3DR,cAAA;IACA,qBAAA;EJyiBA;;EI/eQ;IA3DR,cAAA;IACA,WAAA;EJ8iBA;;EI5eU;IAxDV,eAAA;EJwiBA;;EIhfU;IAxDV,2BAAA;EJ4iBA;;EIpfU;IAxDV,4BAAA;EJgjBA;;EIxfU;IAxDV,iBAAA;EJojBA;;EI5fU;IAxDV,4BAAA;EJwjBA;;EIhgBU;IAxDV,4BAAA;EJ4jBA;;EIpgBU;IAxDV,iBAAA;EJgkBA;;EIxgBU;IAxDV,4BAAA;EJokBA;;EI5gBU;IAxDV,4BAAA;EJwkBA;;EIhhBU;IAxDV,iBAAA;EJ4kBA;;EIphBU;IAxDV,4BAAA;EJglBA;;EIxhBU;IAxDV,4BAAA;EJolBA;;EIjhBM;;IAEE,gBAAA;EJohBR;;EIjhBM;;IAEE,gBAAA;EJohBR;;EI3hBM;;IAEE,sBAAA;EJ8hBR;;EI3hBM;;IAEE,sBAAA;EJ8hBR;;EIriBM;;IAEE,qBAAA;EJwiBR;;EIriBM;;IAEE,qBAAA;EJwiBR;;EI/iBM;;IAEE,mBAAA;EJkjBR;;EI/iBM;;IAEE,mBAAA;EJkjBR;;EIzjBM;;IAEE,qBAAA;EJ4jBR;;EIzjBM;;IAEE,qBAAA;EJ4jBR;;EInkBM;;IAEE,mBAAA;EJskBR;;EInkBM;;IAEE,mBAAA;EJskBR;AACF;AC1nBI;EGGE;IACE,YAAA;EJ0nBN;;EIvnBI;IApCJ,cAAA;IACA,WAAA;EJ+pBA;;EIjpBA;IACE,cAAA;IACA,WAAA;EJopBF;;EItpBA;IACE,cAAA;IACA,UAAA;EJypBF;;EI3pBA;IACE,cAAA;IACA,qBAAA;EJ8pBF;;EIhqBA;IACE,cAAA;IACA,UAAA;EJmqBF;;EIrqBA;IACE,cAAA;IACA,UAAA;EJwqBF;;EI1qBA;IACE,cAAA;IACA,qBAAA;EJ6qBF;;EI9oBI;IAhDJ,cAAA;IACA,WAAA;EJksBA;;EI7oBQ;IA3DR,cAAA;IACA,oBAAA;EJ4sBA;;EIlpBQ;IA3DR,cAAA;IACA,qBAAA;EJitBA;;EIvpBQ;IA3DR,cAAA;IACA,UAAA;EJstBA;;EI5pBQ;IA3DR,cAAA;IACA,qBAAA;EJ2tBA;;EIjqBQ;IA3DR,cAAA;IACA,qBAAA;EJguBA;;EItqBQ;IA3DR,cAAA;IACA,UAAA;EJquBA;;EI3qBQ;IA3DR,cAAA;IACA,qBAAA;EJ0uBA;;EIhrBQ;IA3DR,cAAA;IACA,qBAAA;EJ+uBA;;EIrrBQ;IA3DR,cAAA;IACA,UAAA;EJovBA;;EI1rBQ;IA3DR,cAAA;IACA,qBAAA;EJyvBA;;EI/rBQ;IA3DR,cAAA;IACA,qBAAA;EJ8vBA;;EIpsBQ;IA3DR,cAAA;IACA,WAAA;EJmwBA;;EIjsBU;IAxDV,eAAA;EJ6vBA;;EIrsBU;IAxDV,2BAAA;EJiwBA;;EIzsBU;IAxDV,4BAAA;EJqwBA;;EI7sBU;IAxDV,iBAAA;EJywBA;;EIjtBU;IAxDV,4BAAA;EJ6wBA;;EIrtBU;IAxDV,4BAAA;EJixBA;;EIztBU;IAxDV,iBAAA;EJqxBA;;EI7tBU;IAxDV,4BAAA;EJyxBA;;EIjuBU;IAxDV,4BAAA;EJ6xBA;;EIruBU;IAxDV,iBAAA;EJiyBA;;EIzuBU;IAxDV,4BAAA;EJqyBA;;EI7uBU;IAxDV,4BAAA;EJyyBA;;EItuBM;;IAEE,gBAAA;EJyuBR;;EItuBM;;IAEE,gBAAA;EJyuBR;;EIhvBM;;IAEE,sBAAA;EJmvBR;;EIhvBM;;IAEE,sBAAA;EJmvBR;;EI1vBM;;IAEE,qBAAA;EJ6vBR;;EI1vBM;;IAEE,qBAAA;EJ6vBR;;EIpwBM;;IAEE,mBAAA;EJuwBR;;EIpwBM;;IAEE,mBAAA;EJuwBR;;EI9wBM;;IAEE,qBAAA;EJixBR;;EI9wBM;;IAEE,qBAAA;EJixBR;;EIxxBM;;IAEE,mBAAA;EJ2xBR;;EIxxBM;;IAEE,mBAAA;EJ2xBR;AACF;AC/0BI;EGGE;IACE,YAAA;EJ+0BN;;EI50BI;IApCJ,cAAA;IACA,WAAA;EJo3BA;;EIt2BA;IACE,cAAA;IACA,WAAA;EJy2BF;;EI32BA;IACE,cAAA;IACA,UAAA;EJ82BF;;EIh3BA;IACE,cAAA;IACA,qBAAA;EJm3BF;;EIr3BA;IACE,cAAA;IACA,UAAA;EJw3BF;;EI13BA;IACE,cAAA;IACA,UAAA;EJ63BF;;EI/3BA;IACE,cAAA;IACA,qBAAA;EJk4BF;;EIn2BI;IAhDJ,cAAA;IACA,WAAA;EJu5BA;;EIl2BQ;IA3DR,cAAA;IACA,oBAAA;EJi6BA;;EIv2BQ;IA3DR,cAAA;IACA,qBAAA;EJs6BA;;EI52BQ;IA3DR,cAAA;IACA,UAAA;EJ26BA;;EIj3BQ;IA3DR,cAAA;IACA,qBAAA;EJg7BA;;EIt3BQ;IA3DR,cAAA;IACA,qBAAA;EJq7BA;;EI33BQ;IA3DR,cAAA;IACA,UAAA;EJ07BA;;EIh4BQ;IA3DR,cAAA;IACA,qBAAA;EJ+7BA;;EIr4BQ;IA3DR,cAAA;IACA,qBAAA;EJo8BA;;EI14BQ;IA3DR,cAAA;IACA,UAAA;EJy8BA;;EI/4BQ;IA3DR,cAAA;IACA,qBAAA;EJ88BA;;EIp5BQ;IA3DR,cAAA;IACA,qBAAA;EJm9BA;;EIz5BQ;IA3DR,cAAA;IACA,WAAA;EJw9BA;;EIt5BU;IAxDV,eAAA;EJk9BA;;EI15BU;IAxDV,2BAAA;EJs9BA;;EI95BU;IAxDV,4BAAA;EJ09BA;;EIl6BU;IAxDV,iBAAA;EJ89BA;;EIt6BU;IAxDV,4BAAA;EJk+BA;;EI16BU;IAxDV,4BAAA;EJs+BA;;EI96BU;IAxDV,iBAAA;EJ0+BA;;EIl7BU;IAxDV,4BAAA;EJ8+BA;;EIt7BU;IAxDV,4BAAA;EJk/BA;;EI17BU;IAxDV,iBAAA;EJs/BA;;EI97BU;IAxDV,4BAAA;EJ0/BA;;EIl8BU;IAxDV,4BAAA;EJ8/BA;;EI37BM;;IAEE,gBAAA;EJ87BR;;EI37BM;;IAEE,gBAAA;EJ87BR;;EIr8BM;;IAEE,sBAAA;EJw8BR;;EIr8BM;;IAEE,sBAAA;EJw8BR;;EI/8BM;;IAEE,qBAAA;EJk9BR;;EI/8BM;;IAEE,qBAAA;EJk9BR;;EIz9BM;;IAEE,mBAAA;EJ49BR;;EIz9BM;;IAEE,mBAAA;EJ49BR;;EIn+BM;;IAEE,qBAAA;EJs+BR;;EIn+BM;;IAEE,qBAAA;EJs+BR;;EI7+BM;;IAEE,mBAAA;EJg/BR;;EI7+BM;;IAEE,mBAAA;EJg/BR;AACF;ACpiCI;EGGE;IACE,YAAA;EJoiCN;;EIjiCI;IApCJ,cAAA;IACA,WAAA;EJykCA;;EI3jCA;IACE,cAAA;IACA,WAAA;EJ8jCF;;EIhkCA;IACE,cAAA;IACA,UAAA;EJmkCF;;EIrkCA;IACE,cAAA;IACA,qBAAA;EJwkCF;;EI1kCA;IACE,cAAA;IACA,UAAA;EJ6kCF;;EI/kCA;IACE,cAAA;IACA,UAAA;EJklCF;;EIplCA;IACE,cAAA;IACA,qBAAA;EJulCF;;EIxjCI;IAhDJ,cAAA;IACA,WAAA;EJ4mCA;;EIvjCQ;IA3DR,cAAA;IACA,oBAAA;EJsnCA;;EI5jCQ;IA3DR,cAAA;IACA,qBAAA;EJ2nCA;;EIjkCQ;IA3DR,cAAA;IACA,UAAA;EJgoCA;;EItkCQ;IA3DR,cAAA;IACA,qBAAA;EJqoCA;;EI3kCQ;IA3DR,cAAA;IACA,qBAAA;EJ0oCA;;EIhlCQ;IA3DR,cAAA;IACA,UAAA;EJ+oCA;;EIrlCQ;IA3DR,cAAA;IACA,qBAAA;EJopCA;;EI1lCQ;IA3DR,cAAA;IACA,qBAAA;EJypCA;;EI/lCQ;IA3DR,cAAA;IACA,UAAA;EJ8pCA;;EIpmCQ;IA3DR,cAAA;IACA,qBAAA;EJmqCA;;EIzmCQ;IA3DR,cAAA;IACA,qBAAA;EJwqCA;;EI9mCQ;IA3DR,cAAA;IACA,WAAA;EJ6qCA;;EI3mCU;IAxDV,eAAA;EJuqCA;;EI/mCU;IAxDV,2BAAA;EJ2qCA;;EInnCU;IAxDV,4BAAA;EJ+qCA;;EIvnCU;IAxDV,iBAAA;EJmrCA;;EI3nCU;IAxDV,4BAAA;EJurCA;;EI/nCU;IAxDV,4BAAA;EJ2rCA;;EInoCU;IAxDV,iBAAA;EJ+rCA;;EIvoCU;IAxDV,4BAAA;EJmsCA;;EI3oCU;IAxDV,4BAAA;EJusCA;;EI/oCU;IAxDV,iBAAA;EJ2sCA;;EInpCU;IAxDV,4BAAA;EJ+sCA;;EIvpCU;IAxDV,4BAAA;EJmtCA;;EIhpCM;;IAEE,gBAAA;EJmpCR;;EIhpCM;;IAEE,gBAAA;EJmpCR;;EI1pCM;;IAEE,sBAAA;EJ6pCR;;EI1pCM;;IAEE,sBAAA;EJ6pCR;;EIpqCM;;IAEE,qBAAA;EJuqCR;;EIpqCM;;IAEE,qBAAA;EJuqCR;;EI9qCM;;IAEE,mBAAA;EJirCR;;EI9qCM;;IAEE,mBAAA;EJirCR;;EIxrCM;;IAEE,qBAAA;EJ2rCR;;EIxrCM;;IAEE,qBAAA;EJ2rCR;;EIlsCM;;IAEE,mBAAA;EJqsCR;;EIlsCM;;IAEE,mBAAA;EJqsCR;AACF;AKvwCM;EAEI,0BAAA;ALwwCV;;AK1wCM;EAEI,gCAAA;AL4wCV;;AK9wCM;EAEI,yBAAA;ALgxCV;;AKlxCM;EAEI,wBAAA;ALoxCV;;AKtxCM;EAEI,yBAAA;ALwxCV;;AK1xCM;EAEI,6BAAA;AL4xCV;;AK9xCM;EAEI,8BAAA;ALgyCV;;AKlyCM;EAEI,wBAAA;ALoyCV;;AKtyCM;EAEI,+BAAA;ALwyCV;;AK1yCM;EAEI,wBAAA;AL4yCV;;AK9yCM;EAEI,yBAAA;ALgzCV;;AKlzCM;EAEI,8BAAA;ALozCV;;AKtzCM;EAEI,iCAAA;ALwzCV;;AK1zCM;EAEI,sCAAA;AL4zCV;;AK9zCM;EAEI,yCAAA;ALg0CV;;AKl0CM;EAEI,uBAAA;ALo0CV;;AKt0CM;EAEI,uBAAA;ALw0CV;;AK10CM;EAEI,yBAAA;AL40CV;;AK90CM;EAEI,yBAAA;ALg1CV;;AKl1CM;EAEI,0BAAA;ALo1CV;;AKt1CM;EAEI,4BAAA;ALw1CV;;AK11CM;EAEI,kCAAA;AL41CV;;AK91CM;EAEI,sCAAA;ALg2CV;;AKl2CM;EAEI,oCAAA;ALo2CV;;AKt2CM;EAEI,kCAAA;ALw2CV;;AK12CM;EAEI,yCAAA;AL42CV;;AK92CM;EAEI,wCAAA;ALg3CV;;AKl3CM;EAEI,wCAAA;ALo3CV;;AKt3CM;EAEI,kCAAA;ALw3CV;;AK13CM;EAEI,gCAAA;AL43CV;;AK93CM;EAEI,8BAAA;ALg4CV;;AKl4CM;EAEI,gCAAA;ALo4CV;;AKt4CM;EAEI,+BAAA;ALw4CV;;AK14CM;EAEI,oCAAA;AL44CV;;AK94CM;EAEI,kCAAA;ALg5CV;;AKl5CM;EAEI,gCAAA;ALo5CV;;AKt5CM;EAEI,uCAAA;ALw5CV;;AK15CM;EAEI,sCAAA;AL45CV;;AK95CM;EAEI,iCAAA;ALg6CV;;AKl6CM;EAEI,2BAAA;ALo6CV;;AKt6CM;EAEI,iCAAA;ALw6CV;;AK16CM;EAEI,+BAAA;AL46CV;;AK96CM;EAEI,6BAAA;ALg7CV;;AKl7CM;EAEI,+BAAA;ALo7CV;;AKt7CM;EAEI,8BAAA;ALw7CV;;AK17CM;EAEI,oBAAA;AL47CV;;AK97CM;EAEI,mBAAA;ALg8CV;;AKl8CM;EAEI,mBAAA;ALo8CV;;AKt8CM;EAEI,mBAAA;ALw8CV;;AK18CM;EAEI,mBAAA;AL48CV;;AK98CM;EAEI,mBAAA;ALg9CV;;AKl9CM;EAEI,mBAAA;ALo9CV;;AKt9CM;EAEI,mBAAA;ALw9CV;;AK19CM;EAEI,oBAAA;AL49CV;;AK99CM;EAEI,0BAAA;ALg+CV;;AKl+CM;EAEI,yBAAA;ALo+CV;;AKt+CM;EAEI,uBAAA;ALw+CV;;AK1+CM;EAEI,yBAAA;AL4+CV;;AK9+CM;EAEI,uBAAA;ALg/CV;;AKl/CM;EAEI,uBAAA;ALo/CV;;AKt/CM;EAEI,yBAAA;EAAA,0BAAA;ALy/CV;;AK3/CM;EAEI,+BAAA;EAAA,gCAAA;AL8/CV;;AKhgDM;EAEI,8BAAA;EAAA,+BAAA;ALmgDV;;AKrgDM;EAEI,4BAAA;EAAA,6BAAA;ALwgDV;;AK1gDM;EAEI,8BAAA;EAAA,+BAAA;AL6gDV;;AK/gDM;EAEI,4BAAA;EAAA,6BAAA;ALkhDV;;AKphDM;EAEI,4BAAA;EAAA,6BAAA;ALuhDV;;AKzhDM;EAEI,wBAAA;EAAA,2BAAA;AL4hDV;;AK9hDM;EAEI,8BAAA;EAAA,iCAAA;ALiiDV;;AKniDM;EAEI,6BAAA;EAAA,gCAAA;ALsiDV;;AKxiDM;EAEI,2BAAA;EAAA,8BAAA;AL2iDV;;AK7iDM;EAEI,6BAAA;EAAA,gCAAA;ALgjDV;;AKljDM;EAEI,2BAAA;EAAA,8BAAA;ALqjDV;;AKvjDM;EAEI,2BAAA;EAAA,8BAAA;AL0jDV;;AK5jDM;EAEI,wBAAA;AL8jDV;;AKhkDM;EAEI,8BAAA;ALkkDV;;AKpkDM;EAEI,6BAAA;ALskDV;;AKxkDM;EAEI,2BAAA;AL0kDV;;AK5kDM;EAEI,6BAAA;AL8kDV;;AKhlDM;EAEI,2BAAA;ALklDV;;AKplDM;EAEI,2BAAA;ALslDV;;AKxlDM;EAEI,yBAAA;AL0lDV;;AK5lDM;EAEI,+BAAA;AL8lDV;;AKhmDM;EAEI,8BAAA;ALkmDV;;AKpmDM;EAEI,4BAAA;ALsmDV;;AKxmDM;EAEI,8BAAA;AL0mDV;;AK5mDM;EAEI,4BAAA;AL8mDV;;AKhnDM;EAEI,4BAAA;ALknDV;;AKpnDM;EAEI,2BAAA;ALsnDV;;AKxnDM;EAEI,iCAAA;AL0nDV;;AK5nDM;EAEI,gCAAA;AL8nDV;;AKhoDM;EAEI,8BAAA;ALkoDV;;AKpoDM;EAEI,gCAAA;ALsoDV;;AKxoDM;EAEI,8BAAA;AL0oDV;;AK5oDM;EAEI,8BAAA;AL8oDV;;AKhpDM;EAEI,0BAAA;ALkpDV;;AKppDM;EAEI,gCAAA;ALspDV;;AKxpDM;EAEI,+BAAA;AL0pDV;;AK5pDM;EAEI,6BAAA;AL8pDV;;AKhqDM;EAEI,+BAAA;ALkqDV;;AKpqDM;EAEI,6BAAA;ALsqDV;;AKxqDM;EAEI,6BAAA;AL0qDV;;AK5qDM;EAEI,qBAAA;AL8qDV;;AKhrDM;EAEI,2BAAA;ALkrDV;;AKprDM;EAEI,0BAAA;ALsrDV;;AKxrDM;EAEI,wBAAA;AL0rDV;;AK5rDM;EAEI,0BAAA;AL8rDV;;AKhsDM;EAEI,wBAAA;ALksDV;;AKpsDM;EAEI,0BAAA;EAAA,2BAAA;ALusDV;;AKzsDM;EAEI,gCAAA;EAAA,iCAAA;AL4sDV;;AK9sDM;EAEI,+BAAA;EAAA,gCAAA;ALitDV;;AKntDM;EAEI,6BAAA;EAAA,8BAAA;ALstDV;;AKxtDM;EAEI,+BAAA;EAAA,gCAAA;AL2tDV;;AK7tDM;EAEI,6BAAA;EAAA,8BAAA;ALguDV;;AKluDM;EAEI,yBAAA;EAAA,4BAAA;ALquDV;;AKvuDM;EAEI,+BAAA;EAAA,kCAAA;AL0uDV;;AK5uDM;EAEI,8BAAA;EAAA,iCAAA;AL+uDV;;AKjvDM;EAEI,4BAAA;EAAA,+BAAA;ALovDV;;AKtvDM;EAEI,8BAAA;EAAA,iCAAA;ALyvDV;;AK3vDM;EAEI,4BAAA;EAAA,+BAAA;AL8vDV;;AKhwDM;EAEI,yBAAA;ALkwDV;;AKpwDM;EAEI,+BAAA;ALswDV;;AKxwDM;EAEI,8BAAA;AL0wDV;;AK5wDM;EAEI,4BAAA;AL8wDV;;AKhxDM;EAEI,8BAAA;ALkxDV;;AKpxDM;EAEI,4BAAA;ALsxDV;;AKxxDM;EAEI,0BAAA;AL0xDV;;AK5xDM;EAEI,gCAAA;AL8xDV;;AKhyDM;EAEI,+BAAA;ALkyDV;;AKpyDM;EAEI,6BAAA;ALsyDV;;AKxyDM;EAEI,+BAAA;AL0yDV;;AK5yDM;EAEI,6BAAA;AL8yDV;;AKhzDM;EAEI,4BAAA;ALkzDV;;AKpzDM;EAEI,kCAAA;ALszDV;;AKxzDM;EAEI,iCAAA;AL0zDV;;AK5zDM;EAEI,+BAAA;AL8zDV;;AKh0DM;EAEI,iCAAA;ALk0DV;;AKp0DM;EAEI,+BAAA;ALs0DV;;AKx0DM;EAEI,2BAAA;AL00DV;;AK50DM;EAEI,iCAAA;AL80DV;;AKh1DM;EAEI,gCAAA;ALk1DV;;AKp1DM;EAEI,8BAAA;ALs1DV;;AKx1DM;EAEI,gCAAA;AL01DV;;AK51DM;EAEI,8BAAA;AL81DV;;ACl1DI;EIdE;IAEI,0BAAA;ELm2DR;;EKr2DI;IAEI,gCAAA;ELu2DR;;EKz2DI;IAEI,yBAAA;EL22DR;;EK72DI;IAEI,wBAAA;EL+2DR;;EKj3DI;IAEI,yBAAA;ELm3DR;;EKr3DI;IAEI,6BAAA;ELu3DR;;EKz3DI;IAEI,8BAAA;EL23DR;;EK73DI;IAEI,wBAAA;EL+3DR;;EKj4DI;IAEI,+BAAA;ELm4DR;;EKr4DI;IAEI,wBAAA;ELu4DR;;EKz4DI;IAEI,yBAAA;EL24DR;;EK74DI;IAEI,8BAAA;EL+4DR;;EKj5DI;IAEI,iCAAA;ELm5DR;;EKr5DI;IAEI,sCAAA;ELu5DR;;EKz5DI;IAEI,yCAAA;EL25DR;;EK75DI;IAEI,uBAAA;EL+5DR;;EKj6DI;IAEI,uBAAA;ELm6DR;;EKr6DI;IAEI,yBAAA;ELu6DR;;EKz6DI;IAEI,yBAAA;EL26DR;;EK76DI;IAEI,0BAAA;EL+6DR;;EKj7DI;IAEI,4BAAA;ELm7DR;;EKr7DI;IAEI,kCAAA;ELu7DR;;EKz7DI;IAEI,sCAAA;EL27DR;;EK77DI;IAEI,oCAAA;EL+7DR;;EKj8DI;IAEI,kCAAA;ELm8DR;;EKr8DI;IAEI,yCAAA;ELu8DR;;EKz8DI;IAEI,wCAAA;EL28DR;;EK78DI;IAEI,wCAAA;EL+8DR;;EKj9DI;IAEI,kCAAA;ELm9DR;;EKr9DI;IAEI,gCAAA;ELu9DR;;EKz9DI;IAEI,8BAAA;EL29DR;;EK79DI;IAEI,gCAAA;EL+9DR;;EKj+DI;IAEI,+BAAA;ELm+DR;;EKr+DI;IAEI,oCAAA;ELu+DR;;EKz+DI;IAEI,kCAAA;EL2+DR;;EK7+DI;IAEI,gCAAA;EL++DR;;EKj/DI;IAEI,uCAAA;ELm/DR;;EKr/DI;IAEI,sCAAA;ELu/DR;;EKz/DI;IAEI,iCAAA;EL2/DR;;EK7/DI;IAEI,2BAAA;EL+/DR;;EKjgEI;IAEI,iCAAA;ELmgER;;EKrgEI;IAEI,+BAAA;ELugER;;EKzgEI;IAEI,6BAAA;EL2gER;;EK7gEI;IAEI,+BAAA;EL+gER;;EKjhEI;IAEI,8BAAA;ELmhER;;EKrhEI;IAEI,oBAAA;ELuhER;;EKzhEI;IAEI,mBAAA;EL2hER;;EK7hEI;IAEI,mBAAA;EL+hER;;EKjiEI;IAEI,mBAAA;ELmiER;;EKriEI;IAEI,mBAAA;ELuiER;;EKziEI;IAEI,mBAAA;EL2iER;;EK7iEI;IAEI,mBAAA;EL+iER;;EKjjEI;IAEI,mBAAA;ELmjER;;EKrjEI;IAEI,oBAAA;ELujER;;EKzjEI;IAEI,0BAAA;EL2jER;;EK7jEI;IAEI,yBAAA;EL+jER;;EKjkEI;IAEI,uBAAA;ELmkER;;EKrkEI;IAEI,yBAAA;ELukER;;EKzkEI;IAEI,uBAAA;EL2kER;;EK7kEI;IAEI,uBAAA;EL+kER;;EKjlEI;IAEI,yBAAA;IAAA,0BAAA;ELolER;;EKtlEI;IAEI,+BAAA;IAAA,gCAAA;ELylER;;EK3lEI;IAEI,8BAAA;IAAA,+BAAA;EL8lER;;EKhmEI;IAEI,4BAAA;IAAA,6BAAA;ELmmER;;EKrmEI;IAEI,8BAAA;IAAA,+BAAA;ELwmER;;EK1mEI;IAEI,4BAAA;IAAA,6BAAA;EL6mER;;EK/mEI;IAEI,4BAAA;IAAA,6BAAA;ELknER;;EKpnEI;IAEI,wBAAA;IAAA,2BAAA;ELunER;;EKznEI;IAEI,8BAAA;IAAA,iCAAA;EL4nER;;EK9nEI;IAEI,6BAAA;IAAA,gCAAA;ELioER;;EKnoEI;IAEI,2BAAA;IAAA,8BAAA;ELsoER;;EKxoEI;IAEI,6BAAA;IAAA,gCAAA;EL2oER;;EK7oEI;IAEI,2BAAA;IAAA,8BAAA;ELgpER;;EKlpEI;IAEI,2BAAA;IAAA,8BAAA;ELqpER;;EKvpEI;IAEI,wBAAA;ELypER;;EK3pEI;IAEI,8BAAA;EL6pER;;EK/pEI;IAEI,6BAAA;ELiqER;;EKnqEI;IAEI,2BAAA;ELqqER;;EKvqEI;IAEI,6BAAA;ELyqER;;EK3qEI;IAEI,2BAAA;EL6qER;;EK/qEI;IAEI,2BAAA;ELirER;;EKnrEI;IAEI,yBAAA;ELqrER;;EKvrEI;IAEI,+BAAA;ELyrER;;EK3rEI;IAEI,8BAAA;EL6rER;;EK/rEI;IAEI,4BAAA;ELisER;;EKnsEI;IAEI,8BAAA;ELqsER;;EKvsEI;IAEI,4BAAA;ELysER;;EK3sEI;IAEI,4BAAA;EL6sER;;EK/sEI;IAEI,2BAAA;ELitER;;EKntEI;IAEI,iCAAA;ELqtER;;EKvtEI;IAEI,gCAAA;ELytER;;EK3tEI;IAEI,8BAAA;EL6tER;;EK/tEI;IAEI,gCAAA;ELiuER;;EKnuEI;IAEI,8BAAA;ELquER;;EKvuEI;IAEI,8BAAA;ELyuER;;EK3uEI;IAEI,0BAAA;EL6uER;;EK/uEI;IAEI,gCAAA;ELivER;;EKnvEI;IAEI,+BAAA;ELqvER;;EKvvEI;IAEI,6BAAA;ELyvER;;EK3vEI;IAEI,+BAAA;EL6vER;;EK/vEI;IAEI,6BAAA;ELiwER;;EKnwEI;IAEI,6BAAA;ELqwER;;EKvwEI;IAEI,qBAAA;ELywER;;EK3wEI;IAEI,2BAAA;EL6wER;;EK/wEI;IAEI,0BAAA;ELixER;;EKnxEI;IAEI,wBAAA;ELqxER;;EKvxEI;IAEI,0BAAA;ELyxER;;EK3xEI;IAEI,wBAAA;EL6xER;;EK/xEI;IAEI,0BAAA;IAAA,2BAAA;ELkyER;;EKpyEI;IAEI,gCAAA;IAAA,iCAAA;ELuyER;;EKzyEI;IAEI,+BAAA;IAAA,gCAAA;EL4yER;;EK9yEI;IAEI,6BAAA;IAAA,8BAAA;ELizER;;EKnzEI;IAEI,+BAAA;IAAA,gCAAA;ELszER;;EKxzEI;IAEI,6BAAA;IAAA,8BAAA;EL2zER;;EK7zEI;IAEI,yBAAA;IAAA,4BAAA;ELg0ER;;EKl0EI;IAEI,+BAAA;IAAA,kCAAA;ELq0ER;;EKv0EI;IAEI,8BAAA;IAAA,iCAAA;EL00ER;;EK50EI;IAEI,4BAAA;IAAA,+BAAA;EL+0ER;;EKj1EI;IAEI,8BAAA;IAAA,iCAAA;ELo1ER;;EKt1EI;IAEI,4BAAA;IAAA,+BAAA;ELy1ER;;EK31EI;IAEI,yBAAA;EL61ER;;EK/1EI;IAEI,+BAAA;ELi2ER;;EKn2EI;IAEI,8BAAA;ELq2ER;;EKv2EI;IAEI,4BAAA;ELy2ER;;EK32EI;IAEI,8BAAA;EL62ER;;EK/2EI;IAEI,4BAAA;ELi3ER;;EKn3EI;IAEI,0BAAA;ELq3ER;;EKv3EI;IAEI,gCAAA;ELy3ER;;EK33EI;IAEI,+BAAA;EL63ER;;EK/3EI;IAEI,6BAAA;ELi4ER;;EKn4EI;IAEI,+BAAA;ELq4ER;;EKv4EI;IAEI,6BAAA;ELy4ER;;EK34EI;IAEI,4BAAA;EL64ER;;EK/4EI;IAEI,kCAAA;ELi5ER;;EKn5EI;IAEI,iCAAA;ELq5ER;;EKv5EI;IAEI,+BAAA;ELy5ER;;EK35EI;IAEI,iCAAA;EL65ER;;EK/5EI;IAEI,+BAAA;ELi6ER;;EKn6EI;IAEI,2BAAA;ELq6ER;;EKv6EI;IAEI,iCAAA;ELy6ER;;EK36EI;IAEI,gCAAA;EL66ER;;EK/6EI;IAEI,8BAAA;ELi7ER;;EKn7EI;IAEI,gCAAA;ELq7ER;;EKv7EI;IAEI,8BAAA;ELy7ER;AACF;AC96EI;EIdE;IAEI,0BAAA;EL87ER;;EKh8EI;IAEI,gCAAA;ELk8ER;;EKp8EI;IAEI,yBAAA;ELs8ER;;EKx8EI;IAEI,wBAAA;EL08ER;;EK58EI;IAEI,yBAAA;EL88ER;;EKh9EI;IAEI,6BAAA;ELk9ER;;EKp9EI;IAEI,8BAAA;ELs9ER;;EKx9EI;IAEI,wBAAA;EL09ER;;EK59EI;IAEI,+BAAA;EL89ER;;EKh+EI;IAEI,wBAAA;ELk+ER;;EKp+EI;IAEI,yBAAA;ELs+ER;;EKx+EI;IAEI,8BAAA;EL0+ER;;EK5+EI;IAEI,iCAAA;EL8+ER;;EKh/EI;IAEI,sCAAA;ELk/ER;;EKp/EI;IAEI,yCAAA;ELs/ER;;EKx/EI;IAEI,uBAAA;EL0/ER;;EK5/EI;IAEI,uBAAA;EL8/ER;;EKhgFI;IAEI,yBAAA;ELkgFR;;EKpgFI;IAEI,yBAAA;ELsgFR;;EKxgFI;IAEI,0BAAA;EL0gFR;;EK5gFI;IAEI,4BAAA;EL8gFR;;EKhhFI;IAEI,kCAAA;ELkhFR;;EKphFI;IAEI,sCAAA;ELshFR;;EKxhFI;IAEI,oCAAA;EL0hFR;;EK5hFI;IAEI,kCAAA;EL8hFR;;EKhiFI;IAEI,yCAAA;ELkiFR;;EKpiFI;IAEI,wCAAA;ELsiFR;;EKxiFI;IAEI,wCAAA;EL0iFR;;EK5iFI;IAEI,kCAAA;EL8iFR;;EKhjFI;IAEI,gCAAA;ELkjFR;;EKpjFI;IAEI,8BAAA;ELsjFR;;EKxjFI;IAEI,gCAAA;EL0jFR;;EK5jFI;IAEI,+BAAA;EL8jFR;;EKhkFI;IAEI,oCAAA;ELkkFR;;EKpkFI;IAEI,kCAAA;ELskFR;;EKxkFI;IAEI,gCAAA;EL0kFR;;EK5kFI;IAEI,uCAAA;EL8kFR;;EKhlFI;IAEI,sCAAA;ELklFR;;EKplFI;IAEI,iCAAA;ELslFR;;EKxlFI;IAEI,2BAAA;EL0lFR;;EK5lFI;IAEI,iCAAA;EL8lFR;;EKhmFI;IAEI,+BAAA;ELkmFR;;EKpmFI;IAEI,6BAAA;ELsmFR;;EKxmFI;IAEI,+BAAA;EL0mFR;;EK5mFI;IAEI,8BAAA;EL8mFR;;EKhnFI;IAEI,oBAAA;ELknFR;;EKpnFI;IAEI,mBAAA;ELsnFR;;EKxnFI;IAEI,mBAAA;EL0nFR;;EK5nFI;IAEI,mBAAA;EL8nFR;;EKhoFI;IAEI,mBAAA;ELkoFR;;EKpoFI;IAEI,mBAAA;ELsoFR;;EKxoFI;IAEI,mBAAA;EL0oFR;;EK5oFI;IAEI,mBAAA;EL8oFR;;EKhpFI;IAEI,oBAAA;ELkpFR;;EKppFI;IAEI,0BAAA;ELspFR;;EKxpFI;IAEI,yBAAA;EL0pFR;;EK5pFI;IAEI,uBAAA;EL8pFR;;EKhqFI;IAEI,yBAAA;ELkqFR;;EKpqFI;IAEI,uBAAA;ELsqFR;;EKxqFI;IAEI,uBAAA;EL0qFR;;EK5qFI;IAEI,yBAAA;IAAA,0BAAA;EL+qFR;;EKjrFI;IAEI,+BAAA;IAAA,gCAAA;ELorFR;;EKtrFI;IAEI,8BAAA;IAAA,+BAAA;ELyrFR;;EK3rFI;IAEI,4BAAA;IAAA,6BAAA;EL8rFR;;EKhsFI;IAEI,8BAAA;IAAA,+BAAA;ELmsFR;;EKrsFI;IAEI,4BAAA;IAAA,6BAAA;ELwsFR;;EK1sFI;IAEI,4BAAA;IAAA,6BAAA;EL6sFR;;EK/sFI;IAEI,wBAAA;IAAA,2BAAA;ELktFR;;EKptFI;IAEI,8BAAA;IAAA,iCAAA;ELutFR;;EKztFI;IAEI,6BAAA;IAAA,gCAAA;EL4tFR;;EK9tFI;IAEI,2BAAA;IAAA,8BAAA;ELiuFR;;EKnuFI;IAEI,6BAAA;IAAA,gCAAA;ELsuFR;;EKxuFI;IAEI,2BAAA;IAAA,8BAAA;EL2uFR;;EK7uFI;IAEI,2BAAA;IAAA,8BAAA;ELgvFR;;EKlvFI;IAEI,wBAAA;ELovFR;;EKtvFI;IAEI,8BAAA;ELwvFR;;EK1vFI;IAEI,6BAAA;EL4vFR;;EK9vFI;IAEI,2BAAA;ELgwFR;;EKlwFI;IAEI,6BAAA;ELowFR;;EKtwFI;IAEI,2BAAA;ELwwFR;;EK1wFI;IAEI,2BAAA;EL4wFR;;EK9wFI;IAEI,yBAAA;ELgxFR;;EKlxFI;IAEI,+BAAA;ELoxFR;;EKtxFI;IAEI,8BAAA;ELwxFR;;EK1xFI;IAEI,4BAAA;EL4xFR;;EK9xFI;IAEI,8BAAA;ELgyFR;;EKlyFI;IAEI,4BAAA;ELoyFR;;EKtyFI;IAEI,4BAAA;ELwyFR;;EK1yFI;IAEI,2BAAA;EL4yFR;;EK9yFI;IAEI,iCAAA;ELgzFR;;EKlzFI;IAEI,gCAAA;ELozFR;;EKtzFI;IAEI,8BAAA;ELwzFR;;EK1zFI;IAEI,gCAAA;EL4zFR;;EK9zFI;IAEI,8BAAA;ELg0FR;;EKl0FI;IAEI,8BAAA;ELo0FR;;EKt0FI;IAEI,0BAAA;ELw0FR;;EK10FI;IAEI,gCAAA;EL40FR;;EK90FI;IAEI,+BAAA;ELg1FR;;EKl1FI;IAEI,6BAAA;ELo1FR;;EKt1FI;IAEI,+BAAA;ELw1FR;;EK11FI;IAEI,6BAAA;EL41FR;;EK91FI;IAEI,6BAAA;ELg2FR;;EKl2FI;IAEI,qBAAA;ELo2FR;;EKt2FI;IAEI,2BAAA;ELw2FR;;EK12FI;IAEI,0BAAA;EL42FR;;EK92FI;IAEI,wBAAA;ELg3FR;;EKl3FI;IAEI,0BAAA;ELo3FR;;EKt3FI;IAEI,wBAAA;ELw3FR;;EK13FI;IAEI,0BAAA;IAAA,2BAAA;EL63FR;;EK/3FI;IAEI,gCAAA;IAAA,iCAAA;ELk4FR;;EKp4FI;IAEI,+BAAA;IAAA,gCAAA;ELu4FR;;EKz4FI;IAEI,6BAAA;IAAA,8BAAA;EL44FR;;EK94FI;IAEI,+BAAA;IAAA,gCAAA;ELi5FR;;EKn5FI;IAEI,6BAAA;IAAA,8BAAA;ELs5FR;;EKx5FI;IAEI,yBAAA;IAAA,4BAAA;EL25FR;;EK75FI;IAEI,+BAAA;IAAA,kCAAA;ELg6FR;;EKl6FI;IAEI,8BAAA;IAAA,iCAAA;ELq6FR;;EKv6FI;IAEI,4BAAA;IAAA,+BAAA;EL06FR;;EK56FI;IAEI,8BAAA;IAAA,iCAAA;EL+6FR;;EKj7FI;IAEI,4BAAA;IAAA,+BAAA;ELo7FR;;EKt7FI;IAEI,yBAAA;ELw7FR;;EK17FI;IAEI,+BAAA;EL47FR;;EK97FI;IAEI,8BAAA;ELg8FR;;EKl8FI;IAEI,4BAAA;ELo8FR;;EKt8FI;IAEI,8BAAA;ELw8FR;;EK18FI;IAEI,4BAAA;EL48FR;;EK98FI;IAEI,0BAAA;ELg9FR;;EKl9FI;IAEI,gCAAA;ELo9FR;;EKt9FI;IAEI,+BAAA;ELw9FR;;EK19FI;IAEI,6BAAA;EL49FR;;EK99FI;IAEI,+BAAA;ELg+FR;;EKl+FI;IAEI,6BAAA;ELo+FR;;EKt+FI;IAEI,4BAAA;ELw+FR;;EK1+FI;IAEI,kCAAA;EL4+FR;;EK9+FI;IAEI,iCAAA;ELg/FR;;EKl/FI;IAEI,+BAAA;ELo/FR;;EKt/FI;IAEI,iCAAA;ELw/FR;;EK1/FI;IAEI,+BAAA;EL4/FR;;EK9/FI;IAEI,2BAAA;ELggGR;;EKlgGI;IAEI,iCAAA;ELogGR;;EKtgGI;IAEI,gCAAA;ELwgGR;;EK1gGI;IAEI,8BAAA;EL4gGR;;EK9gGI;IAEI,gCAAA;ELghGR;;EKlhGI;IAEI,8BAAA;ELohGR;AACF;ACzgGI;EIdE;IAEI,0BAAA;ELyhGR;;EK3hGI;IAEI,gCAAA;EL6hGR;;EK/hGI;IAEI,yBAAA;ELiiGR;;EKniGI;IAEI,wBAAA;ELqiGR;;EKviGI;IAEI,yBAAA;ELyiGR;;EK3iGI;IAEI,6BAAA;EL6iGR;;EK/iGI;IAEI,8BAAA;ELijGR;;EKnjGI;IAEI,wBAAA;ELqjGR;;EKvjGI;IAEI,+BAAA;ELyjGR;;EK3jGI;IAEI,wBAAA;EL6jGR;;EK/jGI;IAEI,yBAAA;ELikGR;;EKnkGI;IAEI,8BAAA;ELqkGR;;EKvkGI;IAEI,iCAAA;ELykGR;;EK3kGI;IAEI,sCAAA;EL6kGR;;EK/kGI;IAEI,yCAAA;ELilGR;;EKnlGI;IAEI,uBAAA;ELqlGR;;EKvlGI;IAEI,uBAAA;ELylGR;;EK3lGI;IAEI,yBAAA;EL6lGR;;EK/lGI;IAEI,yBAAA;ELimGR;;EKnmGI;IAEI,0BAAA;ELqmGR;;EKvmGI;IAEI,4BAAA;ELymGR;;EK3mGI;IAEI,kCAAA;EL6mGR;;EK/mGI;IAEI,sCAAA;ELinGR;;EKnnGI;IAEI,oCAAA;ELqnGR;;EKvnGI;IAEI,kCAAA;ELynGR;;EK3nGI;IAEI,yCAAA;EL6nGR;;EK/nGI;IAEI,wCAAA;ELioGR;;EKnoGI;IAEI,wCAAA;ELqoGR;;EKvoGI;IAEI,kCAAA;ELyoGR;;EK3oGI;IAEI,gCAAA;EL6oGR;;EK/oGI;IAEI,8BAAA;ELipGR;;EKnpGI;IAEI,gCAAA;ELqpGR;;EKvpGI;IAEI,+BAAA;ELypGR;;EK3pGI;IAEI,oCAAA;EL6pGR;;EK/pGI;IAEI,kCAAA;ELiqGR;;EKnqGI;IAEI,gCAAA;ELqqGR;;EKvqGI;IAEI,uCAAA;ELyqGR;;EK3qGI;IAEI,sCAAA;EL6qGR;;EK/qGI;IAEI,iCAAA;ELirGR;;EKnrGI;IAEI,2BAAA;ELqrGR;;EKvrGI;IAEI,iCAAA;ELyrGR;;EK3rGI;IAEI,+BAAA;EL6rGR;;EK/rGI;IAEI,6BAAA;ELisGR;;EKnsGI;IAEI,+BAAA;ELqsGR;;EKvsGI;IAEI,8BAAA;ELysGR;;EK3sGI;IAEI,oBAAA;EL6sGR;;EK/sGI;IAEI,mBAAA;ELitGR;;EKntGI;IAEI,mBAAA;ELqtGR;;EKvtGI;IAEI,mBAAA;ELytGR;;EK3tGI;IAEI,mBAAA;EL6tGR;;EK/tGI;IAEI,mBAAA;ELiuGR;;EKnuGI;IAEI,mBAAA;ELquGR;;EKvuGI;IAEI,mBAAA;ELyuGR;;EK3uGI;IAEI,oBAAA;EL6uGR;;EK/uGI;IAEI,0BAAA;ELivGR;;EKnvGI;IAEI,yBAAA;ELqvGR;;EKvvGI;IAEI,uBAAA;ELyvGR;;EK3vGI;IAEI,yBAAA;EL6vGR;;EK/vGI;IAEI,uBAAA;ELiwGR;;EKnwGI;IAEI,uBAAA;ELqwGR;;EKvwGI;IAEI,yBAAA;IAAA,0BAAA;EL0wGR;;EK5wGI;IAEI,+BAAA;IAAA,gCAAA;EL+wGR;;EKjxGI;IAEI,8BAAA;IAAA,+BAAA;ELoxGR;;EKtxGI;IAEI,4BAAA;IAAA,6BAAA;ELyxGR;;EK3xGI;IAEI,8BAAA;IAAA,+BAAA;EL8xGR;;EKhyGI;IAEI,4BAAA;IAAA,6BAAA;ELmyGR;;EKryGI;IAEI,4BAAA;IAAA,6BAAA;ELwyGR;;EK1yGI;IAEI,wBAAA;IAAA,2BAAA;EL6yGR;;EK/yGI;IAEI,8BAAA;IAAA,iCAAA;ELkzGR;;EKpzGI;IAEI,6BAAA;IAAA,gCAAA;ELuzGR;;EKzzGI;IAEI,2BAAA;IAAA,8BAAA;EL4zGR;;EK9zGI;IAEI,6BAAA;IAAA,gCAAA;ELi0GR;;EKn0GI;IAEI,2BAAA;IAAA,8BAAA;ELs0GR;;EKx0GI;IAEI,2BAAA;IAAA,8BAAA;EL20GR;;EK70GI;IAEI,wBAAA;EL+0GR;;EKj1GI;IAEI,8BAAA;ELm1GR;;EKr1GI;IAEI,6BAAA;ELu1GR;;EKz1GI;IAEI,2BAAA;EL21GR;;EK71GI;IAEI,6BAAA;EL+1GR;;EKj2GI;IAEI,2BAAA;ELm2GR;;EKr2GI;IAEI,2BAAA;ELu2GR;;EKz2GI;IAEI,yBAAA;EL22GR;;EK72GI;IAEI,+BAAA;EL+2GR;;EKj3GI;IAEI,8BAAA;ELm3GR;;EKr3GI;IAEI,4BAAA;ELu3GR;;EKz3GI;IAEI,8BAAA;EL23GR;;EK73GI;IAEI,4BAAA;EL+3GR;;EKj4GI;IAEI,4BAAA;ELm4GR;;EKr4GI;IAEI,2BAAA;ELu4GR;;EKz4GI;IAEI,iCAAA;EL24GR;;EK74GI;IAEI,gCAAA;EL+4GR;;EKj5GI;IAEI,8BAAA;ELm5GR;;EKr5GI;IAEI,gCAAA;ELu5GR;;EKz5GI;IAEI,8BAAA;EL25GR;;EK75GI;IAEI,8BAAA;EL+5GR;;EKj6GI;IAEI,0BAAA;ELm6GR;;EKr6GI;IAEI,gCAAA;ELu6GR;;EKz6GI;IAEI,+BAAA;EL26GR;;EK76GI;IAEI,6BAAA;EL+6GR;;EKj7GI;IAEI,+BAAA;ELm7GR;;EKr7GI;IAEI,6BAAA;ELu7GR;;EKz7GI;IAEI,6BAAA;EL27GR;;EK77GI;IAEI,qBAAA;EL+7GR;;EKj8GI;IAEI,2BAAA;ELm8GR;;EKr8GI;IAEI,0BAAA;ELu8GR;;EKz8GI;IAEI,wBAAA;EL28GR;;EK78GI;IAEI,0BAAA;EL+8GR;;EKj9GI;IAEI,wBAAA;ELm9GR;;EKr9GI;IAEI,0BAAA;IAAA,2BAAA;ELw9GR;;EK19GI;IAEI,gCAAA;IAAA,iCAAA;EL69GR;;EK/9GI;IAEI,+BAAA;IAAA,gCAAA;ELk+GR;;EKp+GI;IAEI,6BAAA;IAAA,8BAAA;ELu+GR;;EKz+GI;IAEI,+BAAA;IAAA,gCAAA;EL4+GR;;EK9+GI;IAEI,6BAAA;IAAA,8BAAA;ELi/GR;;EKn/GI;IAEI,yBAAA;IAAA,4BAAA;ELs/GR;;EKx/GI;IAEI,+BAAA;IAAA,kCAAA;EL2/GR;;EK7/GI;IAEI,8BAAA;IAAA,iCAAA;ELggHR;;EKlgHI;IAEI,4BAAA;IAAA,+BAAA;ELqgHR;;EKvgHI;IAEI,8BAAA;IAAA,iCAAA;EL0gHR;;EK5gHI;IAEI,4BAAA;IAAA,+BAAA;EL+gHR;;EKjhHI;IAEI,yBAAA;ELmhHR;;EKrhHI;IAEI,+BAAA;ELuhHR;;EKzhHI;IAEI,8BAAA;EL2hHR;;EK7hHI;IAEI,4BAAA;EL+hHR;;EKjiHI;IAEI,8BAAA;ELmiHR;;EKriHI;IAEI,4BAAA;ELuiHR;;EKziHI;IAEI,0BAAA;EL2iHR;;EK7iHI;IAEI,gCAAA;EL+iHR;;EKjjHI;IAEI,+BAAA;ELmjHR;;EKrjHI;IAEI,6BAAA;ELujHR;;EKzjHI;IAEI,+BAAA;EL2jHR;;EK7jHI;IAEI,6BAAA;EL+jHR;;EKjkHI;IAEI,4BAAA;ELmkHR;;EKrkHI;IAEI,kCAAA;ELukHR;;EKzkHI;IAEI,iCAAA;EL2kHR;;EK7kHI;IAEI,+BAAA;EL+kHR;;EKjlHI;IAEI,iCAAA;ELmlHR;;EKrlHI;IAEI,+BAAA;ELulHR;;EKzlHI;IAEI,2BAAA;EL2lHR;;EK7lHI;IAEI,iCAAA;EL+lHR;;EKjmHI;IAEI,gCAAA;ELmmHR;;EKrmHI;IAEI,8BAAA;ELumHR;;EKzmHI;IAEI,gCAAA;EL2mHR;;EK7mHI;IAEI,8BAAA;EL+mHR;AACF;ACpmHI;EIdE;IAEI,0BAAA;ELonHR;;EKtnHI;IAEI,gCAAA;ELwnHR;;EK1nHI;IAEI,yBAAA;EL4nHR;;EK9nHI;IAEI,wBAAA;ELgoHR;;EKloHI;IAEI,yBAAA;ELooHR;;EKtoHI;IAEI,6BAAA;ELwoHR;;EK1oHI;IAEI,8BAAA;EL4oHR;;EK9oHI;IAEI,wBAAA;ELgpHR;;EKlpHI;IAEI,+BAAA;ELopHR;;EKtpHI;IAEI,wBAAA;ELwpHR;;EK1pHI;IAEI,yBAAA;EL4pHR;;EK9pHI;IAEI,8BAAA;ELgqHR;;EKlqHI;IAEI,iCAAA;ELoqHR;;EKtqHI;IAEI,sCAAA;ELwqHR;;EK1qHI;IAEI,yCAAA;EL4qHR;;EK9qHI;IAEI,uBAAA;ELgrHR;;EKlrHI;IAEI,uBAAA;ELorHR;;EKtrHI;IAEI,yBAAA;ELwrHR;;EK1rHI;IAEI,yBAAA;EL4rHR;;EK9rHI;IAEI,0BAAA;ELgsHR;;EKlsHI;IAEI,4BAAA;ELosHR;;EKtsHI;IAEI,kCAAA;ELwsHR;;EK1sHI;IAEI,sCAAA;EL4sHR;;EK9sHI;IAEI,oCAAA;ELgtHR;;EKltHI;IAEI,kCAAA;ELotHR;;EKttHI;IAEI,yCAAA;ELwtHR;;EK1tHI;IAEI,wCAAA;EL4tHR;;EK9tHI;IAEI,wCAAA;ELguHR;;EKluHI;IAEI,kCAAA;ELouHR;;EKtuHI;IAEI,gCAAA;ELwuHR;;EK1uHI;IAEI,8BAAA;EL4uHR;;EK9uHI;IAEI,gCAAA;ELgvHR;;EKlvHI;IAEI,+BAAA;ELovHR;;EKtvHI;IAEI,oCAAA;ELwvHR;;EK1vHI;IAEI,kCAAA;EL4vHR;;EK9vHI;IAEI,gCAAA;ELgwHR;;EKlwHI;IAEI,uCAAA;ELowHR;;EKtwHI;IAEI,sCAAA;ELwwHR;;EK1wHI;IAEI,iCAAA;EL4wHR;;EK9wHI;IAEI,2BAAA;ELgxHR;;EKlxHI;IAEI,iCAAA;ELoxHR;;EKtxHI;IAEI,+BAAA;ELwxHR;;EK1xHI;IAEI,6BAAA;EL4xHR;;EK9xHI;IAEI,+BAAA;ELgyHR;;EKlyHI;IAEI,8BAAA;ELoyHR;;EKtyHI;IAEI,oBAAA;ELwyHR;;EK1yHI;IAEI,mBAAA;EL4yHR;;EK9yHI;IAEI,mBAAA;ELgzHR;;EKlzHI;IAEI,mBAAA;ELozHR;;EKtzHI;IAEI,mBAAA;ELwzHR;;EK1zHI;IAEI,mBAAA;EL4zHR;;EK9zHI;IAEI,mBAAA;ELg0HR;;EKl0HI;IAEI,mBAAA;ELo0HR;;EKt0HI;IAEI,oBAAA;ELw0HR;;EK10HI;IAEI,0BAAA;EL40HR;;EK90HI;IAEI,yBAAA;ELg1HR;;EKl1HI;IAEI,uBAAA;ELo1HR;;EKt1HI;IAEI,yBAAA;ELw1HR;;EK11HI;IAEI,uBAAA;EL41HR;;EK91HI;IAEI,uBAAA;ELg2HR;;EKl2HI;IAEI,yBAAA;IAAA,0BAAA;ELq2HR;;EKv2HI;IAEI,+BAAA;IAAA,gCAAA;EL02HR;;EK52HI;IAEI,8BAAA;IAAA,+BAAA;EL+2HR;;EKj3HI;IAEI,4BAAA;IAAA,6BAAA;ELo3HR;;EKt3HI;IAEI,8BAAA;IAAA,+BAAA;ELy3HR;;EK33HI;IAEI,4BAAA;IAAA,6BAAA;EL83HR;;EKh4HI;IAEI,4BAAA;IAAA,6BAAA;ELm4HR;;EKr4HI;IAEI,wBAAA;IAAA,2BAAA;ELw4HR;;EK14HI;IAEI,8BAAA;IAAA,iCAAA;EL64HR;;EK/4HI;IAEI,6BAAA;IAAA,gCAAA;ELk5HR;;EKp5HI;IAEI,2BAAA;IAAA,8BAAA;ELu5HR;;EKz5HI;IAEI,6BAAA;IAAA,gCAAA;EL45HR;;EK95HI;IAEI,2BAAA;IAAA,8BAAA;ELi6HR;;EKn6HI;IAEI,2BAAA;IAAA,8BAAA;ELs6HR;;EKx6HI;IAEI,wBAAA;EL06HR;;EK56HI;IAEI,8BAAA;EL86HR;;EKh7HI;IAEI,6BAAA;ELk7HR;;EKp7HI;IAEI,2BAAA;ELs7HR;;EKx7HI;IAEI,6BAAA;EL07HR;;EK57HI;IAEI,2BAAA;EL87HR;;EKh8HI;IAEI,2BAAA;ELk8HR;;EKp8HI;IAEI,yBAAA;ELs8HR;;EKx8HI;IAEI,+BAAA;EL08HR;;EK58HI;IAEI,8BAAA;EL88HR;;EKh9HI;IAEI,4BAAA;ELk9HR;;EKp9HI;IAEI,8BAAA;ELs9HR;;EKx9HI;IAEI,4BAAA;EL09HR;;EK59HI;IAEI,4BAAA;EL89HR;;EKh+HI;IAEI,2BAAA;ELk+HR;;EKp+HI;IAEI,iCAAA;ELs+HR;;EKx+HI;IAEI,gCAAA;EL0+HR;;EK5+HI;IAEI,8BAAA;EL8+HR;;EKh/HI;IAEI,gCAAA;ELk/HR;;EKp/HI;IAEI,8BAAA;ELs/HR;;EKx/HI;IAEI,8BAAA;EL0/HR;;EK5/HI;IAEI,0BAAA;EL8/HR;;EKhgII;IAEI,gCAAA;ELkgIR;;EKpgII;IAEI,+BAAA;ELsgIR;;EKxgII;IAEI,6BAAA;EL0gIR;;EK5gII;IAEI,+BAAA;EL8gIR;;EKhhII;IAEI,6BAAA;ELkhIR;;EKphII;IAEI,6BAAA;ELshIR;;EKxhII;IAEI,qBAAA;EL0hIR;;EK5hII;IAEI,2BAAA;EL8hIR;;EKhiII;IAEI,0BAAA;ELkiIR;;EKpiII;IAEI,wBAAA;ELsiIR;;EKxiII;IAEI,0BAAA;EL0iIR;;EK5iII;IAEI,wBAAA;EL8iIR;;EKhjII;IAEI,0BAAA;IAAA,2BAAA;ELmjIR;;EKrjII;IAEI,gCAAA;IAAA,iCAAA;ELwjIR;;EK1jII;IAEI,+BAAA;IAAA,gCAAA;EL6jIR;;EK/jII;IAEI,6BAAA;IAAA,8BAAA;ELkkIR;;EKpkII;IAEI,+BAAA;IAAA,gCAAA;ELukIR;;EKzkII;IAEI,6BAAA;IAAA,8BAAA;EL4kIR;;EK9kII;IAEI,yBAAA;IAAA,4BAAA;ELilIR;;EKnlII;IAEI,+BAAA;IAAA,kCAAA;ELslIR;;EKxlII;IAEI,8BAAA;IAAA,iCAAA;EL2lIR;;EK7lII;IAEI,4BAAA;IAAA,+BAAA;ELgmIR;;EKlmII;IAEI,8BAAA;IAAA,iCAAA;ELqmIR;;EKvmII;IAEI,4BAAA;IAAA,+BAAA;EL0mIR;;EK5mII;IAEI,yBAAA;EL8mIR;;EKhnII;IAEI,+BAAA;ELknIR;;EKpnII;IAEI,8BAAA;ELsnIR;;EKxnII;IAEI,4BAAA;EL0nIR;;EK5nII;IAEI,8BAAA;EL8nIR;;EKhoII;IAEI,4BAAA;ELkoIR;;EKpoII;IAEI,0BAAA;ELsoIR;;EKxoII;IAEI,gCAAA;EL0oIR;;EK5oII;IAEI,+BAAA;EL8oIR;;EKhpII;IAEI,6BAAA;ELkpIR;;EKppII;IAEI,+BAAA;ELspIR;;EKxpII;IAEI,6BAAA;EL0pIR;;EK5pII;IAEI,4BAAA;EL8pIR;;EKhqII;IAEI,kCAAA;ELkqIR;;EKpqII;IAEI,iCAAA;ELsqIR;;EKxqII;IAEI,+BAAA;EL0qIR;;EK5qII;IAEI,iCAAA;EL8qIR;;EKhrII;IAEI,+BAAA;ELkrIR;;EKprII;IAEI,2BAAA;ELsrIR;;EKxrII;IAEI,iCAAA;EL0rIR;;EK5rII;IAEI,gCAAA;EL8rIR;;EKhsII;IAEI,8BAAA;ELksIR;;EKpsII;IAEI,gCAAA;ELssIR;;EKxsII;IAEI,8BAAA;EL0sIR;AACF;AC/rII;EIdE;IAEI,0BAAA;EL+sIR;;EKjtII;IAEI,gCAAA;ELmtIR;;EKrtII;IAEI,yBAAA;ELutIR;;EKztII;IAEI,wBAAA;EL2tIR;;EK7tII;IAEI,yBAAA;EL+tIR;;EKjuII;IAEI,6BAAA;ELmuIR;;EKruII;IAEI,8BAAA;ELuuIR;;EKzuII;IAEI,wBAAA;EL2uIR;;EK7uII;IAEI,+BAAA;EL+uIR;;EKjvII;IAEI,wBAAA;ELmvIR;;EKrvII;IAEI,yBAAA;ELuvIR;;EKzvII;IAEI,8BAAA;EL2vIR;;EK7vII;IAEI,iCAAA;EL+vIR;;EKjwII;IAEI,sCAAA;ELmwIR;;EKrwII;IAEI,yCAAA;ELuwIR;;EKzwII;IAEI,uBAAA;EL2wIR;;EK7wII;IAEI,uBAAA;EL+wIR;;EKjxII;IAEI,yBAAA;ELmxIR;;EKrxII;IAEI,yBAAA;ELuxIR;;EKzxII;IAEI,0BAAA;EL2xIR;;EK7xII;IAEI,4BAAA;EL+xIR;;EKjyII;IAEI,kCAAA;ELmyIR;;EKryII;IAEI,sCAAA;ELuyIR;;EKzyII;IAEI,oCAAA;EL2yIR;;EK7yII;IAEI,kCAAA;EL+yIR;;EKjzII;IAEI,yCAAA;ELmzIR;;EKrzII;IAEI,wCAAA;ELuzIR;;EKzzII;IAEI,wCAAA;EL2zIR;;EK7zII;IAEI,kCAAA;EL+zIR;;EKj0II;IAEI,gCAAA;ELm0IR;;EKr0II;IAEI,8BAAA;ELu0IR;;EKz0II;IAEI,gCAAA;EL20IR;;EK70II;IAEI,+BAAA;EL+0IR;;EKj1II;IAEI,oCAAA;ELm1IR;;EKr1II;IAEI,kCAAA;ELu1IR;;EKz1II;IAEI,gCAAA;EL21IR;;EK71II;IAEI,uCAAA;EL+1IR;;EKj2II;IAEI,sCAAA;ELm2IR;;EKr2II;IAEI,iCAAA;ELu2IR;;EKz2II;IAEI,2BAAA;EL22IR;;EK72II;IAEI,iCAAA;EL+2IR;;EKj3II;IAEI,+BAAA;ELm3IR;;EKr3II;IAEI,6BAAA;ELu3IR;;EKz3II;IAEI,+BAAA;EL23IR;;EK73II;IAEI,8BAAA;EL+3IR;;EKj4II;IAEI,oBAAA;ELm4IR;;EKr4II;IAEI,mBAAA;ELu4IR;;EKz4II;IAEI,mBAAA;EL24IR;;EK74II;IAEI,mBAAA;EL+4IR;;EKj5II;IAEI,mBAAA;ELm5IR;;EKr5II;IAEI,mBAAA;ELu5IR;;EKz5II;IAEI,mBAAA;EL25IR;;EK75II;IAEI,mBAAA;EL+5IR;;EKj6II;IAEI,oBAAA;ELm6IR;;EKr6II;IAEI,0BAAA;ELu6IR;;EKz6II;IAEI,yBAAA;EL26IR;;EK76II;IAEI,uBAAA;EL+6IR;;EKj7II;IAEI,yBAAA;ELm7IR;;EKr7II;IAEI,uBAAA;ELu7IR;;EKz7II;IAEI,uBAAA;EL27IR;;EK77II;IAEI,yBAAA;IAAA,0BAAA;ELg8IR;;EKl8II;IAEI,+BAAA;IAAA,gCAAA;ELq8IR;;EKv8II;IAEI,8BAAA;IAAA,+BAAA;EL08IR;;EK58II;IAEI,4BAAA;IAAA,6BAAA;EL+8IR;;EKj9II;IAEI,8BAAA;IAAA,+BAAA;ELo9IR;;EKt9II;IAEI,4BAAA;IAAA,6BAAA;ELy9IR;;EK39II;IAEI,4BAAA;IAAA,6BAAA;EL89IR;;EKh+II;IAEI,wBAAA;IAAA,2BAAA;ELm+IR;;EKr+II;IAEI,8BAAA;IAAA,iCAAA;ELw+IR;;EK1+II;IAEI,6BAAA;IAAA,gCAAA;EL6+IR;;EK/+II;IAEI,2BAAA;IAAA,8BAAA;ELk/IR;;EKp/II;IAEI,6BAAA;IAAA,gCAAA;ELu/IR;;EKz/II;IAEI,2BAAA;IAAA,8BAAA;EL4/IR;;EK9/II;IAEI,2BAAA;IAAA,8BAAA;ELigJR;;EKngJI;IAEI,wBAAA;ELqgJR;;EKvgJI;IAEI,8BAAA;ELygJR;;EK3gJI;IAEI,6BAAA;EL6gJR;;EK/gJI;IAEI,2BAAA;ELihJR;;EKnhJI;IAEI,6BAAA;ELqhJR;;EKvhJI;IAEI,2BAAA;ELyhJR;;EK3hJI;IAEI,2BAAA;EL6hJR;;EK/hJI;IAEI,yBAAA;ELiiJR;;EKniJI;IAEI,+BAAA;ELqiJR;;EKviJI;IAEI,8BAAA;ELyiJR;;EK3iJI;IAEI,4BAAA;EL6iJR;;EK/iJI;IAEI,8BAAA;ELijJR;;EKnjJI;IAEI,4BAAA;ELqjJR;;EKvjJI;IAEI,4BAAA;ELyjJR;;EK3jJI;IAEI,2BAAA;EL6jJR;;EK/jJI;IAEI,iCAAA;ELikJR;;EKnkJI;IAEI,gCAAA;ELqkJR;;EKvkJI;IAEI,8BAAA;ELykJR;;EK3kJI;IAEI,gCAAA;EL6kJR;;EK/kJI;IAEI,8BAAA;ELilJR;;EKnlJI;IAEI,8BAAA;ELqlJR;;EKvlJI;IAEI,0BAAA;ELylJR;;EK3lJI;IAEI,gCAAA;EL6lJR;;EK/lJI;IAEI,+BAAA;ELimJR;;EKnmJI;IAEI,6BAAA;ELqmJR;;EKvmJI;IAEI,+BAAA;ELymJR;;EK3mJI;IAEI,6BAAA;EL6mJR;;EK/mJI;IAEI,6BAAA;ELinJR;;EKnnJI;IAEI,qBAAA;ELqnJR;;EKvnJI;IAEI,2BAAA;ELynJR;;EK3nJI;IAEI,0BAAA;EL6nJR;;EK/nJI;IAEI,wBAAA;ELioJR;;EKnoJI;IAEI,0BAAA;ELqoJR;;EKvoJI;IAEI,wBAAA;ELyoJR;;EK3oJI;IAEI,0BAAA;IAAA,2BAAA;EL8oJR;;EKhpJI;IAEI,gCAAA;IAAA,iCAAA;ELmpJR;;EKrpJI;IAEI,+BAAA;IAAA,gCAAA;ELwpJR;;EK1pJI;IAEI,6BAAA;IAAA,8BAAA;EL6pJR;;EK/pJI;IAEI,+BAAA;IAAA,gCAAA;ELkqJR;;EKpqJI;IAEI,6BAAA;IAAA,8BAAA;ELuqJR;;EKzqJI;IAEI,yBAAA;IAAA,4BAAA;EL4qJR;;EK9qJI;IAEI,+BAAA;IAAA,kCAAA;ELirJR;;EKnrJI;IAEI,8BAAA;IAAA,iCAAA;ELsrJR;;EKxrJI;IAEI,4BAAA;IAAA,+BAAA;EL2rJR;;EK7rJI;IAEI,8BAAA;IAAA,iCAAA;ELgsJR;;EKlsJI;IAEI,4BAAA;IAAA,+BAAA;ELqsJR;;EKvsJI;IAEI,yBAAA;ELysJR;;EK3sJI;IAEI,+BAAA;EL6sJR;;EK/sJI;IAEI,8BAAA;ELitJR;;EKntJI;IAEI,4BAAA;ELqtJR;;EKvtJI;IAEI,8BAAA;ELytJR;;EK3tJI;IAEI,4BAAA;EL6tJR;;EK/tJI;IAEI,0BAAA;ELiuJR;;EKnuJI;IAEI,gCAAA;ELquJR;;EKvuJI;IAEI,+BAAA;ELyuJR;;EK3uJI;IAEI,6BAAA;EL6uJR;;EK/uJI;IAEI,+BAAA;ELivJR;;EKnvJI;IAEI,6BAAA;ELqvJR;;EKvvJI;IAEI,4BAAA;ELyvJR;;EK3vJI;IAEI,kCAAA;EL6vJR;;EK/vJI;IAEI,iCAAA;ELiwJR;;EKnwJI;IAEI,+BAAA;ELqwJR;;EKvwJI;IAEI,iCAAA;ELywJR;;EK3wJI;IAEI,+BAAA;EL6wJR;;EK/wJI;IAEI,2BAAA;ELixJR;;EKnxJI;IAEI,iCAAA;ELqxJR;;EKvxJI;IAEI,gCAAA;ELyxJR;;EK3xJI;IAEI,8BAAA;EL6xJR;;EK/xJI;IAEI,gCAAA;ELiyJR;;EKnyJI;IAEI,8BAAA;ELqyJR;AACF;AMnzJA;EDWM;IAEI,0BAAA;EL0yJR;;EK5yJI;IAEI,gCAAA;EL8yJR;;EKhzJI;IAEI,yBAAA;ELkzJR;;EKpzJI;IAEI,wBAAA;ELszJR;;EKxzJI;IAEI,yBAAA;EL0zJR;;EK5zJI;IAEI,6BAAA;EL8zJR;;EKh0JI;IAEI,8BAAA;ELk0JR;;EKp0JI;IAEI,wBAAA;ELs0JR;;EKx0JI;IAEI,+BAAA;EL00JR;;EK50JI;IAEI,wBAAA;EL80JR;AACF", "file": "bootstrap-grid.rtl.css", "sourcesContent": ["/*!\n * Bootstrap Grid v5.0.0-beta3 (https://getbootstrap.com/)\n * Copyright 2011-2021 The Bootstrap Authors\n * Copyright 2011-2021 Twitter, Inc.\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n */\n\n$include-column-box-sizing: true !default;\n\n@import \"functions\";\n@import \"variables\";\n\n@import \"mixins/lists\";\n@import \"mixins/breakpoints\";\n@import \"mixins/container\";\n@import \"mixins/grid\";\n@import \"mixins/utilities\";\n\n@import \"vendor/rfs\";\n\n@import \"containers\";\n@import \"grid\";\n\n@import \"utilities\";\n// Only use the utilities we need\n// stylelint-disable-next-line scss/dollar-variable-default\n$utilities: map-get-multiple(\n  $utilities,\n  (\n    \"display\",\n    \"order\",\n    \"flex\",\n    \"flex-direction\",\n    \"flex-grow\",\n    \"flex-shrink\",\n    \"flex-wrap\",\n    \"justify-content\",\n    \"align-items\",\n    \"align-content\",\n    \"align-self\",\n    \"margin\",\n    \"margin-x\",\n    \"margin-y\",\n    \"margin-top\",\n    \"margin-end\",\n    \"margin-bottom\",\n    \"margin-start\",\n    \"negative-margin\",\n    \"negative-margin-x\",\n    \"negative-margin-y\",\n    \"negative-margin-top\",\n    \"negative-margin-end\",\n    \"negative-margin-bottom\",\n    \"negative-margin-start\",\n    \"padding\",\n    \"padding-x\",\n    \"padding-y\",\n    \"padding-top\",\n    \"padding-end\",\n    \"padding-bottom\",\n    \"padding-start\",\n  )\n);\n\n@import \"utilities/api\";\n", "// Container widths\n//\n// Set the container width, and override it for fixed navbars in media queries.\n\n@if $enable-grid-classes {\n  // Single container class with breakpoint max-widths\n  .container,\n  // 100% wide container at all breakpoints\n  .container-fluid {\n    @include make-container();\n  }\n\n  // Responsive containers that are 100% wide until a breakpoint\n  @each $breakpoint, $container-max-width in $container-max-widths {\n    .container-#{$breakpoint} {\n      @extend .container-fluid;\n    }\n\n    @include media-breakpoint-up($breakpoint, $grid-breakpoints) {\n      %responsive-container-#{$breakpoint} {\n        max-width: $container-max-width;\n      }\n\n      // Extend each breakpoint which is smaller or equal to the current breakpoint\n      $extend-breakpoint: true;\n\n      @each $name, $width in $grid-breakpoints {\n        @if ($extend-breakpoint) {\n          .container#{breakpoint-infix($name, $grid-breakpoints)} {\n            @extend %responsive-container-#{$breakpoint};\n          }\n\n          // Once the current breakpoint is reached, stop extending\n          @if ($breakpoint == $name) {\n            $extend-breakpoint: false;\n          }\n        }\n      }\n    }\n  }\n}\n", "// Container mixins\n\n@mixin make-container($gutter: $container-padding-x) {\n  width: 100%;\n  padding-right: var(--#{$variable-prefix}gutter-x, #{$gutter});\n  padding-left: var(--#{$variable-prefix}gutter-x, #{$gutter});\n  margin-right: auto;\n  margin-left: auto;\n}\n", "/*!\n * Bootstrap Grid v5.0.0-beta3 (https://getbootstrap.com/)\n * Copyright 2011-2021 The Bootstrap Authors\n * Copyright 2011-2021 Twitter, Inc.\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n */\n.container,\n.container-fluid,\n.container-xxl,\n.container-xl,\n.container-lg,\n.container-md,\n.container-sm {\n  width: 100%;\n  padding-right: var(--bs-gutter-x, 0.75rem);\n  padding-left: var(--bs-gutter-x, 0.75rem);\n  margin-right: auto;\n  margin-left: auto;\n}\n\n@media (min-width: 576px) {\n  .container-sm, .container {\n    max-width: 540px;\n  }\n}\n@media (min-width: 768px) {\n  .container-md, .container-sm, .container {\n    max-width: 720px;\n  }\n}\n@media (min-width: 992px) {\n  .container-lg, .container-md, .container-sm, .container {\n    max-width: 960px;\n  }\n}\n@media (min-width: 1200px) {\n  .container-xl, .container-lg, .container-md, .container-sm, .container {\n    max-width: 1140px;\n  }\n}\n@media (min-width: 1400px) {\n  .container-xxl, .container-xl, .container-lg, .container-md, .container-sm, .container {\n    max-width: 1320px;\n  }\n}\n.row {\n  --bs-gutter-x: 1.5rem;\n  --bs-gutter-y: 0;\n  display: flex;\n  flex-wrap: wrap;\n  margin-top: calc(var(--bs-gutter-y) * -1);\n  margin-right: calc(var(--bs-gutter-x) / -2);\n  margin-left: calc(var(--bs-gutter-x) / -2);\n}\n.row > * {\n  box-sizing: border-box;\n  flex-shrink: 0;\n  width: 100%;\n  max-width: 100%;\n  padding-right: calc(var(--bs-gutter-x) / 2);\n  padding-left: calc(var(--bs-gutter-x) / 2);\n  margin-top: var(--bs-gutter-y);\n}\n\n.col {\n  flex: 1 0 0%;\n}\n\n.row-cols-auto > * {\n  flex: 0 0 auto;\n  width: auto;\n}\n\n.row-cols-1 > * {\n  flex: 0 0 auto;\n  width: 100%;\n}\n\n.row-cols-2 > * {\n  flex: 0 0 auto;\n  width: 50%;\n}\n\n.row-cols-3 > * {\n  flex: 0 0 auto;\n  width: 33.3333333333%;\n}\n\n.row-cols-4 > * {\n  flex: 0 0 auto;\n  width: 25%;\n}\n\n.row-cols-5 > * {\n  flex: 0 0 auto;\n  width: 20%;\n}\n\n.row-cols-6 > * {\n  flex: 0 0 auto;\n  width: 16.6666666667%;\n}\n\n.col-auto {\n  flex: 0 0 auto;\n  width: auto;\n}\n\n.col-1 {\n  flex: 0 0 auto;\n  width: 8.3333333333%;\n}\n\n.col-2 {\n  flex: 0 0 auto;\n  width: 16.6666666667%;\n}\n\n.col-3 {\n  flex: 0 0 auto;\n  width: 25%;\n}\n\n.col-4 {\n  flex: 0 0 auto;\n  width: 33.3333333333%;\n}\n\n.col-5 {\n  flex: 0 0 auto;\n  width: 41.6666666667%;\n}\n\n.col-6 {\n  flex: 0 0 auto;\n  width: 50%;\n}\n\n.col-7 {\n  flex: 0 0 auto;\n  width: 58.3333333333%;\n}\n\n.col-8 {\n  flex: 0 0 auto;\n  width: 66.6666666667%;\n}\n\n.col-9 {\n  flex: 0 0 auto;\n  width: 75%;\n}\n\n.col-10 {\n  flex: 0 0 auto;\n  width: 83.3333333333%;\n}\n\n.col-11 {\n  flex: 0 0 auto;\n  width: 91.6666666667%;\n}\n\n.col-12 {\n  flex: 0 0 auto;\n  width: 100%;\n}\n\n.offset-1 {\n  margin-left: 8.3333333333%;\n}\n\n.offset-2 {\n  margin-left: 16.6666666667%;\n}\n\n.offset-3 {\n  margin-left: 25%;\n}\n\n.offset-4 {\n  margin-left: 33.3333333333%;\n}\n\n.offset-5 {\n  margin-left: 41.6666666667%;\n}\n\n.offset-6 {\n  margin-left: 50%;\n}\n\n.offset-7 {\n  margin-left: 58.3333333333%;\n}\n\n.offset-8 {\n  margin-left: 66.6666666667%;\n}\n\n.offset-9 {\n  margin-left: 75%;\n}\n\n.offset-10 {\n  margin-left: 83.3333333333%;\n}\n\n.offset-11 {\n  margin-left: 91.6666666667%;\n}\n\n.g-0,\n.gx-0 {\n  --bs-gutter-x: 0;\n}\n\n.g-0,\n.gy-0 {\n  --bs-gutter-y: 0;\n}\n\n.g-1,\n.gx-1 {\n  --bs-gutter-x: 0.25rem;\n}\n\n.g-1,\n.gy-1 {\n  --bs-gutter-y: 0.25rem;\n}\n\n.g-2,\n.gx-2 {\n  --bs-gutter-x: 0.5rem;\n}\n\n.g-2,\n.gy-2 {\n  --bs-gutter-y: 0.5rem;\n}\n\n.g-3,\n.gx-3 {\n  --bs-gutter-x: 1rem;\n}\n\n.g-3,\n.gy-3 {\n  --bs-gutter-y: 1rem;\n}\n\n.g-4,\n.gx-4 {\n  --bs-gutter-x: 1.5rem;\n}\n\n.g-4,\n.gy-4 {\n  --bs-gutter-y: 1.5rem;\n}\n\n.g-5,\n.gx-5 {\n  --bs-gutter-x: 3rem;\n}\n\n.g-5,\n.gy-5 {\n  --bs-gutter-y: 3rem;\n}\n\n@media (min-width: 576px) {\n  .col-sm {\n    flex: 1 0 0%;\n  }\n\n  .row-cols-sm-auto > * {\n    flex: 0 0 auto;\n    width: auto;\n  }\n\n  .row-cols-sm-1 > * {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n\n  .row-cols-sm-2 > * {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n\n  .row-cols-sm-3 > * {\n    flex: 0 0 auto;\n    width: 33.3333333333%;\n  }\n\n  .row-cols-sm-4 > * {\n    flex: 0 0 auto;\n    width: 25%;\n  }\n\n  .row-cols-sm-5 > * {\n    flex: 0 0 auto;\n    width: 20%;\n  }\n\n  .row-cols-sm-6 > * {\n    flex: 0 0 auto;\n    width: 16.6666666667%;\n  }\n\n  .col-sm-auto {\n    flex: 0 0 auto;\n    width: auto;\n  }\n\n  .col-sm-1 {\n    flex: 0 0 auto;\n    width: 8.3333333333%;\n  }\n\n  .col-sm-2 {\n    flex: 0 0 auto;\n    width: 16.6666666667%;\n  }\n\n  .col-sm-3 {\n    flex: 0 0 auto;\n    width: 25%;\n  }\n\n  .col-sm-4 {\n    flex: 0 0 auto;\n    width: 33.3333333333%;\n  }\n\n  .col-sm-5 {\n    flex: 0 0 auto;\n    width: 41.6666666667%;\n  }\n\n  .col-sm-6 {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n\n  .col-sm-7 {\n    flex: 0 0 auto;\n    width: 58.3333333333%;\n  }\n\n  .col-sm-8 {\n    flex: 0 0 auto;\n    width: 66.6666666667%;\n  }\n\n  .col-sm-9 {\n    flex: 0 0 auto;\n    width: 75%;\n  }\n\n  .col-sm-10 {\n    flex: 0 0 auto;\n    width: 83.3333333333%;\n  }\n\n  .col-sm-11 {\n    flex: 0 0 auto;\n    width: 91.6666666667%;\n  }\n\n  .col-sm-12 {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n\n  .offset-sm-0 {\n    margin-left: 0;\n  }\n\n  .offset-sm-1 {\n    margin-left: 8.3333333333%;\n  }\n\n  .offset-sm-2 {\n    margin-left: 16.6666666667%;\n  }\n\n  .offset-sm-3 {\n    margin-left: 25%;\n  }\n\n  .offset-sm-4 {\n    margin-left: 33.3333333333%;\n  }\n\n  .offset-sm-5 {\n    margin-left: 41.6666666667%;\n  }\n\n  .offset-sm-6 {\n    margin-left: 50%;\n  }\n\n  .offset-sm-7 {\n    margin-left: 58.3333333333%;\n  }\n\n  .offset-sm-8 {\n    margin-left: 66.6666666667%;\n  }\n\n  .offset-sm-9 {\n    margin-left: 75%;\n  }\n\n  .offset-sm-10 {\n    margin-left: 83.3333333333%;\n  }\n\n  .offset-sm-11 {\n    margin-left: 91.6666666667%;\n  }\n\n  .g-sm-0,\n.gx-sm-0 {\n    --bs-gutter-x: 0;\n  }\n\n  .g-sm-0,\n.gy-sm-0 {\n    --bs-gutter-y: 0;\n  }\n\n  .g-sm-1,\n.gx-sm-1 {\n    --bs-gutter-x: 0.25rem;\n  }\n\n  .g-sm-1,\n.gy-sm-1 {\n    --bs-gutter-y: 0.25rem;\n  }\n\n  .g-sm-2,\n.gx-sm-2 {\n    --bs-gutter-x: 0.5rem;\n  }\n\n  .g-sm-2,\n.gy-sm-2 {\n    --bs-gutter-y: 0.5rem;\n  }\n\n  .g-sm-3,\n.gx-sm-3 {\n    --bs-gutter-x: 1rem;\n  }\n\n  .g-sm-3,\n.gy-sm-3 {\n    --bs-gutter-y: 1rem;\n  }\n\n  .g-sm-4,\n.gx-sm-4 {\n    --bs-gutter-x: 1.5rem;\n  }\n\n  .g-sm-4,\n.gy-sm-4 {\n    --bs-gutter-y: 1.5rem;\n  }\n\n  .g-sm-5,\n.gx-sm-5 {\n    --bs-gutter-x: 3rem;\n  }\n\n  .g-sm-5,\n.gy-sm-5 {\n    --bs-gutter-y: 3rem;\n  }\n}\n@media (min-width: 768px) {\n  .col-md {\n    flex: 1 0 0%;\n  }\n\n  .row-cols-md-auto > * {\n    flex: 0 0 auto;\n    width: auto;\n  }\n\n  .row-cols-md-1 > * {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n\n  .row-cols-md-2 > * {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n\n  .row-cols-md-3 > * {\n    flex: 0 0 auto;\n    width: 33.3333333333%;\n  }\n\n  .row-cols-md-4 > * {\n    flex: 0 0 auto;\n    width: 25%;\n  }\n\n  .row-cols-md-5 > * {\n    flex: 0 0 auto;\n    width: 20%;\n  }\n\n  .row-cols-md-6 > * {\n    flex: 0 0 auto;\n    width: 16.6666666667%;\n  }\n\n  .col-md-auto {\n    flex: 0 0 auto;\n    width: auto;\n  }\n\n  .col-md-1 {\n    flex: 0 0 auto;\n    width: 8.3333333333%;\n  }\n\n  .col-md-2 {\n    flex: 0 0 auto;\n    width: 16.6666666667%;\n  }\n\n  .col-md-3 {\n    flex: 0 0 auto;\n    width: 25%;\n  }\n\n  .col-md-4 {\n    flex: 0 0 auto;\n    width: 33.3333333333%;\n  }\n\n  .col-md-5 {\n    flex: 0 0 auto;\n    width: 41.6666666667%;\n  }\n\n  .col-md-6 {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n\n  .col-md-7 {\n    flex: 0 0 auto;\n    width: 58.3333333333%;\n  }\n\n  .col-md-8 {\n    flex: 0 0 auto;\n    width: 66.6666666667%;\n  }\n\n  .col-md-9 {\n    flex: 0 0 auto;\n    width: 75%;\n  }\n\n  .col-md-10 {\n    flex: 0 0 auto;\n    width: 83.3333333333%;\n  }\n\n  .col-md-11 {\n    flex: 0 0 auto;\n    width: 91.6666666667%;\n  }\n\n  .col-md-12 {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n\n  .offset-md-0 {\n    margin-left: 0;\n  }\n\n  .offset-md-1 {\n    margin-left: 8.3333333333%;\n  }\n\n  .offset-md-2 {\n    margin-left: 16.6666666667%;\n  }\n\n  .offset-md-3 {\n    margin-left: 25%;\n  }\n\n  .offset-md-4 {\n    margin-left: 33.3333333333%;\n  }\n\n  .offset-md-5 {\n    margin-left: 41.6666666667%;\n  }\n\n  .offset-md-6 {\n    margin-left: 50%;\n  }\n\n  .offset-md-7 {\n    margin-left: 58.3333333333%;\n  }\n\n  .offset-md-8 {\n    margin-left: 66.6666666667%;\n  }\n\n  .offset-md-9 {\n    margin-left: 75%;\n  }\n\n  .offset-md-10 {\n    margin-left: 83.3333333333%;\n  }\n\n  .offset-md-11 {\n    margin-left: 91.6666666667%;\n  }\n\n  .g-md-0,\n.gx-md-0 {\n    --bs-gutter-x: 0;\n  }\n\n  .g-md-0,\n.gy-md-0 {\n    --bs-gutter-y: 0;\n  }\n\n  .g-md-1,\n.gx-md-1 {\n    --bs-gutter-x: 0.25rem;\n  }\n\n  .g-md-1,\n.gy-md-1 {\n    --bs-gutter-y: 0.25rem;\n  }\n\n  .g-md-2,\n.gx-md-2 {\n    --bs-gutter-x: 0.5rem;\n  }\n\n  .g-md-2,\n.gy-md-2 {\n    --bs-gutter-y: 0.5rem;\n  }\n\n  .g-md-3,\n.gx-md-3 {\n    --bs-gutter-x: 1rem;\n  }\n\n  .g-md-3,\n.gy-md-3 {\n    --bs-gutter-y: 1rem;\n  }\n\n  .g-md-4,\n.gx-md-4 {\n    --bs-gutter-x: 1.5rem;\n  }\n\n  .g-md-4,\n.gy-md-4 {\n    --bs-gutter-y: 1.5rem;\n  }\n\n  .g-md-5,\n.gx-md-5 {\n    --bs-gutter-x: 3rem;\n  }\n\n  .g-md-5,\n.gy-md-5 {\n    --bs-gutter-y: 3rem;\n  }\n}\n@media (min-width: 992px) {\n  .col-lg {\n    flex: 1 0 0%;\n  }\n\n  .row-cols-lg-auto > * {\n    flex: 0 0 auto;\n    width: auto;\n  }\n\n  .row-cols-lg-1 > * {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n\n  .row-cols-lg-2 > * {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n\n  .row-cols-lg-3 > * {\n    flex: 0 0 auto;\n    width: 33.3333333333%;\n  }\n\n  .row-cols-lg-4 > * {\n    flex: 0 0 auto;\n    width: 25%;\n  }\n\n  .row-cols-lg-5 > * {\n    flex: 0 0 auto;\n    width: 20%;\n  }\n\n  .row-cols-lg-6 > * {\n    flex: 0 0 auto;\n    width: 16.6666666667%;\n  }\n\n  .col-lg-auto {\n    flex: 0 0 auto;\n    width: auto;\n  }\n\n  .col-lg-1 {\n    flex: 0 0 auto;\n    width: 8.3333333333%;\n  }\n\n  .col-lg-2 {\n    flex: 0 0 auto;\n    width: 16.6666666667%;\n  }\n\n  .col-lg-3 {\n    flex: 0 0 auto;\n    width: 25%;\n  }\n\n  .col-lg-4 {\n    flex: 0 0 auto;\n    width: 33.3333333333%;\n  }\n\n  .col-lg-5 {\n    flex: 0 0 auto;\n    width: 41.6666666667%;\n  }\n\n  .col-lg-6 {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n\n  .col-lg-7 {\n    flex: 0 0 auto;\n    width: 58.3333333333%;\n  }\n\n  .col-lg-8 {\n    flex: 0 0 auto;\n    width: 66.6666666667%;\n  }\n\n  .col-lg-9 {\n    flex: 0 0 auto;\n    width: 75%;\n  }\n\n  .col-lg-10 {\n    flex: 0 0 auto;\n    width: 83.3333333333%;\n  }\n\n  .col-lg-11 {\n    flex: 0 0 auto;\n    width: 91.6666666667%;\n  }\n\n  .col-lg-12 {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n\n  .offset-lg-0 {\n    margin-left: 0;\n  }\n\n  .offset-lg-1 {\n    margin-left: 8.3333333333%;\n  }\n\n  .offset-lg-2 {\n    margin-left: 16.6666666667%;\n  }\n\n  .offset-lg-3 {\n    margin-left: 25%;\n  }\n\n  .offset-lg-4 {\n    margin-left: 33.3333333333%;\n  }\n\n  .offset-lg-5 {\n    margin-left: 41.6666666667%;\n  }\n\n  .offset-lg-6 {\n    margin-left: 50%;\n  }\n\n  .offset-lg-7 {\n    margin-left: 58.3333333333%;\n  }\n\n  .offset-lg-8 {\n    margin-left: 66.6666666667%;\n  }\n\n  .offset-lg-9 {\n    margin-left: 75%;\n  }\n\n  .offset-lg-10 {\n    margin-left: 83.3333333333%;\n  }\n\n  .offset-lg-11 {\n    margin-left: 91.6666666667%;\n  }\n\n  .g-lg-0,\n.gx-lg-0 {\n    --bs-gutter-x: 0;\n  }\n\n  .g-lg-0,\n.gy-lg-0 {\n    --bs-gutter-y: 0;\n  }\n\n  .g-lg-1,\n.gx-lg-1 {\n    --bs-gutter-x: 0.25rem;\n  }\n\n  .g-lg-1,\n.gy-lg-1 {\n    --bs-gutter-y: 0.25rem;\n  }\n\n  .g-lg-2,\n.gx-lg-2 {\n    --bs-gutter-x: 0.5rem;\n  }\n\n  .g-lg-2,\n.gy-lg-2 {\n    --bs-gutter-y: 0.5rem;\n  }\n\n  .g-lg-3,\n.gx-lg-3 {\n    --bs-gutter-x: 1rem;\n  }\n\n  .g-lg-3,\n.gy-lg-3 {\n    --bs-gutter-y: 1rem;\n  }\n\n  .g-lg-4,\n.gx-lg-4 {\n    --bs-gutter-x: 1.5rem;\n  }\n\n  .g-lg-4,\n.gy-lg-4 {\n    --bs-gutter-y: 1.5rem;\n  }\n\n  .g-lg-5,\n.gx-lg-5 {\n    --bs-gutter-x: 3rem;\n  }\n\n  .g-lg-5,\n.gy-lg-5 {\n    --bs-gutter-y: 3rem;\n  }\n}\n@media (min-width: 1200px) {\n  .col-xl {\n    flex: 1 0 0%;\n  }\n\n  .row-cols-xl-auto > * {\n    flex: 0 0 auto;\n    width: auto;\n  }\n\n  .row-cols-xl-1 > * {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n\n  .row-cols-xl-2 > * {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n\n  .row-cols-xl-3 > * {\n    flex: 0 0 auto;\n    width: 33.3333333333%;\n  }\n\n  .row-cols-xl-4 > * {\n    flex: 0 0 auto;\n    width: 25%;\n  }\n\n  .row-cols-xl-5 > * {\n    flex: 0 0 auto;\n    width: 20%;\n  }\n\n  .row-cols-xl-6 > * {\n    flex: 0 0 auto;\n    width: 16.6666666667%;\n  }\n\n  .col-xl-auto {\n    flex: 0 0 auto;\n    width: auto;\n  }\n\n  .col-xl-1 {\n    flex: 0 0 auto;\n    width: 8.3333333333%;\n  }\n\n  .col-xl-2 {\n    flex: 0 0 auto;\n    width: 16.6666666667%;\n  }\n\n  .col-xl-3 {\n    flex: 0 0 auto;\n    width: 25%;\n  }\n\n  .col-xl-4 {\n    flex: 0 0 auto;\n    width: 33.3333333333%;\n  }\n\n  .col-xl-5 {\n    flex: 0 0 auto;\n    width: 41.6666666667%;\n  }\n\n  .col-xl-6 {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n\n  .col-xl-7 {\n    flex: 0 0 auto;\n    width: 58.3333333333%;\n  }\n\n  .col-xl-8 {\n    flex: 0 0 auto;\n    width: 66.6666666667%;\n  }\n\n  .col-xl-9 {\n    flex: 0 0 auto;\n    width: 75%;\n  }\n\n  .col-xl-10 {\n    flex: 0 0 auto;\n    width: 83.3333333333%;\n  }\n\n  .col-xl-11 {\n    flex: 0 0 auto;\n    width: 91.6666666667%;\n  }\n\n  .col-xl-12 {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n\n  .offset-xl-0 {\n    margin-left: 0;\n  }\n\n  .offset-xl-1 {\n    margin-left: 8.3333333333%;\n  }\n\n  .offset-xl-2 {\n    margin-left: 16.6666666667%;\n  }\n\n  .offset-xl-3 {\n    margin-left: 25%;\n  }\n\n  .offset-xl-4 {\n    margin-left: 33.3333333333%;\n  }\n\n  .offset-xl-5 {\n    margin-left: 41.6666666667%;\n  }\n\n  .offset-xl-6 {\n    margin-left: 50%;\n  }\n\n  .offset-xl-7 {\n    margin-left: 58.3333333333%;\n  }\n\n  .offset-xl-8 {\n    margin-left: 66.6666666667%;\n  }\n\n  .offset-xl-9 {\n    margin-left: 75%;\n  }\n\n  .offset-xl-10 {\n    margin-left: 83.3333333333%;\n  }\n\n  .offset-xl-11 {\n    margin-left: 91.6666666667%;\n  }\n\n  .g-xl-0,\n.gx-xl-0 {\n    --bs-gutter-x: 0;\n  }\n\n  .g-xl-0,\n.gy-xl-0 {\n    --bs-gutter-y: 0;\n  }\n\n  .g-xl-1,\n.gx-xl-1 {\n    --bs-gutter-x: 0.25rem;\n  }\n\n  .g-xl-1,\n.gy-xl-1 {\n    --bs-gutter-y: 0.25rem;\n  }\n\n  .g-xl-2,\n.gx-xl-2 {\n    --bs-gutter-x: 0.5rem;\n  }\n\n  .g-xl-2,\n.gy-xl-2 {\n    --bs-gutter-y: 0.5rem;\n  }\n\n  .g-xl-3,\n.gx-xl-3 {\n    --bs-gutter-x: 1rem;\n  }\n\n  .g-xl-3,\n.gy-xl-3 {\n    --bs-gutter-y: 1rem;\n  }\n\n  .g-xl-4,\n.gx-xl-4 {\n    --bs-gutter-x: 1.5rem;\n  }\n\n  .g-xl-4,\n.gy-xl-4 {\n    --bs-gutter-y: 1.5rem;\n  }\n\n  .g-xl-5,\n.gx-xl-5 {\n    --bs-gutter-x: 3rem;\n  }\n\n  .g-xl-5,\n.gy-xl-5 {\n    --bs-gutter-y: 3rem;\n  }\n}\n@media (min-width: 1400px) {\n  .col-xxl {\n    flex: 1 0 0%;\n  }\n\n  .row-cols-xxl-auto > * {\n    flex: 0 0 auto;\n    width: auto;\n  }\n\n  .row-cols-xxl-1 > * {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n\n  .row-cols-xxl-2 > * {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n\n  .row-cols-xxl-3 > * {\n    flex: 0 0 auto;\n    width: 33.3333333333%;\n  }\n\n  .row-cols-xxl-4 > * {\n    flex: 0 0 auto;\n    width: 25%;\n  }\n\n  .row-cols-xxl-5 > * {\n    flex: 0 0 auto;\n    width: 20%;\n  }\n\n  .row-cols-xxl-6 > * {\n    flex: 0 0 auto;\n    width: 16.6666666667%;\n  }\n\n  .col-xxl-auto {\n    flex: 0 0 auto;\n    width: auto;\n  }\n\n  .col-xxl-1 {\n    flex: 0 0 auto;\n    width: 8.3333333333%;\n  }\n\n  .col-xxl-2 {\n    flex: 0 0 auto;\n    width: 16.6666666667%;\n  }\n\n  .col-xxl-3 {\n    flex: 0 0 auto;\n    width: 25%;\n  }\n\n  .col-xxl-4 {\n    flex: 0 0 auto;\n    width: 33.3333333333%;\n  }\n\n  .col-xxl-5 {\n    flex: 0 0 auto;\n    width: 41.6666666667%;\n  }\n\n  .col-xxl-6 {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n\n  .col-xxl-7 {\n    flex: 0 0 auto;\n    width: 58.3333333333%;\n  }\n\n  .col-xxl-8 {\n    flex: 0 0 auto;\n    width: 66.6666666667%;\n  }\n\n  .col-xxl-9 {\n    flex: 0 0 auto;\n    width: 75%;\n  }\n\n  .col-xxl-10 {\n    flex: 0 0 auto;\n    width: 83.3333333333%;\n  }\n\n  .col-xxl-11 {\n    flex: 0 0 auto;\n    width: 91.6666666667%;\n  }\n\n  .col-xxl-12 {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n\n  .offset-xxl-0 {\n    margin-left: 0;\n  }\n\n  .offset-xxl-1 {\n    margin-left: 8.3333333333%;\n  }\n\n  .offset-xxl-2 {\n    margin-left: 16.6666666667%;\n  }\n\n  .offset-xxl-3 {\n    margin-left: 25%;\n  }\n\n  .offset-xxl-4 {\n    margin-left: 33.3333333333%;\n  }\n\n  .offset-xxl-5 {\n    margin-left: 41.6666666667%;\n  }\n\n  .offset-xxl-6 {\n    margin-left: 50%;\n  }\n\n  .offset-xxl-7 {\n    margin-left: 58.3333333333%;\n  }\n\n  .offset-xxl-8 {\n    margin-left: 66.6666666667%;\n  }\n\n  .offset-xxl-9 {\n    margin-left: 75%;\n  }\n\n  .offset-xxl-10 {\n    margin-left: 83.3333333333%;\n  }\n\n  .offset-xxl-11 {\n    margin-left: 91.6666666667%;\n  }\n\n  .g-xxl-0,\n.gx-xxl-0 {\n    --bs-gutter-x: 0;\n  }\n\n  .g-xxl-0,\n.gy-xxl-0 {\n    --bs-gutter-y: 0;\n  }\n\n  .g-xxl-1,\n.gx-xxl-1 {\n    --bs-gutter-x: 0.25rem;\n  }\n\n  .g-xxl-1,\n.gy-xxl-1 {\n    --bs-gutter-y: 0.25rem;\n  }\n\n  .g-xxl-2,\n.gx-xxl-2 {\n    --bs-gutter-x: 0.5rem;\n  }\n\n  .g-xxl-2,\n.gy-xxl-2 {\n    --bs-gutter-y: 0.5rem;\n  }\n\n  .g-xxl-3,\n.gx-xxl-3 {\n    --bs-gutter-x: 1rem;\n  }\n\n  .g-xxl-3,\n.gy-xxl-3 {\n    --bs-gutter-y: 1rem;\n  }\n\n  .g-xxl-4,\n.gx-xxl-4 {\n    --bs-gutter-x: 1.5rem;\n  }\n\n  .g-xxl-4,\n.gy-xxl-4 {\n    --bs-gutter-y: 1.5rem;\n  }\n\n  .g-xxl-5,\n.gx-xxl-5 {\n    --bs-gutter-x: 3rem;\n  }\n\n  .g-xxl-5,\n.gy-xxl-5 {\n    --bs-gutter-y: 3rem;\n  }\n}\n.d-inline {\n  display: inline !important;\n}\n\n.d-inline-block {\n  display: inline-block !important;\n}\n\n.d-block {\n  display: block !important;\n}\n\n.d-grid {\n  display: grid !important;\n}\n\n.d-table {\n  display: table !important;\n}\n\n.d-table-row {\n  display: table-row !important;\n}\n\n.d-table-cell {\n  display: table-cell !important;\n}\n\n.d-flex {\n  display: flex !important;\n}\n\n.d-inline-flex {\n  display: inline-flex !important;\n}\n\n.d-none {\n  display: none !important;\n}\n\n.flex-fill {\n  flex: 1 1 auto !important;\n}\n\n.flex-row {\n  flex-direction: row !important;\n}\n\n.flex-column {\n  flex-direction: column !important;\n}\n\n.flex-row-reverse {\n  flex-direction: row-reverse !important;\n}\n\n.flex-column-reverse {\n  flex-direction: column-reverse !important;\n}\n\n.flex-grow-0 {\n  flex-grow: 0 !important;\n}\n\n.flex-grow-1 {\n  flex-grow: 1 !important;\n}\n\n.flex-shrink-0 {\n  flex-shrink: 0 !important;\n}\n\n.flex-shrink-1 {\n  flex-shrink: 1 !important;\n}\n\n.flex-wrap {\n  flex-wrap: wrap !important;\n}\n\n.flex-nowrap {\n  flex-wrap: nowrap !important;\n}\n\n.flex-wrap-reverse {\n  flex-wrap: wrap-reverse !important;\n}\n\n.justify-content-start {\n  justify-content: flex-start !important;\n}\n\n.justify-content-end {\n  justify-content: flex-end !important;\n}\n\n.justify-content-center {\n  justify-content: center !important;\n}\n\n.justify-content-between {\n  justify-content: space-between !important;\n}\n\n.justify-content-around {\n  justify-content: space-around !important;\n}\n\n.justify-content-evenly {\n  justify-content: space-evenly !important;\n}\n\n.align-items-start {\n  align-items: flex-start !important;\n}\n\n.align-items-end {\n  align-items: flex-end !important;\n}\n\n.align-items-center {\n  align-items: center !important;\n}\n\n.align-items-baseline {\n  align-items: baseline !important;\n}\n\n.align-items-stretch {\n  align-items: stretch !important;\n}\n\n.align-content-start {\n  align-content: flex-start !important;\n}\n\n.align-content-end {\n  align-content: flex-end !important;\n}\n\n.align-content-center {\n  align-content: center !important;\n}\n\n.align-content-between {\n  align-content: space-between !important;\n}\n\n.align-content-around {\n  align-content: space-around !important;\n}\n\n.align-content-stretch {\n  align-content: stretch !important;\n}\n\n.align-self-auto {\n  align-self: auto !important;\n}\n\n.align-self-start {\n  align-self: flex-start !important;\n}\n\n.align-self-end {\n  align-self: flex-end !important;\n}\n\n.align-self-center {\n  align-self: center !important;\n}\n\n.align-self-baseline {\n  align-self: baseline !important;\n}\n\n.align-self-stretch {\n  align-self: stretch !important;\n}\n\n.order-first {\n  order: -1 !important;\n}\n\n.order-0 {\n  order: 0 !important;\n}\n\n.order-1 {\n  order: 1 !important;\n}\n\n.order-2 {\n  order: 2 !important;\n}\n\n.order-3 {\n  order: 3 !important;\n}\n\n.order-4 {\n  order: 4 !important;\n}\n\n.order-5 {\n  order: 5 !important;\n}\n\n.order-last {\n  order: 6 !important;\n}\n\n.m-0 {\n  margin: 0 !important;\n}\n\n.m-1 {\n  margin: 0.25rem !important;\n}\n\n.m-2 {\n  margin: 0.5rem !important;\n}\n\n.m-3 {\n  margin: 1rem !important;\n}\n\n.m-4 {\n  margin: 1.5rem !important;\n}\n\n.m-5 {\n  margin: 3rem !important;\n}\n\n.m-auto {\n  margin: auto !important;\n}\n\n.mx-0 {\n  margin-right: 0 !important;\n  margin-left: 0 !important;\n}\n\n.mx-1 {\n  margin-right: 0.25rem !important;\n  margin-left: 0.25rem !important;\n}\n\n.mx-2 {\n  margin-right: 0.5rem !important;\n  margin-left: 0.5rem !important;\n}\n\n.mx-3 {\n  margin-right: 1rem !important;\n  margin-left: 1rem !important;\n}\n\n.mx-4 {\n  margin-right: 1.5rem !important;\n  margin-left: 1.5rem !important;\n}\n\n.mx-5 {\n  margin-right: 3rem !important;\n  margin-left: 3rem !important;\n}\n\n.mx-auto {\n  margin-right: auto !important;\n  margin-left: auto !important;\n}\n\n.my-0 {\n  margin-top: 0 !important;\n  margin-bottom: 0 !important;\n}\n\n.my-1 {\n  margin-top: 0.25rem !important;\n  margin-bottom: 0.25rem !important;\n}\n\n.my-2 {\n  margin-top: 0.5rem !important;\n  margin-bottom: 0.5rem !important;\n}\n\n.my-3 {\n  margin-top: 1rem !important;\n  margin-bottom: 1rem !important;\n}\n\n.my-4 {\n  margin-top: 1.5rem !important;\n  margin-bottom: 1.5rem !important;\n}\n\n.my-5 {\n  margin-top: 3rem !important;\n  margin-bottom: 3rem !important;\n}\n\n.my-auto {\n  margin-top: auto !important;\n  margin-bottom: auto !important;\n}\n\n.mt-0 {\n  margin-top: 0 !important;\n}\n\n.mt-1 {\n  margin-top: 0.25rem !important;\n}\n\n.mt-2 {\n  margin-top: 0.5rem !important;\n}\n\n.mt-3 {\n  margin-top: 1rem !important;\n}\n\n.mt-4 {\n  margin-top: 1.5rem !important;\n}\n\n.mt-5 {\n  margin-top: 3rem !important;\n}\n\n.mt-auto {\n  margin-top: auto !important;\n}\n\n.me-0 {\n  margin-right: 0 !important;\n}\n\n.me-1 {\n  margin-right: 0.25rem !important;\n}\n\n.me-2 {\n  margin-right: 0.5rem !important;\n}\n\n.me-3 {\n  margin-right: 1rem !important;\n}\n\n.me-4 {\n  margin-right: 1.5rem !important;\n}\n\n.me-5 {\n  margin-right: 3rem !important;\n}\n\n.me-auto {\n  margin-right: auto !important;\n}\n\n.mb-0 {\n  margin-bottom: 0 !important;\n}\n\n.mb-1 {\n  margin-bottom: 0.25rem !important;\n}\n\n.mb-2 {\n  margin-bottom: 0.5rem !important;\n}\n\n.mb-3 {\n  margin-bottom: 1rem !important;\n}\n\n.mb-4 {\n  margin-bottom: 1.5rem !important;\n}\n\n.mb-5 {\n  margin-bottom: 3rem !important;\n}\n\n.mb-auto {\n  margin-bottom: auto !important;\n}\n\n.ms-0 {\n  margin-left: 0 !important;\n}\n\n.ms-1 {\n  margin-left: 0.25rem !important;\n}\n\n.ms-2 {\n  margin-left: 0.5rem !important;\n}\n\n.ms-3 {\n  margin-left: 1rem !important;\n}\n\n.ms-4 {\n  margin-left: 1.5rem !important;\n}\n\n.ms-5 {\n  margin-left: 3rem !important;\n}\n\n.ms-auto {\n  margin-left: auto !important;\n}\n\n.p-0 {\n  padding: 0 !important;\n}\n\n.p-1 {\n  padding: 0.25rem !important;\n}\n\n.p-2 {\n  padding: 0.5rem !important;\n}\n\n.p-3 {\n  padding: 1rem !important;\n}\n\n.p-4 {\n  padding: 1.5rem !important;\n}\n\n.p-5 {\n  padding: 3rem !important;\n}\n\n.px-0 {\n  padding-right: 0 !important;\n  padding-left: 0 !important;\n}\n\n.px-1 {\n  padding-right: 0.25rem !important;\n  padding-left: 0.25rem !important;\n}\n\n.px-2 {\n  padding-right: 0.5rem !important;\n  padding-left: 0.5rem !important;\n}\n\n.px-3 {\n  padding-right: 1rem !important;\n  padding-left: 1rem !important;\n}\n\n.px-4 {\n  padding-right: 1.5rem !important;\n  padding-left: 1.5rem !important;\n}\n\n.px-5 {\n  padding-right: 3rem !important;\n  padding-left: 3rem !important;\n}\n\n.py-0 {\n  padding-top: 0 !important;\n  padding-bottom: 0 !important;\n}\n\n.py-1 {\n  padding-top: 0.25rem !important;\n  padding-bottom: 0.25rem !important;\n}\n\n.py-2 {\n  padding-top: 0.5rem !important;\n  padding-bottom: 0.5rem !important;\n}\n\n.py-3 {\n  padding-top: 1rem !important;\n  padding-bottom: 1rem !important;\n}\n\n.py-4 {\n  padding-top: 1.5rem !important;\n  padding-bottom: 1.5rem !important;\n}\n\n.py-5 {\n  padding-top: 3rem !important;\n  padding-bottom: 3rem !important;\n}\n\n.pt-0 {\n  padding-top: 0 !important;\n}\n\n.pt-1 {\n  padding-top: 0.25rem !important;\n}\n\n.pt-2 {\n  padding-top: 0.5rem !important;\n}\n\n.pt-3 {\n  padding-top: 1rem !important;\n}\n\n.pt-4 {\n  padding-top: 1.5rem !important;\n}\n\n.pt-5 {\n  padding-top: 3rem !important;\n}\n\n.pe-0 {\n  padding-right: 0 !important;\n}\n\n.pe-1 {\n  padding-right: 0.25rem !important;\n}\n\n.pe-2 {\n  padding-right: 0.5rem !important;\n}\n\n.pe-3 {\n  padding-right: 1rem !important;\n}\n\n.pe-4 {\n  padding-right: 1.5rem !important;\n}\n\n.pe-5 {\n  padding-right: 3rem !important;\n}\n\n.pb-0 {\n  padding-bottom: 0 !important;\n}\n\n.pb-1 {\n  padding-bottom: 0.25rem !important;\n}\n\n.pb-2 {\n  padding-bottom: 0.5rem !important;\n}\n\n.pb-3 {\n  padding-bottom: 1rem !important;\n}\n\n.pb-4 {\n  padding-bottom: 1.5rem !important;\n}\n\n.pb-5 {\n  padding-bottom: 3rem !important;\n}\n\n.ps-0 {\n  padding-left: 0 !important;\n}\n\n.ps-1 {\n  padding-left: 0.25rem !important;\n}\n\n.ps-2 {\n  padding-left: 0.5rem !important;\n}\n\n.ps-3 {\n  padding-left: 1rem !important;\n}\n\n.ps-4 {\n  padding-left: 1.5rem !important;\n}\n\n.ps-5 {\n  padding-left: 3rem !important;\n}\n\n@media (min-width: 576px) {\n  .d-sm-inline {\n    display: inline !important;\n  }\n\n  .d-sm-inline-block {\n    display: inline-block !important;\n  }\n\n  .d-sm-block {\n    display: block !important;\n  }\n\n  .d-sm-grid {\n    display: grid !important;\n  }\n\n  .d-sm-table {\n    display: table !important;\n  }\n\n  .d-sm-table-row {\n    display: table-row !important;\n  }\n\n  .d-sm-table-cell {\n    display: table-cell !important;\n  }\n\n  .d-sm-flex {\n    display: flex !important;\n  }\n\n  .d-sm-inline-flex {\n    display: inline-flex !important;\n  }\n\n  .d-sm-none {\n    display: none !important;\n  }\n\n  .flex-sm-fill {\n    flex: 1 1 auto !important;\n  }\n\n  .flex-sm-row {\n    flex-direction: row !important;\n  }\n\n  .flex-sm-column {\n    flex-direction: column !important;\n  }\n\n  .flex-sm-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n\n  .flex-sm-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n\n  .flex-sm-grow-0 {\n    flex-grow: 0 !important;\n  }\n\n  .flex-sm-grow-1 {\n    flex-grow: 1 !important;\n  }\n\n  .flex-sm-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n\n  .flex-sm-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n\n  .flex-sm-wrap {\n    flex-wrap: wrap !important;\n  }\n\n  .flex-sm-nowrap {\n    flex-wrap: nowrap !important;\n  }\n\n  .flex-sm-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n\n  .justify-content-sm-start {\n    justify-content: flex-start !important;\n  }\n\n  .justify-content-sm-end {\n    justify-content: flex-end !important;\n  }\n\n  .justify-content-sm-center {\n    justify-content: center !important;\n  }\n\n  .justify-content-sm-between {\n    justify-content: space-between !important;\n  }\n\n  .justify-content-sm-around {\n    justify-content: space-around !important;\n  }\n\n  .justify-content-sm-evenly {\n    justify-content: space-evenly !important;\n  }\n\n  .align-items-sm-start {\n    align-items: flex-start !important;\n  }\n\n  .align-items-sm-end {\n    align-items: flex-end !important;\n  }\n\n  .align-items-sm-center {\n    align-items: center !important;\n  }\n\n  .align-items-sm-baseline {\n    align-items: baseline !important;\n  }\n\n  .align-items-sm-stretch {\n    align-items: stretch !important;\n  }\n\n  .align-content-sm-start {\n    align-content: flex-start !important;\n  }\n\n  .align-content-sm-end {\n    align-content: flex-end !important;\n  }\n\n  .align-content-sm-center {\n    align-content: center !important;\n  }\n\n  .align-content-sm-between {\n    align-content: space-between !important;\n  }\n\n  .align-content-sm-around {\n    align-content: space-around !important;\n  }\n\n  .align-content-sm-stretch {\n    align-content: stretch !important;\n  }\n\n  .align-self-sm-auto {\n    align-self: auto !important;\n  }\n\n  .align-self-sm-start {\n    align-self: flex-start !important;\n  }\n\n  .align-self-sm-end {\n    align-self: flex-end !important;\n  }\n\n  .align-self-sm-center {\n    align-self: center !important;\n  }\n\n  .align-self-sm-baseline {\n    align-self: baseline !important;\n  }\n\n  .align-self-sm-stretch {\n    align-self: stretch !important;\n  }\n\n  .order-sm-first {\n    order: -1 !important;\n  }\n\n  .order-sm-0 {\n    order: 0 !important;\n  }\n\n  .order-sm-1 {\n    order: 1 !important;\n  }\n\n  .order-sm-2 {\n    order: 2 !important;\n  }\n\n  .order-sm-3 {\n    order: 3 !important;\n  }\n\n  .order-sm-4 {\n    order: 4 !important;\n  }\n\n  .order-sm-5 {\n    order: 5 !important;\n  }\n\n  .order-sm-last {\n    order: 6 !important;\n  }\n\n  .m-sm-0 {\n    margin: 0 !important;\n  }\n\n  .m-sm-1 {\n    margin: 0.25rem !important;\n  }\n\n  .m-sm-2 {\n    margin: 0.5rem !important;\n  }\n\n  .m-sm-3 {\n    margin: 1rem !important;\n  }\n\n  .m-sm-4 {\n    margin: 1.5rem !important;\n  }\n\n  .m-sm-5 {\n    margin: 3rem !important;\n  }\n\n  .m-sm-auto {\n    margin: auto !important;\n  }\n\n  .mx-sm-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n\n  .mx-sm-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0.25rem !important;\n  }\n\n  .mx-sm-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0.5rem !important;\n  }\n\n  .mx-sm-3 {\n    margin-right: 1rem !important;\n    margin-left: 1rem !important;\n  }\n\n  .mx-sm-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 1.5rem !important;\n  }\n\n  .mx-sm-5 {\n    margin-right: 3rem !important;\n    margin-left: 3rem !important;\n  }\n\n  .mx-sm-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n\n  .my-sm-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n\n  .my-sm-1 {\n    margin-top: 0.25rem !important;\n    margin-bottom: 0.25rem !important;\n  }\n\n  .my-sm-2 {\n    margin-top: 0.5rem !important;\n    margin-bottom: 0.5rem !important;\n  }\n\n  .my-sm-3 {\n    margin-top: 1rem !important;\n    margin-bottom: 1rem !important;\n  }\n\n  .my-sm-4 {\n    margin-top: 1.5rem !important;\n    margin-bottom: 1.5rem !important;\n  }\n\n  .my-sm-5 {\n    margin-top: 3rem !important;\n    margin-bottom: 3rem !important;\n  }\n\n  .my-sm-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n\n  .mt-sm-0 {\n    margin-top: 0 !important;\n  }\n\n  .mt-sm-1 {\n    margin-top: 0.25rem !important;\n  }\n\n  .mt-sm-2 {\n    margin-top: 0.5rem !important;\n  }\n\n  .mt-sm-3 {\n    margin-top: 1rem !important;\n  }\n\n  .mt-sm-4 {\n    margin-top: 1.5rem !important;\n  }\n\n  .mt-sm-5 {\n    margin-top: 3rem !important;\n  }\n\n  .mt-sm-auto {\n    margin-top: auto !important;\n  }\n\n  .me-sm-0 {\n    margin-right: 0 !important;\n  }\n\n  .me-sm-1 {\n    margin-right: 0.25rem !important;\n  }\n\n  .me-sm-2 {\n    margin-right: 0.5rem !important;\n  }\n\n  .me-sm-3 {\n    margin-right: 1rem !important;\n  }\n\n  .me-sm-4 {\n    margin-right: 1.5rem !important;\n  }\n\n  .me-sm-5 {\n    margin-right: 3rem !important;\n  }\n\n  .me-sm-auto {\n    margin-right: auto !important;\n  }\n\n  .mb-sm-0 {\n    margin-bottom: 0 !important;\n  }\n\n  .mb-sm-1 {\n    margin-bottom: 0.25rem !important;\n  }\n\n  .mb-sm-2 {\n    margin-bottom: 0.5rem !important;\n  }\n\n  .mb-sm-3 {\n    margin-bottom: 1rem !important;\n  }\n\n  .mb-sm-4 {\n    margin-bottom: 1.5rem !important;\n  }\n\n  .mb-sm-5 {\n    margin-bottom: 3rem !important;\n  }\n\n  .mb-sm-auto {\n    margin-bottom: auto !important;\n  }\n\n  .ms-sm-0 {\n    margin-left: 0 !important;\n  }\n\n  .ms-sm-1 {\n    margin-left: 0.25rem !important;\n  }\n\n  .ms-sm-2 {\n    margin-left: 0.5rem !important;\n  }\n\n  .ms-sm-3 {\n    margin-left: 1rem !important;\n  }\n\n  .ms-sm-4 {\n    margin-left: 1.5rem !important;\n  }\n\n  .ms-sm-5 {\n    margin-left: 3rem !important;\n  }\n\n  .ms-sm-auto {\n    margin-left: auto !important;\n  }\n\n  .p-sm-0 {\n    padding: 0 !important;\n  }\n\n  .p-sm-1 {\n    padding: 0.25rem !important;\n  }\n\n  .p-sm-2 {\n    padding: 0.5rem !important;\n  }\n\n  .p-sm-3 {\n    padding: 1rem !important;\n  }\n\n  .p-sm-4 {\n    padding: 1.5rem !important;\n  }\n\n  .p-sm-5 {\n    padding: 3rem !important;\n  }\n\n  .px-sm-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n\n  .px-sm-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0.25rem !important;\n  }\n\n  .px-sm-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0.5rem !important;\n  }\n\n  .px-sm-3 {\n    padding-right: 1rem !important;\n    padding-left: 1rem !important;\n  }\n\n  .px-sm-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 1.5rem !important;\n  }\n\n  .px-sm-5 {\n    padding-right: 3rem !important;\n    padding-left: 3rem !important;\n  }\n\n  .py-sm-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n\n  .py-sm-1 {\n    padding-top: 0.25rem !important;\n    padding-bottom: 0.25rem !important;\n  }\n\n  .py-sm-2 {\n    padding-top: 0.5rem !important;\n    padding-bottom: 0.5rem !important;\n  }\n\n  .py-sm-3 {\n    padding-top: 1rem !important;\n    padding-bottom: 1rem !important;\n  }\n\n  .py-sm-4 {\n    padding-top: 1.5rem !important;\n    padding-bottom: 1.5rem !important;\n  }\n\n  .py-sm-5 {\n    padding-top: 3rem !important;\n    padding-bottom: 3rem !important;\n  }\n\n  .pt-sm-0 {\n    padding-top: 0 !important;\n  }\n\n  .pt-sm-1 {\n    padding-top: 0.25rem !important;\n  }\n\n  .pt-sm-2 {\n    padding-top: 0.5rem !important;\n  }\n\n  .pt-sm-3 {\n    padding-top: 1rem !important;\n  }\n\n  .pt-sm-4 {\n    padding-top: 1.5rem !important;\n  }\n\n  .pt-sm-5 {\n    padding-top: 3rem !important;\n  }\n\n  .pe-sm-0 {\n    padding-right: 0 !important;\n  }\n\n  .pe-sm-1 {\n    padding-right: 0.25rem !important;\n  }\n\n  .pe-sm-2 {\n    padding-right: 0.5rem !important;\n  }\n\n  .pe-sm-3 {\n    padding-right: 1rem !important;\n  }\n\n  .pe-sm-4 {\n    padding-right: 1.5rem !important;\n  }\n\n  .pe-sm-5 {\n    padding-right: 3rem !important;\n  }\n\n  .pb-sm-0 {\n    padding-bottom: 0 !important;\n  }\n\n  .pb-sm-1 {\n    padding-bottom: 0.25rem !important;\n  }\n\n  .pb-sm-2 {\n    padding-bottom: 0.5rem !important;\n  }\n\n  .pb-sm-3 {\n    padding-bottom: 1rem !important;\n  }\n\n  .pb-sm-4 {\n    padding-bottom: 1.5rem !important;\n  }\n\n  .pb-sm-5 {\n    padding-bottom: 3rem !important;\n  }\n\n  .ps-sm-0 {\n    padding-left: 0 !important;\n  }\n\n  .ps-sm-1 {\n    padding-left: 0.25rem !important;\n  }\n\n  .ps-sm-2 {\n    padding-left: 0.5rem !important;\n  }\n\n  .ps-sm-3 {\n    padding-left: 1rem !important;\n  }\n\n  .ps-sm-4 {\n    padding-left: 1.5rem !important;\n  }\n\n  .ps-sm-5 {\n    padding-left: 3rem !important;\n  }\n}\n@media (min-width: 768px) {\n  .d-md-inline {\n    display: inline !important;\n  }\n\n  .d-md-inline-block {\n    display: inline-block !important;\n  }\n\n  .d-md-block {\n    display: block !important;\n  }\n\n  .d-md-grid {\n    display: grid !important;\n  }\n\n  .d-md-table {\n    display: table !important;\n  }\n\n  .d-md-table-row {\n    display: table-row !important;\n  }\n\n  .d-md-table-cell {\n    display: table-cell !important;\n  }\n\n  .d-md-flex {\n    display: flex !important;\n  }\n\n  .d-md-inline-flex {\n    display: inline-flex !important;\n  }\n\n  .d-md-none {\n    display: none !important;\n  }\n\n  .flex-md-fill {\n    flex: 1 1 auto !important;\n  }\n\n  .flex-md-row {\n    flex-direction: row !important;\n  }\n\n  .flex-md-column {\n    flex-direction: column !important;\n  }\n\n  .flex-md-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n\n  .flex-md-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n\n  .flex-md-grow-0 {\n    flex-grow: 0 !important;\n  }\n\n  .flex-md-grow-1 {\n    flex-grow: 1 !important;\n  }\n\n  .flex-md-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n\n  .flex-md-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n\n  .flex-md-wrap {\n    flex-wrap: wrap !important;\n  }\n\n  .flex-md-nowrap {\n    flex-wrap: nowrap !important;\n  }\n\n  .flex-md-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n\n  .justify-content-md-start {\n    justify-content: flex-start !important;\n  }\n\n  .justify-content-md-end {\n    justify-content: flex-end !important;\n  }\n\n  .justify-content-md-center {\n    justify-content: center !important;\n  }\n\n  .justify-content-md-between {\n    justify-content: space-between !important;\n  }\n\n  .justify-content-md-around {\n    justify-content: space-around !important;\n  }\n\n  .justify-content-md-evenly {\n    justify-content: space-evenly !important;\n  }\n\n  .align-items-md-start {\n    align-items: flex-start !important;\n  }\n\n  .align-items-md-end {\n    align-items: flex-end !important;\n  }\n\n  .align-items-md-center {\n    align-items: center !important;\n  }\n\n  .align-items-md-baseline {\n    align-items: baseline !important;\n  }\n\n  .align-items-md-stretch {\n    align-items: stretch !important;\n  }\n\n  .align-content-md-start {\n    align-content: flex-start !important;\n  }\n\n  .align-content-md-end {\n    align-content: flex-end !important;\n  }\n\n  .align-content-md-center {\n    align-content: center !important;\n  }\n\n  .align-content-md-between {\n    align-content: space-between !important;\n  }\n\n  .align-content-md-around {\n    align-content: space-around !important;\n  }\n\n  .align-content-md-stretch {\n    align-content: stretch !important;\n  }\n\n  .align-self-md-auto {\n    align-self: auto !important;\n  }\n\n  .align-self-md-start {\n    align-self: flex-start !important;\n  }\n\n  .align-self-md-end {\n    align-self: flex-end !important;\n  }\n\n  .align-self-md-center {\n    align-self: center !important;\n  }\n\n  .align-self-md-baseline {\n    align-self: baseline !important;\n  }\n\n  .align-self-md-stretch {\n    align-self: stretch !important;\n  }\n\n  .order-md-first {\n    order: -1 !important;\n  }\n\n  .order-md-0 {\n    order: 0 !important;\n  }\n\n  .order-md-1 {\n    order: 1 !important;\n  }\n\n  .order-md-2 {\n    order: 2 !important;\n  }\n\n  .order-md-3 {\n    order: 3 !important;\n  }\n\n  .order-md-4 {\n    order: 4 !important;\n  }\n\n  .order-md-5 {\n    order: 5 !important;\n  }\n\n  .order-md-last {\n    order: 6 !important;\n  }\n\n  .m-md-0 {\n    margin: 0 !important;\n  }\n\n  .m-md-1 {\n    margin: 0.25rem !important;\n  }\n\n  .m-md-2 {\n    margin: 0.5rem !important;\n  }\n\n  .m-md-3 {\n    margin: 1rem !important;\n  }\n\n  .m-md-4 {\n    margin: 1.5rem !important;\n  }\n\n  .m-md-5 {\n    margin: 3rem !important;\n  }\n\n  .m-md-auto {\n    margin: auto !important;\n  }\n\n  .mx-md-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n\n  .mx-md-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0.25rem !important;\n  }\n\n  .mx-md-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0.5rem !important;\n  }\n\n  .mx-md-3 {\n    margin-right: 1rem !important;\n    margin-left: 1rem !important;\n  }\n\n  .mx-md-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 1.5rem !important;\n  }\n\n  .mx-md-5 {\n    margin-right: 3rem !important;\n    margin-left: 3rem !important;\n  }\n\n  .mx-md-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n\n  .my-md-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n\n  .my-md-1 {\n    margin-top: 0.25rem !important;\n    margin-bottom: 0.25rem !important;\n  }\n\n  .my-md-2 {\n    margin-top: 0.5rem !important;\n    margin-bottom: 0.5rem !important;\n  }\n\n  .my-md-3 {\n    margin-top: 1rem !important;\n    margin-bottom: 1rem !important;\n  }\n\n  .my-md-4 {\n    margin-top: 1.5rem !important;\n    margin-bottom: 1.5rem !important;\n  }\n\n  .my-md-5 {\n    margin-top: 3rem !important;\n    margin-bottom: 3rem !important;\n  }\n\n  .my-md-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n\n  .mt-md-0 {\n    margin-top: 0 !important;\n  }\n\n  .mt-md-1 {\n    margin-top: 0.25rem !important;\n  }\n\n  .mt-md-2 {\n    margin-top: 0.5rem !important;\n  }\n\n  .mt-md-3 {\n    margin-top: 1rem !important;\n  }\n\n  .mt-md-4 {\n    margin-top: 1.5rem !important;\n  }\n\n  .mt-md-5 {\n    margin-top: 3rem !important;\n  }\n\n  .mt-md-auto {\n    margin-top: auto !important;\n  }\n\n  .me-md-0 {\n    margin-right: 0 !important;\n  }\n\n  .me-md-1 {\n    margin-right: 0.25rem !important;\n  }\n\n  .me-md-2 {\n    margin-right: 0.5rem !important;\n  }\n\n  .me-md-3 {\n    margin-right: 1rem !important;\n  }\n\n  .me-md-4 {\n    margin-right: 1.5rem !important;\n  }\n\n  .me-md-5 {\n    margin-right: 3rem !important;\n  }\n\n  .me-md-auto {\n    margin-right: auto !important;\n  }\n\n  .mb-md-0 {\n    margin-bottom: 0 !important;\n  }\n\n  .mb-md-1 {\n    margin-bottom: 0.25rem !important;\n  }\n\n  .mb-md-2 {\n    margin-bottom: 0.5rem !important;\n  }\n\n  .mb-md-3 {\n    margin-bottom: 1rem !important;\n  }\n\n  .mb-md-4 {\n    margin-bottom: 1.5rem !important;\n  }\n\n  .mb-md-5 {\n    margin-bottom: 3rem !important;\n  }\n\n  .mb-md-auto {\n    margin-bottom: auto !important;\n  }\n\n  .ms-md-0 {\n    margin-left: 0 !important;\n  }\n\n  .ms-md-1 {\n    margin-left: 0.25rem !important;\n  }\n\n  .ms-md-2 {\n    margin-left: 0.5rem !important;\n  }\n\n  .ms-md-3 {\n    margin-left: 1rem !important;\n  }\n\n  .ms-md-4 {\n    margin-left: 1.5rem !important;\n  }\n\n  .ms-md-5 {\n    margin-left: 3rem !important;\n  }\n\n  .ms-md-auto {\n    margin-left: auto !important;\n  }\n\n  .p-md-0 {\n    padding: 0 !important;\n  }\n\n  .p-md-1 {\n    padding: 0.25rem !important;\n  }\n\n  .p-md-2 {\n    padding: 0.5rem !important;\n  }\n\n  .p-md-3 {\n    padding: 1rem !important;\n  }\n\n  .p-md-4 {\n    padding: 1.5rem !important;\n  }\n\n  .p-md-5 {\n    padding: 3rem !important;\n  }\n\n  .px-md-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n\n  .px-md-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0.25rem !important;\n  }\n\n  .px-md-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0.5rem !important;\n  }\n\n  .px-md-3 {\n    padding-right: 1rem !important;\n    padding-left: 1rem !important;\n  }\n\n  .px-md-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 1.5rem !important;\n  }\n\n  .px-md-5 {\n    padding-right: 3rem !important;\n    padding-left: 3rem !important;\n  }\n\n  .py-md-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n\n  .py-md-1 {\n    padding-top: 0.25rem !important;\n    padding-bottom: 0.25rem !important;\n  }\n\n  .py-md-2 {\n    padding-top: 0.5rem !important;\n    padding-bottom: 0.5rem !important;\n  }\n\n  .py-md-3 {\n    padding-top: 1rem !important;\n    padding-bottom: 1rem !important;\n  }\n\n  .py-md-4 {\n    padding-top: 1.5rem !important;\n    padding-bottom: 1.5rem !important;\n  }\n\n  .py-md-5 {\n    padding-top: 3rem !important;\n    padding-bottom: 3rem !important;\n  }\n\n  .pt-md-0 {\n    padding-top: 0 !important;\n  }\n\n  .pt-md-1 {\n    padding-top: 0.25rem !important;\n  }\n\n  .pt-md-2 {\n    padding-top: 0.5rem !important;\n  }\n\n  .pt-md-3 {\n    padding-top: 1rem !important;\n  }\n\n  .pt-md-4 {\n    padding-top: 1.5rem !important;\n  }\n\n  .pt-md-5 {\n    padding-top: 3rem !important;\n  }\n\n  .pe-md-0 {\n    padding-right: 0 !important;\n  }\n\n  .pe-md-1 {\n    padding-right: 0.25rem !important;\n  }\n\n  .pe-md-2 {\n    padding-right: 0.5rem !important;\n  }\n\n  .pe-md-3 {\n    padding-right: 1rem !important;\n  }\n\n  .pe-md-4 {\n    padding-right: 1.5rem !important;\n  }\n\n  .pe-md-5 {\n    padding-right: 3rem !important;\n  }\n\n  .pb-md-0 {\n    padding-bottom: 0 !important;\n  }\n\n  .pb-md-1 {\n    padding-bottom: 0.25rem !important;\n  }\n\n  .pb-md-2 {\n    padding-bottom: 0.5rem !important;\n  }\n\n  .pb-md-3 {\n    padding-bottom: 1rem !important;\n  }\n\n  .pb-md-4 {\n    padding-bottom: 1.5rem !important;\n  }\n\n  .pb-md-5 {\n    padding-bottom: 3rem !important;\n  }\n\n  .ps-md-0 {\n    padding-left: 0 !important;\n  }\n\n  .ps-md-1 {\n    padding-left: 0.25rem !important;\n  }\n\n  .ps-md-2 {\n    padding-left: 0.5rem !important;\n  }\n\n  .ps-md-3 {\n    padding-left: 1rem !important;\n  }\n\n  .ps-md-4 {\n    padding-left: 1.5rem !important;\n  }\n\n  .ps-md-5 {\n    padding-left: 3rem !important;\n  }\n}\n@media (min-width: 992px) {\n  .d-lg-inline {\n    display: inline !important;\n  }\n\n  .d-lg-inline-block {\n    display: inline-block !important;\n  }\n\n  .d-lg-block {\n    display: block !important;\n  }\n\n  .d-lg-grid {\n    display: grid !important;\n  }\n\n  .d-lg-table {\n    display: table !important;\n  }\n\n  .d-lg-table-row {\n    display: table-row !important;\n  }\n\n  .d-lg-table-cell {\n    display: table-cell !important;\n  }\n\n  .d-lg-flex {\n    display: flex !important;\n  }\n\n  .d-lg-inline-flex {\n    display: inline-flex !important;\n  }\n\n  .d-lg-none {\n    display: none !important;\n  }\n\n  .flex-lg-fill {\n    flex: 1 1 auto !important;\n  }\n\n  .flex-lg-row {\n    flex-direction: row !important;\n  }\n\n  .flex-lg-column {\n    flex-direction: column !important;\n  }\n\n  .flex-lg-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n\n  .flex-lg-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n\n  .flex-lg-grow-0 {\n    flex-grow: 0 !important;\n  }\n\n  .flex-lg-grow-1 {\n    flex-grow: 1 !important;\n  }\n\n  .flex-lg-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n\n  .flex-lg-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n\n  .flex-lg-wrap {\n    flex-wrap: wrap !important;\n  }\n\n  .flex-lg-nowrap {\n    flex-wrap: nowrap !important;\n  }\n\n  .flex-lg-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n\n  .justify-content-lg-start {\n    justify-content: flex-start !important;\n  }\n\n  .justify-content-lg-end {\n    justify-content: flex-end !important;\n  }\n\n  .justify-content-lg-center {\n    justify-content: center !important;\n  }\n\n  .justify-content-lg-between {\n    justify-content: space-between !important;\n  }\n\n  .justify-content-lg-around {\n    justify-content: space-around !important;\n  }\n\n  .justify-content-lg-evenly {\n    justify-content: space-evenly !important;\n  }\n\n  .align-items-lg-start {\n    align-items: flex-start !important;\n  }\n\n  .align-items-lg-end {\n    align-items: flex-end !important;\n  }\n\n  .align-items-lg-center {\n    align-items: center !important;\n  }\n\n  .align-items-lg-baseline {\n    align-items: baseline !important;\n  }\n\n  .align-items-lg-stretch {\n    align-items: stretch !important;\n  }\n\n  .align-content-lg-start {\n    align-content: flex-start !important;\n  }\n\n  .align-content-lg-end {\n    align-content: flex-end !important;\n  }\n\n  .align-content-lg-center {\n    align-content: center !important;\n  }\n\n  .align-content-lg-between {\n    align-content: space-between !important;\n  }\n\n  .align-content-lg-around {\n    align-content: space-around !important;\n  }\n\n  .align-content-lg-stretch {\n    align-content: stretch !important;\n  }\n\n  .align-self-lg-auto {\n    align-self: auto !important;\n  }\n\n  .align-self-lg-start {\n    align-self: flex-start !important;\n  }\n\n  .align-self-lg-end {\n    align-self: flex-end !important;\n  }\n\n  .align-self-lg-center {\n    align-self: center !important;\n  }\n\n  .align-self-lg-baseline {\n    align-self: baseline !important;\n  }\n\n  .align-self-lg-stretch {\n    align-self: stretch !important;\n  }\n\n  .order-lg-first {\n    order: -1 !important;\n  }\n\n  .order-lg-0 {\n    order: 0 !important;\n  }\n\n  .order-lg-1 {\n    order: 1 !important;\n  }\n\n  .order-lg-2 {\n    order: 2 !important;\n  }\n\n  .order-lg-3 {\n    order: 3 !important;\n  }\n\n  .order-lg-4 {\n    order: 4 !important;\n  }\n\n  .order-lg-5 {\n    order: 5 !important;\n  }\n\n  .order-lg-last {\n    order: 6 !important;\n  }\n\n  .m-lg-0 {\n    margin: 0 !important;\n  }\n\n  .m-lg-1 {\n    margin: 0.25rem !important;\n  }\n\n  .m-lg-2 {\n    margin: 0.5rem !important;\n  }\n\n  .m-lg-3 {\n    margin: 1rem !important;\n  }\n\n  .m-lg-4 {\n    margin: 1.5rem !important;\n  }\n\n  .m-lg-5 {\n    margin: 3rem !important;\n  }\n\n  .m-lg-auto {\n    margin: auto !important;\n  }\n\n  .mx-lg-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n\n  .mx-lg-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0.25rem !important;\n  }\n\n  .mx-lg-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0.5rem !important;\n  }\n\n  .mx-lg-3 {\n    margin-right: 1rem !important;\n    margin-left: 1rem !important;\n  }\n\n  .mx-lg-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 1.5rem !important;\n  }\n\n  .mx-lg-5 {\n    margin-right: 3rem !important;\n    margin-left: 3rem !important;\n  }\n\n  .mx-lg-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n\n  .my-lg-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n\n  .my-lg-1 {\n    margin-top: 0.25rem !important;\n    margin-bottom: 0.25rem !important;\n  }\n\n  .my-lg-2 {\n    margin-top: 0.5rem !important;\n    margin-bottom: 0.5rem !important;\n  }\n\n  .my-lg-3 {\n    margin-top: 1rem !important;\n    margin-bottom: 1rem !important;\n  }\n\n  .my-lg-4 {\n    margin-top: 1.5rem !important;\n    margin-bottom: 1.5rem !important;\n  }\n\n  .my-lg-5 {\n    margin-top: 3rem !important;\n    margin-bottom: 3rem !important;\n  }\n\n  .my-lg-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n\n  .mt-lg-0 {\n    margin-top: 0 !important;\n  }\n\n  .mt-lg-1 {\n    margin-top: 0.25rem !important;\n  }\n\n  .mt-lg-2 {\n    margin-top: 0.5rem !important;\n  }\n\n  .mt-lg-3 {\n    margin-top: 1rem !important;\n  }\n\n  .mt-lg-4 {\n    margin-top: 1.5rem !important;\n  }\n\n  .mt-lg-5 {\n    margin-top: 3rem !important;\n  }\n\n  .mt-lg-auto {\n    margin-top: auto !important;\n  }\n\n  .me-lg-0 {\n    margin-right: 0 !important;\n  }\n\n  .me-lg-1 {\n    margin-right: 0.25rem !important;\n  }\n\n  .me-lg-2 {\n    margin-right: 0.5rem !important;\n  }\n\n  .me-lg-3 {\n    margin-right: 1rem !important;\n  }\n\n  .me-lg-4 {\n    margin-right: 1.5rem !important;\n  }\n\n  .me-lg-5 {\n    margin-right: 3rem !important;\n  }\n\n  .me-lg-auto {\n    margin-right: auto !important;\n  }\n\n  .mb-lg-0 {\n    margin-bottom: 0 !important;\n  }\n\n  .mb-lg-1 {\n    margin-bottom: 0.25rem !important;\n  }\n\n  .mb-lg-2 {\n    margin-bottom: 0.5rem !important;\n  }\n\n  .mb-lg-3 {\n    margin-bottom: 1rem !important;\n  }\n\n  .mb-lg-4 {\n    margin-bottom: 1.5rem !important;\n  }\n\n  .mb-lg-5 {\n    margin-bottom: 3rem !important;\n  }\n\n  .mb-lg-auto {\n    margin-bottom: auto !important;\n  }\n\n  .ms-lg-0 {\n    margin-left: 0 !important;\n  }\n\n  .ms-lg-1 {\n    margin-left: 0.25rem !important;\n  }\n\n  .ms-lg-2 {\n    margin-left: 0.5rem !important;\n  }\n\n  .ms-lg-3 {\n    margin-left: 1rem !important;\n  }\n\n  .ms-lg-4 {\n    margin-left: 1.5rem !important;\n  }\n\n  .ms-lg-5 {\n    margin-left: 3rem !important;\n  }\n\n  .ms-lg-auto {\n    margin-left: auto !important;\n  }\n\n  .p-lg-0 {\n    padding: 0 !important;\n  }\n\n  .p-lg-1 {\n    padding: 0.25rem !important;\n  }\n\n  .p-lg-2 {\n    padding: 0.5rem !important;\n  }\n\n  .p-lg-3 {\n    padding: 1rem !important;\n  }\n\n  .p-lg-4 {\n    padding: 1.5rem !important;\n  }\n\n  .p-lg-5 {\n    padding: 3rem !important;\n  }\n\n  .px-lg-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n\n  .px-lg-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0.25rem !important;\n  }\n\n  .px-lg-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0.5rem !important;\n  }\n\n  .px-lg-3 {\n    padding-right: 1rem !important;\n    padding-left: 1rem !important;\n  }\n\n  .px-lg-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 1.5rem !important;\n  }\n\n  .px-lg-5 {\n    padding-right: 3rem !important;\n    padding-left: 3rem !important;\n  }\n\n  .py-lg-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n\n  .py-lg-1 {\n    padding-top: 0.25rem !important;\n    padding-bottom: 0.25rem !important;\n  }\n\n  .py-lg-2 {\n    padding-top: 0.5rem !important;\n    padding-bottom: 0.5rem !important;\n  }\n\n  .py-lg-3 {\n    padding-top: 1rem !important;\n    padding-bottom: 1rem !important;\n  }\n\n  .py-lg-4 {\n    padding-top: 1.5rem !important;\n    padding-bottom: 1.5rem !important;\n  }\n\n  .py-lg-5 {\n    padding-top: 3rem !important;\n    padding-bottom: 3rem !important;\n  }\n\n  .pt-lg-0 {\n    padding-top: 0 !important;\n  }\n\n  .pt-lg-1 {\n    padding-top: 0.25rem !important;\n  }\n\n  .pt-lg-2 {\n    padding-top: 0.5rem !important;\n  }\n\n  .pt-lg-3 {\n    padding-top: 1rem !important;\n  }\n\n  .pt-lg-4 {\n    padding-top: 1.5rem !important;\n  }\n\n  .pt-lg-5 {\n    padding-top: 3rem !important;\n  }\n\n  .pe-lg-0 {\n    padding-right: 0 !important;\n  }\n\n  .pe-lg-1 {\n    padding-right: 0.25rem !important;\n  }\n\n  .pe-lg-2 {\n    padding-right: 0.5rem !important;\n  }\n\n  .pe-lg-3 {\n    padding-right: 1rem !important;\n  }\n\n  .pe-lg-4 {\n    padding-right: 1.5rem !important;\n  }\n\n  .pe-lg-5 {\n    padding-right: 3rem !important;\n  }\n\n  .pb-lg-0 {\n    padding-bottom: 0 !important;\n  }\n\n  .pb-lg-1 {\n    padding-bottom: 0.25rem !important;\n  }\n\n  .pb-lg-2 {\n    padding-bottom: 0.5rem !important;\n  }\n\n  .pb-lg-3 {\n    padding-bottom: 1rem !important;\n  }\n\n  .pb-lg-4 {\n    padding-bottom: 1.5rem !important;\n  }\n\n  .pb-lg-5 {\n    padding-bottom: 3rem !important;\n  }\n\n  .ps-lg-0 {\n    padding-left: 0 !important;\n  }\n\n  .ps-lg-1 {\n    padding-left: 0.25rem !important;\n  }\n\n  .ps-lg-2 {\n    padding-left: 0.5rem !important;\n  }\n\n  .ps-lg-3 {\n    padding-left: 1rem !important;\n  }\n\n  .ps-lg-4 {\n    padding-left: 1.5rem !important;\n  }\n\n  .ps-lg-5 {\n    padding-left: 3rem !important;\n  }\n}\n@media (min-width: 1200px) {\n  .d-xl-inline {\n    display: inline !important;\n  }\n\n  .d-xl-inline-block {\n    display: inline-block !important;\n  }\n\n  .d-xl-block {\n    display: block !important;\n  }\n\n  .d-xl-grid {\n    display: grid !important;\n  }\n\n  .d-xl-table {\n    display: table !important;\n  }\n\n  .d-xl-table-row {\n    display: table-row !important;\n  }\n\n  .d-xl-table-cell {\n    display: table-cell !important;\n  }\n\n  .d-xl-flex {\n    display: flex !important;\n  }\n\n  .d-xl-inline-flex {\n    display: inline-flex !important;\n  }\n\n  .d-xl-none {\n    display: none !important;\n  }\n\n  .flex-xl-fill {\n    flex: 1 1 auto !important;\n  }\n\n  .flex-xl-row {\n    flex-direction: row !important;\n  }\n\n  .flex-xl-column {\n    flex-direction: column !important;\n  }\n\n  .flex-xl-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n\n  .flex-xl-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n\n  .flex-xl-grow-0 {\n    flex-grow: 0 !important;\n  }\n\n  .flex-xl-grow-1 {\n    flex-grow: 1 !important;\n  }\n\n  .flex-xl-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n\n  .flex-xl-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n\n  .flex-xl-wrap {\n    flex-wrap: wrap !important;\n  }\n\n  .flex-xl-nowrap {\n    flex-wrap: nowrap !important;\n  }\n\n  .flex-xl-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n\n  .justify-content-xl-start {\n    justify-content: flex-start !important;\n  }\n\n  .justify-content-xl-end {\n    justify-content: flex-end !important;\n  }\n\n  .justify-content-xl-center {\n    justify-content: center !important;\n  }\n\n  .justify-content-xl-between {\n    justify-content: space-between !important;\n  }\n\n  .justify-content-xl-around {\n    justify-content: space-around !important;\n  }\n\n  .justify-content-xl-evenly {\n    justify-content: space-evenly !important;\n  }\n\n  .align-items-xl-start {\n    align-items: flex-start !important;\n  }\n\n  .align-items-xl-end {\n    align-items: flex-end !important;\n  }\n\n  .align-items-xl-center {\n    align-items: center !important;\n  }\n\n  .align-items-xl-baseline {\n    align-items: baseline !important;\n  }\n\n  .align-items-xl-stretch {\n    align-items: stretch !important;\n  }\n\n  .align-content-xl-start {\n    align-content: flex-start !important;\n  }\n\n  .align-content-xl-end {\n    align-content: flex-end !important;\n  }\n\n  .align-content-xl-center {\n    align-content: center !important;\n  }\n\n  .align-content-xl-between {\n    align-content: space-between !important;\n  }\n\n  .align-content-xl-around {\n    align-content: space-around !important;\n  }\n\n  .align-content-xl-stretch {\n    align-content: stretch !important;\n  }\n\n  .align-self-xl-auto {\n    align-self: auto !important;\n  }\n\n  .align-self-xl-start {\n    align-self: flex-start !important;\n  }\n\n  .align-self-xl-end {\n    align-self: flex-end !important;\n  }\n\n  .align-self-xl-center {\n    align-self: center !important;\n  }\n\n  .align-self-xl-baseline {\n    align-self: baseline !important;\n  }\n\n  .align-self-xl-stretch {\n    align-self: stretch !important;\n  }\n\n  .order-xl-first {\n    order: -1 !important;\n  }\n\n  .order-xl-0 {\n    order: 0 !important;\n  }\n\n  .order-xl-1 {\n    order: 1 !important;\n  }\n\n  .order-xl-2 {\n    order: 2 !important;\n  }\n\n  .order-xl-3 {\n    order: 3 !important;\n  }\n\n  .order-xl-4 {\n    order: 4 !important;\n  }\n\n  .order-xl-5 {\n    order: 5 !important;\n  }\n\n  .order-xl-last {\n    order: 6 !important;\n  }\n\n  .m-xl-0 {\n    margin: 0 !important;\n  }\n\n  .m-xl-1 {\n    margin: 0.25rem !important;\n  }\n\n  .m-xl-2 {\n    margin: 0.5rem !important;\n  }\n\n  .m-xl-3 {\n    margin: 1rem !important;\n  }\n\n  .m-xl-4 {\n    margin: 1.5rem !important;\n  }\n\n  .m-xl-5 {\n    margin: 3rem !important;\n  }\n\n  .m-xl-auto {\n    margin: auto !important;\n  }\n\n  .mx-xl-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n\n  .mx-xl-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0.25rem !important;\n  }\n\n  .mx-xl-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0.5rem !important;\n  }\n\n  .mx-xl-3 {\n    margin-right: 1rem !important;\n    margin-left: 1rem !important;\n  }\n\n  .mx-xl-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 1.5rem !important;\n  }\n\n  .mx-xl-5 {\n    margin-right: 3rem !important;\n    margin-left: 3rem !important;\n  }\n\n  .mx-xl-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n\n  .my-xl-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n\n  .my-xl-1 {\n    margin-top: 0.25rem !important;\n    margin-bottom: 0.25rem !important;\n  }\n\n  .my-xl-2 {\n    margin-top: 0.5rem !important;\n    margin-bottom: 0.5rem !important;\n  }\n\n  .my-xl-3 {\n    margin-top: 1rem !important;\n    margin-bottom: 1rem !important;\n  }\n\n  .my-xl-4 {\n    margin-top: 1.5rem !important;\n    margin-bottom: 1.5rem !important;\n  }\n\n  .my-xl-5 {\n    margin-top: 3rem !important;\n    margin-bottom: 3rem !important;\n  }\n\n  .my-xl-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n\n  .mt-xl-0 {\n    margin-top: 0 !important;\n  }\n\n  .mt-xl-1 {\n    margin-top: 0.25rem !important;\n  }\n\n  .mt-xl-2 {\n    margin-top: 0.5rem !important;\n  }\n\n  .mt-xl-3 {\n    margin-top: 1rem !important;\n  }\n\n  .mt-xl-4 {\n    margin-top: 1.5rem !important;\n  }\n\n  .mt-xl-5 {\n    margin-top: 3rem !important;\n  }\n\n  .mt-xl-auto {\n    margin-top: auto !important;\n  }\n\n  .me-xl-0 {\n    margin-right: 0 !important;\n  }\n\n  .me-xl-1 {\n    margin-right: 0.25rem !important;\n  }\n\n  .me-xl-2 {\n    margin-right: 0.5rem !important;\n  }\n\n  .me-xl-3 {\n    margin-right: 1rem !important;\n  }\n\n  .me-xl-4 {\n    margin-right: 1.5rem !important;\n  }\n\n  .me-xl-5 {\n    margin-right: 3rem !important;\n  }\n\n  .me-xl-auto {\n    margin-right: auto !important;\n  }\n\n  .mb-xl-0 {\n    margin-bottom: 0 !important;\n  }\n\n  .mb-xl-1 {\n    margin-bottom: 0.25rem !important;\n  }\n\n  .mb-xl-2 {\n    margin-bottom: 0.5rem !important;\n  }\n\n  .mb-xl-3 {\n    margin-bottom: 1rem !important;\n  }\n\n  .mb-xl-4 {\n    margin-bottom: 1.5rem !important;\n  }\n\n  .mb-xl-5 {\n    margin-bottom: 3rem !important;\n  }\n\n  .mb-xl-auto {\n    margin-bottom: auto !important;\n  }\n\n  .ms-xl-0 {\n    margin-left: 0 !important;\n  }\n\n  .ms-xl-1 {\n    margin-left: 0.25rem !important;\n  }\n\n  .ms-xl-2 {\n    margin-left: 0.5rem !important;\n  }\n\n  .ms-xl-3 {\n    margin-left: 1rem !important;\n  }\n\n  .ms-xl-4 {\n    margin-left: 1.5rem !important;\n  }\n\n  .ms-xl-5 {\n    margin-left: 3rem !important;\n  }\n\n  .ms-xl-auto {\n    margin-left: auto !important;\n  }\n\n  .p-xl-0 {\n    padding: 0 !important;\n  }\n\n  .p-xl-1 {\n    padding: 0.25rem !important;\n  }\n\n  .p-xl-2 {\n    padding: 0.5rem !important;\n  }\n\n  .p-xl-3 {\n    padding: 1rem !important;\n  }\n\n  .p-xl-4 {\n    padding: 1.5rem !important;\n  }\n\n  .p-xl-5 {\n    padding: 3rem !important;\n  }\n\n  .px-xl-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n\n  .px-xl-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0.25rem !important;\n  }\n\n  .px-xl-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0.5rem !important;\n  }\n\n  .px-xl-3 {\n    padding-right: 1rem !important;\n    padding-left: 1rem !important;\n  }\n\n  .px-xl-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 1.5rem !important;\n  }\n\n  .px-xl-5 {\n    padding-right: 3rem !important;\n    padding-left: 3rem !important;\n  }\n\n  .py-xl-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n\n  .py-xl-1 {\n    padding-top: 0.25rem !important;\n    padding-bottom: 0.25rem !important;\n  }\n\n  .py-xl-2 {\n    padding-top: 0.5rem !important;\n    padding-bottom: 0.5rem !important;\n  }\n\n  .py-xl-3 {\n    padding-top: 1rem !important;\n    padding-bottom: 1rem !important;\n  }\n\n  .py-xl-4 {\n    padding-top: 1.5rem !important;\n    padding-bottom: 1.5rem !important;\n  }\n\n  .py-xl-5 {\n    padding-top: 3rem !important;\n    padding-bottom: 3rem !important;\n  }\n\n  .pt-xl-0 {\n    padding-top: 0 !important;\n  }\n\n  .pt-xl-1 {\n    padding-top: 0.25rem !important;\n  }\n\n  .pt-xl-2 {\n    padding-top: 0.5rem !important;\n  }\n\n  .pt-xl-3 {\n    padding-top: 1rem !important;\n  }\n\n  .pt-xl-4 {\n    padding-top: 1.5rem !important;\n  }\n\n  .pt-xl-5 {\n    padding-top: 3rem !important;\n  }\n\n  .pe-xl-0 {\n    padding-right: 0 !important;\n  }\n\n  .pe-xl-1 {\n    padding-right: 0.25rem !important;\n  }\n\n  .pe-xl-2 {\n    padding-right: 0.5rem !important;\n  }\n\n  .pe-xl-3 {\n    padding-right: 1rem !important;\n  }\n\n  .pe-xl-4 {\n    padding-right: 1.5rem !important;\n  }\n\n  .pe-xl-5 {\n    padding-right: 3rem !important;\n  }\n\n  .pb-xl-0 {\n    padding-bottom: 0 !important;\n  }\n\n  .pb-xl-1 {\n    padding-bottom: 0.25rem !important;\n  }\n\n  .pb-xl-2 {\n    padding-bottom: 0.5rem !important;\n  }\n\n  .pb-xl-3 {\n    padding-bottom: 1rem !important;\n  }\n\n  .pb-xl-4 {\n    padding-bottom: 1.5rem !important;\n  }\n\n  .pb-xl-5 {\n    padding-bottom: 3rem !important;\n  }\n\n  .ps-xl-0 {\n    padding-left: 0 !important;\n  }\n\n  .ps-xl-1 {\n    padding-left: 0.25rem !important;\n  }\n\n  .ps-xl-2 {\n    padding-left: 0.5rem !important;\n  }\n\n  .ps-xl-3 {\n    padding-left: 1rem !important;\n  }\n\n  .ps-xl-4 {\n    padding-left: 1.5rem !important;\n  }\n\n  .ps-xl-5 {\n    padding-left: 3rem !important;\n  }\n}\n@media (min-width: 1400px) {\n  .d-xxl-inline {\n    display: inline !important;\n  }\n\n  .d-xxl-inline-block {\n    display: inline-block !important;\n  }\n\n  .d-xxl-block {\n    display: block !important;\n  }\n\n  .d-xxl-grid {\n    display: grid !important;\n  }\n\n  .d-xxl-table {\n    display: table !important;\n  }\n\n  .d-xxl-table-row {\n    display: table-row !important;\n  }\n\n  .d-xxl-table-cell {\n    display: table-cell !important;\n  }\n\n  .d-xxl-flex {\n    display: flex !important;\n  }\n\n  .d-xxl-inline-flex {\n    display: inline-flex !important;\n  }\n\n  .d-xxl-none {\n    display: none !important;\n  }\n\n  .flex-xxl-fill {\n    flex: 1 1 auto !important;\n  }\n\n  .flex-xxl-row {\n    flex-direction: row !important;\n  }\n\n  .flex-xxl-column {\n    flex-direction: column !important;\n  }\n\n  .flex-xxl-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n\n  .flex-xxl-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n\n  .flex-xxl-grow-0 {\n    flex-grow: 0 !important;\n  }\n\n  .flex-xxl-grow-1 {\n    flex-grow: 1 !important;\n  }\n\n  .flex-xxl-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n\n  .flex-xxl-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n\n  .flex-xxl-wrap {\n    flex-wrap: wrap !important;\n  }\n\n  .flex-xxl-nowrap {\n    flex-wrap: nowrap !important;\n  }\n\n  .flex-xxl-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n\n  .justify-content-xxl-start {\n    justify-content: flex-start !important;\n  }\n\n  .justify-content-xxl-end {\n    justify-content: flex-end !important;\n  }\n\n  .justify-content-xxl-center {\n    justify-content: center !important;\n  }\n\n  .justify-content-xxl-between {\n    justify-content: space-between !important;\n  }\n\n  .justify-content-xxl-around {\n    justify-content: space-around !important;\n  }\n\n  .justify-content-xxl-evenly {\n    justify-content: space-evenly !important;\n  }\n\n  .align-items-xxl-start {\n    align-items: flex-start !important;\n  }\n\n  .align-items-xxl-end {\n    align-items: flex-end !important;\n  }\n\n  .align-items-xxl-center {\n    align-items: center !important;\n  }\n\n  .align-items-xxl-baseline {\n    align-items: baseline !important;\n  }\n\n  .align-items-xxl-stretch {\n    align-items: stretch !important;\n  }\n\n  .align-content-xxl-start {\n    align-content: flex-start !important;\n  }\n\n  .align-content-xxl-end {\n    align-content: flex-end !important;\n  }\n\n  .align-content-xxl-center {\n    align-content: center !important;\n  }\n\n  .align-content-xxl-between {\n    align-content: space-between !important;\n  }\n\n  .align-content-xxl-around {\n    align-content: space-around !important;\n  }\n\n  .align-content-xxl-stretch {\n    align-content: stretch !important;\n  }\n\n  .align-self-xxl-auto {\n    align-self: auto !important;\n  }\n\n  .align-self-xxl-start {\n    align-self: flex-start !important;\n  }\n\n  .align-self-xxl-end {\n    align-self: flex-end !important;\n  }\n\n  .align-self-xxl-center {\n    align-self: center !important;\n  }\n\n  .align-self-xxl-baseline {\n    align-self: baseline !important;\n  }\n\n  .align-self-xxl-stretch {\n    align-self: stretch !important;\n  }\n\n  .order-xxl-first {\n    order: -1 !important;\n  }\n\n  .order-xxl-0 {\n    order: 0 !important;\n  }\n\n  .order-xxl-1 {\n    order: 1 !important;\n  }\n\n  .order-xxl-2 {\n    order: 2 !important;\n  }\n\n  .order-xxl-3 {\n    order: 3 !important;\n  }\n\n  .order-xxl-4 {\n    order: 4 !important;\n  }\n\n  .order-xxl-5 {\n    order: 5 !important;\n  }\n\n  .order-xxl-last {\n    order: 6 !important;\n  }\n\n  .m-xxl-0 {\n    margin: 0 !important;\n  }\n\n  .m-xxl-1 {\n    margin: 0.25rem !important;\n  }\n\n  .m-xxl-2 {\n    margin: 0.5rem !important;\n  }\n\n  .m-xxl-3 {\n    margin: 1rem !important;\n  }\n\n  .m-xxl-4 {\n    margin: 1.5rem !important;\n  }\n\n  .m-xxl-5 {\n    margin: 3rem !important;\n  }\n\n  .m-xxl-auto {\n    margin: auto !important;\n  }\n\n  .mx-xxl-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n\n  .mx-xxl-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0.25rem !important;\n  }\n\n  .mx-xxl-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0.5rem !important;\n  }\n\n  .mx-xxl-3 {\n    margin-right: 1rem !important;\n    margin-left: 1rem !important;\n  }\n\n  .mx-xxl-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 1.5rem !important;\n  }\n\n  .mx-xxl-5 {\n    margin-right: 3rem !important;\n    margin-left: 3rem !important;\n  }\n\n  .mx-xxl-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n\n  .my-xxl-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n\n  .my-xxl-1 {\n    margin-top: 0.25rem !important;\n    margin-bottom: 0.25rem !important;\n  }\n\n  .my-xxl-2 {\n    margin-top: 0.5rem !important;\n    margin-bottom: 0.5rem !important;\n  }\n\n  .my-xxl-3 {\n    margin-top: 1rem !important;\n    margin-bottom: 1rem !important;\n  }\n\n  .my-xxl-4 {\n    margin-top: 1.5rem !important;\n    margin-bottom: 1.5rem !important;\n  }\n\n  .my-xxl-5 {\n    margin-top: 3rem !important;\n    margin-bottom: 3rem !important;\n  }\n\n  .my-xxl-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n\n  .mt-xxl-0 {\n    margin-top: 0 !important;\n  }\n\n  .mt-xxl-1 {\n    margin-top: 0.25rem !important;\n  }\n\n  .mt-xxl-2 {\n    margin-top: 0.5rem !important;\n  }\n\n  .mt-xxl-3 {\n    margin-top: 1rem !important;\n  }\n\n  .mt-xxl-4 {\n    margin-top: 1.5rem !important;\n  }\n\n  .mt-xxl-5 {\n    margin-top: 3rem !important;\n  }\n\n  .mt-xxl-auto {\n    margin-top: auto !important;\n  }\n\n  .me-xxl-0 {\n    margin-right: 0 !important;\n  }\n\n  .me-xxl-1 {\n    margin-right: 0.25rem !important;\n  }\n\n  .me-xxl-2 {\n    margin-right: 0.5rem !important;\n  }\n\n  .me-xxl-3 {\n    margin-right: 1rem !important;\n  }\n\n  .me-xxl-4 {\n    margin-right: 1.5rem !important;\n  }\n\n  .me-xxl-5 {\n    margin-right: 3rem !important;\n  }\n\n  .me-xxl-auto {\n    margin-right: auto !important;\n  }\n\n  .mb-xxl-0 {\n    margin-bottom: 0 !important;\n  }\n\n  .mb-xxl-1 {\n    margin-bottom: 0.25rem !important;\n  }\n\n  .mb-xxl-2 {\n    margin-bottom: 0.5rem !important;\n  }\n\n  .mb-xxl-3 {\n    margin-bottom: 1rem !important;\n  }\n\n  .mb-xxl-4 {\n    margin-bottom: 1.5rem !important;\n  }\n\n  .mb-xxl-5 {\n    margin-bottom: 3rem !important;\n  }\n\n  .mb-xxl-auto {\n    margin-bottom: auto !important;\n  }\n\n  .ms-xxl-0 {\n    margin-left: 0 !important;\n  }\n\n  .ms-xxl-1 {\n    margin-left: 0.25rem !important;\n  }\n\n  .ms-xxl-2 {\n    margin-left: 0.5rem !important;\n  }\n\n  .ms-xxl-3 {\n    margin-left: 1rem !important;\n  }\n\n  .ms-xxl-4 {\n    margin-left: 1.5rem !important;\n  }\n\n  .ms-xxl-5 {\n    margin-left: 3rem !important;\n  }\n\n  .ms-xxl-auto {\n    margin-left: auto !important;\n  }\n\n  .p-xxl-0 {\n    padding: 0 !important;\n  }\n\n  .p-xxl-1 {\n    padding: 0.25rem !important;\n  }\n\n  .p-xxl-2 {\n    padding: 0.5rem !important;\n  }\n\n  .p-xxl-3 {\n    padding: 1rem !important;\n  }\n\n  .p-xxl-4 {\n    padding: 1.5rem !important;\n  }\n\n  .p-xxl-5 {\n    padding: 3rem !important;\n  }\n\n  .px-xxl-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n\n  .px-xxl-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0.25rem !important;\n  }\n\n  .px-xxl-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0.5rem !important;\n  }\n\n  .px-xxl-3 {\n    padding-right: 1rem !important;\n    padding-left: 1rem !important;\n  }\n\n  .px-xxl-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 1.5rem !important;\n  }\n\n  .px-xxl-5 {\n    padding-right: 3rem !important;\n    padding-left: 3rem !important;\n  }\n\n  .py-xxl-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n\n  .py-xxl-1 {\n    padding-top: 0.25rem !important;\n    padding-bottom: 0.25rem !important;\n  }\n\n  .py-xxl-2 {\n    padding-top: 0.5rem !important;\n    padding-bottom: 0.5rem !important;\n  }\n\n  .py-xxl-3 {\n    padding-top: 1rem !important;\n    padding-bottom: 1rem !important;\n  }\n\n  .py-xxl-4 {\n    padding-top: 1.5rem !important;\n    padding-bottom: 1.5rem !important;\n  }\n\n  .py-xxl-5 {\n    padding-top: 3rem !important;\n    padding-bottom: 3rem !important;\n  }\n\n  .pt-xxl-0 {\n    padding-top: 0 !important;\n  }\n\n  .pt-xxl-1 {\n    padding-top: 0.25rem !important;\n  }\n\n  .pt-xxl-2 {\n    padding-top: 0.5rem !important;\n  }\n\n  .pt-xxl-3 {\n    padding-top: 1rem !important;\n  }\n\n  .pt-xxl-4 {\n    padding-top: 1.5rem !important;\n  }\n\n  .pt-xxl-5 {\n    padding-top: 3rem !important;\n  }\n\n  .pe-xxl-0 {\n    padding-right: 0 !important;\n  }\n\n  .pe-xxl-1 {\n    padding-right: 0.25rem !important;\n  }\n\n  .pe-xxl-2 {\n    padding-right: 0.5rem !important;\n  }\n\n  .pe-xxl-3 {\n    padding-right: 1rem !important;\n  }\n\n  .pe-xxl-4 {\n    padding-right: 1.5rem !important;\n  }\n\n  .pe-xxl-5 {\n    padding-right: 3rem !important;\n  }\n\n  .pb-xxl-0 {\n    padding-bottom: 0 !important;\n  }\n\n  .pb-xxl-1 {\n    padding-bottom: 0.25rem !important;\n  }\n\n  .pb-xxl-2 {\n    padding-bottom: 0.5rem !important;\n  }\n\n  .pb-xxl-3 {\n    padding-bottom: 1rem !important;\n  }\n\n  .pb-xxl-4 {\n    padding-bottom: 1.5rem !important;\n  }\n\n  .pb-xxl-5 {\n    padding-bottom: 3rem !important;\n  }\n\n  .ps-xxl-0 {\n    padding-left: 0 !important;\n  }\n\n  .ps-xxl-1 {\n    padding-left: 0.25rem !important;\n  }\n\n  .ps-xxl-2 {\n    padding-left: 0.5rem !important;\n  }\n\n  .ps-xxl-3 {\n    padding-left: 1rem !important;\n  }\n\n  .ps-xxl-4 {\n    padding-left: 1.5rem !important;\n  }\n\n  .ps-xxl-5 {\n    padding-left: 3rem !important;\n  }\n}\n@media print {\n  .d-print-inline {\n    display: inline !important;\n  }\n\n  .d-print-inline-block {\n    display: inline-block !important;\n  }\n\n  .d-print-block {\n    display: block !important;\n  }\n\n  .d-print-grid {\n    display: grid !important;\n  }\n\n  .d-print-table {\n    display: table !important;\n  }\n\n  .d-print-table-row {\n    display: table-row !important;\n  }\n\n  .d-print-table-cell {\n    display: table-cell !important;\n  }\n\n  .d-print-flex {\n    display: flex !important;\n  }\n\n  .d-print-inline-flex {\n    display: inline-flex !important;\n  }\n\n  .d-print-none {\n    display: none !important;\n  }\n}\n\n/*# sourceMappingURL=bootstrap-grid.css.map */\n", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @if not $n {\n    @error \"breakpoint `#{$name}` not found in `#{$breakpoints}`\";\n  }\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width.\n// The maximum value is reduced by 0.02px to work around the limitations of\n// `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $max: map-get($breakpoints, $name);\n  @return if($max and $max > 0, $max - .02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min:  breakpoint-min($name, $breakpoints);\n  $next: breakpoint-next($name, $breakpoints);\n  $max:  breakpoint-max($next);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($next, $breakpoints) {\n      @content;\n    }\n  }\n}\n", "// Variables\n//\n// Variables should follow the `$component-state-property-size` formula for\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\n\n// Color system\n\n// scss-docs-start gray-color-variables\n$white:    #fff !default;\n$gray-100: #f8f9fa !default;\n$gray-200: #e9ecef !default;\n$gray-300: #dee2e6 !default;\n$gray-400: #ced4da !default;\n$gray-500: #adb5bd !default;\n$gray-600: #6c757d !default;\n$gray-700: #495057 !default;\n$gray-800: #343a40 !default;\n$gray-900: #212529 !default;\n$black:    #000 !default;\n// scss-docs-end gray-color-variables\n\n// fusv-disable\n// scss-docs-start gray-colors-map\n$grays: (\n  \"100\": $gray-100,\n  \"200\": $gray-200,\n  \"300\": $gray-300,\n  \"400\": $gray-400,\n  \"500\": $gray-500,\n  \"600\": $gray-600,\n  \"700\": $gray-700,\n  \"800\": $gray-800,\n  \"900\": $gray-900\n) !default;\n// scss-docs-end gray-colors-map\n// fusv-enable\n\n// scss-docs-start color-variables\n$blue:    #0d6efd !default;\n$indigo:  #6610f2 !default;\n$purple:  #6f42c1 !default;\n$pink:    #d63384 !default;\n$red:     #dc3545 !default;\n$orange:  #fd7e14 !default;\n$yellow:  #ffc107 !default;\n$green:   #198754 !default;\n$teal:    #20c997 !default;\n$cyan:    #0dcaf0 !default;\n// scss-docs-end color-variables\n\n// scss-docs-start colors-map\n$colors: (\n  \"blue\":       $blue,\n  \"indigo\":     $indigo,\n  \"purple\":     $purple,\n  \"pink\":       $pink,\n  \"red\":        $red,\n  \"orange\":     $orange,\n  \"yellow\":     $yellow,\n  \"green\":      $green,\n  \"teal\":       $teal,\n  \"cyan\":       $cyan,\n  \"white\":      $white,\n  \"gray\":       $gray-600,\n  \"gray-dark\":  $gray-800\n) !default;\n// scss-docs-end colors-map\n\n// scss-docs-start theme-color-variables\n$primary:       $blue !default;\n$secondary:     $gray-600 !default;\n$success:       $green !default;\n$info:          $cyan !default;\n$warning:       $yellow !default;\n$danger:        $red !default;\n$light:         $gray-100 !default;\n$dark:          $gray-900 !default;\n// scss-docs-end theme-color-variables\n\n// scss-docs-start theme-colors-map\n$theme-colors: (\n  \"primary\":    $primary,\n  \"secondary\":  $secondary,\n  \"success\":    $success,\n  \"info\":       $info,\n  \"warning\":    $warning,\n  \"danger\":     $danger,\n  \"light\":      $light,\n  \"dark\":       $dark\n) !default;\n// scss-docs-end theme-colors-map\n\n// The contrast ratio to reach against white, to determine if color changes from \"light\" to \"dark\". Acceptable values for WCAG 2.0 are 3, 4.5 and 7.\n// See https://www.w3.org/TR/WCAG20/#visual-audio-contrast-contrast\n$min-contrast-ratio:   4.5 !default;\n\n// Customize the light and dark text colors for use in our color contrast function.\n$color-contrast-dark:      $black !default;\n$color-contrast-light:     $white !default;\n\n// fusv-disable\n$blue-100: tint-color($blue, 80%) !default;\n$blue-200: tint-color($blue, 60%) !default;\n$blue-300: tint-color($blue, 40%) !default;\n$blue-400: tint-color($blue, 20%) !default;\n$blue-500: $blue !default;\n$blue-600: shade-color($blue, 20%) !default;\n$blue-700: shade-color($blue, 40%) !default;\n$blue-800: shade-color($blue, 60%) !default;\n$blue-900: shade-color($blue, 80%) !default;\n\n$indigo-100: tint-color($indigo, 80%) !default;\n$indigo-200: tint-color($indigo, 60%) !default;\n$indigo-300: tint-color($indigo, 40%) !default;\n$indigo-400: tint-color($indigo, 20%) !default;\n$indigo-500: $indigo !default;\n$indigo-600: shade-color($indigo, 20%) !default;\n$indigo-700: shade-color($indigo, 40%) !default;\n$indigo-800: shade-color($indigo, 60%) !default;\n$indigo-900: shade-color($indigo, 80%) !default;\n\n$purple-100: tint-color($purple, 80%) !default;\n$purple-200: tint-color($purple, 60%) !default;\n$purple-300: tint-color($purple, 40%) !default;\n$purple-400: tint-color($purple, 20%) !default;\n$purple-500: $purple !default;\n$purple-600: shade-color($purple, 20%) !default;\n$purple-700: shade-color($purple, 40%) !default;\n$purple-800: shade-color($purple, 60%) !default;\n$purple-900: shade-color($purple, 80%) !default;\n\n$pink-100: tint-color($pink, 80%) !default;\n$pink-200: tint-color($pink, 60%) !default;\n$pink-300: tint-color($pink, 40%) !default;\n$pink-400: tint-color($pink, 20%) !default;\n$pink-500: $pink !default;\n$pink-600: shade-color($pink, 20%) !default;\n$pink-700: shade-color($pink, 40%) !default;\n$pink-800: shade-color($pink, 60%) !default;\n$pink-900: shade-color($pink, 80%) !default;\n\n$red-100: tint-color($red, 80%) !default;\n$red-200: tint-color($red, 60%) !default;\n$red-300: tint-color($red, 40%) !default;\n$red-400: tint-color($red, 20%) !default;\n$red-500: $red !default;\n$red-600: shade-color($red, 20%) !default;\n$red-700: shade-color($red, 40%) !default;\n$red-800: shade-color($red, 60%) !default;\n$red-900: shade-color($red, 80%) !default;\n\n$orange-100: tint-color($orange, 80%) !default;\n$orange-200: tint-color($orange, 60%) !default;\n$orange-300: tint-color($orange, 40%) !default;\n$orange-400: tint-color($orange, 20%) !default;\n$orange-500: $orange !default;\n$orange-600: shade-color($orange, 20%) !default;\n$orange-700: shade-color($orange, 40%) !default;\n$orange-800: shade-color($orange, 60%) !default;\n$orange-900: shade-color($orange, 80%) !default;\n\n$yellow-100: tint-color($yellow, 80%) !default;\n$yellow-200: tint-color($yellow, 60%) !default;\n$yellow-300: tint-color($yellow, 40%) !default;\n$yellow-400: tint-color($yellow, 20%) !default;\n$yellow-500: $yellow !default;\n$yellow-600: shade-color($yellow, 20%) !default;\n$yellow-700: shade-color($yellow, 40%) !default;\n$yellow-800: shade-color($yellow, 60%) !default;\n$yellow-900: shade-color($yellow, 80%) !default;\n\n$green-100: tint-color($green, 80%) !default;\n$green-200: tint-color($green, 60%) !default;\n$green-300: tint-color($green, 40%) !default;\n$green-400: tint-color($green, 20%) !default;\n$green-500: $green !default;\n$green-600: shade-color($green, 20%) !default;\n$green-700: shade-color($green, 40%) !default;\n$green-800: shade-color($green, 60%) !default;\n$green-900: shade-color($green, 80%) !default;\n\n$teal-100: tint-color($teal, 80%) !default;\n$teal-200: tint-color($teal, 60%) !default;\n$teal-300: tint-color($teal, 40%) !default;\n$teal-400: tint-color($teal, 20%) !default;\n$teal-500: $teal !default;\n$teal-600: shade-color($teal, 20%) !default;\n$teal-700: shade-color($teal, 40%) !default;\n$teal-800: shade-color($teal, 60%) !default;\n$teal-900: shade-color($teal, 80%) !default;\n\n$cyan-100: tint-color($cyan, 80%) !default;\n$cyan-200: tint-color($cyan, 60%) !default;\n$cyan-300: tint-color($cyan, 40%) !default;\n$cyan-400: tint-color($cyan, 20%) !default;\n$cyan-500: $cyan !default;\n$cyan-600: shade-color($cyan, 20%) !default;\n$cyan-700: shade-color($cyan, 40%) !default;\n$cyan-800: shade-color($cyan, 60%) !default;\n$cyan-900: shade-color($cyan, 80%) !default;\n// fusv-enable\n\n// Characters which are escaped by the escape-svg function\n$escaped-characters: (\n  (\"<\", \"%3c\"),\n  (\">\", \"%3e\"),\n  (\"#\", \"%23\"),\n  (\"(\", \"%28\"),\n  (\")\", \"%29\"),\n) !default;\n\n// Options\n//\n// Quickly modify global styling by enabling or disabling optional features.\n\n$enable-caret:                true !default;\n$enable-rounded:              true !default;\n$enable-shadows:              false !default;\n$enable-gradients:            false !default;\n$enable-transitions:          true !default;\n$enable-reduced-motion:       true !default;\n$enable-smooth-scroll:        true !default;\n$enable-grid-classes:         true !default;\n$enable-button-pointers:      true !default;\n$enable-rfs:                  true !default;\n$enable-validation-icons:     true !default;\n$enable-negative-margins:     false !default;\n$enable-deprecation-messages: true !default;\n$enable-important-utilities:  true !default;\n\n// Prefix for :root CSS variables\n\n$variable-prefix:             bs- !default;\n\n// Gradient\n//\n// The gradient which is added to components if `$enable-gradients` is `true`\n// This gradient is also added to elements with `.bg-gradient`\n// scss-docs-start variable-gradient\n$gradient: linear-gradient(180deg, rgba($white, .15), rgba($white, 0)) !default;\n// scss-docs-end variable-gradient\n\n// Spacing\n//\n// Control the default styling of most Bootstrap elements by modifying these\n// variables. Mostly focused on spacing.\n// You can add more entries to the $spacers map, should you need more variation.\n\n// scss-docs-start spacer-variables-maps\n$spacer: 1rem !default;\n$spacers: (\n  0: 0,\n  1: $spacer / 4,\n  2: $spacer / 2,\n  3: $spacer,\n  4: $spacer * 1.5,\n  5: $spacer * 3,\n) !default;\n\n$negative-spacers: if($enable-negative-margins, negativify-map($spacers), null) !default;\n// scss-docs-end spacer-variables-maps\n\n// Position\n//\n// Define the edge positioning anchors of the position utilities.\n\n// scss-docs-start position-map\n$position-values: (\n  0: 0,\n  50: 50%,\n  100: 100%\n) !default;\n// scss-docs-end position-map\n\n// Body\n//\n// Settings for the `<body>` element.\n\n$body-bg:                   $white !default;\n$body-color:                $gray-900 !default;\n$body-text-align:           null !default;\n\n\n// Links\n//\n// Style anchor elements.\n\n$link-color:                              $primary !default;\n$link-decoration:                         underline !default;\n$link-shade-percentage:                   20% !default;\n$link-hover-color:                        shift-color($link-color, $link-shade-percentage) !default;\n$link-hover-decoration:                   null !default;\n\n$stretched-link-pseudo-element:           after !default;\n$stretched-link-z-index:                  1 !default;\n\n// Paragraphs\n//\n// Style p element.\n\n$paragraph-margin-bottom:   1rem !default;\n\n\n// Grid breakpoints\n//\n// Define the minimum dimensions at which your layout will change,\n// adapting to different screen sizes, for use in media queries.\n\n// scss-docs-start grid-breakpoints\n$grid-breakpoints: (\n  xs: 0,\n  sm: 576px,\n  md: 768px,\n  lg: 992px,\n  xl: 1200px,\n  xxl: 1400px\n) !default;\n// scss-docs-end grid-breakpoints\n\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\");\n@include _assert-starts-at-zero($grid-breakpoints, \"$grid-breakpoints\");\n\n\n// Grid containers\n//\n// Define the maximum width of `.container` for different screen sizes.\n\n// scss-docs-start container-max-widths\n$container-max-widths: (\n  sm: 540px,\n  md: 720px,\n  lg: 960px,\n  xl: 1140px,\n  xxl: 1320px\n) !default;\n// scss-docs-end container-max-widths\n\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\n\n\n// Grid columns\n//\n// Set the number of columns and specify the width of the gutters.\n\n$grid-columns:                12 !default;\n$grid-gutter-width:           1.5rem !default;\n$grid-row-columns:            6 !default;\n\n$gutters: $spacers !default;\n\n// Container padding\n\n$container-padding-x: $grid-gutter-width / 2 !default;\n\n\n// Components\n//\n// Define common padding and border radius sizes and more.\n\n// scss-docs-start border-variables\n$border-width:                1px !default;\n$border-widths: (\n  1: 1px,\n  2: 2px,\n  3: 3px,\n  4: 4px,\n  5: 5px\n) !default;\n\n$border-color:                $gray-300 !default;\n// scss-docs-end border-variables\n\n// scss-docs-start border-radius-variables\n$border-radius:               .25rem !default;\n$border-radius-sm:            .2rem !default;\n$border-radius-lg:            .3rem !default;\n$border-radius-pill:          50rem !default;\n// scss-docs-end border-radius-variables\n\n// scss-docs-start box-shadow-variables\n$box-shadow:                  0 .5rem 1rem rgba($black, .15) !default;\n$box-shadow-sm:               0 .125rem .25rem rgba($black, .075) !default;\n$box-shadow-lg:               0 1rem 3rem rgba($black, .175) !default;\n$box-shadow-inset:            inset 0 1px 2px rgba($black, .075) !default;\n// scss-docs-end box-shadow-variables\n\n$component-active-color:      $white !default;\n$component-active-bg:         $primary !default;\n\n// scss-docs-start caret-variables\n$caret-width:                 .3em !default;\n$caret-vertical-align:        $caret-width * .85 !default;\n$caret-spacing:               $caret-width * .85 !default;\n// scss-docs-end caret-variables\n\n$transition-base:             all .2s ease-in-out !default;\n$transition-fade:             opacity .15s linear !default;\n// scss-docs-start collapse-transition\n$transition-collapse:         height .35s ease !default;\n// scss-docs-end collapse-transition\n\n// stylelint-disable function-disallowed-list\n// scss-docs-start aspect-ratios\n$aspect-ratios: (\n  \"1x1\": 100%,\n  \"4x3\": calc(3 / 4 * 100%),\n  \"16x9\": calc(9 / 16 * 100%),\n  \"21x9\": calc(9 / 21 * 100%)\n) !default;\n// scss-docs-end aspect-ratios\n// stylelint-enable function-disallowed-list\n\n// Typography\n//\n// Font, line-height, and color for body text, headings, and more.\n\n// scss-docs-start font-variables\n// stylelint-disable value-keyword-case\n$font-family-sans-serif:      system-ui, -apple-system, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", \"Liberation Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\" !default;\n$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace !default;\n// stylelint-enable value-keyword-case\n$font-family-base:            var(--#{$variable-prefix}font-sans-serif) !default;\n$font-family-code:            var(--#{$variable-prefix}font-monospace) !default;\n\n// $font-size-root effects the value of `rem`, which is used for as well font sizes, paddings and margins\n// $font-size-base effects the font size of the body text\n$font-size-root:              null !default;\n$font-size-base:              1rem !default; // Assumes the browser default, typically `16px`\n$font-size-sm:                $font-size-base * .875 !default;\n$font-size-lg:                $font-size-base * 1.25 !default;\n\n$font-weight-lighter:         lighter !default;\n$font-weight-light:           300 !default;\n$font-weight-normal:          400 !default;\n$font-weight-bold:            700 !default;\n$font-weight-bolder:          bolder !default;\n\n$font-weight-base:            $font-weight-normal !default;\n\n$line-height-base:            1.5 !default;\n$line-height-sm:              1.25 !default;\n$line-height-lg:              2 !default;\n\n$h1-font-size:                $font-size-base * 2.5 !default;\n$h2-font-size:                $font-size-base * 2 !default;\n$h3-font-size:                $font-size-base * 1.75 !default;\n$h4-font-size:                $font-size-base * 1.5 !default;\n$h5-font-size:                $font-size-base * 1.25 !default;\n$h6-font-size:                $font-size-base !default;\n// scss-docs-end font-variables\n\n// scss-docs-start font-sizes\n$font-sizes: (\n  1: $h1-font-size,\n  2: $h2-font-size,\n  3: $h3-font-size,\n  4: $h4-font-size,\n  5: $h5-font-size,\n  6: $h6-font-size\n) !default;\n// scss-docs-end font-sizes\n\n// scss-docs-start headings-variables\n$headings-margin-bottom:      $spacer / 2 !default;\n$headings-font-family:        null !default;\n$headings-font-style:         null !default;\n$headings-font-weight:        500 !default;\n$headings-line-height:        1.2 !default;\n$headings-color:              null !default;\n// scss-docs-end headings-variables\n\n// scss-docs-start display-headings\n$display-font-sizes: (\n  1: 5rem,\n  2: 4.5rem,\n  3: 4rem,\n  4: 3.5rem,\n  5: 3rem,\n  6: 2.5rem\n) !default;\n\n$display-font-weight: 300 !default;\n$display-line-height: $headings-line-height !default;\n// scss-docs-end display-headings\n\n// scss-docs-start type-variables\n$lead-font-size:              $font-size-base * 1.25 !default;\n$lead-font-weight:            300 !default;\n\n$small-font-size:             .875em !default;\n\n$sub-sup-font-size:           .75em !default;\n\n$text-muted:                  $gray-600 !default;\n\n$initialism-font-size:        $small-font-size !default;\n\n$blockquote-margin-y:         $spacer !default;\n$blockquote-font-size:        $font-size-base * 1.25 !default;\n$blockquote-footer-color:     $gray-600 !default;\n$blockquote-footer-font-size: $small-font-size !default;\n\n$hr-margin-y:                 $spacer !default;\n$hr-color:                    inherit !default;\n$hr-height:                   $border-width !default;\n$hr-opacity:                  .25 !default;\n\n$legend-margin-bottom:        .5rem !default;\n$legend-font-size:            1.5rem !default;\n$legend-font-weight:          null !default;\n\n$mark-padding:                .2em !default;\n\n$dt-font-weight:              $font-weight-bold !default;\n\n$nested-kbd-font-weight:      $font-weight-bold !default;\n\n$list-inline-padding:         .5rem !default;\n\n$mark-bg:                     #fcf8e3 !default;\n// scss-docs-end type-variables\n\n\n// Tables\n//\n// Customizes the `.table` component with basic values, each used across all table variations.\n\n// scss-docs-start table-variables\n$table-cell-padding-y:        .5rem !default;\n$table-cell-padding-x:        .5rem !default;\n$table-cell-padding-y-sm:     .25rem !default;\n$table-cell-padding-x-sm:     .25rem !default;\n\n$table-cell-vertical-align:   top !default;\n\n$table-color:                 $body-color !default;\n$table-bg:                    transparent !default;\n\n$table-th-font-weight:        null !default;\n\n$table-striped-color:         $table-color !default;\n$table-striped-bg-factor:     .05 !default;\n$table-striped-bg:            rgba($black, $table-striped-bg-factor) !default;\n\n$table-active-color:          $table-color !default;\n$table-active-bg-factor:      .1 !default;\n$table-active-bg:             rgba($black, $table-active-bg-factor) !default;\n\n$table-hover-color:           $table-color !default;\n$table-hover-bg-factor:       .075 !default;\n$table-hover-bg:              rgba($black, $table-hover-bg-factor) !default;\n\n$table-border-factor:         .1 !default;\n$table-border-width:          $border-width !default;\n$table-border-color:          $border-color !default;\n\n$table-striped-order:         odd !default;\n\n$table-group-separator-color: currentColor !default;\n\n$table-caption-color:         $text-muted !default;\n\n$table-bg-scale:              -80% !default;\n// scss-docs-end table-variables\n\n// scss-docs-start table-loop\n$table-variants: (\n  \"primary\":    shift-color($primary, $table-bg-scale),\n  \"secondary\":  shift-color($secondary, $table-bg-scale),\n  \"success\":    shift-color($success, $table-bg-scale),\n  \"info\":       shift-color($info, $table-bg-scale),\n  \"warning\":    shift-color($warning, $table-bg-scale),\n  \"danger\":     shift-color($danger, $table-bg-scale),\n  \"light\":      $light,\n  \"dark\":       $dark,\n) !default;\n// scss-docs-end table-loop\n\n\n// Buttons + Forms\n//\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\n\n// scss-docs-start input-btn-variables\n$input-btn-padding-y:         .375rem !default;\n$input-btn-padding-x:         .75rem !default;\n$input-btn-font-family:       null !default;\n$input-btn-font-size:         $font-size-base !default;\n$input-btn-line-height:       $line-height-base !default;\n\n$input-btn-focus-width:         .25rem !default;\n$input-btn-focus-color-opacity: .25 !default;\n$input-btn-focus-color:         rgba($component-active-bg, $input-btn-focus-color-opacity) !default;\n$input-btn-focus-blur:          0 !default;\n$input-btn-focus-box-shadow:    0 0 $input-btn-focus-blur $input-btn-focus-width $input-btn-focus-color !default;\n\n$input-btn-padding-y-sm:      .25rem !default;\n$input-btn-padding-x-sm:      .5rem !default;\n$input-btn-font-size-sm:      $font-size-sm !default;\n\n$input-btn-padding-y-lg:      .5rem !default;\n$input-btn-padding-x-lg:      1rem !default;\n$input-btn-font-size-lg:      $font-size-lg !default;\n\n$input-btn-border-width:      $border-width !default;\n// scss-docs-end input-btn-variables\n\n\n// Buttons\n//\n// For each of Bootstrap's buttons, define text, background, and border color.\n\n// scss-docs-start btn-variables\n$btn-padding-y:               $input-btn-padding-y !default;\n$btn-padding-x:               $input-btn-padding-x !default;\n$btn-font-family:             $input-btn-font-family !default;\n$btn-font-size:               $input-btn-font-size !default;\n$btn-line-height:             $input-btn-line-height !default;\n$btn-white-space:             null !default; // Set to `nowrap` to prevent text wrapping\n\n$btn-padding-y-sm:            $input-btn-padding-y-sm !default;\n$btn-padding-x-sm:            $input-btn-padding-x-sm !default;\n$btn-font-size-sm:            $input-btn-font-size-sm !default;\n\n$btn-padding-y-lg:            $input-btn-padding-y-lg !default;\n$btn-padding-x-lg:            $input-btn-padding-x-lg !default;\n$btn-font-size-lg:            $input-btn-font-size-lg !default;\n\n$btn-border-width:            $input-btn-border-width !default;\n\n$btn-font-weight:             $font-weight-normal !default;\n$btn-box-shadow:              inset 0 1px 0 rgba($white, .15), 0 1px 1px rgba($black, .075) !default;\n$btn-focus-width:             $input-btn-focus-width !default;\n$btn-focus-box-shadow:        $input-btn-focus-box-shadow !default;\n$btn-disabled-opacity:        .65 !default;\n$btn-active-box-shadow:       inset 0 3px 5px rgba($black, .125) !default;\n\n$btn-link-color:              $link-color !default;\n$btn-link-hover-color:        $link-hover-color !default;\n$btn-link-disabled-color:     $gray-600 !default;\n\n// Allows for customizing button radius independently from global border radius\n$btn-border-radius:           $border-radius !default;\n$btn-border-radius-sm:        $border-radius-sm !default;\n$btn-border-radius-lg:        $border-radius-lg !default;\n\n$btn-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$btn-hover-bg-shade-amount:       15% !default;\n$btn-hover-bg-tint-amount:        15% !default;\n$btn-hover-border-shade-amount:   20% !default;\n$btn-hover-border-tint-amount:    10% !default;\n$btn-active-bg-shade-amount:      20% !default;\n$btn-active-bg-tint-amount:       20% !default;\n$btn-active-border-shade-amount:  25% !default;\n$btn-active-border-tint-amount:   10% !default;\n// scss-docs-end btn-variables\n\n\n// Forms\n\n// scss-docs-start form-text-variables\n$form-text-margin-top:                  .25rem !default;\n$form-text-font-size:                   $small-font-size !default;\n$form-text-font-style:                  null !default;\n$form-text-font-weight:                 null !default;\n$form-text-color:                       $text-muted !default;\n// scss-docs-end form-text-variables\n\n// scss-docs-start form-label-variables\n$form-label-margin-bottom:              .5rem !default;\n$form-label-font-size:                  null !default;\n$form-label-font-style:                 null !default;\n$form-label-font-weight:                null !default;\n$form-label-color:                      null !default;\n// scss-docs-end form-label-variables\n\n// scss-docs-start form-input-variables\n$input-padding-y:                       $input-btn-padding-y !default;\n$input-padding-x:                       $input-btn-padding-x !default;\n$input-font-family:                     $input-btn-font-family !default;\n$input-font-size:                       $input-btn-font-size !default;\n$input-font-weight:                     $font-weight-base !default;\n$input-line-height:                     $input-btn-line-height !default;\n\n$input-padding-y-sm:                    $input-btn-padding-y-sm !default;\n$input-padding-x-sm:                    $input-btn-padding-x-sm !default;\n$input-font-size-sm:                    $input-btn-font-size-sm !default;\n\n$input-padding-y-lg:                    $input-btn-padding-y-lg !default;\n$input-padding-x-lg:                    $input-btn-padding-x-lg !default;\n$input-font-size-lg:                    $input-btn-font-size-lg !default;\n\n$input-bg:                              $white !default;\n$input-disabled-bg:                     $gray-200 !default;\n$input-disabled-border-color:           null !default;\n\n$input-color:                           $body-color !default;\n$input-border-color:                    $gray-400 !default;\n$input-border-width:                    $input-btn-border-width !default;\n$input-box-shadow:                      $box-shadow-inset !default;\n\n$input-border-radius:                   $border-radius !default;\n$input-border-radius-sm:                $border-radius-sm !default;\n$input-border-radius-lg:                $border-radius-lg !default;\n\n$input-focus-bg:                        $input-bg !default;\n$input-focus-border-color:              tint-color($component-active-bg, 50%) !default;\n$input-focus-color:                     $input-color !default;\n$input-focus-width:                     $input-btn-focus-width !default;\n$input-focus-box-shadow:                $input-btn-focus-box-shadow !default;\n\n$input-placeholder-color:               $gray-600 !default;\n$input-plaintext-color:                 $body-color !default;\n\n$input-height-border:                   $input-border-width * 2 !default;\n\n$input-height-inner:                    add($input-line-height * 1em, $input-padding-y * 2) !default;\n$input-height-inner-half:               add($input-line-height * .5em, $input-padding-y) !default;\n$input-height-inner-quarter:            add($input-line-height * .25em, $input-padding-y / 2) !default;\n\n$input-height:                          add($input-line-height * 1em, add($input-padding-y * 2, $input-height-border, false)) !default;\n$input-height-sm:                       add($input-line-height * 1em, add($input-padding-y-sm * 2, $input-height-border, false)) !default;\n$input-height-lg:                       add($input-line-height * 1em, add($input-padding-y-lg * 2, $input-height-border, false)) !default;\n\n$input-transition:                      border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n// scss-docs-end form-input-variables\n\n// scss-docs-start form-check-variables\n$form-check-input-width:                  1em !default;\n$form-check-min-height:                   $font-size-base * $line-height-base !default;\n$form-check-padding-start:                $form-check-input-width + .5em !default;\n$form-check-margin-bottom:                .125rem !default;\n$form-check-label-color:                  null !default;\n$form-check-label-cursor:                 null !default;\n$form-check-transition:                   null !default;\n\n$form-check-input-active-filter:          brightness(90%) !default;\n\n$form-check-input-bg:                     $input-bg !default;\n$form-check-input-border:                 1px solid rgba(0, 0, 0, .25) !default;\n$form-check-input-border-radius:          .25em !default;\n$form-check-radio-border-radius:          50% !default;\n$form-check-input-focus-border:           $input-focus-border-color !default;\n$form-check-input-focus-box-shadow:       $input-btn-focus-box-shadow !default;\n\n$form-check-input-checked-color:          $component-active-color !default;\n$form-check-input-checked-bg-color:       $component-active-bg !default;\n$form-check-input-checked-border-color:   $form-check-input-checked-bg-color !default;\n$form-check-input-checked-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-checked-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/></svg>\") !default;\n$form-check-radio-checked-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='2' fill='#{$form-check-input-checked-color}'/></svg>\") !default;\n\n$form-check-input-indeterminate-color:          $component-active-color !default;\n$form-check-input-indeterminate-bg-color:       $component-active-bg !default;\n$form-check-input-indeterminate-border-color:   $form-check-input-indeterminate-bg-color !default;\n$form-check-input-indeterminate-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-indeterminate-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/></svg>\") !default;\n\n$form-check-input-disabled-opacity:        .5 !default;\n$form-check-label-disabled-opacity:        $form-check-input-disabled-opacity !default;\n$form-check-btn-check-disabled-opacity:    $btn-disabled-opacity !default;\n\n$form-check-inline-margin-end:    1rem !default;\n// scss-docs-end form-check-variables\n\n// scss-docs-start form-switch-variables\n$form-switch-color:               rgba(0, 0, 0, .25) !default;\n$form-switch-width:               2em !default;\n$form-switch-padding-start:       $form-switch-width + .5em !default;\n$form-switch-bg-image:            url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-color}'/></svg>\") !default;\n$form-switch-border-radius:       $form-switch-width !default;\n$form-switch-transition:          background-position .15s ease-in-out !default;\n\n$form-switch-focus-color:         $input-focus-border-color !default;\n$form-switch-focus-bg-image:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-focus-color}'/></svg>\") !default;\n\n$form-switch-checked-color:       $component-active-color !default;\n$form-switch-checked-bg-image:    url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-checked-color}'/></svg>\") !default;\n$form-switch-checked-bg-position: right center !default;\n// scss-docs-end form-switch-variables\n\n// scss-docs-start input-group-variables\n$input-group-addon-padding-y:           $input-padding-y !default;\n$input-group-addon-padding-x:           $input-padding-x !default;\n$input-group-addon-font-weight:         $input-font-weight !default;\n$input-group-addon-color:               $input-color !default;\n$input-group-addon-bg:                  $gray-200 !default;\n$input-group-addon-border-color:        $input-border-color !default;\n// scss-docs-end input-group-variables\n\n// scss-docs-start form-select-variables\n$form-select-padding-y:             $input-padding-y !default;\n$form-select-padding-x:             $input-padding-x !default;\n$form-select-font-family:           $input-font-family !default;\n$form-select-font-size:             $input-font-size !default;\n$form-select-indicator-padding:     $form-select-padding-x * 3 !default; // Extra padding for background-image\n$form-select-font-weight:           $input-font-weight !default;\n$form-select-line-height:           $input-line-height !default;\n$form-select-color:                 $input-color !default;\n$form-select-bg:                    $input-bg !default;\n$form-select-disabled-color:        null !default;\n$form-select-disabled-bg:           $gray-200 !default;\n$form-select-disabled-border-color: $input-disabled-border-color !default;\n$form-select-bg-position:           right $form-select-padding-x center !default;\n$form-select-bg-size:               16px 12px !default; // In pixels because image dimensions\n$form-select-indicator-color:       $gray-800 !default;\n$form-select-indicator:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'><path fill='none' stroke='#{$form-select-indicator-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/></svg>\") !default;\n\n$form-select-feedback-icon-padding-end: $form-select-padding-x * 2.5 + $form-select-indicator-padding !default;\n$form-select-feedback-icon-position:    center right $form-select-indicator-padding !default;\n$form-select-feedback-icon-size:        $input-height-inner-half $input-height-inner-half !default;\n\n$form-select-border-width:        $input-border-width !default;\n$form-select-border-color:        $input-border-color !default;\n$form-select-border-radius:       $border-radius !default;\n$form-select-box-shadow:          $box-shadow-inset !default;\n\n$form-select-focus-border-color:  $input-focus-border-color !default;\n$form-select-focus-width:         $input-focus-width !default;\n$form-select-focus-box-shadow:    0 0 0 $form-select-focus-width $input-btn-focus-color !default;\n\n$form-select-padding-y-sm:        $input-padding-y-sm !default;\n$form-select-padding-x-sm:        $input-padding-x-sm !default;\n$form-select-font-size-sm:        $input-font-size-sm !default;\n\n$form-select-padding-y-lg:        $input-padding-y-lg !default;\n$form-select-padding-x-lg:        $input-padding-x-lg !default;\n$form-select-font-size-lg:        $input-font-size-lg !default;\n// scss-docs-end form-select-variables\n\n// scss-docs-start form-range-variables\n$form-range-track-width:          100% !default;\n$form-range-track-height:         .5rem !default;\n$form-range-track-cursor:         pointer !default;\n$form-range-track-bg:             $gray-300 !default;\n$form-range-track-border-radius:  1rem !default;\n$form-range-track-box-shadow:     $box-shadow-inset !default;\n\n$form-range-thumb-width:                   1rem !default;\n$form-range-thumb-height:                  $form-range-thumb-width !default;\n$form-range-thumb-bg:                      $component-active-bg !default;\n$form-range-thumb-border:                  0 !default;\n$form-range-thumb-border-radius:           1rem !default;\n$form-range-thumb-box-shadow:              0 .1rem .25rem rgba($black, .1) !default;\n$form-range-thumb-focus-box-shadow:        0 0 0 1px $body-bg, $input-focus-box-shadow !default;\n$form-range-thumb-focus-box-shadow-width:  $input-focus-width !default; // For focus box shadow issue in Edge\n$form-range-thumb-active-bg:               tint-color($component-active-bg, 70%) !default;\n$form-range-thumb-disabled-bg:             $gray-500 !default;\n$form-range-thumb-transition:              background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n// scss-docs-end form-range-variables\n\n// scss-docs-start form-file-variables\n$form-file-button-color:          $input-color !default;\n$form-file-button-bg:             $input-group-addon-bg !default;\n$form-file-button-hover-bg:       shade-color($form-file-button-bg, 5%) !default;\n// scss-docs-end form-file-variables\n\n// scss-docs-start form-floating-variables\n$form-floating-height:            add(3.5rem, $input-height-border) !default;\n$form-floating-padding-x:         $input-padding-x !default;\n$form-floating-padding-y:         1rem !default;\n$form-floating-input-padding-t:   1.625rem !default;\n$form-floating-input-padding-b:   .625rem !default;\n$form-floating-label-opacity:     .65 !default;\n$form-floating-label-transform:   scale(.85) translateY(-.5rem) translateX(.15rem) !default;\n$form-floating-transition:        opacity .1s ease-in-out, transform .1s ease-in-out !default;\n// scss-docs-end form-floating-variables\n\n// Form validation\n\n// scss-docs-start form-feedback-variables\n$form-feedback-margin-top:          $form-text-margin-top !default;\n$form-feedback-font-size:           $form-text-font-size !default;\n$form-feedback-font-style:          $form-text-font-style !default;\n$form-feedback-valid-color:         $success !default;\n$form-feedback-invalid-color:       $danger !default;\n\n$form-feedback-icon-valid-color:    $form-feedback-valid-color !default;\n$form-feedback-icon-valid:          url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'><path fill='#{$form-feedback-icon-valid-color}' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/></svg>\") !default;\n$form-feedback-icon-invalid-color:  $form-feedback-invalid-color !default;\n$form-feedback-icon-invalid:        url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='#{$form-feedback-icon-invalid-color}'><circle cx='6' cy='6' r='4.5'/><path stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/><circle cx='6' cy='8.2' r='.6' fill='#{$form-feedback-icon-invalid-color}' stroke='none'/></svg>\") !default;\n// scss-docs-end form-feedback-variables\n\n// scss-docs-start form-validation-states\n$form-validation-states: (\n  \"valid\": (\n    \"color\": $form-feedback-valid-color,\n    \"icon\": $form-feedback-icon-valid\n  ),\n  \"invalid\": (\n    \"color\": $form-feedback-invalid-color,\n    \"icon\": $form-feedback-icon-invalid\n  )\n) !default;\n// scss-docs-end form-validation-states\n\n// Z-index master list\n//\n// Warning: Avoid customizing these values. They're used for a bird's eye view\n// of components dependent on the z-axis and are designed to all work together.\n\n// scss-docs-start zindex-stack\n$zindex-dropdown:                   1000 !default;\n$zindex-sticky:                     1020 !default;\n$zindex-fixed:                      1030 !default;\n$zindex-offcanvas:                  1040 !default;\n$zindex-modal-backdrop:             1050 !default;\n$zindex-modal:                      1060 !default;\n$zindex-popover:                    1070 !default;\n$zindex-tooltip:                    1080 !default;\n// scss-docs-end zindex-stack\n\n\n// Navs\n\n// scss-docs-start nav-variables\n$nav-link-padding-y:                .5rem !default;\n$nav-link-padding-x:                1rem !default;\n$nav-link-font-size:                null !default;\n$nav-link-font-weight:              null !default;\n$nav-link-color:                    null !default;\n$nav-link-hover-color:              null !default;\n$nav-link-transition:               color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out !default;\n$nav-link-disabled-color:           $gray-600 !default;\n\n$nav-tabs-border-color:             $gray-300 !default;\n$nav-tabs-border-width:             $border-width !default;\n$nav-tabs-border-radius:            $border-radius !default;\n$nav-tabs-link-hover-border-color:  $gray-200 $gray-200 $nav-tabs-border-color !default;\n$nav-tabs-link-active-color:        $gray-700 !default;\n$nav-tabs-link-active-bg:           $body-bg !default;\n$nav-tabs-link-active-border-color: $gray-300 $gray-300 $nav-tabs-link-active-bg !default;\n\n$nav-pills-border-radius:           $border-radius !default;\n$nav-pills-link-active-color:       $component-active-color !default;\n$nav-pills-link-active-bg:          $component-active-bg !default;\n// scss-docs-end nav-variables\n\n\n// Navbar\n\n// scss-docs-start navbar-variables\n$navbar-padding-y:                  $spacer / 2 !default;\n$navbar-padding-x:                  null !default;\n\n$navbar-nav-link-padding-x:         .5rem !default;\n\n$navbar-brand-font-size:            $font-size-lg !default;\n// Compute the navbar-brand padding-y so the navbar-brand will have the same height as navbar-text and nav-link\n$nav-link-height:                   $font-size-base * $line-height-base + $nav-link-padding-y * 2 !default;\n$navbar-brand-height:               $navbar-brand-font-size * $line-height-base !default;\n$navbar-brand-padding-y:            ($nav-link-height - $navbar-brand-height) / 2 !default;\n$navbar-brand-margin-end:           1rem !default;\n\n$navbar-toggler-padding-y:          .25rem !default;\n$navbar-toggler-padding-x:          .75rem !default;\n$navbar-toggler-font-size:          $font-size-lg !default;\n$navbar-toggler-border-radius:      $btn-border-radius !default;\n$navbar-toggler-focus-width:        $btn-focus-width !default;\n$navbar-toggler-transition:         box-shadow .15s ease-in-out !default;\n// scss-docs-end navbar-variables\n\n// scss-docs-start navbar-theme-variables\n$navbar-dark-color:                 rgba($white, .55) !default;\n$navbar-dark-hover-color:           rgba($white, .75) !default;\n$navbar-dark-active-color:          $white !default;\n$navbar-dark-disabled-color:        rgba($white, .25) !default;\n$navbar-dark-toggler-icon-bg:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-dark-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\n$navbar-dark-toggler-border-color:  rgba($white, .1) !default;\n\n$navbar-light-color:                rgba($black, .55) !default;\n$navbar-light-hover-color:          rgba($black, .7) !default;\n$navbar-light-active-color:         rgba($black, .9) !default;\n$navbar-light-disabled-color:       rgba($black, .3) !default;\n$navbar-light-toggler-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-light-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\n$navbar-light-toggler-border-color: rgba($black, .1) !default;\n\n$navbar-light-brand-color:                $navbar-light-active-color !default;\n$navbar-light-brand-hover-color:          $navbar-light-active-color !default;\n$navbar-dark-brand-color:                 $navbar-dark-active-color !default;\n$navbar-dark-brand-hover-color:           $navbar-dark-active-color !default;\n// scss-docs-end navbar-theme-variables\n\n\n// Dropdowns\n//\n// Dropdown menu container and contents.\n\n// scss-docs-start dropdown-variables\n$dropdown-min-width:                10rem !default;\n$dropdown-padding-x:                0 !default;\n$dropdown-padding-y:                .5rem !default;\n$dropdown-spacer:                   .125rem !default;\n$dropdown-font-size:                $font-size-base !default;\n$dropdown-color:                    $body-color !default;\n$dropdown-bg:                       $white !default;\n$dropdown-border-color:             rgba($black, .15) !default;\n$dropdown-border-radius:            $border-radius !default;\n$dropdown-border-width:             $border-width !default;\n$dropdown-inner-border-radius:      subtract($dropdown-border-radius, $dropdown-border-width) !default;\n$dropdown-divider-bg:               $dropdown-border-color !default;\n$dropdown-divider-margin-y:         $spacer / 2 !default;\n$dropdown-box-shadow:               $box-shadow !default;\n\n$dropdown-link-color:               $gray-900 !default;\n$dropdown-link-hover-color:         shade-color($gray-900, 10%) !default;\n$dropdown-link-hover-bg:            $gray-200 !default;\n\n$dropdown-link-active-color:        $component-active-color !default;\n$dropdown-link-active-bg:           $component-active-bg !default;\n\n$dropdown-link-disabled-color:      $gray-500 !default;\n\n$dropdown-item-padding-y:           $spacer / 4 !default;\n$dropdown-item-padding-x:           $spacer !default;\n\n$dropdown-header-color:             $gray-600 !default;\n$dropdown-header-padding:           $dropdown-padding-y $dropdown-item-padding-x !default;\n// scss-docs-end dropdown-variables\n\n// scss-docs-start dropdown-dark-variables\n$dropdown-dark-color:               $gray-300 !default;\n$dropdown-dark-bg:                  $gray-800 !default;\n$dropdown-dark-border-color:        $dropdown-border-color !default;\n$dropdown-dark-divider-bg:          $dropdown-divider-bg !default;\n$dropdown-dark-box-shadow:          null !default;\n$dropdown-dark-link-color:          $dropdown-dark-color !default;\n$dropdown-dark-link-hover-color:    $white !default;\n$dropdown-dark-link-hover-bg:       rgba($white, .15) !default;\n$dropdown-dark-link-active-color:   $dropdown-link-active-color !default;\n$dropdown-dark-link-active-bg:      $dropdown-link-active-bg !default;\n$dropdown-dark-link-disabled-color: $gray-500 !default;\n$dropdown-dark-header-color:        $gray-500 !default;\n// scss-docs-end dropdown-dark-variables\n\n\n// Pagination\n\n// scss-docs-start pagination-variables\n$pagination-padding-y:              .375rem !default;\n$pagination-padding-x:              .75rem !default;\n$pagination-padding-y-sm:           .25rem !default;\n$pagination-padding-x-sm:           .5rem !default;\n$pagination-padding-y-lg:           .75rem !default;\n$pagination-padding-x-lg:           1.5rem !default;\n\n$pagination-color:                  $link-color !default;\n$pagination-bg:                     $white !default;\n$pagination-border-width:           $border-width !default;\n$pagination-border-radius:          $border-radius !default;\n$pagination-margin-start:           -$pagination-border-width !default;\n$pagination-border-color:           $gray-300 !default;\n\n$pagination-focus-color:            $link-hover-color !default;\n$pagination-focus-bg:               $gray-200 !default;\n$pagination-focus-box-shadow:       $input-btn-focus-box-shadow !default;\n$pagination-focus-outline:          0 !default;\n\n$pagination-hover-color:            $link-hover-color !default;\n$pagination-hover-bg:               $gray-200 !default;\n$pagination-hover-border-color:     $gray-300 !default;\n\n$pagination-active-color:           $component-active-color !default;\n$pagination-active-bg:              $component-active-bg !default;\n$pagination-active-border-color:    $pagination-active-bg !default;\n\n$pagination-disabled-color:         $gray-600 !default;\n$pagination-disabled-bg:            $white !default;\n$pagination-disabled-border-color:  $gray-300 !default;\n\n$pagination-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$pagination-border-radius-sm:       $border-radius-sm !default;\n$pagination-border-radius-lg:       $border-radius-lg !default;\n// scss-docs-end pagination-variables\n\n\n// Cards\n\n// scss-docs-start card-variables\n$card-spacer-y:                     $spacer !default;\n$card-spacer-x:                     $spacer !default;\n$card-title-spacer-y:               $spacer / 2 !default;\n$card-border-width:                 $border-width !default;\n$card-border-radius:                $border-radius !default;\n$card-border-color:                 rgba($black, .125) !default;\n$card-inner-border-radius:          subtract($card-border-radius, $card-border-width) !default;\n$card-cap-padding-y:                $card-spacer-y / 2 !default;\n$card-cap-padding-x:                $card-spacer-x !default;\n$card-cap-bg:                       rgba($black, .03) !default;\n$card-cap-color:                    null !default;\n$card-height:                       null !default;\n$card-color:                        null !default;\n$card-bg:                           $white !default;\n$card-img-overlay-padding:          $spacer !default;\n$card-group-margin:                 $grid-gutter-width / 2 !default;\n// scss-docs-end card-variables\n\n// Accordion\n\n// scss-docs-start accordion-variables\n$accordion-padding-y:                     1rem !default;\n$accordion-padding-x:                     1.25rem !default;\n$accordion-color:                         $body-color !default;\n$accordion-bg:                            $body-bg !default;\n$accordion-border-width:                  $border-width !default;\n$accordion-border-color:                  rgba($black, .125) !default;\n$accordion-border-radius:                 $border-radius !default;\n$accordion-inner-border-radius:           subtract($accordion-border-radius, $accordion-border-width) !default;\n\n$accordion-body-padding-y:                $accordion-padding-y !default;\n$accordion-body-padding-x:                $accordion-padding-x !default;\n\n$accordion-button-padding-y:              $accordion-padding-y !default;\n$accordion-button-padding-x:              $accordion-padding-x !default;\n$accordion-button-color:                  $accordion-color !default;\n$accordion-button-bg:                     $accordion-bg !default;\n$accordion-transition:                    $btn-transition, border-radius .15s ease !default;\n$accordion-button-active-bg:              tint-color($component-active-bg, 90%) !default;\n$accordion-button-active-color:           shade-color($primary, 10%) !default;\n\n$accordion-button-focus-border-color:     $input-focus-border-color !default;\n$accordion-button-focus-box-shadow:       $btn-focus-box-shadow !default;\n\n$accordion-icon-width:                    1.25rem !default;\n$accordion-icon-color:                    $accordion-color !default;\n$accordion-icon-active-color:             $accordion-button-active-color !default;\n$accordion-icon-transition:               transform .2s ease-in-out !default;\n$accordion-icon-transform:                rotate(180deg) !default;\n\n$accordion-button-icon:         url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-color}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>\") !default;\n$accordion-button-active-icon:  url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-active-color}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>\") !default;\n// scss-docs-end accordion-variables\n\n// Tooltips\n\n// scss-docs-start tooltip-variables\n$tooltip-font-size:                 $font-size-sm !default;\n$tooltip-max-width:                 200px !default;\n$tooltip-color:                     $white !default;\n$tooltip-bg:                        $black !default;\n$tooltip-border-radius:             $border-radius !default;\n$tooltip-opacity:                   .9 !default;\n$tooltip-padding-y:                 $spacer / 4 !default;\n$tooltip-padding-x:                 $spacer / 2 !default;\n$tooltip-margin:                    0 !default;\n\n$tooltip-arrow-width:               .8rem !default;\n$tooltip-arrow-height:              .4rem !default;\n$tooltip-arrow-color:               $tooltip-bg !default;\n// scss-docs-end tooltip-variables\n\n// Form tooltips must come after regular tooltips\n// scss-docs-start tooltip-feedback-variables\n$form-feedback-tooltip-padding-y:     $tooltip-padding-y !default;\n$form-feedback-tooltip-padding-x:     $tooltip-padding-x !default;\n$form-feedback-tooltip-font-size:     $tooltip-font-size !default;\n$form-feedback-tooltip-line-height:   null !default;\n$form-feedback-tooltip-opacity:       $tooltip-opacity !default;\n$form-feedback-tooltip-border-radius: $tooltip-border-radius !default;\n// scss-docs-start tooltip-feedback-variables\n\n\n// Popovers\n\n// scss-docs-start popover-variables\n$popover-font-size:                 $font-size-sm !default;\n$popover-bg:                        $white !default;\n$popover-max-width:                 276px !default;\n$popover-border-width:              $border-width !default;\n$popover-border-color:              rgba($black, .2) !default;\n$popover-border-radius:             $border-radius-lg !default;\n$popover-inner-border-radius:       subtract($popover-border-radius, $popover-border-width) !default;\n$popover-box-shadow:                $box-shadow !default;\n\n$popover-header-bg:                 shade-color($popover-bg, 6%) !default;\n$popover-header-color:              $headings-color !default;\n$popover-header-padding-y:          .5rem !default;\n$popover-header-padding-x:          $spacer !default;\n\n$popover-body-color:                $body-color !default;\n$popover-body-padding-y:            $spacer !default;\n$popover-body-padding-x:            $spacer !default;\n\n$popover-arrow-width:               1rem !default;\n$popover-arrow-height:              .5rem !default;\n$popover-arrow-color:               $popover-bg !default;\n\n$popover-arrow-outer-color:         fade-in($popover-border-color, .05) !default;\n// scss-docs-end popover-variables\n\n\n// Toasts\n\n// scss-docs-start toast-variables\n$toast-max-width:                   350px !default;\n$toast-padding-x:                   .75rem !default;\n$toast-padding-y:                   .5rem !default;\n$toast-font-size:                   .875rem !default;\n$toast-color:                       null !default;\n$toast-background-color:            rgba($white, .85) !default;\n$toast-border-width:                1px !default;\n$toast-border-color:                rgba(0, 0, 0, .1) !default;\n$toast-border-radius:               $border-radius !default;\n$toast-box-shadow:                  $box-shadow !default;\n$toast-spacing:                     $container-padding-x !default;\n\n$toast-header-color:                $gray-600 !default;\n$toast-header-background-color:     rgba($white, .85) !default;\n$toast-header-border-color:         rgba(0, 0, 0, .05) !default;\n// scss-docs-end toast-variables\n\n\n// Badges\n\n// scss-docs-start badge-variables\n$badge-font-size:                   .75em !default;\n$badge-font-weight:                 $font-weight-bold !default;\n$badge-color:                       $white !default;\n$badge-padding-y:                   .35em !default;\n$badge-padding-x:                   .65em !default;\n$badge-border-radius:               $border-radius !default;\n// scss-docs-end badge-variables\n\n\n// Modals\n\n// scss-docs-start modal-variables\n$modal-inner-padding:               $spacer !default;\n\n$modal-footer-margin-between:       .5rem !default;\n\n$modal-dialog-margin:               .5rem !default;\n$modal-dialog-margin-y-sm-up:       1.75rem !default;\n\n$modal-title-line-height:           $line-height-base !default;\n\n$modal-content-color:               null !default;\n$modal-content-bg:                  $white !default;\n$modal-content-border-color:        rgba($black, .2) !default;\n$modal-content-border-width:        $border-width !default;\n$modal-content-border-radius:       $border-radius-lg !default;\n$modal-content-inner-border-radius: subtract($modal-content-border-radius, $modal-content-border-width) !default;\n$modal-content-box-shadow-xs:       $box-shadow-sm !default;\n$modal-content-box-shadow-sm-up:    $box-shadow !default;\n\n$modal-backdrop-bg:                 $black !default;\n$modal-backdrop-opacity:            .5 !default;\n$modal-header-border-color:         $border-color !default;\n$modal-footer-border-color:         $modal-header-border-color !default;\n$modal-header-border-width:         $modal-content-border-width !default;\n$modal-footer-border-width:         $modal-header-border-width !default;\n$modal-header-padding-y:            $modal-inner-padding !default;\n$modal-header-padding-x:            $modal-inner-padding !default;\n$modal-header-padding:              $modal-header-padding-y $modal-header-padding-x !default; // Keep this for backwards compatibility\n\n$modal-sm:                          300px !default;\n$modal-md:                          500px !default;\n$modal-lg:                          800px !default;\n$modal-xl:                          1140px !default;\n\n$modal-fade-transform:              translate(0, -50px) !default;\n$modal-show-transform:              none !default;\n$modal-transition:                  transform .3s ease-out !default;\n$modal-scale-transform:             scale(1.02) !default;\n// scss-docs-end modal-variables\n\n\n// Alerts\n//\n// Define alert colors, border radius, and padding.\n\n// scss-docs-start alert-variables\n$alert-padding-y:               $spacer !default;\n$alert-padding-x:               $spacer !default;\n$alert-margin-bottom:           1rem !default;\n$alert-border-radius:           $border-radius !default;\n$alert-link-font-weight:        $font-weight-bold !default;\n$alert-border-width:            $border-width !default;\n$alert-bg-scale:                -80% !default;\n$alert-border-scale:            -70% !default;\n$alert-color-scale:             40% !default;\n$alert-dismissible-padding-r:   $alert-padding-x * 3 !default; // 3x covers width of x plus default padding on either side\n// scss-docs-end alert-variables\n\n\n// Progress bars\n\n// scss-docs-start progress-variables\n$progress-height:                   1rem !default;\n$progress-font-size:                $font-size-base * .75 !default;\n$progress-bg:                       $gray-200 !default;\n$progress-border-radius:            $border-radius !default;\n$progress-box-shadow:               $box-shadow-inset !default;\n$progress-bar-color:                $white !default;\n$progress-bar-bg:                   $primary !default;\n$progress-bar-animation-timing:     1s linear infinite !default;\n$progress-bar-transition:           width .6s ease !default;\n// scss-docs-end progress-variables\n\n\n// List group\n\n// scss-docs-start list-group-variables\n$list-group-color:                  $gray-900 !default;\n$list-group-bg:                     $white !default;\n$list-group-border-color:           rgba($black, .125) !default;\n$list-group-border-width:           $border-width !default;\n$list-group-border-radius:          $border-radius !default;\n\n$list-group-item-padding-y:         $spacer / 2 !default;\n$list-group-item-padding-x:         $spacer !default;\n$list-group-item-bg-scale:          -80% !default;\n$list-group-item-color-scale:       40% !default;\n\n$list-group-hover-bg:               $gray-100 !default;\n$list-group-active-color:           $component-active-color !default;\n$list-group-active-bg:              $component-active-bg !default;\n$list-group-active-border-color:    $list-group-active-bg !default;\n\n$list-group-disabled-color:         $gray-600 !default;\n$list-group-disabled-bg:            $list-group-bg !default;\n\n$list-group-action-color:           $gray-700 !default;\n$list-group-action-hover-color:     $list-group-action-color !default;\n\n$list-group-action-active-color:    $body-color !default;\n$list-group-action-active-bg:       $gray-200 !default;\n// scss-docs-end list-group-variables\n\n\n// Image thumbnails\n\n// scss-docs-start thumbnail-variables\n$thumbnail-padding:                 .25rem !default;\n$thumbnail-bg:                      $body-bg !default;\n$thumbnail-border-width:            $border-width !default;\n$thumbnail-border-color:            $gray-300 !default;\n$thumbnail-border-radius:           $border-radius !default;\n$thumbnail-box-shadow:              $box-shadow-sm !default;\n// scss-docs-end thumbnail-variables\n\n\n// Figures\n\n// scss-docs-start figure-variables\n$figure-caption-font-size:          $small-font-size !default;\n$figure-caption-color:              $gray-600 !default;\n// scss-docs-end figure-variables\n\n\n// Breadcrumbs\n\n// scss-docs-start breadcrumb-variables\n$breadcrumb-font-size:              null !default;\n$breadcrumb-padding-y:              0 !default;\n$breadcrumb-padding-x:              0 !default;\n$breadcrumb-item-padding-x:         .5rem !default;\n$breadcrumb-margin-bottom:          1rem !default;\n$breadcrumb-bg:                     null !default;\n$breadcrumb-divider-color:          $gray-600 !default;\n$breadcrumb-active-color:           $gray-600 !default;\n$breadcrumb-divider:                quote(\"/\") !default;\n$breadcrumb-divider-flipped:        $breadcrumb-divider !default;\n$breadcrumb-border-radius:          null !default;\n// scss-docs-end breadcrumb-variables\n\n// Carousel\n\n// scss-docs-start carousel-variables\n$carousel-control-color:             $white !default;\n$carousel-control-width:             15% !default;\n$carousel-control-opacity:           .5 !default;\n$carousel-control-hover-opacity:     .9 !default;\n$carousel-control-transition:        opacity .15s ease !default;\n\n$carousel-indicator-width:           30px !default;\n$carousel-indicator-height:          3px !default;\n$carousel-indicator-hit-area-height: 10px !default;\n$carousel-indicator-spacer:          3px !default;\n$carousel-indicator-opacity:         .5 !default;\n$carousel-indicator-active-bg:       $white !default;\n$carousel-indicator-active-opacity:  1 !default;\n$carousel-indicator-transition:      opacity .6s ease !default;\n\n$carousel-caption-width:             70% !default;\n$carousel-caption-color:             $white !default;\n$carousel-caption-padding-y:         1.25rem !default;\n$carousel-caption-spacer:            1.25rem !default;\n\n$carousel-control-icon-width:        2rem !default;\n\n$carousel-control-prev-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/></svg>\") !default;\n$carousel-control-next-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/></svg>\") !default;\n\n$carousel-transition-duration:       .6s !default;\n$carousel-transition:                transform $carousel-transition-duration ease-in-out !default; // Define transform transition first if using multiple transitions (e.g., `transform 2s ease, opacity .5s ease-out`)\n\n$carousel-dark-indicator-active-bg:  $black !default;\n$carousel-dark-caption-color:        $black !default;\n$carousel-dark-control-icon-filter:  invert(1) grayscale(100) !default;\n// scss-docs-end carousel-variables\n\n\n// Spinners\n\n// scss-docs-start spinner-variables\n$spinner-width:           2rem !default;\n$spinner-height:          $spinner-width !default;\n$spinner-border-width:    .25em !default;\n$spinner-animation-speed: .75s !default;\n\n$spinner-width-sm:        1rem !default;\n$spinner-height-sm:       $spinner-width-sm !default;\n$spinner-border-width-sm: .2em !default;\n// scss-docs-end spinner-variables\n\n\n// Close\n\n// scss-docs-start close-variables\n$btn-close-width:            1em !default;\n$btn-close-height:           $btn-close-width !default;\n$btn-close-padding-x:        .25em !default;\n$btn-close-padding-y:        $btn-close-padding-x !default;\n$btn-close-color:            $black !default;\n$btn-close-bg:               url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$btn-close-color}'><path d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/></svg>\") !default;\n$btn-close-focus-shadow:     $input-btn-focus-box-shadow !default;\n$btn-close-opacity:          .5 !default;\n$btn-close-hover-opacity:    .75 !default;\n$btn-close-focus-opacity:    1 !default;\n$btn-close-disabled-opacity: .25 !default;\n$btn-close-white-filter:     invert(1) grayscale(100%) brightness(200%) !default;\n// scss-docs-end close-variables\n\n\n// Offcanvas\n\n// scss-docs-start offcanvas-variables\n$offcanvas-padding-y:               $modal-inner-padding !default;\n$offcanvas-padding-x:               $modal-inner-padding !default;\n$offcanvas-horizontal-width:        400px !default;\n$offcanvas-vertical-height:         30vh !default;\n$offcanvas-transition-duration:     .3s !default;\n$offcanvas-border-color:            $modal-content-border-color !default;\n$offcanvas-border-width:            $modal-content-border-width !default;\n$offcanvas-title-line-height:       $modal-title-line-height !default;\n$offcanvas-bg-color:                $modal-content-bg !default;\n$offcanvas-color:                   $modal-content-color !default;\n$offcanvas-body-backdrop-color:     rgba($modal-backdrop-bg, $modal-backdrop-opacity) !default;\n$offcanvas-box-shadow:              $modal-content-box-shadow-xs !default;\n// scss-docs-end offcanvas-variables\n\n// Code\n\n$code-font-size:                    $small-font-size !default;\n$code-color:                        $pink !default;\n\n$kbd-padding-y:                     .2rem !default;\n$kbd-padding-x:                     .4rem !default;\n$kbd-font-size:                     $code-font-size !default;\n$kbd-color:                         $white !default;\n$kbd-bg:                            $gray-900 !default;\n\n$pre-color:                         null !default;\n", "// Row\n//\n// Rows contain your columns.\n\n@if $enable-grid-classes {\n  .row {\n    @include make-row();\n\n    > * {\n      @include make-col-ready();\n    }\n  }\n}\n\n\n// Columns\n//\n// Common styles for small and large grid columns\n\n@if $enable-grid-classes {\n  @include make-grid-columns();\n}\n", "/// Grid system\n//\n// Generate semantic grid columns with these mixins.\n\n@mixin make-row($gutter: $grid-gutter-width) {\n  --#{$variable-prefix}gutter-x: #{$gutter};\n  --#{$variable-prefix}gutter-y: 0;\n  display: flex;\n  flex-wrap: wrap;\n  margin-top: calc(var(--#{$variable-prefix}gutter-y) * -1); // stylelint-disable-line function-disallowed-list\n  margin-right: calc(var(--#{$variable-prefix}gutter-x) / -2); // stylelint-disable-line function-disallowed-list\n  margin-left: calc(var(--#{$variable-prefix}gutter-x) / -2); // stylelint-disable-line function-disallowed-list\n}\n\n@mixin make-col-ready($gutter: $grid-gutter-width) {\n  // Add box sizing if only the grid is loaded\n  box-sizing: if(variable-exists(include-column-box-sizing) and $include-column-box-sizing, border-box, null);\n  // Prevent columns from becoming too narrow when at smaller grid tiers by\n  // always setting `width: 100%;`. This works because we set the width\n  // later on to override this initial width.\n  flex-shrink: 0;\n  width: 100%;\n  max-width: 100%; // Prevent `.col-auto`, `.col` (& responsive variants) from breaking out the grid\n  padding-right: calc(var(--#{$variable-prefix}gutter-x) / 2); // stylelint-disable-line function-disallowed-list\n  padding-left: calc(var(--#{$variable-prefix}gutter-x) / 2); // stylelint-disable-line function-disallowed-list\n  margin-top: var(--#{$variable-prefix}gutter-y);\n}\n\n@mixin make-col($size, $columns: $grid-columns) {\n  flex: 0 0 auto;\n  width: percentage($size / $columns);\n}\n\n@mixin make-col-auto() {\n  flex: 0 0 auto;\n  width: auto;\n}\n\n@mixin make-col-offset($size, $columns: $grid-columns) {\n  $num: $size / $columns;\n  margin-left: if($num == 0, 0, percentage($num));\n}\n\n// Row columns\n//\n// Specify on a parent element(e.g., .row) to force immediate children into NN\n// numberof columns. Supports wrapping to new lines, but does not do a Masonry\n// style grid.\n@mixin row-cols($count) {\n  > * {\n    flex: 0 0 auto;\n    width: 100% / $count;\n  }\n}\n\n// Framework grid generation\n//\n// Used only by Bootstrap to generate the correct number of grid classes given\n// any value of `$grid-columns`.\n\n@mixin make-grid-columns($columns: $grid-columns, $gutter: $grid-gutter-width, $breakpoints: $grid-breakpoints) {\n  @each $breakpoint in map-keys($breakpoints) {\n    $infix: breakpoint-infix($breakpoint, $breakpoints);\n\n    @include media-breakpoint-up($breakpoint, $breakpoints) {\n      // Provide basic `.col-{bp}` classes for equal-width flexbox columns\n      .col#{$infix} {\n        flex: 1 0 0%; // Flexbugs #4: https://github.com/philipwalton/flexbugs#flexbug-4\n      }\n\n      .row-cols#{$infix}-auto > * {\n        @include make-col-auto();\n      }\n\n      @if $grid-row-columns > 0 {\n        @for $i from 1 through $grid-row-columns {\n          .row-cols#{$infix}-#{$i} {\n            @include row-cols($i);\n          }\n        }\n      }\n\n      .col#{$infix}-auto {\n        @include make-col-auto();\n      }\n\n      @if $columns > 0 {\n        @for $i from 1 through $columns {\n          .col#{$infix}-#{$i} {\n            @include make-col($i, $columns);\n          }\n        }\n\n        // `$columns - 1` because offsetting by the width of an entire row isn't possible\n        @for $i from 0 through ($columns - 1) {\n          @if not ($infix == \"\" and $i == 0) { // Avoid emitting useless .offset-0\n            .offset#{$infix}-#{$i} {\n              @include make-col-offset($i, $columns);\n            }\n          }\n        }\n      }\n\n      // Gutters\n      //\n      // Make use of `.g-*`, `.gx-*` or `.gy-*` utilities to change spacing between the columns.\n      @each $key, $value in $gutters {\n        .g#{$infix}-#{$key},\n        .gx#{$infix}-#{$key} {\n          --#{$variable-prefix}gutter-x: #{$value};\n        }\n\n        .g#{$infix}-#{$key},\n        .gy#{$infix}-#{$key} {\n          --#{$variable-prefix}gutter-y: #{$value};\n        }\n      }\n    }\n  }\n}\n", "// Utility generator\n// Used to generate utilities & print utilities\n@mixin generate-utility($utility, $infix, $is-rfs-media-query: false) {\n  $values: map-get($utility, values);\n\n  // If the values are a list or string, convert it into a map\n  @if type-of($values) == \"string\" or type-of(nth($values, 1)) != \"list\" {\n    $values: zip($values, $values);\n  }\n\n  @each $key, $value in $values {\n    $properties: map-get($utility, property);\n\n    // Multiple properties are possible, for example with vertical or horizontal margins or paddings\n    @if type-of($properties) == \"string\" {\n      $properties: append((), $properties);\n    }\n\n    // Use custom class if present\n    $property-class: if(map-has-key($utility, class), map-get($utility, class), nth($properties, 1));\n    $property-class: if($property-class == null, \"\", $property-class);\n\n    // State params to generate pseudo-classes\n    $state: if(map-has-key($utility, state), map-get($utility, state), ());\n\n    $infix: if($property-class == \"\" and str-slice($infix, 1, 1) == \"-\", str-slice($infix, 2), $infix);\n\n    // Don't prefix if value key is null (eg. with shadow class)\n    $property-class-modifier: if($key, if($property-class == \"\" and $infix == \"\", \"\", \"-\") + $key, \"\");\n\n    @if map-get($utility, rfs) {\n      // Inside the media query\n      @if $is-rfs-media-query {\n        $val: rfs-value($value);\n\n        // Do not render anything if fluid and non fluid values are the same\n        $value: if($val == rfs-fluid-value($value), null, $val);\n      }\n      @else {\n        $value: rfs-fluid-value($value);\n      }\n    }\n\n    $is-rtl: map-get($utility, rtl);\n\n    @if $value != null {\n      @if $is-rtl == false {\n        /* rtl:begin:remove */\n      }\n      .#{$property-class + $infix + $property-class-modifier} {\n        @each $property in $properties {\n          #{$property}: $value if($enable-important-utilities, !important, null);\n        }\n      }\n\n      @each $pseudo in $state {\n        .#{$property-class + $infix + $property-class-modifier}-#{$pseudo}:#{$pseudo} {\n          @each $property in $properties {\n            #{$property}: $value if($enable-important-utilities, !important, null);\n          }\n        }\n      }\n      @if $is-rtl == false {\n        /* rtl:end:remove */\n      }\n    }\n  }\n}\n", "// Loop over each breakpoint\n@each $breakpoint in map-keys($grid-breakpoints) {\n\n  // Generate media query if needed\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    // Loop over each utility property\n    @each $key, $utility in $utilities {\n      // The utility can be disabled with `false`, thus check if the utility is a map first\n      // Only proceed if responsive media queries are enabled or if it's the base media query\n      @if type-of($utility) == \"map\" and (map-get($utility, responsive) or $infix == \"\") {\n        @include generate-utility($utility, $infix);\n      }\n    }\n  }\n}\n\n// RFS rescaling\n@media (min-width: $rfs-mq-value) {\n  @each $breakpoint in map-keys($grid-breakpoints) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    @if (map-get($grid-breakpoints, $breakpoint) < $rfs-breakpoint) {\n      // Loop over each utility property\n      @each $key, $utility in $utilities {\n        // The utility can be disabled with `false`, thus check if the utility is a map first\n        // Only proceed if responsive media queries are enabled or if it's the base media query\n        @if type-of($utility) == \"map\" and map-get($utility, rfs) and (map-get($utility, responsive) or $infix == \"\") {\n          @include generate-utility($utility, $infix, true);\n        }\n      }\n    }\n  }\n}\n\n\n// Print utilities\n@media print {\n  @each $key, $utility in $utilities {\n    // The utility can be disabled with `false`, thus check if the utility is a map first\n    // Then check if the utility needs print styles\n    @if type-of($utility) == \"map\" and map-get($utility, print) == true {\n      @include generate-utility($utility, \"-print\");\n    }\n  }\n}\n"]}