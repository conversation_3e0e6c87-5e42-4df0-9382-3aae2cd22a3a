'use client';

import React, { useContext } from 'react';
import { ThemeContext } from '@/contexts/ThemeContext';
import { SunIcon, MoonIcon } from '@heroicons/react/24/outline';
import Button from './Button';

interface ThemeToggleProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({
  className = '',
  size = 'md'
}) => {
  const context = useContext(ThemeContext);

  // If no context, don't render anything
  if (!context) {
    return null;
  }

  const { theme, toggleTheme } = context;

  return (
    <Button
      variant="outline"
      size={size}
      onClick={toggleTheme}
      className={`relative ${className}`}
      title={theme === 'dark' ? 'Beralih ke mode terang' : 'Beralih ke mode gelap'}
    >
      {theme === 'dark' ? (
        <SunIcon className="h-4 w-4" />
      ) : (
        <MoonIcon className="h-4 w-4" />
      )}
      <span className="sr-only">
        {theme === 'dark' ? 'Beralih ke mode terang' : 'Beralih ke mode gelap'}
      </span>
    </Button>
  );
};

export default ThemeToggle;
