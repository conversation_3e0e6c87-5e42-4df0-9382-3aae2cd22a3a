<?php

namespace App\Controllers\AdminEvent;

use App\Controllers\BaseController;
use App\Models\Kontingen;
use App\Models\PendaftaranEvent;
use App\Models\Atlet;
use App\Models\Negara;
use App\Models\Provinsi;
use App\Models\KabupatenKota;
use App\Models\Event; // Pastikan model Event sudah ada

class InformasiEventController extends BaseController
{
    /**
     * Menampilkan informasi event beserta data kontingen yang terdaftar.
     *
     * @return \CodeIgniter\HTTP\ResponseInterface|string
     */
    public function index()
    {
        // Ambil event_id dari session
        $eventId = session('event_id');

        if (!$eventId) {
            $data['kontingens'] = [];
            return view('admin-event/informasi-event/index', $data);
        }

        // Inisialisasi model-model yang diperlukan
        $pendaftaranEvent = new PendaftaranEvent();
        $kontingenModel   = new Kontingen();
        $atletModel       = new Atlet();
        $negaraModel      = new Negara();
        $provinsiModel    = new Provinsi();
        $kabupatenKotaModel = new KabupatenKota();
        $eventModel       = new Event();

        // Ambil detail event dan periksa statusnya
        $currentEvent = $eventModel->find($eventId);
        // dd($currentEvent);
        if (!$currentEvent || $currentEvent['status'] !== 'active') {
            // Jika event tidak aktif (misalnya sudah completed), jangan tampilkan data kontingen
            $data['kontingens'] = [];
            return view('admin-event/informasi-event/index', $data);
        }

        // Ambil semua ID kontingen yang terdaftar di event ini
        $kontingenIds = $pendaftaranEvent
            ->where('id_event', $eventId)
            ->select('id_kontingen')
            ->findColumn('id_kontingen');

        $data['kontingens'] = [];

        if (!empty($kontingenIds)) {
            foreach ($kontingenIds as $kontingenId) {
                // Ambil data kontingen berdasarkan ID
                $kontingenData = $kontingenModel->find($kontingenId);
                if ($kontingenData) {
                    // Ambil nama lokasi dari tabel terkait, jika tidak ditemukan tampilkan 'N/A'
                    $negara = $negaraModel->find($kontingenData['negara']);
                    $provinsi = $provinsiModel->find($kontingenData['provinsi']);
                    $kabupatenKota = $kabupatenKotaModel->find($kontingenData['kabupaten_kota']);

                    // Hitung jumlah atlet yang terdaftar untuk kontingen tersebut
                    $atletCount = $atletModel
                        ->where('id_kontingen', $kontingenId)
                        ->countAllResults();

                    $data['kontingens'][] = [
                        'id'             => $kontingenData['id'],
                        'name'           => $kontingenData['name'],
                        'negara'         => $negara ? $negara['name'] : 'N/A',
                        'provinsi'       => $provinsi ? $provinsi['name'] : 'N/A',
                        'kabupaten_kota' => $kabupatenKota ? $kabupatenKota['name'] : 'N/A',
                        'atlet_count'    => $atletCount,
                    ];
                }
            }
        }

        return view('admin-event/informasi-event/index', $data);
    }
}
