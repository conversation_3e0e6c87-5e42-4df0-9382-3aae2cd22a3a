import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../config/database';
import <PERSON>egara from './Negara';

interface ProvinsiInterface {
  id: number;
  name: string;
  id_negara: number;
}

interface ProvinsiCreationAttributes extends Optional<ProvinsiInterface, 'id'> {}

class Provinsi extends Model<ProvinsiInterface, ProvinsiCreationAttributes> implements ProvinsiInterface {
  public id!: number;
  public name!: string;
  public id_negara!: number;
}

Provinsi.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    id_negara: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: Negara,
        key: 'id',
      },
    },
  },
  {
    sequelize,
    tableName: 'provinsi',
    timestamps: false,
  }
);

export default Provinsi;
