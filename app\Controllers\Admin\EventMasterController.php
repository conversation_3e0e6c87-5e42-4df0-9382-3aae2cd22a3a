<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\Event;

class EventMasterController extends BaseController
{
    public function index()
    {
        $eventModel = new Event();
        $data['events'] = $eventModel->findAll();
        return view('admin/event-master/index', $data);
    }

    public function delete($id)
    {
        $eventModel = new Event();
        
        // Pastikan event ada
        $event = $eventModel->find($id);
        if (!$event) {
            return redirect()->to('/event-master')
                ->with('error', 'Event tidak ditemukan.');
        }
        
        // Hapus event dan periksa jika terjadi kes<PERSON>han
        if ($eventModel->delete($id) === false) {
            return redirect()->to('/event-master')
                ->with('error', 'Gagal menghapus event.');
        }
        
        return redirect()->to('/event-master')
            ->with('success', 'Event berhasil dihapus.');
    }
}
