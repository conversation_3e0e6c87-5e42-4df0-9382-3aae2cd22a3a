'use client';

import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import PWAInstallButton from '@/components/pwa/PWAInstallButton';
import {
  ArrowLeftIcon,
  UserCircleIcon,
  ArrowRightOnRectangleIcon,
  HomeIcon,
  Cog6ToothIcon,
} from '@heroicons/react/24/outline';

interface ProfileLayoutProps {
  children: React.ReactNode;
}

const ProfileLayout = ({ children }: ProfileLayoutProps) => {
  const { user, logout } = useAuth();
  const router = useRouter();

  const handleLogout = async () => {
    await logout();
    router.push('/');
  };

  return (
    <div className="min-h-screen bg-black">
      {/* Header */}
      <header className="bg-gray-900 border-b border-gold-500/30 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Left side */}
            <div className="flex items-center space-x-4">
              <Link
                href="/"
                className="flex items-center text-gray-300 hover:text-gold-400 transition-colors duration-300"
              >
                <ArrowLeftIcon className="h-5 w-5 mr-2" />
                <span className="text-sm font-medium">Back to Home</span>
              </Link>
              
              <div className="h-6 w-px bg-gray-600"></div>
              
              <div className="flex items-center">
                <img className="h-8 w-auto rounded border border-gold-500/30" src="/baja.jpeg" alt="BAJA" />
                <span className="ml-2 text-lg font-semibold text-white">BAJA</span>
              </div>
            </div>

            {/* Center - Navigation */}
            <nav className="hidden md:flex items-center space-x-6">
              <Link
                href="/profile"
                className="flex items-center space-x-2 text-gray-300 hover:text-gold-400 transition-colors duration-300"
              >
                <UserCircleIcon className="h-5 w-5" />
                <span className="text-sm font-medium">Profile</span>
              </Link>
              
              <Link
                href="/profile/settings"
                className="flex items-center space-x-2 text-gray-300 hover:text-gold-400 transition-colors duration-300"
              >
                <Cog6ToothIcon className="h-5 w-5" />
                <span className="text-sm font-medium">Settings</span>
              </Link>
            </nav>

            {/* Right side */}
            <div className="flex items-center space-x-4">
              <PWAInstallButton />
              
              <Link href="/dashboard">
                <button className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-300 hover:text-gold-400 bg-gray-800 hover:bg-gray-700 rounded-md transition-colors duration-300">
                  <HomeIcon className="h-4 w-4" />
                  <span>Dashboard</span>
                </button>
              </Link>

              <div className="flex items-center space-x-3">
                <div className="text-right">
                  <p className="text-sm font-medium text-white">{user?.name}</p>
                  <p className="text-xs text-gray-400">{user?.role}</p>
                </div>
                <UserCircleIcon className="h-8 w-8 text-gold-400" />
              </div>

              <button
                onClick={handleLogout}
                className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-300 hover:text-red-400 bg-gray-800 hover:bg-red-900/20 rounded-md transition-colors duration-300"
              >
                <ArrowRightOnRectangleIcon className="h-4 w-4" />
                <span>Logout</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Mobile Navigation */}
      <div className="md:hidden bg-gray-800 border-b border-gold-500/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-3">
            <nav className="flex space-x-6">
              <Link
                href="/profile"
                className="flex items-center space-x-2 text-gray-300 hover:text-gold-400 transition-colors duration-300"
              >
                <UserCircleIcon className="h-5 w-5" />
                <span className="text-sm font-medium">Profile</span>
              </Link>
              
              <Link
                href="/profile/settings"
                className="flex items-center space-x-2 text-gray-300 hover:text-gold-400 transition-colors duration-300"
              >
                <Cog6ToothIcon className="h-5 w-5" />
                <span className="text-sm font-medium">Settings</span>
              </Link>
            </nav>
          </div>
        </div>
      </div>

      {/* Main content */}
      <main className="py-6 bg-black min-h-screen">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          {children}
        </div>
      </main>
    </div>
  );
};

export default ProfileLayout;
