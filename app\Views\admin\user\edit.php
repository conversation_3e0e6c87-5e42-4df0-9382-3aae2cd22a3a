<?= $this->extend('layouts/backend/template') ?>
<?= $this->section('content') ?>

<div id="content">
    <div class="container-fluid">

        <!-- Page Heading -->
        <h1 class="h3 mb-2 text-gray-800">Edit Users</h1>

        <!-- Form Tambah Users -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Form Edit Users</h6>
            </div>
            <div class="card-body">
                <form action="<?= base_url('users/update') ?>" method="post" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="profile">Profile (Foto)</label>
                        <input type="file" class="form-control" id="profile" name="profile" value="<?= $users['profile'] ?>" required>
                        <?php if(isset(session('errors')['profile'])): ?>
                            <small class="text-danger"><?= session('errors')['profile'] ?></small>
                        <?php endif; ?>
                    </div>
                    <div class="form-group">
                        <label for="nama_lengkap">Nama <PERSON>gkap</label>
                        <input type="text" class="form-control" id="nama_lengkap" name="name" value="<?= $users['name'] ?>" required>
                        <?php if(isset(session('errors')['name'])): ?>
                            <small class="text-danger"><?= session('errors')['name'] ?></small>
                        <?php endif; ?>
                    </div>
                    <div class="form-group">
                        <label for="email">Email</label>
                        <input type="email" class="form-control" id="email" name="email" value="<?= $users['email'] ?>" required>
                        <?php if(isset(session('errors')['email'])): ?>
                            <small class="text-danger"><?= session('errors')['email'] ?></small>
                        <?php endif; ?>
                    </div>
                    <div class="form-group">
                        <label for="password">Password</label>
                        <input type="password" class="form-control" id="password" name="password" value="<?= $users['password'] ?>" required>
                        <?php if(isset(session('errors')['password'])): ?>
                            <small class="text-danger"><?= session('errors')['password'] ?></small>
                        <?php endif; ?>
                    </div>
                    <div class="form-group">
                        <label for="no_hp">No HP</label>
                        <input type="text" class="form-control" id="no_hp" name="no_hp" value="<?= $users['no_hp'] ?>" required>
                        <?php if(isset(session('errors')['no_hp'])): ?>
                            <small class="text-danger"><?= session('errors')['no_hp'] ?></small>
                        <?php endif; ?>
                    </div>
                    <div class="form-group">
                        <label for="alamat">Alamat</label>
                        <textarea class="form-control" id="alamat" name="alamat" rows="3" value="<?= $users['alamat'] ?>" required></textarea>
                        <?php if(isset(session('errors')['alamat'])): ?>
                            <small class="text-danger"><?= session('errors')['alamat'] ?></small>
                        <?php endif; ?>
                    </div>
                    <div class="form-group">
                        <label for="agama">Agama</label>
                        <select class="form-control" id="agama" name="agama" value="<?= $users['agama'] ?>" required>
                            <option value="">-- Pilih Agama --</option>
                            <option value="Islam">Islam</option>
                            <option value="Kristen">Kristen</option>
                            <option value="Katolik">Katolik</option>
                            <option value="Hindu">Hindu</option>
                            <option value="Buddha">Buddha</option>
                            <option value="Konghucu">Konghucu</option>
                        </select>
                        <?php if(isset(session('errors')['agama'])): ?>
                            <small class="text-danger"><?= session('errors')['agama'] ?></small>
                        <?php endif; ?>
                    </div>
                    <div class="form-group">
                        <label for="status">Status</label>
                        <select class="form-control" id="status" name="status" value="<?= $users['status'] ?>" required>
                            <option value="">-- Pilih Status --</option>
                            <option value="0">Active</option>
                            <option value="1">Inactive</option>
                        </select>
                        <?php if(isset(session('errors')['status'])): ?>
                            <small class="text-danger"><?= session('errors')['status'] ?></small>
                        <?php endif; ?>
                    </div>
                    <div class="form-group">
                        <label for="role">Role</label>
                        <select class="form-control" id="role" name="role" value="<?= $users['role'] ?>" required>
                            <option value="">-- Pilih Role --</option>
                            <option value="admin-event">Admin Event</option>
                            <option value="ketua-kontingen">Ketua Kontingen</option>
                        </select>
                        <?php if(isset(session('errors')['role'])): ?>
                            <small class="text-danger"><?= session('errors')['role'] ?></small>
                        <?php endif; ?>
                    </div>
                    <button type="submit" class="btn btn-primary">Perbarui</button>
                    <a href="<?= base_url('users') ?>" class="btn btn-secondary">Batal</a>
                </form>
            </div>
        </div>

    </div>
    <!-- /.container-fluid -->

</div>
<!-- End of Main Content -->

<?= $this->endSection(); ?>
