# BAJA Event Organizer - Setup Guide

Panduan lengkap untuk menjalankan aplikasi BAJA Event Organizer.

## Prerequisites

Pastikan Anda sudah menginstall:
- **Node.js** (v18 atau lebih baru)
- **MySQL** (v8.0 atau lebih baru)
- **Git** (untuk clone repository)

## Database Setup

1. **Install dan jalankan MySQL Server**
2. **Buat database baru:**
```sql
CREATE DATABASE db_baja;
```

3. **Buat user MySQL (opsional):**
```sql
CREATE USER 'baja_user'@'localhost' IDENTIFIED BY 'baja_password';
GRANT ALL PRIVILEGES ON db_baja.* TO 'baja_user'@'localhost';
FLUSH PRIVILEGES;
```

## Backend Setup

1. **Masuk ke folder backend:**
```bash
cd baja-backend
```

2. **Install dependencies:**
```bash
npm install
```

3. **Setup environment variables:**
```bash
cp .env.example .env
```

4. **Edit file `.env`:**
```env
NODE_ENV=development
PORT=5000
API_VERSION=v1

# Database Configuration
DB_HOST=127.0.0.1
DB_PORT=3306
DB_NAME=db_baja
DB_USER=root
DB_PASSWORD=root

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here-change-this-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your-refresh-secret-key-here
JWT_REFRESH_EXPIRES_IN=30d

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# File Upload Configuration
UPLOAD_PATH=uploads
MAX_FILE_SIZE=5242880

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security
BCRYPT_SALT_ROUNDS=12
```

5. **Jalankan backend:**
```bash
npm run dev
```

Backend akan berjalan di `http://localhost:5000`

## Frontend Setup

1. **Buka terminal baru dan masuk ke folder frontend:**
```bash
cd baja-frontend
```

2. **Install dependencies:**
```bash
npm install
```

3. **Setup environment variables:**
```bash
cp .env.local.example .env.local
```

4. **Edit file `.env.local` (opsional):**
```env
NEXT_PUBLIC_API_URL=http://localhost:5000/api/v1
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME=BAJA Event Organizer
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_MAX_FILE_SIZE=5242880
NODE_ENV=development
```

5. **Jalankan frontend:**
```bash
npm run dev
```

Frontend akan berjalan di `http://localhost:3000`

## Quick Start (Windows)

Untuk memudahkan, Anda bisa menggunakan script batch yang sudah disediakan:

1. **Jalankan backend:**
   - Double-click `start-backend.bat`

2. **Jalankan frontend:**
   - Double-click `start-frontend.bat`

## Testing the Application

1. **Buka browser dan akses:**
   - Frontend: `http://localhost:3000`
   - Backend API: `http://localhost:5000/health`

2. **Test API Health Check:**
```bash
curl http://localhost:5000/health
```

Response yang diharapkan:
```json
{
  "status": "OK",
  "message": "BAJA Backend API is running",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "version": "v1"
}
```

## Default User Accounts

Setelah aplikasi berjalan, Anda bisa membuat akun melalui halaman register atau menggunakan seeder (jika tersedia).

### Membuat Admin User

1. **Register melalui API:**
```bash
curl -X POST http://localhost:5000/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Admin BAJA",
    "email": "<EMAIL>",
    "password": "admin123",
    "role": "admin"
  }'
```

2. **Login:**
```bash
curl -X POST http://localhost:5000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "admin123"
  }'
```

## Troubleshooting

### Backend Issues

**Error: Cannot connect to database**
- Pastikan MySQL server berjalan
- Periksa konfigurasi database di file `.env`
- Pastikan database `db_baja` sudah dibuat

**Error: Port 5000 already in use**
- Ubah port di file `.env`: `PORT=5001`
- Atau stop aplikasi yang menggunakan port 5000

**Error: JWT_SECRET is required**
- Pastikan `JWT_SECRET` sudah diset di file `.env`
- Gunakan string yang panjang dan random untuk production

### Frontend Issues

**Error: Cannot connect to API**
- Pastikan backend sudah berjalan di `http://localhost:5000`
- Periksa `NEXT_PUBLIC_API_URL` di file `.env.local`

**Error: Port 3000 already in use**
- Next.js akan otomatis menggunakan port lain (3001, 3002, dst)
- Atau stop aplikasi yang menggunakan port 3000

**Error: Module not found**
- Hapus folder `node_modules` dan `package-lock.json`
- Jalankan `npm install` lagi

### Database Issues

**Error: Access denied for user**
- Periksa username dan password MySQL di file `.env`
- Pastikan user memiliki akses ke database `db_baja`

**Error: Database does not exist**
- Buat database dengan: `CREATE DATABASE db_baja;`

## Development Tips

1. **Hot Reload:**
   - Backend menggunakan nodemon untuk auto-restart
   - Frontend menggunakan Next.js hot reload

2. **Debugging:**
   - Backend: Gunakan `console.log` atau debugger
   - Frontend: Gunakan browser dev tools

3. **Database Changes:**
   - Sequelize akan otomatis sync model dengan database
   - Untuk production, gunakan migrations

4. **API Testing:**
   - Gunakan Postman atau Thunder Client
   - Import collection dari dokumentasi API

## Production Deployment

### Backend
```bash
npm run build
npm start
```

### Frontend
```bash
npm run build
npm start
```

Untuk deployment ke cloud, sesuaikan environment variables dan database configuration.

## Support

Jika mengalami masalah:
1. Periksa log error di terminal
2. Pastikan semua dependencies terinstall
3. Periksa konfigurasi environment variables
4. Restart aplikasi jika diperlukan

Happy coding! 🚀
