import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../config/database';

interface NegaraInterface {
  id: number;
  name: string;
}

interface NegaraCreationAttributes extends Optional<NegaraInterface, 'id'> {}

class Negara extends Model<NegaraInterface, NegaraCreationAttributes> implements NegaraInterface {
  public id!: number;
  public name!: string;
}

Negara.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
  },
  {
    sequelize,
    tableName: 'negara',
    timestamps: false,
  }
);

export default Negara;
