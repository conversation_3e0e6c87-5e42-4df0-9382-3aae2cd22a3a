<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreatePendaftaranAtletDetailTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 5,
                'unsigned' => true,
                'auto_increment' => true
            ],
            'id_pendaftaran_atlet' => [
                'type' => 'INT',
                'constraint' => 5,
                'unsigned' => true,
            ],
            'id_atlet' => [
                'type' => 'INT',
                'constraint' => 5,
                'unsigned' => true,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ]
        ])
        ->addKey('id', true)
        ->addForeignKey('id_pendaftaran_atlet', 'pendaftaran_atlet', 'id', 'CASCADE', 'CASCADE')
        ->addForeignKey('id_atlet', 'atlet', 'id', 'CASCADE', 'CASCADE')
        ->createTable('pendaftaran_atlet_detail');
    }

    public function down()
    {
        $this->forge->dropTable('pendaftaran_atlet_detail');
    }
}
