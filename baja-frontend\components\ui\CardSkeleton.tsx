import React from 'react';
import { Card, CardContent } from './Card';

interface CardSkeletonProps {
  count?: number;
  hasImage?: boolean;
  gridCols?: string;
}

const CardSkeleton: React.FC<CardSkeletonProps> = ({ 
  count = 6, 
  hasImage = false,
  gridCols = 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
}) => {
  return (
    <div className={`grid ${gridCols} gap-6`}>
      {Array.from({ length: count }).map((_, index) => (
        <Card key={index} className="animate-pulse">
          {hasImage && (
            <div className="aspect-[3/4] bg-gray-700 rounded-t-lg"></div>
          )}
          <CardContent className="p-4">
            <div className="h-4 bg-gray-700 rounded w-3/4 mb-2"></div>
            <div className="h-3 bg-gray-700 rounded w-full mb-2"></div>
            <div className="h-3 bg-gray-700 rounded w-2/3 mb-4"></div>
            <div className="flex justify-between items-center">
              <div className="h-6 bg-gray-700 rounded w-16"></div>
              <div className="h-8 bg-gray-700 rounded w-20"></div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default CardSkeleton;
