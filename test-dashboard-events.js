const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api/v1';

// Test data
const testAdmin = {
  name: 'Test Admin',
  email: '<EMAIL>',
  password: 'password123',
  role: 'admin'
};

const testAdminEvent = {
  name: 'Test Admin Event',
  email: '<EMAIL>',
  password: 'password123',
  role: 'admin-event'
};

let adminToken = '';
let adminEventToken = '';

const makeRequest = async (method, endpoint, data = null, token = null) => {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
      },
    };

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    return { success: true, data: response.data };
  } catch (error) {
    return { 
      success: false, 
      error: error.response?.data?.message || error.message,
      status: error.response?.status
    };
  }
};

const testDashboardEvents = async () => {
  console.log('\n=== Testing Dashboard Events Fix ===');
  
  // Test 1: Register users
  console.log('\n--- Registering Test Users ---');
  
  let result = await makeRequest('POST', '/auth/register', testAdmin);
  console.log('Admin Registration:', result.success ? '✅ PASS' : '❌ FAIL');
  if (!result.success && result.error !== 'User already exists') {
    console.log('Error:', result.error);
  }
  
  result = await makeRequest('POST', '/auth/register', testAdminEvent);
  console.log('Admin Event Registration:', result.success ? '✅ PASS' : '❌ FAIL');
  if (!result.success && result.error !== 'User already exists') {
    console.log('Error:', result.error);
  }
  
  // Test 2: Login users
  console.log('\n--- Logging in Test Users ---');
  
  result = await makeRequest('POST', '/auth/login', {
    email: testAdmin.email,
    password: testAdmin.password
  });
  console.log('Admin Login:', result.success ? '✅ PASS' : '❌ FAIL');
  if (result.success) {
    adminToken = result.data.data.token;
    console.log('Admin token received');
  } else {
    console.log('Error:', result.error);
    return;
  }
  
  result = await makeRequest('POST', '/auth/login', {
    email: testAdminEvent.email,
    password: testAdminEvent.password
  });
  console.log('Admin Event Login:', result.success ? '✅ PASS' : '❌ FAIL');
  if (result.success) {
    adminEventToken = result.data.data.token;
    console.log('Admin Event token received');
  } else {
    console.log('Error:', result.error);
    return;
  }
  
  // Test 3: Create events
  console.log('\n--- Creating Test Events ---');
  
  const eventData = {
    name: 'Test Championship',
    description: 'Test event description',
    start_date: '2024-06-01',
    end_date: '2024-06-03',
    lokasi: 'Jakarta',
    biaya_registrasi: 500000,
    metode_pembayaran: 'Transfer Bank'
  };
  
  result = await makeRequest('POST', '/events', eventData, adminToken);
  console.log('Create Event (Admin):', result.success ? '✅ PASS' : '❌ FAIL');
  if (!result.success) console.log('Error:', result.error);
  
  result = await makeRequest('POST', '/events', {
    ...eventData,
    name: 'Admin Event Championship'
  }, adminEventToken);
  console.log('Create Event (Admin Event):', result.success ? '✅ PASS' : '❌ FAIL');
  if (!result.success) console.log('Error:', result.error);
  
  // Test 4: Test main events endpoint (should show all events)
  console.log('\n--- Testing Main Events Endpoint ---');
  
  result = await makeRequest('GET', '/events');
  console.log('Get All Events (Public):', result.success ? '✅ PASS' : '❌ FAIL');
  if (result.success) {
    console.log(`Found ${result.data.data.events.length} events (should show all events)`);
  } else {
    console.log('Error:', result.error);
  }
  
  // Test 5: Test dashboard events endpoint
  console.log('\n--- Testing Dashboard Events Endpoint ---');
  
  result = await makeRequest('GET', '/admin/dashboard-events', null, adminToken);
  console.log('Get Dashboard Events (Admin):', result.success ? '✅ PASS' : '❌ FAIL');
  if (result.success) {
    console.log(`Admin sees ${result.data.data.events.length} events (should see all events)`);
  } else {
    console.log('Error:', result.error);
  }
  
  result = await makeRequest('GET', '/admin/dashboard-events', null, adminEventToken);
  console.log('Get Dashboard Events (Admin Event):', result.success ? '✅ PASS' : '❌ FAIL');
  if (result.success) {
    console.log(`Admin Event sees ${result.data.data.events.length} events (should see only their events)`);
  } else {
    console.log('Error:', result.error);
  }
  
  console.log('\n✅ Dashboard Events Test Completed!');
};

// Run the test
testDashboardEvents().catch(console.error);
