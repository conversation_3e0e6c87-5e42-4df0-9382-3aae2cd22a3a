'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import ImageUpload from '@/components/ui/ImageUpload';
import { api } from '@/lib/api';
import { uploadService } from '@/lib/upload.service';
import Link from 'next/link';
import { ArrowLeftIcon, DocumentArrowUpIcon } from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

interface CreateEventData {
  name: string;
  description: string;
  start_date: string;
  end_date: string;
  lokasi: string;
  biaya_registrasi: number;
  metode_pembayaran: string;
}

const CreateEventPage = () => {
  const { user } = useAuth();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [selectedProposal, setSelectedProposal] = useState<File | null>(null);
  const [formData, setFormData] = useState<CreateEventData>({
    name: '',
    description: '',
    start_date: '',
    end_date: '',
    lokasi: '',
    biaya_registrasi: 0,
    metode_pembayaran: 'transfer'
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'biaya_registrasi' ? Number(value) : value
    }));
  };

  const handleImageSelect = (files: File[]) => {
    if (files.length > 0) {
      setSelectedImage(files[0]);
    }
  };

  const handleProposalSelect = (file: File) => {
    setSelectedProposal(file);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Create event first
      const response = await api.post('/events', formData);

      if (response.data.success) {
        const eventId = response.data.data.id;

        // Upload image if selected
        if (selectedImage) {
          try {
            await uploadService.uploadEventImage(eventId.toString(), selectedImage);
          } catch (uploadError) {
            console.error('Error uploading image:', uploadError);
            toast.error('Event created but image upload failed');
          }
        }

        // Upload proposal if selected
        if (selectedProposal) {
          try {
            await uploadService.uploadEventProposal(eventId.toString(), selectedProposal);
          } catch (uploadError) {
            console.error('Error uploading proposal:', uploadError);
            toast.error('Event created but proposal upload failed');
          }
        }

        toast.success('Event created successfully!');
        router.push('/dashboard/my-events');
      } else {
        toast.error(response.data.message || 'Failed to create event');
      }
    } catch (error: any) {
      console.error('Error creating event:', error);
      toast.error(error.response?.data?.message || 'Failed to create event');
    } finally {
      setLoading(false);
    }
  };

  if (user?.role !== 'admin-event') {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold text-white">Access Denied</h1>
          <p className="text-gray-300 mt-2">You don't have permission to access this page.</p>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <Link href="/dashboard/my-events">
            <Button variant="outline" size="sm">
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Events
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-white">Create New Event</h1>
            <p className="text-gray-400 mt-1">Fill in the details to create a new event</p>
          </div>
        </div>

        {/* Form */}
        <Card className="p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Event Name */}
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gold-400 mb-2">
                  Event Name *
                </label>
                <Input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="Enter event name"
                  required
                />
              </div>

              {/* Description */}
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gold-400 mb-2">
                  Description
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  placeholder="Enter event description"
                  rows={4}
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gold-400 focus:border-transparent"
                />
              </div>

              {/* Start Date */}
              <div>
                <label className="block text-sm font-medium text-gold-400 mb-2">
                  Start Date *
                </label>
                <Input
                  type="date"
                  name="start_date"
                  value={formData.start_date}
                  onChange={handleInputChange}
                  required
                />
              </div>

              {/* End Date */}
              <div>
                <label className="block text-sm font-medium text-gold-400 mb-2">
                  End Date *
                </label>
                <Input
                  type="date"
                  name="end_date"
                  value={formData.end_date}
                  onChange={handleInputChange}
                  required
                />
              </div>

              {/* Location */}
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gold-400 mb-2">
                  Location *
                </label>
                <Input
                  type="text"
                  name="lokasi"
                  value={formData.lokasi}
                  onChange={handleInputChange}
                  placeholder="Enter event location"
                  required
                />
              </div>

              {/* Registration Fee */}
              <div>
                <label className="block text-sm font-medium text-gold-400 mb-2">
                  Registration Fee (IDR) *
                </label>
                <Input
                  type="number"
                  name="biaya_registrasi"
                  value={formData.biaya_registrasi}
                  onChange={handleInputChange}
                  placeholder="0"
                  min="0"
                  required
                />
              </div>

              {/* Payment Method */}
              <div>
                <label className="block text-sm font-medium text-gold-400 mb-2">
                  Payment Method *
                </label>
                <select
                  name="metode_pembayaran"
                  value={formData.metode_pembayaran}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-gold-400"
                  required
                >
                  <option value="transfer">Bank Transfer</option>
                  <option value="cash">Cash</option>
                  <option value="online">Online Payment</option>
                </select>
              </div>
            </div>

            {/* Event Image Upload */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gold-400 mb-2">
                Event Image
              </label>
              <ImageUpload
                onImagesSelected={handleImageSelect}
                maxImages={1}
                placeholder="Select an event image"
                maxSize={5}
                acceptedFormats={['image/jpeg', 'image/jpg', 'image/png']}
                showPreview={true}
                selectButtonText="Choose Image"
                disabled={loading}
              />
              {selectedImage && (
                <p className="text-sm text-green-400 mt-2">
                  ✓ Image selected: {selectedImage.name}
                </p>
              )}
            </div>

            {/* Event Proposal Upload */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gold-400 mb-2">
                Event Proposal (PDF)
              </label>
              <div className="border-2 border-dashed border-gold-500/30 rounded-lg p-6 hover:border-gold-500/50 transition-colors">
                <input
                  type="file"
                  accept=".pdf"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) handleProposalSelect(file);
                  }}
                  className="hidden"
                  id="proposal-upload"
                  disabled={loading}
                />
                <label
                  htmlFor="proposal-upload"
                  className="cursor-pointer flex flex-col items-center justify-center space-y-3"
                >
                  <DocumentArrowUpIcon className="h-12 w-12 text-gold-400" />
                  <div className="text-center">
                    <span className="text-white font-medium">
                      {selectedProposal ? selectedProposal.name : 'Click to upload proposal'}
                    </span>
                    <p className="text-gray-400 text-sm mt-1">
                      PDF files only, max 10MB
                    </p>
                  </div>
                </label>
              </div>
              {selectedProposal && (
                <p className="text-sm text-green-400 mt-2">
                  ✓ Proposal selected: {selectedProposal.name}
                </p>
              )}
            </div>

            {/* Submit Buttons */}
            <div className="flex justify-end space-x-4 pt-6 border-t border-gray-700">
              <Link href="/dashboard/my-events">
                <Button variant="outline" type="button">
                  Cancel
                </Button>
              </Link>
              <Button
                type="submit"
                disabled={loading}
                className="bg-gold-500 hover:bg-gold-600 text-black"
              >
                {loading ? (
                  <>
                    <LoadingSpinner size="sm" className="mr-2" />
                    Creating...
                  </>
                ) : (
                  'Create Event'
                )}
              </Button>
            </div>
          </form>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default CreateEventPage;
