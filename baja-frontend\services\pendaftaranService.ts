import { api } from '@/lib/api';

export interface PendaftaranEvent {
  id: number;
  id_event: number;
  id_kontingen: number;
  event?: {
    id: number;
    name: string;
    start_date: string;
    end_date: string;
    lokasi: string;
    biaya_registrasi?: number;
    description?: string;
    metode_pembayaran?: string;
    status?: string;
  };
  kontingen?: {
    id: number;
    name: string;
  };
  created_at: string;
  updated_at: string;
}

export interface CreatePendaftaranData {
  id_event: number;
}

class PendaftaranService {
  async getAllPendaftaran(page = 1, limit = 10, search = '', event_id = ''): Promise<{
    pendaftaran: PendaftaranEvent[];
    pagination: {
      total: number;
      page: number;
      limit: number;
      totalPages: number;
    };
  }> {
    try {
      const response = await api.get('/pendaftaran', {
        params: { page, limit, search, event_id }
      });
      return response.data.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch registrations');
    }
  }

  async getPendaftaranById(id: number): Promise<PendaftaranEvent> {
    try {
      const response = await api.get(`/pendaftaran/${id}`);
      return response.data.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch registration');
    }
  }

  async createPendaftaran(data: CreatePendaftaranData): Promise<PendaftaranEvent> {
    try {
      const response = await api.post('/pendaftaran', data);
      return response.data.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to register for event');
    }
  }

  async deletePendaftaran(id: number): Promise<void> {
    try {
      await api.delete(`/pendaftaran/${id}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to delete registration');
    }
  }
}

export const pendaftaranService = new PendaftaranService();
