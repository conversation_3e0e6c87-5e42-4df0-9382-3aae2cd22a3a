import { Router } from 'express';
import { authenticate, authorize } from '../middleware/auth.middleware';
import {
  getAllKontingen,
  getKontingenById,
  createKontingen,
  updateKontingen,
  deleteKontingen,
  getMyKontingen
} from '../controllers/kontingen.controller';

const router = Router();

// All routes require authentication
router.use(authenticate);

router.get('/', getAllKontingen);
router.post('/', authorize('admin', 'ketua-kontingen'), createKontingen);
router.get('/my', authorize('ketua-kontingen'), getMyKontingen);
router.get('/:id', getKontingenById);
router.put('/:id', authorize('admin', 'ketua-kontingen'), updateKontingen);
router.delete('/:id', authorize('admin', 'ketua-kontingen'), deleteKontingen);

export default router;
