{"version": 3, "sources": ["../../js/src/util/index.js", "../../js/src/dom/data.js", "../../js/src/dom/event-handler.js", "../../js/src/base-component.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/dom/manipulator.js", "../../js/src/dom/selector-engine.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../js/src/dropdown.js", "../../js/src/modal.js", "../../js/src/util/scrollbar.js", "../../js/src/offcanvas.js", "../../js/src/util/sanitizer.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/toast.js", "../../js/index.umd.js"], "names": ["getUID", "prefix", "Math", "floor", "random", "document", "getElementById", "getSelector", "element", "selector", "getAttribute", "hrefAttr", "includes", "startsWith", "split", "trim", "getSelectorFromElement", "querySelector", "getElementFromSelector", "getTransitionDurationFromElement", "transitionDuration", "transitionDelay", "window", "getComputedStyle", "floatTransitionDuration", "Number", "parseFloat", "floatTransitionDelay", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "obj", "nodeType", "emulateTransitionEnd", "duration", "called", "emulatedDuration", "addEventListener", "listener", "removeEventListener", "setTimeout", "typeCheckConfig", "componentName", "config", "configTypes", "Object", "keys", "for<PERSON>ach", "property", "expectedTypes", "value", "valueType", "toString", "call", "match", "toLowerCase", "RegExp", "test", "TypeError", "toUpperCase", "isVisible", "style", "parentNode", "elementStyle", "parentNodeStyle", "display", "visibility", "isDisabled", "Node", "ELEMENT_NODE", "classList", "contains", "disabled", "hasAttribute", "findShadowRoot", "documentElement", "attachShadow", "getRootNode", "root", "ShadowRoot", "noop", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "body", "isRTL", "dir", "defineJQueryPlugin", "name", "plugin", "callback", "$", "JQUERY_NO_CONFLICT", "fn", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "readyState", "elementMap", "Map", "Data", "set", "key", "instance", "has", "instanceMap", "get", "size", "console", "error", "Array", "from", "remove", "delete", "namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "nativeEvents", "Set", "getUidEvent", "uid", "getEvent", "<PERSON><PERSON><PERSON><PERSON>", "events", "handler", "delegationSelector", "uidEventList", "i", "len", "length", "event", "<PERSON><PERSON><PERSON><PERSON>", "normalizeParams", "originalTypeEvent", "delegationFn", "delegation", "typeEvent", "replace", "custom", "add<PERSON><PERSON><PERSON>", "oneOff", "handlers", "previousFn", "dom<PERSON><PERSON>s", "querySelectorAll", "target", "this", "<PERSON><PERSON><PERSON><PERSON>", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "bootstrapHandler", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "on", "one", "inNamespace", "isNamespace", "elementEvent", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "removeNamespacedHandlers", "slice", "keyHandlers", "trigger", "args", "isNative", "jQueryEvent", "bubbles", "nativeDispatch", "defaultPrevented", "evt", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "createEvent", "initEvent", "CustomEvent", "cancelable", "defineProperty", "preventDefault", "BaseComponent", "constructor", "_element", "DATA_KEY", "dispose", "[object Object]", "VERSION", "<PERSON><PERSON>", "close", "rootElement", "_getRootElement", "customEvent", "_triggerCloseEvent", "_removeElement", "closest", "_destroyElement", "<PERSON><PERSON><PERSON><PERSON>", "each", "data", "alertInstance", "handle<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "toggle", "setAttribute", "normalizeData", "val", "normalizeDataKey", "chr", "button", "Manipulator", "setDataAttribute", "removeDataAttribute", "removeAttribute", "getDataAttributes", "attributes", "dataset", "filter", "pureKey", "char<PERSON>t", "getDataAttribute", "offset", "rect", "getBoundingClientRect", "top", "scrollTop", "left", "scrollLeft", "position", "offsetTop", "offsetLeft", "SelectorEngine", "find", "concat", "Element", "prototype", "findOne", "children", "child", "matches", "parents", "ancestor", "push", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "<PERSON><PERSON><PERSON>", "interval", "keyboard", "slide", "pause", "wrap", "touch", "DefaultType", "ORDER_NEXT", "ORDER_PREV", "DIRECTION_LEFT", "DIRECTION_RIGHT", "Carousel", "super", "_items", "_interval", "_activeElement", "_isPaused", "_isSliding", "touchTimeout", "touchStartX", "touchDeltaX", "_config", "_getConfig", "_indicatorsElement", "_touchSupported", "navigator", "maxTouchPoints", "_pointerEvent", "PointerEvent", "_addEventListeners", "_slide", "nextWhenVisible", "hidden", "cycle", "clearInterval", "_updateInterval", "setInterval", "visibilityState", "bind", "to", "index", "activeIndex", "_getItemIndex", "order", "_handleSwipe", "absDeltax", "abs", "direction", "_keydown", "_addTouchEventListeners", "start", "pointerType", "touches", "clientX", "move", "end", "clearTimeout", "itemImg", "e", "add", "tagName", "indexOf", "_getItemByOrder", "activeElement", "isNext", "isPrev", "lastItemIndex", "itemIndex", "_triggerSlideEvent", "relatedTarget", "eventDirectionName", "targetIndex", "fromIndex", "_setActiveIndicatorElement", "activeIndicator", "indicators", "parseInt", "elementInterval", "defaultInterval", "directionOrOrder", "_directionToOrder", "activeElementIndex", "nextElement", "nextElementIndex", "isCycling", "directionalClassName", "orderClassName", "_orderToDirection", "action", "ride", "carouselInterface", "slideIndex", "dataApiClickHandler", "carousels", "parent", "Collapse", "_isTransitioning", "_triggerArray", "id", "toggleList", "elem", "filterElement", "foundElem", "_selector", "_parent", "_getParent", "_addAriaAndCollapsedClass", "hide", "show", "actives", "activesData", "container", "tempActiveData", "elemActive", "collapseInterface", "dimension", "_getDimension", "setTransitioning", "scrollSize", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isTransitioning", "j<PERSON>y", "selected", "trigger<PERSON><PERSON>y", "isOpen", "triggerData", "REGEXP_KEYDOWN", "PLACEMENT_TOP", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "boundary", "reference", "popperConfig", "Dropdown", "_popper", "_menu", "_getMenuElement", "_inNavbar", "_detectNavbar", "isActive", "clearMenus", "getParentFromElement", "<PERSON><PERSON>", "referenceElement", "_getPopperConfig", "isDisplayStatic", "modifiers", "modifier", "enabled", "createPopper", "focus", "destroy", "update", "_getPlacement", "parentDropdown", "isEnd", "getPropertyValue", "_getOffset", "map", "popperData", "defaultBsPopperConfig", "placement", "options", "dropdownInterface", "toggles", "context", "clickEvent", "dropdownMenu", "some", "<PERSON><PERSON><PERSON>", "stopPropagation", "click", "items", "dataApiKeydownHandler", "backdrop", "Modal", "_dialog", "_backdrop", "_isShown", "_isBodyOverflowing", "_ignoreBackdropClick", "_scrollbarWidth", "_isAnimated", "showEvent", "_checkScrollbar", "_setScrollbar", "_adjustDialog", "_setEscapeEvent", "_setResizeEvent", "_showBackdrop", "_showElement", "isAnimated", "_hideModal", "htmlElement", "handleUpdate", "modalBody", "append<PERSON><PERSON><PERSON>", "_enforceFocus", "transitionComplete", "_triggerBackdropTransition", "_resetAdjustments", "_resetScrollbar", "_removeBackdrop", "createElement", "className", "currentTarget", "backdropTransitionDuration", "callback<PERSON><PERSON><PERSON>", "isModalOverflowing", "scrollHeight", "clientHeight", "overflowY", "modalTransitionDuration", "paddingLeft", "paddingRight", "round", "right", "innerWidth", "_getScrollbarWidth", "_setElementAttributes", "calculatedValue", "styleProp", "clientWidth", "actualValue", "_resetElementAttributes", "scrollDiv", "scrollbarWidth", "width", "getWidth", "documentWidth", "removeProperty", "scroll", "<PERSON><PERSON><PERSON>", "overflow", "scrollBarHide", "_enforceFocusOnElement", "blur", "undefined", "allReadyOpen", "el", "uriAttrs", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "allowedAttribute", "attr", "allowedAttributeList", "attrName", "nodeName", "nodeValue", "regExp", "attrRegex", "sanitizeHtml", "unsafeHtml", "allowList", "sanitizeFn", "createdDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "allow<PERSON><PERSON><PERSON><PERSON>", "elements", "el<PERSON>ame", "attributeList", "allowedAttributes", "innerHTML", "BSCLS_PREFIX_REGEX", "DISALLOWED_ATTRIBUTES", "animation", "template", "title", "delay", "html", "fallbackPlacements", "customClass", "sanitize", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "*", "a", "area", "b", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "img", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "HIDE", "HIDDEN", "SHOW", "SHOWN", "INSERTED", "CLICK", "FOCUSIN", "FOCUSOUT", "MOUSEENTER", "MOUSELEAVE", "<PERSON><PERSON><PERSON>", "_isEnabled", "_timeout", "_hoverState", "_activeTrigger", "tip", "_setListeners", "NAME", "EVENT_KEY", "enable", "disable", "toggle<PERSON>nabled", "_initializeOnDelegatedTarget", "_isWithActiveTrigger", "_enter", "_leave", "getTipElement", "_hideModalHandler", "Error", "isWithContent", "shadowRoot", "isInTheDom", "ownerDocument", "tipId", "<PERSON><PERSON><PERSON><PERSON>", "attachment", "_getAttachment", "_addAttachmentClass", "_get<PERSON><PERSON><PERSON>", "complete", "prevHoverState", "_cleanTipClass", "getTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "textContent", "updateAttachment", "dataKey", "_getDelegateConfig", "altBoundary", "phase", "_handlePopperPlacementChange", "onFirstUpdate", "eventIn", "eventOut", "_fixTitle", "originalTitleType", "dataAttributes", "dataAttr", "tabClass", "token", "tClass", "state", "popper", "Popover", "_getContent", "method", "ScrollSpy", "_scrollElement", "_offsets", "_targets", "_activeTarget", "_scrollHeight", "_process", "refresh", "autoMethod", "offsetMethod", "offsetBase", "_getScrollTop", "_getScrollHeight", "targetSelector", "targetBCR", "height", "item", "sort", "pageYOffset", "max", "_getOffsetHeight", "innerHeight", "maxScroll", "_activate", "_clear", "queries", "link", "join", "listGroup", "navItem", "node", "spy", "Tab", "listElement", "itemSelector", "hideEvent", "active", "_transitionComplete", "dropdown<PERSON><PERSON>d", "dropdown", "autohide", "Toast", "_clearTimeout"], "mappings": ";;;;;ykBAOA,MAmBMA,EAASC,IACb,GACEA,GAAUC,KAAKC,MArBH,IAqBSD,KAAKE,gBACnBC,SAASC,eAAeL,IAEjC,OAAOA,GAGHM,EAAcC,IAClB,IAAIC,EAAWD,EAAQE,aAAa,kBAEpC,IAAKD,GAAyB,MAAbA,EAAkB,CACjC,IAAIE,EAAWH,EAAQE,aAAa,QAMpC,IAAKC,IAAcA,EAASC,SAAS,OAASD,EAASE,WAAW,KAChE,OAAO,KAILF,EAASC,SAAS,OAASD,EAASE,WAAW,OACjDF,EAAW,IAAMA,EAASG,MAAM,KAAK,IAGvCL,EAAWE,GAAyB,MAAbA,EAAmBA,EAASI,OAAS,KAG9D,OAAON,GAGHO,EAAyBR,IAC7B,MAAMC,EAAWF,EAAYC,GAE7B,OAAIC,GACKJ,SAASY,cAAcR,GAAYA,EAGrC,MAGHS,EAAyBV,IAC7B,MAAMC,EAAWF,EAAYC,GAE7B,OAAOC,EAAWJ,SAASY,cAAcR,GAAY,MAGjDU,EAAmCX,IACvC,IAAKA,EACH,OAAO,EAIT,IAAIY,mBAAEA,EAAFC,gBAAsBA,GAAoBC,OAAOC,iBAAiBf,GAEtE,MAAMgB,EAA0BC,OAAOC,WAAWN,GAC5CO,EAAuBF,OAAOC,WAAWL,GAG/C,OAAKG,GAA4BG,GAKjCP,EAAqBA,EAAmBN,MAAM,KAAK,GACnDO,EAAkBA,EAAgBP,MAAM,KAAK,GArFf,KAuFtBW,OAAOC,WAAWN,GAAsBK,OAAOC,WAAWL,KAPzD,GAULO,EAAuBpB,IAC3BA,EAAQqB,cAAc,IAAIC,MA1FL,mBA6FjBC,EAAYC,IAAQA,EAAI,IAAMA,GAAKC,SAEnCC,EAAuB,CAAC1B,EAAS2B,KACrC,IAAIC,GAAS,EACb,MACMC,EAAmBF,EADD,EAQxB3B,EAAQ8B,iBAzGa,iBAoGrB,SAASC,IACPH,GAAS,EACT5B,EAAQgC,oBAtGW,gBAsGyBD,MAI9CE,WAAW,KACJL,GACHR,EAAqBpB,IAEtB6B,IAGCK,EAAkB,CAACC,EAAeC,EAAQC,KAC9CC,OAAOC,KAAKF,GAAaG,QAAQC,IAC/B,MAAMC,EAAgBL,EAAYI,GAC5BE,EAAQP,EAAOK,GACfG,EAAYD,GAASpB,EAAUoB,GAAS,UAjH5CnB,OADSA,EAkHsDmB,GAhHzD,GAAEnB,EAGL,GAAGqB,SAASC,KAAKtB,GAAKuB,MAAM,eAAe,GAAGC,cALxCxB,IAAAA,EAoHX,IAAK,IAAIyB,OAAOP,GAAeQ,KAAKN,GAClC,MAAM,IAAIO,UACLhB,EAAciB,cAAhB,KACA,WAAUX,qBAA4BG,MACtC,sBAAqBF,UAMxBW,EAAYrD,IAChB,IAAKA,EACH,OAAO,EAGT,GAAIA,EAAQsD,OAAStD,EAAQuD,YAAcvD,EAAQuD,WAAWD,MAAO,CACnE,MAAME,EAAezC,iBAAiBf,GAChCyD,EAAkB1C,iBAAiBf,EAAQuD,YAEjD,MAAgC,SAAzBC,EAAaE,SACU,SAA5BD,EAAgBC,SACY,WAA5BF,EAAaG,WAGjB,OAAO,GAGHC,EAAa5D,IACZA,GAAWA,EAAQyB,WAAaoC,KAAKC,gBAItC9D,EAAQ+D,UAAUC,SAAS,mBAIC,IAArBhE,EAAQiE,SACVjE,EAAQiE,SAGVjE,EAAQkE,aAAa,aAAoD,UAArClE,EAAQE,aAAa,aAG5DiE,EAAiBnE,IACrB,IAAKH,SAASuE,gBAAgBC,aAC5B,OAAO,KAIT,GAAmC,mBAAxBrE,EAAQsE,YAA4B,CAC7C,MAAMC,EAAOvE,EAAQsE,cACrB,OAAOC,aAAgBC,WAAaD,EAAO,KAG7C,OAAIvE,aAAmBwE,WACdxE,EAIJA,EAAQuD,WAINY,EAAenE,EAAQuD,YAHrB,MAMLkB,EAAO,IAAM,aAEbC,EAAS1E,GAAWA,EAAQ2E,aAE5BC,EAAY,KAChB,MAAMC,OAAEA,GAAW/D,OAEnB,OAAI+D,IAAWhF,SAASiF,KAAKZ,aAAa,qBACjCW,EAGF,MAWHE,EAAQ,IAAuC,QAAjClF,SAASuE,gBAAgBY,IAEvCC,EAAqB,CAACC,EAAMC,KAVPC,IAAAA,EAAAA,EAWN,KACjB,MAAMC,EAAIT,IAEV,GAAIS,EAAG,CACL,MAAMC,EAAqBD,EAAEE,GAAGL,GAChCG,EAAEE,GAAGL,GAAQC,EAAOK,gBACpBH,EAAEE,GAAGL,GAAMO,YAAcN,EACzBE,EAAEE,GAAGL,GAAMQ,WAAa,KACtBL,EAAEE,GAAGL,GAAQI,EACNH,EAAOK,mBAnBQ,YAAxB3F,SAAS8F,WACX9F,SAASiC,iBAAiB,mBAAoBsD,GAE9CA,KCvMEQ,EAAa,IAAIC,IAEvB,IAAAC,EAAe,CACbC,IAAI/F,EAASgG,EAAKC,GACXL,EAAWM,IAAIlG,IAClB4F,EAAWG,IAAI/F,EAAS,IAAI6F,KAG9B,MAAMM,EAAcP,EAAWQ,IAAIpG,GAI9BmG,EAAYD,IAAIF,IAA6B,IAArBG,EAAYE,KAMzCF,EAAYJ,IAAIC,EAAKC,GAJnBK,QAAQC,MAAO,+EAA8EC,MAAMC,KAAKN,EAAY5D,QAAQ,QAOhI6D,IAAG,CAACpG,EAASgG,IACPJ,EAAWM,IAAIlG,IACV4F,EAAWQ,IAAIpG,GAASoG,IAAIJ,IAG9B,KAGTU,OAAO1G,EAASgG,GACd,IAAKJ,EAAWM,IAAIlG,GAClB,OAGF,MAAMmG,EAAcP,EAAWQ,IAAIpG,GAEnCmG,EAAYQ,OAAOX,GAGM,IAArBG,EAAYE,MACdT,EAAWe,OAAO3G,KCtCxB,MAAM4G,EAAiB,qBACjBC,EAAiB,OACjBC,EAAgB,SAChBC,EAAgB,GACtB,IAAIC,EAAW,EACf,MAAMC,EAAe,CACnBC,WAAY,YACZC,WAAY,YAERC,EAAe,IAAIC,IAAI,CAC3B,QACA,WACA,UACA,YACA,cACA,aACA,iBACA,YACA,WACA,YACA,cACA,YACA,UACA,WACA,QACA,oBACA,aACA,YACA,WACA,cACA,cACA,cACA,YACA,eACA,gBACA,eACA,gBACA,aACA,QACA,OACA,SACA,QACA,SACA,SACA,UACA,WACA,OACA,SACA,eACA,SACA,OACA,mBACA,mBACA,QACA,QACA,WASF,SAASC,EAAYtH,EAASuH,GAC5B,OAAQA,GAAQ,GAAEA,MAAQP,OAAiBhH,EAAQgH,UAAYA,IAGjE,SAASQ,EAASxH,GAChB,MAAMuH,EAAMD,EAAYtH,GAKxB,OAHAA,EAAQgH,SAAWO,EACnBR,EAAcQ,GAAOR,EAAcQ,IAAQ,GAEpCR,EAAcQ,GAuCvB,SAASE,EAAYC,EAAQC,EAASC,EAAqB,MACzD,MAAMC,EAAevF,OAAOC,KAAKmF,GAEjC,IAAK,IAAII,EAAI,EAAGC,EAAMF,EAAaG,OAAQF,EAAIC,EAAKD,IAAK,CACvD,MAAMG,EAAQP,EAAOG,EAAaC,IAElC,GAAIG,EAAMC,kBAAoBP,GAAWM,EAAML,qBAAuBA,EACpE,OAAOK,EAIX,OAAO,KAGT,SAASE,EAAgBC,EAAmBT,EAASU,GACnD,MAAMC,EAAgC,iBAAZX,EACpBO,EAAkBI,EAAaD,EAAeV,EAGpD,IAAIY,EAAYH,EAAkBI,QAAQ3B,EAAgB,IAC1D,MAAM4B,EAASxB,EAAasB,GAY5B,OAVIE,IACFF,EAAYE,GAGGrB,EAAalB,IAAIqC,KAGhCA,EAAYH,GAGP,CAACE,EAAYJ,EAAiBK,GAGvC,SAASG,EAAW1I,EAASoI,EAAmBT,EAASU,EAAcM,GACrE,GAAiC,iBAAtBP,IAAmCpI,EAC5C,OAGG2H,IACHA,EAAUU,EACVA,EAAe,MAGjB,MAAOC,EAAYJ,EAAiBK,GAAaJ,EAAgBC,EAAmBT,EAASU,GACvFX,EAASF,EAASxH,GAClB4I,EAAWlB,EAAOa,KAAeb,EAAOa,GAAa,IACrDM,EAAapB,EAAYmB,EAAUV,EAAiBI,EAAaX,EAAU,MAEjF,GAAIkB,EAGF,YAFAA,EAAWF,OAASE,EAAWF,QAAUA,GAK3C,MAAMpB,EAAMD,EAAYY,EAAiBE,EAAkBI,QAAQ5B,EAAgB,KAC7ErB,EAAK+C,EAjFb,SAAoCtI,EAASC,EAAUsF,GACrD,OAAO,SAASoC,EAAQM,GACtB,MAAMa,EAAc9I,EAAQ+I,iBAAiB9I,GAE7C,IAAK,IAAI+I,OAAEA,GAAWf,EAAOe,GAAUA,IAAWC,KAAMD,EAASA,EAAOzF,WACtE,IAAK,IAAIuE,EAAIgB,EAAYd,OAAQF,KAC/B,GAAIgB,EAAYhB,KAAOkB,EAQrB,OAPAf,EAAMiB,eAAiBF,EAEnBrB,EAAQgB,QAEVQ,EAAaC,IAAIpJ,EAASiI,EAAMoB,KAAM9D,GAGjCA,EAAG+D,MAAMN,EAAQ,CAACf,IAM/B,OAAO,MA8DPsB,CAA2BvJ,EAAS2H,EAASU,GA9FjD,SAA0BrI,EAASuF,GACjC,OAAO,SAASoC,EAAQM,GAOtB,OANAA,EAAMiB,eAAiBlJ,EAEnB2H,EAAQgB,QACVQ,EAAaC,IAAIpJ,EAASiI,EAAMoB,KAAM9D,GAGjCA,EAAG+D,MAAMtJ,EAAS,CAACiI,KAuF1BuB,CAAiBxJ,EAAS2H,GAE5BpC,EAAGqC,mBAAqBU,EAAaX,EAAU,KAC/CpC,EAAG2C,gBAAkBA,EACrB3C,EAAGoD,OAASA,EACZpD,EAAGyB,SAAWO,EACdqB,EAASrB,GAAOhC,EAEhBvF,EAAQ8B,iBAAiByG,EAAWhD,EAAI+C,GAG1C,SAASmB,EAAczJ,EAAS0H,EAAQa,EAAWZ,EAASC,GAC1D,MAAMrC,EAAKkC,EAAYC,EAAOa,GAAYZ,EAASC,GAE9CrC,IAILvF,EAAQgC,oBAAoBuG,EAAWhD,EAAImE,QAAQ9B,WAC5CF,EAAOa,GAAWhD,EAAGyB,WAe9B,MAAMmC,EAAe,CACnBQ,GAAG3J,EAASiI,EAAON,EAASU,GAC1BK,EAAW1I,EAASiI,EAAON,EAASU,GAAc,IAGpDuB,IAAI5J,EAASiI,EAAON,EAASU,GAC3BK,EAAW1I,EAASiI,EAAON,EAASU,GAAc,IAGpDe,IAAIpJ,EAASoI,EAAmBT,EAASU,GACvC,GAAiC,iBAAtBD,IAAmCpI,EAC5C,OAGF,MAAOsI,EAAYJ,EAAiBK,GAAaJ,EAAgBC,EAAmBT,EAASU,GACvFwB,EAActB,IAAcH,EAC5BV,EAASF,EAASxH,GAClB8J,EAAc1B,EAAkB/H,WAAW,KAEjD,QAA+B,IAApB6H,EAAiC,CAE1C,IAAKR,IAAWA,EAAOa,GACrB,OAIF,YADAkB,EAAczJ,EAAS0H,EAAQa,EAAWL,EAAiBI,EAAaX,EAAU,MAIhFmC,GACFxH,OAAOC,KAAKmF,GAAQlF,QAAQuH,KA1ClC,SAAkC/J,EAAS0H,EAAQa,EAAWyB,GAC5D,MAAMC,EAAoBvC,EAAOa,IAAc,GAE/CjG,OAAOC,KAAK0H,GAAmBzH,QAAQ0H,IACrC,GAAIA,EAAW9J,SAAS4J,GAAY,CAClC,MAAM/B,EAAQgC,EAAkBC,GAEhCT,EAAczJ,EAAS0H,EAAQa,EAAWN,EAAMC,gBAAiBD,EAAML,uBAoCrEuC,CAAyBnK,EAAS0H,EAAQqC,EAAc3B,EAAkBgC,MAAM,MAIpF,MAAMH,EAAoBvC,EAAOa,IAAc,GAC/CjG,OAAOC,KAAK0H,GAAmBzH,QAAQ6H,IACrC,MAAMH,EAAaG,EAAY7B,QAAQ1B,EAAe,IAEtD,IAAK+C,GAAezB,EAAkBhI,SAAS8J,GAAa,CAC1D,MAAMjC,EAAQgC,EAAkBI,GAEhCZ,EAAczJ,EAAS0H,EAAQa,EAAWN,EAAMC,gBAAiBD,EAAML,wBAK7E0C,QAAQtK,EAASiI,EAAOsC,GACtB,GAAqB,iBAAVtC,IAAuBjI,EAChC,OAAO,KAGT,MAAMqF,EAAIT,IACJ2D,EAAYN,EAAMO,QAAQ3B,EAAgB,IAC1CgD,EAAc5B,IAAUM,EACxBiC,EAAWpD,EAAalB,IAAIqC,GAElC,IAAIkC,EACAC,GAAU,EACVC,GAAiB,EACjBC,GAAmB,EACnBC,EAAM,KA4CV,OA1CIhB,GAAexE,IACjBoF,EAAcpF,EAAE/D,MAAM2G,EAAOsC,GAE7BlF,EAAErF,GAASsK,QAAQG,GACnBC,GAAWD,EAAYK,uBACvBH,GAAkBF,EAAYM,gCAC9BH,EAAmBH,EAAYO,sBAG7BR,GACFK,EAAMhL,SAASoL,YAAY,cAC3BJ,EAAIK,UAAU3C,EAAWmC,GAAS,IAElCG,EAAM,IAAIM,YAAYlD,EAAO,CAC3ByC,QAAAA,EACAU,YAAY,SAKI,IAATb,GACTjI,OAAOC,KAAKgI,GAAM/H,QAAQwD,IACxB1D,OAAO+I,eAAeR,EAAK7E,EAAK,CAC9BI,IAAG,IACMmE,EAAKvE,OAMhB4E,GACFC,EAAIS,iBAGFX,GACF3K,EAAQqB,cAAcwJ,GAGpBA,EAAID,uBAA2C,IAAhBH,GACjCA,EAAYa,iBAGPT,ICrTX,MAAMU,EACJC,YAAYxL,IACVA,EAA6B,iBAAZA,EAAuBH,SAASY,cAAcT,GAAWA,KAM1EiJ,KAAKwC,SAAWzL,EAChB8F,EAAKC,IAAIkD,KAAKwC,SAAUxC,KAAKuC,YAAYE,SAAUzC,OAGrD0C,UACE7F,EAAKY,OAAOuC,KAAKwC,SAAUxC,KAAKuC,YAAYE,UAC5CzC,KAAKwC,SAAW,KAKAG,mBAAC5L,GACjB,OAAO8F,EAAKM,IAAIpG,EAASiJ,KAAKyC,UAGdG,qBAChB,MA1BY,eC6BhB,MAAMC,UAAcP,EAGCG,sBACjB,MAxBa,WA6BfK,MAAM/L,GACJ,MAAMgM,EAAchM,EAAUiJ,KAAKgD,gBAAgBjM,GAAWiJ,KAAKwC,SAC7DS,EAAcjD,KAAKkD,mBAAmBH,GAExB,OAAhBE,GAAwBA,EAAYtB,kBAIxC3B,KAAKmD,eAAeJ,GAKtBC,gBAAgBjM,GACd,OAAOU,EAAuBV,IAAYA,EAAQqM,QAAS,UAG7DF,mBAAmBnM,GACjB,OAAOmJ,EAAamB,QAAQtK,EAzCX,kBA4CnBoM,eAAepM,GAGb,GAFAA,EAAQ+D,UAAU2C,OAvCE,SAyCf1G,EAAQ+D,UAAUC,SA1CH,QA4ClB,YADAiF,KAAKqD,gBAAgBtM,GAIvB,MAAMY,EAAqBD,EAAiCX,GAE5DmJ,EAAaS,IAAI5J,EAAS,gBAAiB,IAAMiJ,KAAKqD,gBAAgBtM,IACtE0B,EAAqB1B,EAASY,GAGhC0L,gBAAgBtM,GACVA,EAAQuD,YACVvD,EAAQuD,WAAWgJ,YAAYvM,GAGjCmJ,EAAamB,QAAQtK,EA9DH,mBAmEE4L,uBAACxJ,GACrB,OAAO6G,KAAKuD,MAAK,WACf,IAAIC,EAAO3G,EAAKM,IAAI6C,KA5ET,YA8ENwD,IACHA,EAAO,IAAIX,EAAM7C,OAGJ,UAAX7G,GACFqK,EAAKrK,GAAQ6G,SAKC2C,qBAACc,GACnB,OAAO,SAAUzE,GACXA,GACFA,EAAMqD,iBAGRoB,EAAcX,MAAM9C,QAW1BE,EAAaQ,GAAG9J,SAjGc,0BAJL,4BAqGyCiM,EAAMa,cAAc,IAAIb,IAS1F7G,EAnHa,QAmHY6G,GCvGzB,MAAMc,UAAerB,EAGAG,sBACjB,MApBa,YAyBfmB,SAEE5D,KAAKwC,SAASqB,aAAa,eAAgB7D,KAAKwC,SAAS1H,UAAU8I,OAvB7C,WA4BFjB,uBAACxJ,GACrB,OAAO6G,KAAKuD,MAAK,WACf,IAAIC,EAAO3G,EAAKM,IAAI6C,KAlCT,aAoCNwD,IACHA,EAAO,IAAIG,EAAO3D,OAGL,WAAX7G,GACFqK,EAAKrK,SCrDb,SAAS2K,EAAcC,GACrB,MAAY,SAARA,GAIQ,UAARA,IAIAA,IAAQ/L,OAAO+L,GAAKnK,WACf5B,OAAO+L,GAGJ,KAARA,GAAsB,SAARA,EACT,KAGFA,GAGT,SAASC,EAAiBjH,GACxB,OAAOA,EAAIwC,QAAQ,SAAU0E,GAAQ,IAAGA,EAAIlK,eD4C9CmG,EAAaQ,GAAG9J,SA7Cc,2BAFD,4BA+CyCoI,IACpEA,EAAMqD,iBAEN,MAAM6B,EAASlF,EAAMe,OAAOqD,QAlDD,6BAoD3B,IAAII,EAAO3G,EAAKM,IAAI+G,EA1DL,aA2DVV,IACHA,EAAO,IAAIG,EAAOO,IAGpBV,EAAKI,WAUP5H,EA1Ea,SA0EY2H,GC7DzB,MAAMQ,EAAc,CAClBC,iBAAiBrN,EAASgG,EAAKrD,GAC7B3C,EAAQ8M,aAAc,WAAUG,EAAiBjH,GAAQrD,IAG3D2K,oBAAoBtN,EAASgG,GAC3BhG,EAAQuN,gBAAiB,WAAUN,EAAiBjH,KAGtDwH,kBAAkBxN,GAChB,IAAKA,EACH,MAAO,GAGT,MAAMyN,EAAa,GAUnB,OARAnL,OAAOC,KAAKvC,EAAQ0N,SACjBC,OAAO3H,GAAOA,EAAI3F,WAAW,OAC7BmC,QAAQwD,IACP,IAAI4H,EAAU5H,EAAIwC,QAAQ,MAAO,IACjCoF,EAAUA,EAAQC,OAAO,GAAG7K,cAAgB4K,EAAQxD,MAAM,EAAGwD,EAAQ5F,QACrEyF,EAAWG,GAAWb,EAAc/M,EAAQ0N,QAAQ1H,MAGjDyH,GAGTK,iBAAgB,CAAC9N,EAASgG,IACjB+G,EAAc/M,EAAQE,aAAc,WAAU+M,EAAiBjH,KAGxE+H,OAAO/N,GACL,MAAMgO,EAAOhO,EAAQiO,wBAErB,MAAO,CACLC,IAAKF,EAAKE,IAAMrO,SAASiF,KAAKqJ,UAC9BC,KAAMJ,EAAKI,KAAOvO,SAASiF,KAAKuJ,aAIpCC,SAAStO,IACA,CACLkO,IAAKlO,EAAQuO,UACbH,KAAMpO,EAAQwO,cC3DdC,EAAiB,CACrBC,KAAI,CAACzO,EAAUD,EAAUH,SAASuE,kBACzB,GAAGuK,UAAUC,QAAQC,UAAU9F,iBAAiBjG,KAAK9C,EAASC,IAGvE6O,QAAO,CAAC7O,EAAUD,EAAUH,SAASuE,kBAC5BwK,QAAQC,UAAUpO,cAAcqC,KAAK9C,EAASC,GAGvD8O,SAAQ,CAAC/O,EAASC,IACT,GAAG0O,UAAU3O,EAAQ+O,UACzBpB,OAAOqB,GAASA,EAAMC,QAAQhP,IAGnCiP,QAAQlP,EAASC,GACf,MAAMiP,EAAU,GAEhB,IAAIC,EAAWnP,EAAQuD,WAEvB,KAAO4L,GAAYA,EAAS1N,WAAaoC,KAAKC,cArBhC,IAqBgDqL,EAAS1N,UACjE0N,EAASF,QAAQhP,IACnBiP,EAAQE,KAAKD,GAGfA,EAAWA,EAAS5L,WAGtB,OAAO2L,GAGTG,KAAKrP,EAASC,GACZ,IAAIqP,EAAWtP,EAAQuP,uBAEvB,KAAOD,GAAU,CACf,GAAIA,EAASL,QAAQhP,GACnB,MAAO,CAACqP,GAGVA,EAAWA,EAASC,uBAGtB,MAAO,IAGTC,KAAKxP,EAASC,GACZ,IAAIuP,EAAOxP,EAAQyP,mBAEnB,KAAOD,GAAM,CACX,GAAIA,EAAKP,QAAQhP,GACf,MAAO,CAACuP,GAGVA,EAAOA,EAAKC,mBAGd,MAAO,KC9BLC,EAAU,CACdC,SAAU,IACVC,UAAU,EACVC,OAAO,EACPC,MAAO,QACPC,MAAM,EACNC,OAAO,GAGHC,EAAc,CAClBN,SAAU,mBACVC,SAAU,UACVC,MAAO,mBACPC,MAAO,mBACPC,KAAM,UACNC,MAAO,WAGHE,EAAa,OACbC,EAAa,OACbC,EAAiB,OACjBC,EAAkB,QA2CxB,MAAMC,UAAiB/E,EACrBC,YAAYxL,EAASoC,GACnBmO,MAAMvQ,GAENiJ,KAAKuH,OAAS,KACdvH,KAAKwH,UAAY,KACjBxH,KAAKyH,eAAiB,KACtBzH,KAAK0H,WAAY,EACjB1H,KAAK2H,YAAa,EAClB3H,KAAK4H,aAAe,KACpB5H,KAAK6H,YAAc,EACnB7H,KAAK8H,YAAc,EAEnB9H,KAAK+H,QAAU/H,KAAKgI,WAAW7O,GAC/B6G,KAAKiI,mBAAqBzC,EAAeK,QA3BjB,uBA2B8C7F,KAAKwC,UAC3ExC,KAAKkI,gBAAkB,iBAAkBtR,SAASuE,iBAAmBgN,UAAUC,eAAiB,EAChGpI,KAAKqI,cAAgB5H,QAAQ5I,OAAOyQ,cAEpCtI,KAAKuI,qBAKW9B,qBAChB,OAAOA,EAGUhE,sBACjB,MArGa,cA0Gf8D,OACOvG,KAAK2H,YACR3H,KAAKwI,OAAOvB,GAIhBwB,mBAGO7R,SAAS8R,QAAUtO,EAAU4F,KAAKwC,WACrCxC,KAAKuG,OAITH,OACOpG,KAAK2H,YACR3H,KAAKwI,OAAOtB,GAIhBL,MAAM7H,GACCA,IACHgB,KAAK0H,WAAY,GAGflC,EAAeK,QAxEI,2CAwEwB7F,KAAKwC,YAClDrK,EAAqB6H,KAAKwC,UAC1BxC,KAAK2I,OAAM,IAGbC,cAAc5I,KAAKwH,WACnBxH,KAAKwH,UAAY,KAGnBmB,MAAM3J,GACCA,IACHgB,KAAK0H,WAAY,GAGf1H,KAAKwH,YACPoB,cAAc5I,KAAKwH,WACnBxH,KAAKwH,UAAY,MAGfxH,KAAK+H,SAAW/H,KAAK+H,QAAQrB,WAAa1G,KAAK0H,YACjD1H,KAAK6I,kBAEL7I,KAAKwH,UAAYsB,aACdlS,SAASmS,gBAAkB/I,KAAKyI,gBAAkBzI,KAAKuG,MAAMyC,KAAKhJ,MACnEA,KAAK+H,QAAQrB,WAKnBuC,GAAGC,GACDlJ,KAAKyH,eAAiBjC,EAAeK,QAzGZ,wBAyG0C7F,KAAKwC,UACxE,MAAM2G,EAAcnJ,KAAKoJ,cAAcpJ,KAAKyH,gBAE5C,GAAIyB,EAAQlJ,KAAKuH,OAAOxI,OAAS,GAAKmK,EAAQ,EAC5C,OAGF,GAAIlJ,KAAK2H,WAEP,YADAzH,EAAaS,IAAIX,KAAKwC,SAxIR,mBAwI8B,IAAMxC,KAAKiJ,GAAGC,IAI5D,GAAIC,IAAgBD,EAGlB,OAFAlJ,KAAK6G,aACL7G,KAAK2I,QAIP,MAAMU,EAAQH,EAAQC,EACpBlC,EACAC,EAEFlH,KAAKwI,OAAOa,EAAOrJ,KAAKuH,OAAO2B,IAGjCxG,UACExC,EAAaC,IAAIH,KAAKwC,SA1LP,gBA4LfxC,KAAKuH,OAAS,KACdvH,KAAK+H,QAAU,KACf/H,KAAKwH,UAAY,KACjBxH,KAAK0H,UAAY,KACjB1H,KAAK2H,WAAa,KAClB3H,KAAKyH,eAAiB,KACtBzH,KAAKiI,mBAAqB,KAE1BX,MAAM5E,UAKRsF,WAAW7O,GAMT,OALAA,EAAS,IACJsN,KACAtN,GAELF,EAhNS,WAgNaE,EAAQ6N,GACvB7N,EAGTmQ,eACE,MAAMC,EAAY9S,KAAK+S,IAAIxJ,KAAK8H,aAEhC,GAAIyB,GA/MgB,GAgNlB,OAGF,MAAME,EAAYF,EAAYvJ,KAAK8H,YAEnC9H,KAAK8H,YAAc,EAEd2B,GAILzJ,KAAKwI,OAAOiB,EAAY,EAAIrC,EAAkBD,GAGhDoB,qBACMvI,KAAK+H,QAAQpB,UACfzG,EAAaQ,GAAGV,KAAKwC,SArMJ,sBAqM6BxD,GAASgB,KAAK0J,SAAS1K,IAG5C,UAAvBgB,KAAK+H,QAAQlB,QACf3G,EAAaQ,GAAGV,KAAKwC,SAxMD,yBAwM6BxD,GAASgB,KAAK6G,MAAM7H,IACrEkB,EAAaQ,GAAGV,KAAKwC,SAxMD,yBAwM6BxD,GAASgB,KAAK2I,MAAM3J,KAGnEgB,KAAK+H,QAAQhB,OAAS/G,KAAKkI,iBAC7BlI,KAAK2J,0BAITA,0BACE,MAAMC,EAAQ5K,KACRgB,KAAKqI,eApLU,QAoLQrJ,EAAM6K,aArLZ,UAqLgD7K,EAAM6K,YAE/D7J,KAAKqI,gBACfrI,KAAK6H,YAAc7I,EAAM8K,QAAQ,GAAGC,SAFpC/J,KAAK6H,YAAc7I,EAAM+K,SAMvBC,EAAOhL,IAEXgB,KAAK8H,YAAc9I,EAAM8K,SAAW9K,EAAM8K,QAAQ/K,OAAS,EACzD,EACAC,EAAM8K,QAAQ,GAAGC,QAAU/J,KAAK6H,aAG9BoC,EAAMjL,KACNgB,KAAKqI,eAnMU,QAmMQrJ,EAAM6K,aApMZ,UAoMgD7K,EAAM6K,cACzE7J,KAAK8H,YAAc9I,EAAM+K,QAAU/J,KAAK6H,aAG1C7H,KAAKsJ,eACsB,UAAvBtJ,KAAK+H,QAAQlB,QASf7G,KAAK6G,QACD7G,KAAK4H,cACPsC,aAAalK,KAAK4H,cAGpB5H,KAAK4H,aAAe5O,WAAWgG,GAASgB,KAAK2I,MAAM3J,GAlR5B,IAkR6DgB,KAAK+H,QAAQrB,YAIrGlB,EAAeC,KAlOO,qBAkOiBzF,KAAKwC,UAAUjJ,QAAQ4Q,IAC5DjK,EAAaQ,GAAGyJ,EAnPI,wBAmPuBC,GAAKA,EAAE/H,oBAGhDrC,KAAKqI,eACPnI,EAAaQ,GAAGV,KAAKwC,SAzPA,0BAyP6BxD,GAAS4K,EAAM5K,IACjEkB,EAAaQ,GAAGV,KAAKwC,SAzPF,wBAyP6BxD,GAASiL,EAAIjL,IAE7DgB,KAAKwC,SAAS1H,UAAUuP,IA/OG,mBAiP3BnK,EAAaQ,GAAGV,KAAKwC,SAjQD,yBAiQ6BxD,GAAS4K,EAAM5K,IAChEkB,EAAaQ,GAAGV,KAAKwC,SAjQF,wBAiQ6BxD,GAASgL,EAAKhL,IAC9DkB,EAAaQ,GAAGV,KAAKwC,SAjQH,uBAiQ6BxD,GAASiL,EAAIjL,KAIhE0K,SAAS1K,GACH,kBAAkB/E,KAAK+E,EAAMe,OAAOuK,WAzSrB,cA6SftL,EAAMjC,KACRiC,EAAMqD,iBACNrC,KAAKwI,OAAOrB,IA9SM,eA+STnI,EAAMjC,MACfiC,EAAMqD,iBACNrC,KAAKwI,OAAOpB,KAIhBgC,cAAcrS,GAKZ,OAJAiJ,KAAKuH,OAASxQ,GAAWA,EAAQuD,WAC/BkL,EAAeC,KAnQC,iBAmQmB1O,EAAQuD,YAC3C,GAEK0F,KAAKuH,OAAOgD,QAAQxT,GAG7ByT,gBAAgBnB,EAAOoB,GACrB,MAAMC,EAASrB,IAAUpC,EACnB0D,EAAStB,IAAUnC,EACnBiC,EAAcnJ,KAAKoJ,cAAcqB,GACjCG,EAAgB5K,KAAKuH,OAAOxI,OAAS,EAG3C,IAFuB4L,GAA0B,IAAhBxB,GAAuBuB,GAAUvB,IAAgByB,KAE5D5K,KAAK+H,QAAQjB,KACjC,OAAO2D,EAGT,MACMI,GAAa1B,GADLwB,GAAU,EAAI,IACc3K,KAAKuH,OAAOxI,OAEtD,OAAsB,IAAf8L,EACL7K,KAAKuH,OAAOvH,KAAKuH,OAAOxI,OAAS,GACjCiB,KAAKuH,OAAOsD,GAGhBC,mBAAmBC,EAAeC,GAChC,MAAMC,EAAcjL,KAAKoJ,cAAc2B,GACjCG,EAAYlL,KAAKoJ,cAAc5D,EAAeK,QA/R3B,wBA+RyD7F,KAAKwC,WAEvF,OAAOtC,EAAamB,QAAQrB,KAAKwC,SAzThB,oBAyTuC,CACtDuI,cAAAA,EACAtB,UAAWuB,EACXxN,KAAM0N,EACNjC,GAAIgC,IAIRE,2BAA2BpU,GACzB,GAAIiJ,KAAKiI,mBAAoB,CAC3B,MAAMmD,EAAkB5F,EAAeK,QA5SrB,UA4S8C7F,KAAKiI,oBAErEmD,EAAgBtQ,UAAU2C,OAtTN,UAuTpB2N,EAAgB9G,gBAAgB,gBAEhC,MAAM+G,EAAa7F,EAAeC,KA3Sb,mBA2SsCzF,KAAKiI,oBAEhE,IAAK,IAAIpJ,EAAI,EAAGA,EAAIwM,EAAWtM,OAAQF,IACrC,GAAI7G,OAAOsT,SAASD,EAAWxM,GAAG5H,aAAa,oBAAqB,MAAQ+I,KAAKoJ,cAAcrS,GAAU,CACvGsU,EAAWxM,GAAG/D,UAAUuP,IA7TR,UA8ThBgB,EAAWxM,GAAGgF,aAAa,eAAgB,QAC3C,QAMRgF,kBACE,MAAM9R,EAAUiJ,KAAKyH,gBAAkBjC,EAAeK,QA7T7B,wBA6T2D7F,KAAKwC,UAEzF,IAAKzL,EACH,OAGF,MAAMwU,EAAkBvT,OAAOsT,SAASvU,EAAQE,aAAa,oBAAqB,IAE9EsU,GACFvL,KAAK+H,QAAQyD,gBAAkBxL,KAAK+H,QAAQyD,iBAAmBxL,KAAK+H,QAAQrB,SAC5E1G,KAAK+H,QAAQrB,SAAW6E,GAExBvL,KAAK+H,QAAQrB,SAAW1G,KAAK+H,QAAQyD,iBAAmBxL,KAAK+H,QAAQrB,SAIzE8B,OAAOiD,EAAkB1U,GACvB,MAAMsS,EAAQrJ,KAAK0L,kBAAkBD,GAC/BhB,EAAgBjF,EAAeK,QA/UZ,wBA+U0C7F,KAAKwC,UAClEmJ,EAAqB3L,KAAKoJ,cAAcqB,GACxCmB,EAAc7U,GAAWiJ,KAAKwK,gBAAgBnB,EAAOoB,GAErDoB,EAAmB7L,KAAKoJ,cAAcwC,GACtCE,EAAYrL,QAAQT,KAAKwH,WAEzBkD,EAASrB,IAAUpC,EACnB8E,EAAuBrB,EA7VR,sBADF,oBA+VbsB,EAAiBtB,EA7VH,qBACA,qBA6VdM,EAAqBhL,KAAKiM,kBAAkB5C,GAElD,GAAIuC,GAAeA,EAAY9Q,UAAUC,SApWnB,UAqWpBiF,KAAK2H,YAAa,OAKpB,IADmB3H,KAAK8K,mBAAmBc,EAAaZ,GACzCrJ,kBAIV8I,GAAkBmB,EAAvB,CAcA,GATA5L,KAAK2H,YAAa,EAEdmE,GACF9L,KAAK6G,QAGP7G,KAAKmL,2BAA2BS,GAChC5L,KAAKyH,eAAiBmE,EAElB5L,KAAKwC,SAAS1H,UAAUC,SA3XP,SA2XmC,CACtD6Q,EAAY9Q,UAAUuP,IAAI2B,GAE1BvQ,EAAOmQ,GAEPnB,EAAc3P,UAAUuP,IAAI0B,GAC5BH,EAAY9Q,UAAUuP,IAAI0B,GAE1B,MAAMpU,EAAqBD,EAAiC+S,GAE5DvK,EAAaS,IAAI8J,EAAe,gBAAiB,KAC/CmB,EAAY9Q,UAAU2C,OAAOsO,EAAsBC,GACnDJ,EAAY9Q,UAAUuP,IAxYJ,UA0YlBI,EAAc3P,UAAU2C,OA1YN,SA0YgCuO,EAAgBD,GAElE/L,KAAK2H,YAAa,EAElB3O,WAAW,KACTkH,EAAamB,QAAQrB,KAAKwC,SA7ZhB,mBA6ZsC,CAC9CuI,cAAea,EACfnC,UAAWuB,EACXxN,KAAMmO,EACN1C,GAAI4C,KAEL,KAGLpT,EAAqBgS,EAAe9S,QAEpC8S,EAAc3P,UAAU2C,OA1ZJ,UA2ZpBmO,EAAY9Q,UAAUuP,IA3ZF,UA6ZpBrK,KAAK2H,YAAa,EAClBzH,EAAamB,QAAQrB,KAAKwC,SA5aZ,mBA4akC,CAC9CuI,cAAea,EACfnC,UAAWuB,EACXxN,KAAMmO,EACN1C,GAAI4C,IAIJC,GACF9L,KAAK2I,SAIT+C,kBAAkBjC,GAChB,MAAK,CAACrC,EAAiBD,GAAgBhQ,SAASsS,GAI5C3N,IACK2N,IAAcrC,EAAkBF,EAAaD,EAG/CwC,IAAcrC,EAAkBH,EAAaC,EAP3CuC,EAUXwC,kBAAkB5C,GAChB,MAAK,CAACpC,EAAYC,GAAY/P,SAASkS,GAInCvN,IACKuN,IAAUpC,EAAaE,EAAiBC,EAG1CiC,IAAUpC,EAAaG,EAAkBD,EAPvCkC,EAYa1G,yBAAC5L,EAASoC,GAChC,IAAIqK,EAAO3G,EAAKM,IAAIpG,EArfP,eAsfTgR,EAAU,IACTtB,KACAtC,EAAYI,kBAAkBxN,IAGb,iBAAXoC,IACT4O,EAAU,IACLA,KACA5O,IAIP,MAAM+S,EAA2B,iBAAX/S,EAAsBA,EAAS4O,EAAQnB,MAM7D,GAJKpD,IACHA,EAAO,IAAI6D,EAAStQ,EAASgR,IAGT,iBAAX5O,EACTqK,EAAKyF,GAAG9P,QACH,GAAsB,iBAAX+S,EAAqB,CACrC,QAA4B,IAAjB1I,EAAK0I,GACd,MAAM,IAAIhS,UAAW,oBAAmBgS,MAG1C1I,EAAK0I,UACInE,EAAQrB,UAAYqB,EAAQoE,OACrC3I,EAAKqD,QACLrD,EAAKmF,SAIahG,uBAACxJ,GACrB,OAAO6G,KAAKuD,MAAK,WACf8D,EAAS+E,kBAAkBpM,KAAM7G,MAIXwJ,2BAAC3D,GACzB,MAAMe,EAAStI,EAAuBuI,MAEtC,IAAKD,IAAWA,EAAOjF,UAAUC,SAjfT,YAkftB,OAGF,MAAM5B,EAAS,IACVgL,EAAYI,kBAAkBxE,MAC9BoE,EAAYI,kBAAkBvE,OAE7BqM,EAAarM,KAAK/I,aAAa,oBAEjCoV,IACFlT,EAAOuN,UAAW,GAGpBW,EAAS+E,kBAAkBrM,EAAQ5G,GAE/BkT,GACFxP,EAAKM,IAAI4C,EAhjBE,eAgjBgBkJ,GAAGoD,GAGhCrN,EAAMqD,kBAUVnC,EAAaQ,GAAG9J,SAjhBc,6BAkBF,sCA+fyCyQ,EAASiF,qBAE9EpM,EAAaQ,GAAG7I,OAphBa,4BAohBgB,KAC3C,MAAM0U,EAAY/G,EAAeC,KAjgBR,6BAmgBzB,IAAK,IAAI5G,EAAI,EAAGC,EAAMyN,EAAUxN,OAAQF,EAAIC,EAAKD,IAC/CwI,EAAS+E,kBAAkBG,EAAU1N,GAAIhC,EAAKM,IAAIoP,EAAU1N,GAnkB/C,kBA8kBjB7C,EA/kBa,WA+kBYqL,GChlBzB,MAKMZ,EAAU,CACd7C,QAAQ,EACR4I,OAAQ,IAGJxF,EAAc,CAClBpD,OAAQ,UACR4I,OAAQ,oBA0BV,MAAMC,UAAiBnK,EACrBC,YAAYxL,EAASoC,GACnBmO,MAAMvQ,GAENiJ,KAAK0M,kBAAmB,EACxB1M,KAAK+H,QAAU/H,KAAKgI,WAAW7O,GAC/B6G,KAAK2M,cAAgBnH,EAAeC,KACjC,sCAAiCzF,KAAKwC,SAASoK,qDACJ5M,KAAKwC,SAASoK,QAG5D,MAAMC,EAAarH,EAAeC,KAnBT,+BAqBzB,IAAK,IAAI5G,EAAI,EAAGC,EAAM+N,EAAW9N,OAAQF,EAAIC,EAAKD,IAAK,CACrD,MAAMiO,EAAOD,EAAWhO,GAClB7H,EAAWO,EAAuBuV,GAClCC,EAAgBvH,EAAeC,KAAKzO,GACvC0N,OAAOsI,GAAaA,IAAchN,KAAKwC,UAEzB,OAAbxL,GAAqB+V,EAAchO,SACrCiB,KAAKiN,UAAYjW,EACjBgJ,KAAK2M,cAAcxG,KAAK2G,IAI5B9M,KAAKkN,QAAUlN,KAAK+H,QAAQyE,OAASxM,KAAKmN,aAAe,KAEpDnN,KAAK+H,QAAQyE,QAChBxM,KAAKoN,0BAA0BpN,KAAKwC,SAAUxC,KAAK2M,eAGjD3M,KAAK+H,QAAQnE,QACf5D,KAAK4D,SAMS6C,qBAChB,OAAOA,EAGUhE,sBACjB,MAhFa,cAqFfmB,SACM5D,KAAKwC,SAAS1H,UAAUC,SAlER,QAmElBiF,KAAKqN,OAELrN,KAAKsN,OAITA,OACE,GAAItN,KAAK0M,kBAAoB1M,KAAKwC,SAAS1H,UAAUC,SA1EjC,QA2ElB,OAGF,IAAIwS,EACAC,EAEAxN,KAAKkN,UACPK,EAAU/H,EAAeC,KA1EN,qBA0E6BzF,KAAKkN,SAClDxI,OAAOoI,GAC6B,iBAAxB9M,KAAK+H,QAAQyE,OACfM,EAAK7V,aAAa,oBAAsB+I,KAAK+H,QAAQyE,OAGvDM,EAAKhS,UAAUC,SAvFJ,aA0FC,IAAnBwS,EAAQxO,SACVwO,EAAU,OAId,MAAME,EAAYjI,EAAeK,QAAQ7F,KAAKiN,WAC9C,GAAIM,EAAS,CACX,MAAMG,EAAiBH,EAAQ9H,KAAKqH,GAAQW,IAAcX,GAG1D,GAFAU,EAAcE,EAAiB7Q,EAAKM,IAAIuQ,EAvH7B,eAuHyD,KAEhEF,GAAeA,EAAYd,iBAC7B,OAKJ,GADmBxM,EAAamB,QAAQrB,KAAKwC,SAhH7B,oBAiHDb,iBACb,OAGE4L,GACFA,EAAQhU,QAAQoU,IACVF,IAAcE,GAChBlB,EAASmB,kBAAkBD,EAAY,QAGpCH,GACH3Q,EAAKC,IAAI6Q,EA1IF,cA0IwB,QAKrC,MAAME,EAAY7N,KAAK8N,gBAEvB9N,KAAKwC,SAAS1H,UAAU2C,OA5HA,YA6HxBuC,KAAKwC,SAAS1H,UAAUuP,IA5HE,cA8H1BrK,KAAKwC,SAASnI,MAAMwT,GAAa,EAE7B7N,KAAK2M,cAAc5N,QACrBiB,KAAK2M,cAAcpT,QAAQxC,IACzBA,EAAQ+D,UAAU2C,OAjIG,aAkIrB1G,EAAQ8M,aAAa,iBAAiB,KAI1C7D,KAAK+N,kBAAiB,GAEtB,MAYMC,EAAc,UADSH,EAAU,GAAG1T,cAAgB0T,EAAU1M,MAAM,IAEpExJ,EAAqBD,EAAiCsI,KAAKwC,UAEjEtC,EAAaS,IAAIX,KAAKwC,SAAU,gBAff,KACfxC,KAAKwC,SAAS1H,UAAU2C,OA1IA,cA2IxBuC,KAAKwC,SAAS1H,UAAUuP,IA5IF,WADJ,QA+IlBrK,KAAKwC,SAASnI,MAAMwT,GAAa,GAEjC7N,KAAK+N,kBAAiB,GAEtB7N,EAAamB,QAAQrB,KAAKwC,SAxJX,uBAiKjB/J,EAAqBuH,KAAKwC,SAAU7K,GACpCqI,KAAKwC,SAASnI,MAAMwT,GAAgB7N,KAAKwC,SAASwL,GAAhB,KAGpCX,OACE,GAAIrN,KAAK0M,mBAAqB1M,KAAKwC,SAAS1H,UAAUC,SAjKlC,QAkKlB,OAIF,GADmBmF,EAAamB,QAAQrB,KAAKwC,SAzK7B,oBA0KDb,iBACb,OAGF,MAAMkM,EAAY7N,KAAK8N,gBAEvB9N,KAAKwC,SAASnI,MAAMwT,GAAgB7N,KAAKwC,SAASwC,wBAAwB6I,GAAxC,KAElCpS,EAAOuE,KAAKwC,UAEZxC,KAAKwC,SAAS1H,UAAUuP,IA9KE,cA+K1BrK,KAAKwC,SAAS1H,UAAU2C,OAhLA,WADJ,QAmLpB,MAAMwQ,EAAqBjO,KAAK2M,cAAc5N,OAC9C,GAAIkP,EAAqB,EACvB,IAAK,IAAIpP,EAAI,EAAGA,EAAIoP,EAAoBpP,IAAK,CAC3C,MAAMwC,EAAUrB,KAAK2M,cAAc9N,GAC7BiO,EAAOrV,EAAuB4J,GAEhCyL,IAASA,EAAKhS,UAAUC,SAzLZ,UA0LdsG,EAAQvG,UAAUuP,IAvLC,aAwLnBhJ,EAAQwC,aAAa,iBAAiB,IAK5C7D,KAAK+N,kBAAiB,GAStB/N,KAAKwC,SAASnI,MAAMwT,GAAa,GACjC,MAAMlW,EAAqBD,EAAiCsI,KAAKwC,UAEjEtC,EAAaS,IAAIX,KAAKwC,SAAU,gBAVf,KACfxC,KAAK+N,kBAAiB,GACtB/N,KAAKwC,SAAS1H,UAAU2C,OAlMA,cAmMxBuC,KAAKwC,SAAS1H,UAAUuP,IApMF,YAqMtBnK,EAAamB,QAAQrB,KAAKwC,SAzMV,wBAgNlB/J,EAAqBuH,KAAKwC,SAAU7K,GAGtCoW,iBAAiBG,GACflO,KAAK0M,iBAAmBwB,EAG1BxL,UACE4E,MAAM5E,UACN1C,KAAK+H,QAAU,KACf/H,KAAKkN,QAAU,KACflN,KAAK2M,cAAgB,KACrB3M,KAAK0M,iBAAmB,KAK1B1E,WAAW7O,GAOT,OANAA,EAAS,IACJsN,KACAtN,IAEEyK,OAASnD,QAAQtH,EAAOyK,QAC/B3K,EAzPS,WAyPaE,EAAQ6N,GACvB7N,EAGT2U,gBACE,OAAO9N,KAAKwC,SAAS1H,UAAUC,SApOrB,SAAA,QACC,SAsOboS,aACE,IAAIX,OAAEA,GAAWxM,KAAK+H,QAElBzP,EAAUkU,QAEiB,IAAlBA,EAAO2B,aAA+C,IAAd3B,EAAO,KACxDA,EAASA,EAAO,IAGlBA,EAAShH,EAAeK,QAAQ2G,GAGlC,MAAMxV,EAAY,+CAA0CwV,MAY5D,OAVAhH,EAAeC,KAAKzO,EAAUwV,GAC3BjT,QAAQxC,IACP,MAAMqX,EAAW3W,EAAuBV,GAExCiJ,KAAKoN,0BACHgB,EACA,CAACrX,MAIAyV,EAGTY,0BAA0BrW,EAASsX,GACjC,IAAKtX,IAAYsX,EAAatP,OAC5B,OAGF,MAAMuP,EAASvX,EAAQ+D,UAAUC,SA5Qb,QA8QpBsT,EAAa9U,QAAQuT,IACfwB,EACFxB,EAAKhS,UAAU2C,OA7QM,aA+QrBqP,EAAKhS,UAAUuP,IA/QM,aAkRvByC,EAAKjJ,aAAa,gBAAiByK,KAMf3L,yBAAC5L,EAASoC,GAChC,IAAIqK,EAAO3G,EAAKM,IAAIpG,EAhTP,eAiTb,MAAMgR,EAAU,IACXtB,KACAtC,EAAYI,kBAAkBxN,MACX,iBAAXoC,GAAuBA,EAASA,EAAS,IAWtD,IARKqK,GAAQuE,EAAQnE,QAA4B,iBAAXzK,GAAuB,YAAYc,KAAKd,KAC5E4O,EAAQnE,QAAS,GAGdJ,IACHA,EAAO,IAAIiJ,EAAS1V,EAASgR,IAGT,iBAAX5O,EAAqB,CAC9B,QAA4B,IAAjBqK,EAAKrK,GACd,MAAM,IAAIe,UAAW,oBAAmBf,MAG1CqK,EAAKrK,MAIawJ,uBAACxJ,GACrB,OAAO6G,KAAKuD,MAAK,WACfkJ,EAASmB,kBAAkB5N,KAAM7G,OAWvC+G,EAAaQ,GAAG9J,SAnUc,6BAWD,+BAwTyC,SAAUoI,IAEjD,MAAzBA,EAAMe,OAAOuK,SAAoBtL,EAAMiB,gBAAmD,MAAjCjB,EAAMiB,eAAeqK,UAChFtL,EAAMqD,iBAGR,MAAMkM,EAAcpK,EAAYI,kBAAkBvE,MAC5ChJ,EAAWO,EAAuByI,MACfwF,EAAeC,KAAKzO,GAE5BuC,QAAQxC,IACvB,MAAMyM,EAAO3G,EAAKM,IAAIpG,EAhWT,eAiWb,IAAIoC,EACAqK,GAEmB,OAAjBA,EAAK0J,SAAkD,iBAAvBqB,EAAY/B,SAC9ChJ,EAAKuE,QAAQyE,OAAS+B,EAAY/B,OAClChJ,EAAK0J,QAAU1J,EAAK2J,cAGtBhU,EAAS,UAETA,EAASoV,EAGX9B,EAASmB,kBAAkB7W,EAASoC,QAWxC6C,EA1Xa,WA0XYyQ,GCzXzB,MAYM+B,EAAiB,IAAIxU,OAAQ,4BAuB7ByU,GAAgB3S,IAAU,UAAY,YACtC4S,GAAmB5S,IAAU,YAAc,UAC3C6S,GAAmB7S,IAAU,aAAe,eAC5C8S,GAAsB9S,IAAU,eAAiB,aACjD+S,GAAkB/S,IAAU,aAAe,cAC3CgT,GAAiBhT,IAAU,cAAgB,aAE3C2K,GAAU,CACd3B,OAAQ,CAAC,EAAG,GACZiK,SAAU,kBACVC,UAAW,SACXvU,QAAS,UACTwU,aAAc,MAGVjI,GAAc,CAClBlC,OAAQ,0BACRiK,SAAU,mBACVC,UAAW,0BACXvU,QAAS,SACTwU,aAAc,0BAShB,MAAMC,WAAiB5M,EACrBC,YAAYxL,EAASoC,GACnBmO,MAAMvQ,GAENiJ,KAAKmP,QAAU,KACfnP,KAAK+H,QAAU/H,KAAKgI,WAAW7O,GAC/B6G,KAAKoP,MAAQpP,KAAKqP,kBAClBrP,KAAKsP,UAAYtP,KAAKuP,gBAEtBvP,KAAKuI,qBAKW9B,qBAChB,OAAOA,GAGaO,yBACpB,OAAOA,GAGUvE,sBACjB,MAtFa,cA2FfmB,SACE,GAAI5D,KAAKwC,SAASxH,UAAYgF,KAAKwC,SAAS1H,UAAUC,SAtE9B,YAuEtB,OAGF,MAAMyU,EAAWxP,KAAKwC,SAAS1H,UAAUC,SAzErB,QA2EpBmU,GAASO,aAELD,GAIJxP,KAAKsN,OAGPA,OACE,GAAItN,KAAKwC,SAASxH,UAAYgF,KAAKwC,SAAS1H,UAAUC,SAtF9B,aAsF+DiF,KAAKoP,MAAMtU,UAAUC,SArFxF,QAsFlB,OAGF,MAAMyR,EAAS0C,GAASQ,qBAAqB1P,KAAKwC,UAC5CuI,EAAgB,CACpBA,cAAe/K,KAAKwC,UAKtB,IAFkBtC,EAAamB,QAAQrB,KAAKwC,SAtG5B,mBAsGkDuI,GAEpDpJ,iBAAd,CAKA,GAAI3B,KAAKsP,UACPnL,EAAYC,iBAAiBpE,KAAKoP,MAAO,SAAU,YAC9C,CACL,QAAsB,IAAXO,EACT,MAAM,IAAIzV,UAAU,gEAGtB,IAAI0V,EAAmB5P,KAAKwC,SAEG,WAA3BxC,KAAK+H,QAAQiH,UACfY,EAAmBpD,EACVlU,EAAU0H,KAAK+H,QAAQiH,YAChCY,EAAmB5P,KAAK+H,QAAQiH,eAGa,IAAlChP,KAAK+H,QAAQiH,UAAUb,SAChCyB,EAAmB5P,KAAK+H,QAAQiH,UAAU,KAED,iBAA3BhP,KAAK+H,QAAQiH,YAC7BY,EAAmB5P,KAAK+H,QAAQiH,WAGlC,MAAMC,EAAejP,KAAK6P,mBACpBC,EAAkBb,EAAac,UAAUtK,KAAKuK,GAA8B,gBAAlBA,EAAS/T,OAA+C,IAArB+T,EAASC,SAE5GjQ,KAAKmP,QAAUQ,EAAOO,aAAaN,EAAkB5P,KAAKoP,MAAOH,GAE7Da,GACF3L,EAAYC,iBAAiBpE,KAAKoP,MAAO,SAAU,UAQnD,iBAAkBxY,SAASuE,kBAC5BqR,EAAOpJ,QAlIc,gBAmItB,GAAGsC,UAAU9O,SAASiF,KAAKiK,UACxBvM,QAAQuT,GAAQ5M,EAAaQ,GAAGoM,EAAM,YAAa,MVAzC,gBUGf9M,KAAKwC,SAAS2N,QACdnQ,KAAKwC,SAASqB,aAAa,iBAAiB,GAE5C7D,KAAKoP,MAAMtU,UAAU8I,OAlJD,QAmJpB5D,KAAKwC,SAAS1H,UAAU8I,OAnJJ,QAoJpB1D,EAAamB,QAAQrB,KAAKwC,SA3JT,oBA2JgCuI,IAGnDsC,OACE,GAAIrN,KAAKwC,SAASxH,UAAYgF,KAAKwC,SAAS1H,UAAUC,SAzJ9B,cAyJgEiF,KAAKoP,MAAMtU,UAAUC,SAxJzF,QAyJlB,OAGF,MAAMgQ,EAAgB,CACpBA,cAAe/K,KAAKwC,UAGJtC,EAAamB,QAAQrB,KAAKwC,SA1K5B,mBA0KkDuI,GAEpDpJ,mBAIV3B,KAAKmP,SACPnP,KAAKmP,QAAQiB,UAGfpQ,KAAKoP,MAAMtU,UAAU8I,OA1KD,QA2KpB5D,KAAKwC,SAAS1H,UAAU8I,OA3KJ,QA4KpBO,EAAYE,oBAAoBrE,KAAKoP,MAAO,UAC5ClP,EAAamB,QAAQrB,KAAKwC,SAtLR,qBAsLgCuI,IAGpDrI,UACExC,EAAaC,IAAIH,KAAKwC,SAvMP,gBAwMfxC,KAAKoP,MAAQ,KAETpP,KAAKmP,UACPnP,KAAKmP,QAAQiB,UACbpQ,KAAKmP,QAAU,MAGjB7H,MAAM5E,UAGR2N,SACErQ,KAAKsP,UAAYtP,KAAKuP,gBAClBvP,KAAKmP,SACPnP,KAAKmP,QAAQkB,SAMjB9H,qBACErI,EAAaQ,GAAGV,KAAKwC,SA5MJ,oBA4M2BxD,IAC1CA,EAAMqD,iBACNrC,KAAK4D,WAIToE,WAAW7O,GAST,GARAA,EAAS,IACJ6G,KAAKuC,YAAYkE,WACjBtC,EAAYI,kBAAkBvE,KAAKwC,aACnCrJ,GAGLF,EA3OS,WA2OaE,EAAQ6G,KAAKuC,YAAYyE,aAEf,iBAArB7N,EAAO6V,YAA2B1W,EAAUa,EAAO6V,YACV,mBAA3C7V,EAAO6V,UAAUhK,sBAGxB,MAAM,IAAI9K,UAjPH,WAiPqBC,cAAP,kGAGvB,OAAOhB,EAGTkW,kBACE,OAAO7J,EAAee,KAAKvG,KAAKwC,SAzNd,kBAyNuC,GAG3D8N,gBACE,MAAMC,EAAiBvQ,KAAKwC,SAASlI,WAErC,GAAIiW,EAAezV,UAAUC,SApON,WAqOrB,OAAO8T,GAGT,GAAI0B,EAAezV,UAAUC,SAvOJ,aAwOvB,OAAO+T,GAIT,MAAM0B,EAAkF,QAA1E1Y,iBAAiBkI,KAAKoP,OAAOqB,iBAAiB,iBAAiBnZ,OAE7E,OAAIiZ,EAAezV,UAAUC,SAhPP,UAiPbyV,EAAQ9B,GAAmBD,GAG7B+B,EAAQ5B,GAAsBD,GAGvCY,gBACE,OAA0D,OAAnDvP,KAAKwC,SAASY,QAAS,WAGhCsN,aACE,MAAM5L,OAAEA,GAAW9E,KAAK+H,QAExB,MAAsB,iBAAXjD,EACFA,EAAOzN,MAAM,KAAKsZ,IAAI5M,GAAO/L,OAAOsT,SAASvH,EAAK,KAGrC,mBAAXe,EACF8L,GAAc9L,EAAO8L,EAAY5Q,KAAKwC,UAGxCsC,EAGT+K,mBACE,MAAMgB,EAAwB,CAC5BC,UAAW9Q,KAAKsQ,gBAChBP,UAAW,CAAC,CACV9T,KAAM,kBACN8U,QAAS,CACPhC,SAAU/O,KAAK+H,QAAQgH,WAG3B,CACE9S,KAAM,SACN8U,QAAS,CACPjM,OAAQ9E,KAAK0Q,iBAanB,MAP6B,WAAzB1Q,KAAK+H,QAAQtN,UACfoW,EAAsBd,UAAY,CAAC,CACjC9T,KAAM,cACNgU,SAAS,KAIN,IACFY,KACsC,mBAA9B7Q,KAAK+H,QAAQkH,aAA8BjP,KAAK+H,QAAQkH,aAAa4B,GAAyB7Q,KAAK+H,QAAQkH,cAMlGtM,yBAAC5L,EAASoC,GAChC,IAAIqK,EAAO3G,EAAKM,IAAIpG,EAnUP,eA0Ub,GAJKyM,IACHA,EAAO,IAAI0L,GAASnY,EAHY,iBAAXoC,EAAsBA,EAAS,OAMhC,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjBqK,EAAKrK,GACd,MAAM,IAAIe,UAAW,oBAAmBf,MAG1CqK,EAAKrK,MAIawJ,uBAACxJ,GACrB,OAAO6G,KAAKuD,MAAK,WACf2L,GAAS8B,kBAAkBhR,KAAM7G,MAIpBwJ,kBAAC3D,GAChB,GAAIA,EAAO,CACT,GAlVqB,IAkVjBA,EAAMkF,QAAiD,UAAflF,EAAMoB,MArVxC,QAqV4DpB,EAAMjC,IAC1E,OAGF,GAAI,8BAA8B9C,KAAK+E,EAAMe,OAAOuK,SAClD,OAIJ,MAAM2G,EAAUzL,EAAeC,KAvUN,+BAyUzB,IAAK,IAAI5G,EAAI,EAAGC,EAAMmS,EAAQlS,OAAQF,EAAIC,EAAKD,IAAK,CAClD,MAAMqS,EAAUrU,EAAKM,IAAI8T,EAAQpS,GAvWtB,eAwWLkM,EAAgB,CACpBA,cAAekG,EAAQpS,IAOzB,GAJIG,GAAwB,UAAfA,EAAMoB,OACjB2K,EAAcoG,WAAanS,IAGxBkS,EACH,SAGF,MAAME,EAAeF,EAAQ9B,MAC7B,GAAK6B,EAAQpS,GAAG/D,UAAUC,SA9VR,QA8VlB,CAIA,GAAIiE,EAAO,CAET,GAAI,CAACkS,EAAQ1O,UAAU6O,KAAKta,GAAWiI,EAAMsS,eAAena,SAASJ,IACnE,SAIF,GAAmB,UAAfiI,EAAMoB,MA1XF,QA0XsBpB,EAAMjC,KAAmBqU,EAAarW,SAASiE,EAAMe,QACjF,SAIcG,EAAamB,QAAQ4P,EAAQpS,GAxXjC,mBAwXiDkM,GACjDpJ,mBAMV,iBAAkB/K,SAASuE,iBAC7B,GAAGuK,UAAU9O,SAASiF,KAAKiK,UACxBvM,QAAQuT,GAAQ5M,EAAaC,IAAI2M,EAAM,YAAa,MV3O5C,gBU8ObmE,EAAQpS,GAAGgF,aAAa,gBAAiB,SAErCqN,EAAQ/B,SACV+B,EAAQ/B,QAAQiB,UAGlBgB,EAAatW,UAAU2C,OAhYL,QAiYlBwT,EAAQpS,GAAG/D,UAAU2C,OAjYH,QAkYlB0G,EAAYE,oBAAoB+M,EAAc,UAC9ClR,EAAamB,QAAQ4P,EAAQpS,GA5Yb,qBA4Y+BkM,MAIxBpI,4BAAC5L,GAC1B,OAAOU,EAAuBV,IAAYA,EAAQuD,WAGxBqI,6BAAC3D,GAQ3B,GAAI,kBAAkB/E,KAAK+E,EAAMe,OAAOuK,SAra1B,UAsaZtL,EAAMjC,KAvaO,WAuaeiC,EAAMjC,MAnajB,cAoafiC,EAAMjC,KAraO,YAqamBiC,EAAMjC,KACtCiC,EAAMe,OAAOqD,QA/YC,oBAgZfoL,EAAevU,KAAK+E,EAAMjC,KAC3B,OAMF,GAHAiC,EAAMqD,iBACNrD,EAAMuS,kBAEFvR,KAAKhF,UAAYgF,KAAKlF,UAAUC,SA/ZZ,YAgatB,OAGF,MAAMyR,EAAS0C,GAASQ,qBAAqB1P,MACvCwP,EAAWxP,KAAKlF,UAAUC,SAnaZ,QAqapB,GAxbe,WAwbXiE,EAAMjC,IAIR,OAHeiD,KAAKgG,QAhaG,+BAga6BhG,KAAOwF,EAAeY,KAAKpG,KAhaxD,+BAgaoF,IACpGmQ,aACPjB,GAASO,aAIX,IAAKD,IA5bY,YA4bCxQ,EAAMjC,KA3bL,cA2b6BiC,EAAMjC,KAGpD,YAFeiD,KAAKgG,QAvaG,+BAua6BhG,KAAOwF,EAAeY,KAAKpG,KAvaxD,+BAuaoF,IACpGwR,QAIT,IAAKhC,GApcS,UAocGxQ,EAAMjC,IAErB,YADAmS,GAASO,aAIX,MAAMgC,EAAQjM,EAAeC,KA9aF,8DA8a+B+G,GAAQ9H,OAAOtK,GAEzE,IAAKqX,EAAM1S,OACT,OAGF,IAAImK,EAAQuI,EAAMlH,QAAQvL,EAAMe,QA7cf,YAgdbf,EAAMjC,KAAwBmM,EAAQ,GACxCA,IAhdiB,cAodflK,EAAMjC,KAA0BmM,EAAQuI,EAAM1S,OAAS,GACzDmK,IAIFA,GAAmB,IAAXA,EAAe,EAAIA,EAE3BuI,EAAMvI,GAAOiH,SAUjBjQ,EAAaQ,GAAG9J,SA1dgB,+BAUH,8BAgd2CsY,GAASwC,uBACjFxR,EAAaQ,GAAG9J,SA3dgB,+BAWV,iBAgd2CsY,GAASwC,uBAC1ExR,EAAaQ,GAAG9J,SA7dc,6BA6dkBsY,GAASO,YACzDvP,EAAaQ,GAAG9J,SA5dc,6BA4dkBsY,GAASO,YACzDvP,EAAaQ,GAAG9J,SA/dc,6BAWD,+BAodyC,SAAUoI,GAC9EA,EAAMqD,iBACN6M,GAAS8B,kBAAkBhR,SAU7BhE,EA9fa,WA8fYkT,IC/fzB,MAMMzI,GAAU,CACdkL,UAAU,EACVhL,UAAU,EACVwJ,OAAO,GAGHnJ,GAAc,CAClB2K,SAAU,mBACVhL,SAAU,UACVwJ,MAAO,WAoCT,MAAMyB,WAActP,EAClBC,YAAYxL,EAASoC,GACnBmO,MAAMvQ,GAENiJ,KAAK+H,QAAU/H,KAAKgI,WAAW7O,GAC/B6G,KAAK6R,QAAUrM,EAAeK,QAlBV,gBAkBmC7F,KAAKwC,UAC5DxC,KAAK8R,UAAY,KACjB9R,KAAK+R,UAAW,EAChB/R,KAAKgS,oBAAqB,EAC1BhS,KAAKiS,sBAAuB,EAC5BjS,KAAK0M,kBAAmB,EACxB1M,KAAKkS,gBAAkB,EAKPzL,qBAChB,OAAOA,GAGUhE,sBACjB,MAvEa,WA4EfmB,OAAOmH,GACL,OAAO/K,KAAK+R,SAAW/R,KAAKqN,OAASrN,KAAKsN,KAAKvC,GAGjDuC,KAAKvC,GACH,GAAI/K,KAAK+R,UAAY/R,KAAK0M,iBACxB,OAGE1M,KAAKmS,gBACPnS,KAAK0M,kBAAmB,GAG1B,MAAM0F,EAAYlS,EAAamB,QAAQrB,KAAKwC,SArE5B,gBAqEkD,CAChEuI,cAAAA,IAGE/K,KAAK+R,UAAYK,EAAUzQ,mBAI/B3B,KAAK+R,UAAW,EAEhB/R,KAAKqS,kBACLrS,KAAKsS,gBAELtS,KAAKuS,gBAELvS,KAAKwS,kBACLxS,KAAKyS,kBAELvS,EAAaQ,GAAGV,KAAKwC,SAnFI,yBAgBC,4BAmEiDxD,GAASgB,KAAKqN,KAAKrO,IAE9FkB,EAAaQ,GAAGV,KAAK6R,QAlFQ,6BAkF0B,KACrD3R,EAAaS,IAAIX,KAAKwC,SApFG,2BAoF8BxD,IACjDA,EAAMe,SAAWC,KAAKwC,WACxBxC,KAAKiS,sBAAuB,OAKlCjS,KAAK0S,cAAc,IAAM1S,KAAK2S,aAAa5H,KAG7CsC,KAAKrO,GAKH,GAJIA,GACFA,EAAMqD,kBAGHrC,KAAK+R,UAAY/R,KAAK0M,iBACzB,OAKF,GAFkBxM,EAAamB,QAAQrB,KAAKwC,SAhH5B,iBAkHFb,iBACZ,OAGF3B,KAAK+R,UAAW,EAChB,MAAMa,EAAa5S,KAAKmS,cAgBxB,GAdIS,IACF5S,KAAK0M,kBAAmB,GAG1B1M,KAAKwS,kBACLxS,KAAKyS,kBAELvS,EAAaC,IAAIvJ,SA3HE,oBA6HnBoJ,KAAKwC,SAAS1H,UAAU2C,OAjHJ,QAmHpByC,EAAaC,IAAIH,KAAKwC,SA7HG,0BA8HzBtC,EAAaC,IAAIH,KAAK6R,QA3HO,8BA6HzBe,EAAY,CACd,MAAMjb,EAAqBD,EAAiCsI,KAAKwC,UAEjEtC,EAAaS,IAAIX,KAAKwC,SAAU,gBAAiBxD,GAASgB,KAAK6S,WAAW7T,IAC1EvG,EAAqBuH,KAAKwC,SAAU7K,QAEpCqI,KAAK6S,aAITnQ,UACE,CAAC7K,OAAQmI,KAAKwC,SAAUxC,KAAK6R,SAC1BtY,QAAQuZ,GAAe5S,EAAaC,IAAI2S,EAnK5B,cAqKfxL,MAAM5E,UAONxC,EAAaC,IAAIvJ,SAvJE,oBAyJnBoJ,KAAK+H,QAAU,KACf/H,KAAK6R,QAAU,KACf7R,KAAK8R,UAAY,KACjB9R,KAAK+R,SAAW,KAChB/R,KAAKgS,mBAAqB,KAC1BhS,KAAKiS,qBAAuB,KAC5BjS,KAAK0M,iBAAmB,KACxB1M,KAAKkS,gBAAkB,KAGzBa,eACE/S,KAAKuS,gBAKPvK,WAAW7O,GAMT,OALAA,EAAS,IACJsN,MACAtN,GAELF,EArMS,QAqMaE,EAAQ6N,IACvB7N,EAGTwZ,aAAa5H,GACX,MAAM6H,EAAa5S,KAAKmS,cAClBa,EAAYxN,EAAeK,QApKT,cAoKsC7F,KAAK6R,SAE9D7R,KAAKwC,SAASlI,YAAc0F,KAAKwC,SAASlI,WAAW9B,WAAaoC,KAAKC,cAE1EjE,SAASiF,KAAKoX,YAAYjT,KAAKwC,UAGjCxC,KAAKwC,SAASnI,MAAMI,QAAU,QAC9BuF,KAAKwC,SAAS8B,gBAAgB,eAC9BtE,KAAKwC,SAASqB,aAAa,cAAc,GACzC7D,KAAKwC,SAASqB,aAAa,OAAQ,UACnC7D,KAAKwC,SAAS0C,UAAY,EAEtB8N,IACFA,EAAU9N,UAAY,GAGpB0N,GACFnX,EAAOuE,KAAKwC,UAGdxC,KAAKwC,SAAS1H,UAAUuP,IA7LJ,QA+LhBrK,KAAK+H,QAAQoI,OACfnQ,KAAKkT,gBAGP,MAAMC,EAAqB,KACrBnT,KAAK+H,QAAQoI,OACfnQ,KAAKwC,SAAS2N,QAGhBnQ,KAAK0M,kBAAmB,EACxBxM,EAAamB,QAAQrB,KAAKwC,SAtNX,iBAsNkC,CAC/CuI,cAAAA,KAIJ,GAAI6H,EAAY,CACd,MAAMjb,EAAqBD,EAAiCsI,KAAK6R,SAEjE3R,EAAaS,IAAIX,KAAK6R,QAAS,gBAAiBsB,GAChD1a,EAAqBuH,KAAK6R,QAASla,QAEnCwb,IAIJD,gBACEhT,EAAaC,IAAIvJ,SArOE,oBAsOnBsJ,EAAaQ,GAAG9J,SAtOG,mBAsOsBoI,IACnCpI,WAAaoI,EAAMe,QACnBC,KAAKwC,WAAaxD,EAAMe,QACvBC,KAAKwC,SAASzH,SAASiE,EAAMe,SAChCC,KAAKwC,SAAS2N,UAKpBqC,kBACMxS,KAAK+R,SACP7R,EAAaQ,GAAGV,KAAKwC,SA9OI,2BA8O6BxD,IAChDgB,KAAK+H,QAAQpB,UArQN,WAqQkB3H,EAAMjC,KACjCiC,EAAMqD,iBACNrC,KAAKqN,QACKrN,KAAK+H,QAAQpB,UAxQd,WAwQ0B3H,EAAMjC,KACzCiD,KAAKoT,+BAITlT,EAAaC,IAAIH,KAAKwC,SAvPG,4BA2P7BiQ,kBACMzS,KAAK+R,SACP7R,EAAaQ,GAAG7I,OA/PA,kBA+PsB,IAAMmI,KAAKuS,iBAEjDrS,EAAaC,IAAItI,OAjQD,mBAqQpBgb,aACE7S,KAAKwC,SAASnI,MAAMI,QAAU,OAC9BuF,KAAKwC,SAASqB,aAAa,eAAe,GAC1C7D,KAAKwC,SAAS8B,gBAAgB,cAC9BtE,KAAKwC,SAAS8B,gBAAgB,QAC9BtE,KAAK0M,kBAAmB,EACxB1M,KAAK0S,cAAc,KACjB9b,SAASiF,KAAKf,UAAU2C,OAnQN,cAoQlBuC,KAAKqT,oBACLrT,KAAKsT,kBACLpT,EAAamB,QAAQrB,KAAKwC,SAnRV,qBAuRpB+Q,kBACEvT,KAAK8R,UAAUxX,WAAWgJ,YAAYtD,KAAK8R,WAC3C9R,KAAK8R,UAAY,KAGnBY,cAAcvW,GACZ,MAAMyW,EAAa5S,KAAKmS,cACxB,GAAInS,KAAK+R,UAAY/R,KAAK+H,QAAQ4J,SAAU,CAiC1C,GAhCA3R,KAAK8R,UAAYlb,SAAS4c,cAAc,OACxCxT,KAAK8R,UAAU2B,UApRO,iBAsRlBb,GACF5S,KAAK8R,UAAUhX,UAAUuP,IArRT,QAwRlBzT,SAASiF,KAAKoX,YAAYjT,KAAK8R,WAE/B5R,EAAaQ,GAAGV,KAAKwC,SAnSE,yBAmS6BxD,IAC9CgB,KAAKiS,qBACPjS,KAAKiS,sBAAuB,EAI1BjT,EAAMe,SAAWf,EAAM0U,gBAIG,WAA1B1T,KAAK+H,QAAQ4J,SACf3R,KAAKoT,6BAELpT,KAAKqN,UAILuF,GACFnX,EAAOuE,KAAK8R,WAGd9R,KAAK8R,UAAUhX,UAAUuP,IA9SP,SAgTbuI,EAEH,YADAzW,IAIF,MAAMwX,EAA6Bjc,EAAiCsI,KAAK8R,WAEzE5R,EAAaS,IAAIX,KAAK8R,UAAW,gBAAiB3V,GAClD1D,EAAqBuH,KAAK8R,UAAW6B,QAChC,IAAK3T,KAAK+R,UAAY/R,KAAK8R,UAAW,CAC3C9R,KAAK8R,UAAUhX,UAAU2C,OA1TP,QA4TlB,MAAMmW,EAAiB,KACrB5T,KAAKuT,kBACLpX,KAGF,GAAIyW,EAAY,CACd,MAAMe,EAA6Bjc,EAAiCsI,KAAK8R,WACzE5R,EAAaS,IAAIX,KAAK8R,UAAW,gBAAiB8B,GAClDnb,EAAqBuH,KAAK8R,UAAW6B,QAErCC,SAGFzX,IAIJgW,cACE,OAAOnS,KAAKwC,SAAS1H,UAAUC,SA/UX,QAkVtBqY,6BAEE,GADkBlT,EAAamB,QAAQrB,KAAKwC,SAlWlB,0BAmWZb,iBACZ,OAGF,MAAMkS,EAAqB7T,KAAKwC,SAASsR,aAAeld,SAASuE,gBAAgB4Y,aAE5EF,IACH7T,KAAKwC,SAASnI,MAAM2Z,UAAY,UAGlChU,KAAKwC,SAAS1H,UAAUuP,IA5VF,gBA6VtB,MAAM4J,EAA0Bvc,EAAiCsI,KAAK6R,SACtE3R,EAAaC,IAAIH,KAAKwC,SAAU,iBAChCtC,EAAaS,IAAIX,KAAKwC,SAAU,gBAAiB,KAC/CxC,KAAKwC,SAAS1H,UAAU2C,OAhWJ,gBAiWfoW,IACH3T,EAAaS,IAAIX,KAAKwC,SAAU,gBAAiB,KAC/CxC,KAAKwC,SAASnI,MAAM2Z,UAAY,KAElCvb,EAAqBuH,KAAKwC,SAAUyR,MAGxCxb,EAAqBuH,KAAKwC,SAAUyR,GACpCjU,KAAKwC,SAAS2N,QAOhBoC,gBACE,MAAMsB,EAAqB7T,KAAKwC,SAASsR,aAAeld,SAASuE,gBAAgB4Y,eAE3E/T,KAAKgS,oBAAsB6B,IAAuB/X,KAAakE,KAAKgS,qBAAuB6B,GAAsB/X,OACrHkE,KAAKwC,SAASnI,MAAM6Z,YAAiBlU,KAAKkS,gBAAP,OAGhClS,KAAKgS,qBAAuB6B,IAAuB/X,MAAckE,KAAKgS,oBAAsB6B,GAAsB/X,OACrHkE,KAAKwC,SAASnI,MAAM8Z,aAAkBnU,KAAKkS,gBAAP,MAIxCmB,oBACErT,KAAKwC,SAASnI,MAAM6Z,YAAc,GAClClU,KAAKwC,SAASnI,MAAM8Z,aAAe,GAGrC9B,kBACE,MAAMtN,EAAOnO,SAASiF,KAAKmJ,wBAC3BhF,KAAKgS,mBAAqBvb,KAAK2d,MAAMrP,EAAKI,KAAOJ,EAAKsP,OAASxc,OAAOyc,WACtEtU,KAAKkS,gBAAkBlS,KAAKuU,qBAG9BjC,gBACMtS,KAAKgS,qBACPhS,KAAKwU,sBAnYoB,oDAmY0B,eAAgBC,GAAmBA,EAAkBzU,KAAKkS,iBAC7GlS,KAAKwU,sBAnYqB,cAmY0B,cAAeC,GAAmBA,EAAkBzU,KAAKkS,iBAC7GlS,KAAKwU,sBAAsB,OAAQ,eAAgBC,GAAmBA,EAAkBzU,KAAKkS,kBAG/Ftb,SAASiF,KAAKf,UAAUuP,IAjZJ,cAoZtBmK,sBAAsBxd,EAAU0d,EAAWvY,GACzCqJ,EAAeC,KAAKzO,GACjBuC,QAAQxC,IACP,GAAIA,IAAYH,SAASiF,MAAQhE,OAAOyc,WAAavd,EAAQ4d,YAAc3U,KAAKkS,gBAC9E,OAGF,MAAM0C,EAAc7d,EAAQsD,MAAMqa,GAC5BD,EAAkB5c,OAAOC,iBAAiBf,GAAS2d,GACzDvQ,EAAYC,iBAAiBrN,EAAS2d,EAAWE,GACjD7d,EAAQsD,MAAMqa,GAAavY,EAASnE,OAAOC,WAAWwc,IAAoB,OAIhFnB,kBACEtT,KAAK6U,wBA1ZsB,oDA0Z0B,gBACrD7U,KAAK6U,wBA1ZuB,cA0Z0B,eACtD7U,KAAK6U,wBAAwB,OAAQ,gBAGvCA,wBAAwB7d,EAAU0d,GAChClP,EAAeC,KAAKzO,GAAUuC,QAAQxC,IACpC,MAAM2C,EAAQyK,EAAYU,iBAAiB9N,EAAS2d,QAC/B,IAAVhb,GAAyB3C,IAAYH,SAASiF,KACvD9E,EAAQsD,MAAMqa,GAAa,IAE3BvQ,EAAYE,oBAAoBtN,EAAS2d,GACzC3d,EAAQsD,MAAMqa,GAAahb,KAKjC6a,qBACE,MAAMO,EAAYle,SAAS4c,cAAc,OACzCsB,EAAUrB,UAxbwB,0BAyblC7c,SAASiF,KAAKoX,YAAY6B,GAC1B,MAAMC,EAAiBD,EAAU9P,wBAAwBgQ,MAAQF,EAAUH,YAE3E,OADA/d,SAASiF,KAAKyH,YAAYwR,GACnBC,EAKapS,uBAACxJ,EAAQ4R,GAC7B,OAAO/K,KAAKuD,MAAK,WACf,IAAIC,EAAO3G,EAAKM,IAAI6C,KAjeT,YAkeX,MAAM+H,EAAU,IACXtB,MACAtC,EAAYI,kBAAkBvE,SACX,iBAAX7G,GAAuBA,EAASA,EAAS,IAOtD,GAJKqK,IACHA,EAAO,IAAIoO,GAAM5R,KAAM+H,IAGH,iBAAX5O,EAAqB,CAC9B,QAA4B,IAAjBqK,EAAKrK,GACd,MAAM,IAAIe,UAAW,oBAAmBf,MAG1CqK,EAAKrK,GAAQ4R,QAYrB7K,EAAaQ,GAAG9J,SAjec,0BAWD,4BAsdyC,SAAUoI,GAC9E,MAAMe,EAAStI,EAAuBuI,MAEjB,MAAjBA,KAAKsK,SAAoC,SAAjBtK,KAAKsK,SAC/BtL,EAAMqD,iBAGRnC,EAAaS,IAAIZ,EAhfC,gBAgfmBqS,IAC/BA,EAAUzQ,kBAKdzB,EAAaS,IAAIZ,EAvfC,kBAufqB,KACjC3F,EAAU4F,OACZA,KAAKmQ,YAKX,IAAI3M,EAAO3G,EAAKM,IAAI4C,EAjhBL,YAkhBf,IAAKyD,EAAM,CACT,MAAMrK,EAAS,IACVgL,EAAYI,kBAAkBxE,MAC9BoE,EAAYI,kBAAkBvE,OAGnCwD,EAAO,IAAIoO,GAAM7R,EAAQ5G,GAG3BqK,EAAKI,OAAO5D,SAUdhE,EAtiBa,QAsiBY4V,ICzjBzB,MAGMqD,GAAW,KAEf,MAAMC,EAAgBte,SAASuE,gBAAgBwZ,YAC/C,OAAOle,KAAK+S,IAAI3R,OAAOyc,WAAaY,IAUhCV,GAAwB,CAACxd,EAAU0d,EAAWvY,KAClD,MAAM4Y,EAAiBE,KACvBzP,EAAeC,KAAKzO,GACjBuC,QAAQxC,IACP,GAAIA,IAAYH,SAASiF,MAAQhE,OAAOyc,WAAavd,EAAQ4d,YAAcI,EACzE,OAGF,MAAMH,EAAc7d,EAAQsD,MAAMqa,GAC5BD,EAAkB5c,OAAOC,iBAAiBf,GAAS2d,GACzDvQ,EAAYC,iBAAiBrN,EAAS2d,EAAWE,GACjD7d,EAAQsD,MAAMqa,GAAavY,EAASnE,OAAOC,WAAWwc,IAAoB,QAW1EI,GAA0B,CAAC7d,EAAU0d,KACzClP,EAAeC,KAAKzO,GAAUuC,QAAQxC,IACpC,MAAM2C,EAAQyK,EAAYU,iBAAiB9N,EAAS2d,QAC/B,IAAVhb,GAAyB3C,IAAYH,SAASiF,KACvD9E,EAAQsD,MAAM8a,eAAeT,IAE7BvQ,EAAYE,oBAAoBtN,EAAS2d,GACzC3d,EAAQsD,MAAMqa,GAAahb,MCnB3B+M,GAAU,CACdkL,UAAU,EACVhL,UAAU,EACVyO,QAAQ,GAGJpO,GAAc,CAClB2K,SAAU,UACVhL,SAAU,UACVyO,OAAQ,WA0BV,MAAMC,WAAkB/S,EACtBC,YAAYxL,EAASoC,GACnBmO,MAAMvQ,GAENiJ,KAAK+H,QAAU/H,KAAKgI,WAAW7O,GAC/B6G,KAAK+R,UAAW,EAChB/R,KAAKuI,qBAKW9B,qBAChB,OAAOA,GAGUhE,sBACjB,MAzDa,eA8DfmB,OAAOmH,GACL,OAAO/K,KAAK+R,SAAW/R,KAAKqN,OAASrN,KAAKsN,KAAKvC,GAGjDuC,KAAKvC,GACC/K,KAAK+R,UAIS7R,EAAamB,QAAQrB,KAAKwC,SA/C5B,oBA+CkD,CAAEuI,cAAAA,IAEtDpJ,mBAId3B,KAAK+R,UAAW,EAChB/R,KAAKwC,SAASnI,MAAMK,WAAa,UAE7BsF,KAAK+H,QAAQ4J,UACf/a,SAASiF,KAAKf,UAAUuP,IA/DG,sBAkExBrK,KAAK+H,QAAQqN,QD/FT,EAACJ,EAAQC,QACpBre,SAASiF,KAAKxB,MAAMib,SAAW,SAC/Bd,GAX6B,uCAWiB,eAAgBC,GAAmBA,EAAkBO,GACnGR,GAX8B,cAWiB,cAAeC,GAAmBA,EAAkBO,GACnGR,GAAsB,OAAQ,eAAgBC,GAAmBA,EAAkBO,IC4F/EO,GAGFvV,KAAKwC,SAAS1H,UAAUuP,IApEA,sBAqExBrK,KAAKwC,SAAS8B,gBAAgB,eAC9BtE,KAAKwC,SAASqB,aAAa,cAAc,GACzC7D,KAAKwC,SAASqB,aAAa,OAAQ,UACnC7D,KAAKwC,SAAS1H,UAAUuP,IAzEJ,QAiFpBrR,WANyB,KACvBgH,KAAKwC,SAAS1H,UAAU2C,OA3EF,sBA4EtByC,EAAamB,QAAQrB,KAAKwC,SAvEX,qBAuEkC,CAAEuI,cAAAA,IACnD/K,KAAKwV,uBAAuBxV,KAAKwC,WAGN9K,EAAiCsI,KAAKwC,YAGrE6K,OACOrN,KAAK+R,WAIQ7R,EAAamB,QAAQrB,KAAKwC,SAlF5B,qBAoFFb,mBAId3B,KAAKwC,SAAS1H,UAAUuP,IA9FA,sBA+FxBnK,EAAaC,IAAIvJ,SAvFE,wBAwFnBoJ,KAAKwC,SAASiT,OACdzV,KAAK+R,UAAW,EAChB/R,KAAKwC,SAAS1H,UAAU2C,OAnGJ,QAuHpBzE,WAlByB,KACvBgH,KAAKwC,SAASqB,aAAa,eAAe,GAC1C7D,KAAKwC,SAAS8B,gBAAgB,cAC9BtE,KAAKwC,SAAS8B,gBAAgB,QAC9BtE,KAAKwC,SAASnI,MAAMK,WAAa,SAE7BsF,KAAK+H,QAAQ4J,UACf/a,SAASiF,KAAKf,UAAU2C,OA7GC,sBAgHtBuC,KAAK+H,QAAQqN,SDtHtBxe,SAASiF,KAAKxB,MAAMib,SAAW,OAC/BT,GAjC6B,uCAiCmB,gBAChDA,GAjC8B,cAiCmB,eACjDA,GAAwB,OAAQ,iBCuH5B3U,EAAamB,QAAQrB,KAAKwC,SA3GV,uBA4GhBxC,KAAKwC,SAAS1H,UAAU2C,OAnHF,uBAsHK/F,EAAiCsI,KAAKwC,aAKrEwF,WAAW7O,GAOT,OANAA,EAAS,IACJsN,MACAtC,EAAYI,kBAAkBvE,KAAKwC,aAChB,iBAAXrJ,EAAsBA,EAAS,IAE5CF,EAtJS,YAsJaE,EAAQ6N,IACvB7N,EAGTqc,uBAAuBze,GACrBmJ,EAAaC,IAAIvJ,SA9HE,wBA+HnBsJ,EAAaQ,GAAG9J,SA/HG,uBA+HsBoI,IACnCpI,WAAaoI,EAAMe,QACrBhJ,IAAYiI,EAAMe,QACjBhJ,EAAQgE,SAASiE,EAAMe,SACxBhJ,EAAQoZ,UAGZpZ,EAAQoZ,QAGV5H,qBACErI,EAAaQ,GAAGV,KAAKwC,SAxII,6BAEC,gCAsIiD,IAAMxC,KAAKqN,QAEtFnN,EAAaQ,GAAG9J,SAAU,UAAWoI,IAC/BgB,KAAK+H,QAAQpB,UArKJ,WAqKgB3H,EAAMjC,KACjCiD,KAAKqN,SAITnN,EAAaQ,GAAG9J,SAjJU,8BAiJsBoI,IAC9C,MAAMe,EAASyF,EAAeK,QAAQtO,EAAuByH,EAAMe,SAC9DC,KAAKwC,SAASzH,SAASiE,EAAMe,SAAWA,IAAWC,KAAKwC,UAC3DxC,KAAKqN,SAOW1K,uBAACxJ,GACrB,OAAO6G,KAAKuD,MAAK,WACf,MAAMC,EAAO3G,EAAKM,IAAI6C,KA1LX,iBA0L8B,IAAIqV,GAAUrV,KAAwB,iBAAX7G,EAAsBA,EAAS,IAEnG,GAAsB,iBAAXA,EAAX,CAIA,QAAqBuc,IAAjBlS,EAAKrK,IAAyBA,EAAO/B,WAAW,MAAmB,gBAAX+B,EAC1D,MAAM,IAAIe,UAAW,oBAAmBf,MAG1CqK,EAAKrK,GAAQ6G,WAWnBE,EAAaQ,GAAG9J,SAlLc,8BAID,gCA8KyC,SAAUoI,GAC9E,MAAMe,EAAStI,EAAuBuI,MAMtC,GAJI,CAAC,IAAK,QAAQ7I,SAAS6I,KAAKsK,UAC9BtL,EAAMqD,iBAGJ1H,EAAWqF,MACb,OAGFE,EAAaS,IAAIZ,EA/LG,sBA+LmB,KAEjC3F,EAAU4F,OACZA,KAAKmQ,UAKT,MAAMwF,EAAenQ,EAAeK,QA5Mb,wCA6MnB8P,GAAgBA,IAAiB5V,IAIxBlD,EAAKM,IAAI4C,EAvOP,iBAuO4B,IAAIsV,GAAUtV,IAEpD6D,OAAO5D,SAGdE,EAAaQ,GAAG7I,OAzOa,6BAyOgB,KAC3C2N,EAAeC,KAxNK,mBAwNelM,QAAQqc,IAAO/Y,EAAKM,IAAIyY,EA7O5C,iBA6O6D,IAAIP,GAAUO,IAAKtI,UASjGtR,EAvPa,YAuPYqZ,IC7QzB,MAAMQ,GAAW,IAAIzX,IAAI,CACvB,aACA,OACA,OACA,WACA,WACA,SACA,MACA,eAUI0X,GAAmB,6DAOnBC,GAAmB,qIAEnBC,GAAmB,CAACC,EAAMC,KAC9B,MAAMC,EAAWF,EAAKG,SAASrc,cAE/B,GAAImc,EAAqB/e,SAASgf,GAChC,OAAIN,GAAS5Y,IAAIkZ,IACR1V,QAAQqV,GAAiB7b,KAAKgc,EAAKI,YAAcN,GAAiB9b,KAAKgc,EAAKI,YAMvF,MAAMC,EAASJ,EAAqBxR,OAAO6R,GAAaA,aAAqBvc,QAG7E,IAAK,IAAI6E,EAAI,EAAGC,EAAMwX,EAAOvX,OAAQF,EAAIC,EAAKD,IAC5C,GAAIyX,EAAOzX,GAAG5E,KAAKkc,GACjB,OAAO,EAIX,OAAO,GAqCF,SAASK,GAAaC,EAAYC,EAAWC,GAClD,IAAKF,EAAW1X,OACd,OAAO0X,EAGT,GAAIE,GAAoC,mBAAfA,EACvB,OAAOA,EAAWF,GAGpB,MACMG,GADY,IAAI/e,OAAOgf,WACKC,gBAAgBL,EAAY,aACxDM,EAAgB1d,OAAOC,KAAKod,GAC5BM,EAAW,GAAGtR,UAAUkR,EAAgB/a,KAAKiE,iBAAiB,MAEpE,IAAK,IAAIjB,EAAI,EAAGC,EAAMkY,EAASjY,OAAQF,EAAIC,EAAKD,IAAK,CACnD,MAAM+W,EAAKoB,EAASnY,GACdoY,EAASrB,EAAGQ,SAASrc,cAE3B,IAAKgd,EAAc5f,SAAS8f,GAAS,CACnCrB,EAAGtb,WAAWgJ,YAAYsS,GAE1B,SAGF,MAAMsB,EAAgB,GAAGxR,UAAUkQ,EAAGpR,YAChC2S,EAAoB,GAAGzR,OAAOgR,EAAU,MAAQ,GAAIA,EAAUO,IAAW,IAE/EC,EAAc3d,QAAQ0c,IACfD,GAAiBC,EAAMkB,IAC1BvB,EAAGtR,gBAAgB2R,EAAKG,YAK9B,OAAOQ,EAAgB/a,KAAKub,UCzF9B,MAIMC,GAAqB,IAAIrd,OAAQ,wBAA6B,KAC9Dsd,GAAwB,IAAIlZ,IAAI,CAAC,WAAY,YAAa,eAE1D4I,GAAc,CAClBuQ,UAAW,UACXC,SAAU,SACVC,MAAO,4BACPpW,QAAS,SACTqW,MAAO,kBACPC,KAAM,UACN3gB,SAAU,mBACV8Z,UAAW,oBACXhM,OAAQ,0BACR2I,UAAW,2BACXmK,mBAAoB,QACpB7I,SAAU,mBACV8I,YAAa,oBACbC,SAAU,UACVnB,WAAY,kBACZD,UAAW,SACXzH,aAAc,0BAGV8I,GAAgB,CACpBC,KAAM,OACNC,IAAK,MACLC,MAAOpc,IAAU,OAAS,QAC1Bqc,OAAQ,SACRC,KAAMtc,IAAU,QAAU,QAGtB2K,GAAU,CACd8Q,WAAW,EACXC,SAAU,+GAIVnW,QAAS,cACToW,MAAO,GACPC,MAAO,EACPC,MAAM,EACN3gB,UAAU,EACV8Z,UAAW,MACXhM,OAAQ,CAAC,EAAG,GACZ2I,WAAW,EACXmK,mBAAoB,CAAC,MAAO,QAAS,SAAU,QAC/C7I,SAAU,kBACV8I,YAAa,GACbC,UAAU,EACVnB,WAAY,KACZD,UDjC8B,CAE9B2B,IAAK,CAAC,QAAS,MAAO,KAAM,OAAQ,OAzCP,kBA0C7BC,EAAG,CAAC,SAAU,OAAQ,QAAS,OAC/BC,KAAM,GACNC,EAAG,GACHC,GAAI,GACJC,IAAK,GACLC,KAAM,GACNC,IAAK,GACLC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJva,EAAG,GACHwa,IAAK,CAAC,MAAO,SAAU,MAAO,QAAS,QAAS,UAChDC,GAAI,GACJC,GAAI,GACJC,EAAG,GACHC,IAAK,GACLC,EAAG,GACHC,MAAO,GACPC,KAAM,GACNC,IAAK,GACLC,IAAK,GACLC,OAAQ,GACRC,EAAG,GACHC,GAAI,ICGJhL,aAAc,MAGV5W,GAAQ,CACZ6hB,KAAO,kBACPC,OAAS,oBACTC,KAAO,kBACPC,MAAQ,mBACRC,SAAW,sBACXC,MAAQ,mBACRC,QAAU,qBACVC,SAAW,sBACXC,WAAa,wBACbC,WAAa,yBAuBf,MAAMC,WAAgBtY,EACpBC,YAAYxL,EAASoC,GACnB,QAAsB,IAAXwW,EACT,MAAM,IAAIzV,UAAU,+DAGtBoN,MAAMvQ,GAGNiJ,KAAK6a,YAAa,EAClB7a,KAAK8a,SAAW,EAChB9a,KAAK+a,YAAc,GACnB/a,KAAKgb,eAAiB,GACtBhb,KAAKmP,QAAU,KAGfnP,KAAK7G,OAAS6G,KAAKgI,WAAW7O,GAC9B6G,KAAKib,IAAM,KAEXjb,KAAKkb,gBAKWzU,qBAChB,OAAOA,GAGM0U,kBACb,MAxHS,UA2HQ1Y,sBACjB,MA3Ha,aA8HCpK,mBACd,OAAOA,GAGW+iB,uBAClB,MAlIe,cAqIKpU,yBACpB,OAAOA,GAKTqU,SACErb,KAAK6a,YAAa,EAGpBS,UACEtb,KAAK6a,YAAa,EAGpBU,gBACEvb,KAAK6a,YAAc7a,KAAK6a,WAG1BjX,OAAO5E,GACL,GAAKgB,KAAK6a,WAIV,GAAI7b,EAAO,CACT,MAAMkS,EAAUlR,KAAKwb,6BAA6Bxc,GAElDkS,EAAQ8J,eAAexJ,OAASN,EAAQ8J,eAAexJ,MAEnDN,EAAQuK,uBACVvK,EAAQwK,OAAO,KAAMxK,GAErBA,EAAQyK,OAAO,KAAMzK,OAElB,CACL,GAAIlR,KAAK4b,gBAAgB9gB,UAAUC,SAhGjB,QAkGhB,YADAiF,KAAK2b,OAAO,KAAM3b,MAIpBA,KAAK0b,OAAO,KAAM1b,OAItB0C,UACEwH,aAAalK,KAAK8a,UAElB5a,EAAaC,IAAIH,KAAKwC,SAAUxC,KAAKuC,YAAY6Y,WACjDlb,EAAaC,IAAIH,KAAKwC,SAASY,QAAS,UAAwB,gBAAiBpD,KAAK6b,mBAElF7b,KAAKib,KAAOjb,KAAKib,IAAI3gB,YACvB0F,KAAKib,IAAI3gB,WAAWgJ,YAAYtD,KAAKib,KAGvCjb,KAAK6a,WAAa,KAClB7a,KAAK8a,SAAW,KAChB9a,KAAK+a,YAAc,KACnB/a,KAAKgb,eAAiB,KAClBhb,KAAKmP,SACPnP,KAAKmP,QAAQiB,UAGfpQ,KAAKmP,QAAU,KACfnP,KAAK7G,OAAS,KACd6G,KAAKib,IAAM,KACX3T,MAAM5E,UAGR4K,OACE,GAAoC,SAAhCtN,KAAKwC,SAASnI,MAAMI,QACtB,MAAM,IAAIqhB,MAAM,uCAGlB,IAAM9b,KAAK+b,kBAAmB/b,KAAK6a,WACjC,OAGF,MAAMzI,EAAYlS,EAAamB,QAAQrB,KAAKwC,SAAUxC,KAAKuC,YAAYlK,MAAM+hB,MACvE4B,EAAa9gB,EAAe8E,KAAKwC,UACjCyZ,EAA4B,OAAfD,EACjBhc,KAAKwC,SAAS0Z,cAAc/gB,gBAAgBJ,SAASiF,KAAKwC,UAC1DwZ,EAAWjhB,SAASiF,KAAKwC,UAE3B,GAAI4P,EAAUzQ,mBAAqBsa,EACjC,OAGF,MAAMhB,EAAMjb,KAAK4b,gBACXO,EAAQ5lB,EAAOyJ,KAAKuC,YAAY4Y,MAEtCF,EAAIpX,aAAa,KAAMsY,GACvBnc,KAAKwC,SAASqB,aAAa,mBAAoBsY,GAE/Cnc,KAAKoc,aAEDpc,KAAK7G,OAAOoe,WACd0D,EAAIngB,UAAUuP,IA/JI,QAkKpB,MAAMyG,EAA6C,mBAA1B9Q,KAAK7G,OAAO2X,UACnC9Q,KAAK7G,OAAO2X,UAAUjX,KAAKmG,KAAMib,EAAKjb,KAAKwC,UAC3CxC,KAAK7G,OAAO2X,UAERuL,EAAarc,KAAKsc,eAAexL,GACvC9Q,KAAKuc,oBAAoBF,GAEzB,MAAM5O,EAAYzN,KAAKwc,gBACvB3f,EAAKC,IAAIme,EAAKjb,KAAKuC,YAAYE,SAAUzC,MAEpCA,KAAKwC,SAAS0Z,cAAc/gB,gBAAgBJ,SAASiF,KAAKib,OAC7DxN,EAAUwF,YAAYgI,GACtB/a,EAAamB,QAAQrB,KAAKwC,SAAUxC,KAAKuC,YAAYlK,MAAMiiB,WAGzDta,KAAKmP,QACPnP,KAAKmP,QAAQkB,SAEbrQ,KAAKmP,QAAUQ,EAAOO,aAAalQ,KAAKwC,SAAUyY,EAAKjb,KAAK6P,iBAAiBwM,IAG/EpB,EAAIngB,UAAUuP,IArLM,QAuLpB,MAAMwN,EAAiD,mBAA5B7X,KAAK7G,OAAO0e,YAA6B7X,KAAK7G,OAAO0e,cAAgB7X,KAAK7G,OAAO0e,YACxGA,GACFoD,EAAIngB,UAAUuP,OAAOwN,EAAYxgB,MAAM,MAOrC,iBAAkBT,SAASuE,iBAC7B,GAAGuK,UAAU9O,SAASiF,KAAKiK,UAAUvM,QAAQxC,IAC3CmJ,EAAaQ,GAAG3J,EAAS,af7Gd,iBeiHf,MAAM0lB,EAAW,KACf,MAAMC,EAAiB1c,KAAK+a,YAE5B/a,KAAK+a,YAAc,KACnB7a,EAAamB,QAAQrB,KAAKwC,SAAUxC,KAAKuC,YAAYlK,MAAMgiB,OAvMzC,QAyMdqC,GACF1c,KAAK2b,OAAO,KAAM3b,OAItB,GAAIA,KAAKib,IAAIngB,UAAUC,SAnNH,QAmN8B,CAChD,MAAMpD,EAAqBD,EAAiCsI,KAAKib,KACjE/a,EAAaS,IAAIX,KAAKib,IAAK,gBAAiBwB,GAC5ChkB,EAAqBuH,KAAKib,IAAKtjB,QAE/B8kB,IAIJpP,OACE,IAAKrN,KAAKmP,QACR,OAGF,MAAM8L,EAAMjb,KAAK4b,gBACXa,EAAW,KACXzc,KAAKyb,yBA/NU,SAmOfzb,KAAK+a,aAAoCE,EAAI3gB,YAC/C2gB,EAAI3gB,WAAWgJ,YAAY2X,GAG7Bjb,KAAK2c,iBACL3c,KAAKwC,SAAS8B,gBAAgB,oBAC9BpE,EAAamB,QAAQrB,KAAKwC,SAAUxC,KAAKuC,YAAYlK,MAAM8hB,QAEvDna,KAAKmP,UACPnP,KAAKmP,QAAQiB,UACbpQ,KAAKmP,QAAU,QAKnB,IADkBjP,EAAamB,QAAQrB,KAAKwC,SAAUxC,KAAKuC,YAAYlK,MAAM6hB,MAC/DvY,iBAAd,CAiBA,GAbAsZ,EAAIngB,UAAU2C,OAxPM,QA4PhB,iBAAkB7G,SAASuE,iBAC7B,GAAGuK,UAAU9O,SAASiF,KAAKiK,UACxBvM,QAAQxC,GAAWmJ,EAAaC,IAAIpJ,EAAS,YAAayE,IAG/DwE,KAAKgb,eAAL,OAAqC,EACrChb,KAAKgb,eAAL,OAAqC,EACrChb,KAAKgb,eAAL,OAAqC,EAEjChb,KAAKib,IAAIngB,UAAUC,SAvQH,QAuQ8B,CAChD,MAAMpD,EAAqBD,EAAiCujB,GAE5D/a,EAAaS,IAAIsa,EAAK,gBAAiBwB,GACvChkB,EAAqBwiB,EAAKtjB,QAE1B8kB,IAGFzc,KAAK+a,YAAc,IAGrB1K,SACuB,OAAjBrQ,KAAKmP,SACPnP,KAAKmP,QAAQkB,SAMjB0L,gBACE,OAAOtb,QAAQT,KAAK4c,YAGtBhB,gBACE,GAAI5b,KAAKib,IACP,OAAOjb,KAAKib,IAGd,MAAMlkB,EAAUH,SAAS4c,cAAc,OAIvC,OAHAzc,EAAQqgB,UAAYpX,KAAK7G,OAAOqe,SAEhCxX,KAAKib,IAAMlkB,EAAQ+O,SAAS,GACrB9F,KAAKib,IAGdmB,aACE,MAAMnB,EAAMjb,KAAK4b,gBACjB5b,KAAK6c,kBAAkBrX,EAAeK,QAtSX,iBAsS2CoV,GAAMjb,KAAK4c,YACjF3B,EAAIngB,UAAU2C,OA9SM,OAEA,QA+StBof,kBAAkB9lB,EAAS+lB,GACzB,GAAgB,OAAZ/lB,EAIJ,MAAuB,iBAAZ+lB,GAAwBxkB,EAAUwkB,IACvCA,EAAQ3O,SACV2O,EAAUA,EAAQ,SAIhB9c,KAAK7G,OAAOwe,KACVmF,EAAQxiB,aAAevD,IACzBA,EAAQqgB,UAAY,GACpBrgB,EAAQkc,YAAY6J,IAGtB/lB,EAAQgmB,YAAcD,EAAQC,mBAM9B/c,KAAK7G,OAAOwe,MACV3X,KAAK7G,OAAO2e,WACdgF,EAAUtG,GAAasG,EAAS9c,KAAK7G,OAAOud,UAAW1W,KAAK7G,OAAOwd,aAGrE5f,EAAQqgB,UAAY0F,GAEpB/lB,EAAQgmB,YAAcD,GAI1BF,WACE,IAAInF,EAAQzX,KAAKwC,SAASvL,aAAa,0BAQvC,OANKwgB,IACHA,EAAqC,mBAAtBzX,KAAK7G,OAAOse,MACzBzX,KAAK7G,OAAOse,MAAM5d,KAAKmG,KAAKwC,UAC5BxC,KAAK7G,OAAOse,OAGTA,EAGTuF,iBAAiBX,GACf,MAAmB,UAAfA,EACK,MAGU,SAAfA,EACK,QAGFA,EAKTb,6BAA6Bxc,EAAOkS,GAClC,MAAM+L,EAAUjd,KAAKuC,YAAYE,SAQjC,OAPAyO,EAAUA,GAAWrU,EAAKM,IAAI6B,EAAMiB,eAAgBgd,MAGlD/L,EAAU,IAAIlR,KAAKuC,YAAYvD,EAAMiB,eAAgBD,KAAKkd,sBAC1DrgB,EAAKC,IAAIkC,EAAMiB,eAAgBgd,EAAS/L,IAGnCA,EAGTR,aACE,MAAM5L,OAAEA,GAAW9E,KAAK7G,OAExB,MAAsB,iBAAX2L,EACFA,EAAOzN,MAAM,KAAKsZ,IAAI5M,GAAO/L,OAAOsT,SAASvH,EAAK,KAGrC,mBAAXe,EACF8L,GAAc9L,EAAO8L,EAAY5Q,KAAKwC,UAGxCsC,EAGT+K,iBAAiBwM,GACf,MAAMxL,EAAwB,CAC5BC,UAAWuL,EACXtM,UAAW,CACT,CACE9T,KAAM,OACN8U,QAAS,CACPoM,aAAa,EACbvF,mBAAoB5X,KAAK7G,OAAOye,qBAGpC,CACE3b,KAAM,SACN8U,QAAS,CACPjM,OAAQ9E,KAAK0Q,eAGjB,CACEzU,KAAM,kBACN8U,QAAS,CACPhC,SAAU/O,KAAK7G,OAAO4V,WAG1B,CACE9S,KAAM,QACN8U,QAAS,CACPha,QAAU,IAAGiJ,KAAKuC,YAAY4Y,eAGlC,CACElf,KAAM,WACNgU,SAAS,EACTmN,MAAO,aACP9gB,GAAIkH,GAAQxD,KAAKqd,6BAA6B7Z,KAGlD8Z,cAAe9Z,IACTA,EAAKuN,QAAQD,YAActN,EAAKsN,WAClC9Q,KAAKqd,6BAA6B7Z,KAKxC,MAAO,IACFqN,KACqC,mBAA7B7Q,KAAK7G,OAAO8V,aAA8BjP,KAAK7G,OAAO8V,aAAa4B,GAAyB7Q,KAAK7G,OAAO8V,cAIvHsN,oBAAoBF,GAClBrc,KAAK4b,gBAAgB9gB,UAAUuP,IAAK,cAAkBrK,KAAKgd,iBAAiBX,IAG9EG,gBACE,OAA8B,IAA1Bxc,KAAK7G,OAAOsU,UACP7W,SAASiF,KAGdvD,EAAU0H,KAAK7G,OAAOsU,WACjBzN,KAAK7G,OAAOsU,UAGdjI,EAAeK,QAAQ7F,KAAK7G,OAAOsU,WAG5C6O,eAAexL,GACb,OAAOiH,GAAcjH,EAAU3W,eAGjC+gB,gBACmBlb,KAAK7G,OAAOkI,QAAQhK,MAAM,KAElCkC,QAAQ8H,IACf,GAAgB,UAAZA,EACFnB,EAAaQ,GAAGV,KAAKwC,SAAUxC,KAAKuC,YAAYlK,MAAMkiB,MAAOva,KAAK7G,OAAOnC,SAAUgI,GAASgB,KAAK4D,OAAO5E,SACnG,GAtcU,WAscNqC,EAA4B,CACrC,MAAMkc,EA1cQ,UA0cElc,EACdrB,KAAKuC,YAAYlK,MAAMqiB,WACvB1a,KAAKuC,YAAYlK,MAAMmiB,QACnBgD,EA7cQ,UA6cGnc,EACfrB,KAAKuC,YAAYlK,MAAMsiB,WACvB3a,KAAKuC,YAAYlK,MAAMoiB,SAEzBva,EAAaQ,GAAGV,KAAKwC,SAAU+a,EAASvd,KAAK7G,OAAOnC,SAAUgI,GAASgB,KAAK0b,OAAO1c,IACnFkB,EAAaQ,GAAGV,KAAKwC,SAAUgb,EAAUxd,KAAK7G,OAAOnC,SAAUgI,GAASgB,KAAK2b,OAAO3c,OAIxFgB,KAAK6b,kBAAoB,KACnB7b,KAAKwC,UACPxC,KAAKqN,QAITnN,EAAaQ,GAAGV,KAAKwC,SAASY,QAAS,UAAwB,gBAAiBpD,KAAK6b,mBAEjF7b,KAAK7G,OAAOnC,SACdgJ,KAAK7G,OAAS,IACT6G,KAAK7G,OACRkI,QAAS,SACTrK,SAAU,IAGZgJ,KAAKyd,YAITA,YACE,MAAMhG,EAAQzX,KAAKwC,SAASvL,aAAa,SACnCymB,SAA2B1d,KAAKwC,SAASvL,aAAa,2BAExDwgB,GAA+B,WAAtBiG,KACX1d,KAAKwC,SAASqB,aAAa,yBAA0B4T,GAAS,KAC1DA,GAAUzX,KAAKwC,SAASvL,aAAa,eAAkB+I,KAAKwC,SAASua,aACvE/c,KAAKwC,SAASqB,aAAa,aAAc4T,GAG3CzX,KAAKwC,SAASqB,aAAa,QAAS,KAIxC6X,OAAO1c,EAAOkS,GACZA,EAAUlR,KAAKwb,6BAA6Bxc,EAAOkS,GAE/ClS,IACFkS,EAAQ8J,eACS,YAAfhc,EAAMoB,KA3fQ,QADA,UA6fZ,GAGF8Q,EAAQ0K,gBAAgB9gB,UAAUC,SAvgBlB,SAEC,SAqgB8CmW,EAAQ6J,YACzE7J,EAAQ6J,YAtgBW,QA0gBrB7Q,aAAagH,EAAQ4J,UAErB5J,EAAQ6J,YA5gBa,OA8gBhB7J,EAAQ/X,OAAOue,OAAUxG,EAAQ/X,OAAOue,MAAMpK,KAKnD4D,EAAQ4J,SAAW9hB,WAAW,KAnhBT,SAohBfkY,EAAQ6J,aACV7J,EAAQ5D,QAET4D,EAAQ/X,OAAOue,MAAMpK,MARtB4D,EAAQ5D,QAWZqO,OAAO3c,EAAOkS,GACZA,EAAUlR,KAAKwb,6BAA6Bxc,EAAOkS,GAE/ClS,IACFkS,EAAQ8J,eACS,aAAfhc,EAAMoB,KAzhBQ,QADA,SA2hBZ8Q,EAAQ1O,SAASzH,SAASiE,EAAM+L,gBAGlCmG,EAAQuK,yBAIZvR,aAAagH,EAAQ4J,UAErB5J,EAAQ6J,YAxiBY,MA0iBf7J,EAAQ/X,OAAOue,OAAUxG,EAAQ/X,OAAOue,MAAMrK,KAKnD6D,EAAQ4J,SAAW9hB,WAAW,KA/iBV,QAgjBdkY,EAAQ6J,aACV7J,EAAQ7D,QAET6D,EAAQ/X,OAAOue,MAAMrK,MARtB6D,EAAQ7D,QAWZoO,uBACE,IAAK,MAAMpa,KAAWrB,KAAKgb,eACzB,GAAIhb,KAAKgb,eAAe3Z,GACtB,OAAO,EAIX,OAAO,EAGT2G,WAAW7O,GACT,MAAMwkB,EAAiBxZ,EAAYI,kBAAkBvE,KAAKwC,UAuC1D,OArCAnJ,OAAOC,KAAKqkB,GAAgBpkB,QAAQqkB,IAC9BtG,GAAsBra,IAAI2gB,WACrBD,EAAeC,KAItBzkB,GAAsC,iBAArBA,EAAOsU,WAA0BtU,EAAOsU,UAAUU,SACrEhV,EAAOsU,UAAYtU,EAAOsU,UAAU,IASV,iBAN5BtU,EAAS,IACJ6G,KAAKuC,YAAYkE,WACjBkX,KACmB,iBAAXxkB,GAAuBA,EAASA,EAAS,KAGpCue,QAChBve,EAAOue,MAAQ,CACbpK,KAAMnU,EAAOue,MACbrK,KAAMlU,EAAOue,QAIW,iBAAjBve,EAAOse,QAChBte,EAAOse,MAAQte,EAAOse,MAAM7d,YAGA,iBAAnBT,EAAO2jB,UAChB3jB,EAAO2jB,QAAU3jB,EAAO2jB,QAAQljB,YAGlCX,EA9qBS,UA8qBaE,EAAQ6G,KAAKuC,YAAYyE,aAE3C7N,EAAO2e,WACT3e,EAAOqe,SAAWhB,GAAard,EAAOqe,SAAUre,EAAOud,UAAWvd,EAAOwd,aAGpExd,EAGT+jB,qBACE,MAAM/jB,EAAS,GAEf,GAAI6G,KAAK7G,OACP,IAAK,MAAM4D,KAAOiD,KAAK7G,OACjB6G,KAAKuC,YAAYkE,QAAQ1J,KAASiD,KAAK7G,OAAO4D,KAChD5D,EAAO4D,GAAOiD,KAAK7G,OAAO4D,IAKhC,OAAO5D,EAGTwjB,iBACE,MAAM1B,EAAMjb,KAAK4b,gBACXiC,EAAW5C,EAAIhkB,aAAa,SAAS6C,MAAMud,IAChC,OAAbwG,GAAqBA,EAAS9e,OAAS,GACzC8e,EAASlN,IAAImN,GAASA,EAAMxmB,QACzBiC,QAAQwkB,GAAU9C,EAAIngB,UAAU2C,OAAOsgB,IAI9CV,6BAA6BzM,GAC3B,MAAMoN,MAAEA,GAAUpN,EAEboN,IAILhe,KAAKib,IAAM+C,EAAMhH,SAASiH,OAC1Bje,KAAK2c,iBACL3c,KAAKuc,oBAAoBvc,KAAKsc,eAAe0B,EAAMlN,aAK/BnO,uBAACxJ,GACrB,OAAO6G,KAAKuD,MAAK,WACf,IAAIC,EAAO3G,EAAKM,IAAI6C,KA7tBT,cA8tBX,MAAM+H,EAA4B,iBAAX5O,GAAuBA,EAE9C,IAAKqK,IAAQ,eAAevJ,KAAKd,MAI5BqK,IACHA,EAAO,IAAIoX,GAAQ5a,KAAM+H,IAGL,iBAAX5O,GAAqB,CAC9B,QAA4B,IAAjBqK,EAAKrK,GACd,MAAM,IAAIe,UAAW,oBAAmBf,MAG1CqK,EAAKrK,UAab6C,EA3vBa,UA2vBY4e,IC7wBzB,MAIMvD,GAAqB,IAAIrd,OAAQ,wBAA6B,KAE9DyM,GAAU,IACXmU,GAAQnU,QACXqK,UAAW,QACXhM,OAAQ,CAAC,EAAG,GACZzD,QAAS,QACTyb,QAAS,GACTtF,SAAU,+IAONxQ,GAAc,IACf4T,GAAQ5T,YACX8V,QAAS,6BAGLzkB,GAAQ,CACZ6hB,KAAO,kBACPC,OAAS,oBACTC,KAAO,kBACPC,MAAQ,mBACRC,SAAW,sBACXC,MAAQ,mBACRC,QAAU,qBACVC,SAAW,sBACXC,WAAa,wBACbC,WAAa,yBAef,MAAMuD,WAAgBtD,GAGFnU,qBAChB,OAAOA,GAGM0U,kBACb,MAzDS,UA4DQ1Y,sBACjB,MA5Da,aA+DCpK,mBACd,OAAOA,GAGW+iB,uBAClB,MAnEe,cAsEKpU,yBACpB,OAAOA,GAKT+U,gBACE,OAAO/b,KAAK4c,YAAc5c,KAAKme,cAGjC/B,aACE,MAAMnB,EAAMjb,KAAK4b,gBAGjB5b,KAAK6c,kBAAkBrX,EAAeK,QA9CnB,kBA8C2CoV,GAAMjb,KAAK4c,YACzE,IAAIE,EAAU9c,KAAKme,cACI,mBAAZrB,IACTA,EAAUA,EAAQjjB,KAAKmG,KAAKwC,WAG9BxC,KAAK6c,kBAAkBrX,EAAeK,QAnDjB,gBAmD2CoV,GAAM6B,GAEtE7B,EAAIngB,UAAU2C,OAzDM,OACA,QA6DtB8e,oBAAoBF,GAClBrc,KAAK4b,gBAAgB9gB,UAAUuP,IAAK,cAAkBrK,KAAKgd,iBAAiBX,IAG9E8B,cACE,OAAOne,KAAKwC,SAASvL,aAAa,oBAAsB+I,KAAK7G,OAAO2jB,QAGtEH,iBACE,MAAM1B,EAAMjb,KAAK4b,gBACXiC,EAAW5C,EAAIhkB,aAAa,SAAS6C,MAAMud,IAChC,OAAbwG,GAAqBA,EAAS9e,OAAS,GACzC8e,EAASlN,IAAImN,GAASA,EAAMxmB,QACzBiC,QAAQwkB,GAAU9C,EAAIngB,UAAU2C,OAAOsgB,IAMxBpb,uBAACxJ,GACrB,OAAO6G,KAAKuD,MAAK,WACf,IAAIC,EAAO3G,EAAKM,IAAI6C,KAvHT,cAwHX,MAAM+H,EAA4B,iBAAX5O,EAAsBA,EAAS,KAEtD,IAAKqK,IAAQ,eAAevJ,KAAKd,MAI5BqK,IACHA,EAAO,IAAI0a,GAAQle,KAAM+H,GACzBlL,EAAKC,IAAIkD,KAhIA,aAgIgBwD,IAGL,iBAAXrK,GAAqB,CAC9B,QAA4B,IAAjBqK,EAAKrK,GACd,MAAM,IAAIe,UAAW,oBAAmBf,MAG1CqK,EAAKrK,UAab6C,EAtJa,UAsJYkiB,IC9IzB,MAKMzX,GAAU,CACd3B,OAAQ,GACRsZ,OAAQ,OACRre,OAAQ,IAGJiH,GAAc,CAClBlC,OAAQ,SACRsZ,OAAQ,SACRre,OAAQ,oBA2BV,MAAMse,WAAkB/b,EACtBC,YAAYxL,EAASoC,GACnBmO,MAAMvQ,GACNiJ,KAAKse,eAA2C,SAA1Bte,KAAKwC,SAAS8H,QAAqBzS,OAASmI,KAAKwC,SACvExC,KAAK+H,QAAU/H,KAAKgI,WAAW7O,GAC/B6G,KAAKiN,UAAa,GAAEjN,KAAK+H,QAAQhI,qBAAiCC,KAAK+H,QAAQhI,4BAAkCC,KAAK+H,QAAQhI,wBAC9HC,KAAKue,SAAW,GAChBve,KAAKwe,SAAW,GAChBxe,KAAKye,cAAgB,KACrBze,KAAK0e,cAAgB,EAErBxe,EAAaQ,GAAGV,KAAKse,eAlCH,sBAkCiC,IAAMte,KAAK2e,YAE9D3e,KAAK4e,UACL5e,KAAK2e,WAKWlY,qBAChB,OAAOA,GAGUhE,sBACjB,MAhEa,eAqEfmc,UACE,MAAMC,EAAa7e,KAAKse,iBAAmBte,KAAKse,eAAezmB,OAvC7C,SACE,WA0CdinB,EAAuC,SAAxB9e,KAAK+H,QAAQqW,OAChCS,EACA7e,KAAK+H,QAAQqW,OAETW,EA9Cc,aA8CDD,EACjB9e,KAAKgf,gBACL,EAEFhf,KAAKue,SAAW,GAChBve,KAAKwe,SAAW,GAChBxe,KAAK0e,cAAgB1e,KAAKif,mBAEVzZ,EAAeC,KAAKzF,KAAKiN,WAEjC0D,IAAI5Z,IACV,MAAMmoB,EAAiB3nB,EAAuBR,GACxCgJ,EAASmf,EAAiB1Z,EAAeK,QAAQqZ,GAAkB,KAEzE,GAAInf,EAAQ,CACV,MAAMof,EAAYpf,EAAOiF,wBACzB,GAAIma,EAAUnK,OAASmK,EAAUC,OAC/B,MAAO,CACLjb,EAAY2a,GAAc/e,GAAQkF,IAAM8Z,EACxCG,GAKN,OAAO,OAENxa,OAAO2a,GAAQA,GACfC,KAAK,CAAChH,EAAGE,IAAMF,EAAE,GAAKE,EAAE,IACxBjf,QAAQ8lB,IACPrf,KAAKue,SAASpY,KAAKkZ,EAAK,IACxBrf,KAAKwe,SAASrY,KAAKkZ,EAAK,MAI9B3c,UACE4E,MAAM5E,UACNxC,EAAaC,IAAIH,KAAKse,eAjHP,iBAmHfte,KAAKse,eAAiB,KACtBte,KAAK+H,QAAU,KACf/H,KAAKiN,UAAY,KACjBjN,KAAKue,SAAW,KAChBve,KAAKwe,SAAW,KAChBxe,KAAKye,cAAgB,KACrBze,KAAK0e,cAAgB,KAKvB1W,WAAW7O,GAMT,GAA6B,iBAL7BA,EAAS,IACJsN,MACmB,iBAAXtN,GAAuBA,EAASA,EAAS,KAGpC4G,QAAuBzH,EAAUa,EAAO4G,QAAS,CACjE,IAAI6M,GAAEA,GAAOzT,EAAO4G,OACf6M,IACHA,EAAKrW,EAzIA,aA0IL4C,EAAO4G,OAAO6M,GAAKA,GAGrBzT,EAAO4G,OAAU,IAAG6M,EAKtB,OAFA3T,EAhJS,YAgJaE,EAAQ6N,IAEvB7N,EAGT6lB,gBACE,OAAOhf,KAAKse,iBAAmBzmB,OAC7BmI,KAAKse,eAAeiB,YACpBvf,KAAKse,eAAepZ,UAGxB+Z,mBACE,OAAOjf,KAAKse,eAAexK,cAAgBrd,KAAK+oB,IAC9C5oB,SAASiF,KAAKiY,aACdld,SAASuE,gBAAgB2Y,cAI7B2L,mBACE,OAAOzf,KAAKse,iBAAmBzmB,OAC7BA,OAAO6nB,YACP1f,KAAKse,eAAetZ,wBAAwBoa,OAGhDT,WACE,MAAMzZ,EAAYlF,KAAKgf,gBAAkBhf,KAAK+H,QAAQjD,OAChDgP,EAAe9T,KAAKif,mBACpBU,EAAY3f,KAAK+H,QAAQjD,OAASgP,EAAe9T,KAAKyf,mBAM5D,GAJIzf,KAAK0e,gBAAkB5K,GACzB9T,KAAK4e,UAGH1Z,GAAaya,EAAjB,CACE,MAAM5f,EAASC,KAAKwe,SAASxe,KAAKwe,SAASzf,OAAS,GAEhDiB,KAAKye,gBAAkB1e,GACzBC,KAAK4f,UAAU7f,OAJnB,CAUA,GAAIC,KAAKye,eAAiBvZ,EAAYlF,KAAKue,SAAS,IAAMve,KAAKue,SAAS,GAAK,EAG3E,OAFAve,KAAKye,cAAgB,UACrBze,KAAK6f,SAIP,IAAK,IAAIhhB,EAAImB,KAAKue,SAASxf,OAAQF,KACVmB,KAAKye,gBAAkBze,KAAKwe,SAAS3f,IACxDqG,GAAalF,KAAKue,SAAS1f,UACM,IAAzBmB,KAAKue,SAAS1f,EAAI,IAAsBqG,EAAYlF,KAAKue,SAAS1f,EAAI,KAGhFmB,KAAK4f,UAAU5f,KAAKwe,SAAS3f,KAKnC+gB,UAAU7f,GACRC,KAAKye,cAAgB1e,EAErBC,KAAK6f,SAEL,MAAMC,EAAU9f,KAAKiN,UAAU5V,MAAM,KAClCsZ,IAAI3Z,GAAa,GAAEA,qBAA4B+I,OAAY/I,WAAkB+I,OAE1EggB,EAAOva,EAAeK,QAAQia,EAAQE,KAAK,MAE7CD,EAAKjlB,UAAUC,SAjMU,kBAkM3ByK,EAAeK,QAzLY,mBAyLsBka,EAAK3c,QA1LlC,cA2LjBtI,UAAUuP,IAlMO,UAoMpB0V,EAAKjlB,UAAUuP,IApMK,YAuMpB0V,EAAKjlB,UAAUuP,IAvMK,UAyMpB7E,EAAeS,QAAQ8Z,EAtMG,qBAuMvBxmB,QAAQ0mB,IAGPza,EAAeY,KAAK6Z,EAAY,+BAC7B1mB,QAAQ8lB,GAAQA,EAAKvkB,UAAUuP,IA9MlB,WAiNhB7E,EAAeY,KAAK6Z,EA5MH,aA6Md1mB,QAAQ2mB,IACP1a,EAAeM,SAASoa,EA/MX,aAgNV3mB,QAAQ8lB,GAAQA,EAAKvkB,UAAUuP,IApNtB,gBAyNtBnK,EAAamB,QAAQrB,KAAKse,eA9NN,wBA8NsC,CACxDvT,cAAehL,IAInB8f,SACEra,EAAeC,KAAKzF,KAAKiN,WACtBvI,OAAOyb,GAAQA,EAAKrlB,UAAUC,SAhOX,WAiOnBxB,QAAQ4mB,GAAQA,EAAKrlB,UAAU2C,OAjOZ,WAsOFkF,uBAACxJ,GACrB,OAAO6G,KAAKuD,MAAK,WACf,IAAIC,EAAO3G,EAAKM,IAAI6C,KA7PT,gBAoQX,GAJKwD,IACHA,EAAO,IAAI6a,GAAUre,KAHW,iBAAX7G,GAAuBA,IAMxB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjBqK,EAAKrK,GACd,MAAM,IAAIe,UAAW,oBAAmBf,MAG1CqK,EAAKrK,UAYb+G,EAAaQ,GAAG7I,OAnQa,6BAmQgB,KAC3C2N,EAAeC,KA/PS,0BAgQrBlM,QAAQ6mB,GAAO,IAAI/B,GAAU+B,EAAKjc,EAAYI,kBAAkB6b,OAUrEpkB,EAlSa,YAkSYqiB,ICpQzB,MAAMgC,WAAY/d,EAGGG,sBACjB,MAjCa,SAsCf6K,OACE,GAAKtN,KAAKwC,SAASlI,YACjB0F,KAAKwC,SAASlI,WAAW9B,WAAaoC,KAAKC,cAC3CmF,KAAKwC,SAAS1H,UAAUC,SA9BJ,WA+BpBJ,EAAWqF,KAAKwC,UAChB,OAGF,IAAI6D,EACJ,MAAMtG,EAAStI,EAAuBuI,KAAKwC,UACrC8d,EAActgB,KAAKwC,SAASY,QAhCN,qBAkC5B,GAAIkd,EAAa,CACf,MAAMC,EAAwC,OAAzBD,EAAYlK,UAA8C,OAAzBkK,EAAYlK,SAjC7C,wBADH,UAmClB/P,EAAWb,EAAeC,KAAK8a,EAAcD,GAC7Cja,EAAWA,EAASA,EAAStH,OAAS,GAGxC,MAAMyhB,EAAYna,EAChBnG,EAAamB,QAAQgF,EArDP,cAqD6B,CACzC0E,cAAe/K,KAAKwC,WAEtB,KAMF,GAJkBtC,EAAamB,QAAQrB,KAAKwC,SAxD5B,cAwDkD,CAChEuI,cAAe1E,IAGH1E,kBAAmC,OAAd6e,GAAsBA,EAAU7e,iBACjE,OAGF3B,KAAK4f,UAAU5f,KAAKwC,SAAU8d,GAE9B,MAAM7D,EAAW,KACfvc,EAAamB,QAAQgF,EApEL,gBAoE6B,CAC3C0E,cAAe/K,KAAKwC,WAEtBtC,EAAamB,QAAQrB,KAAKwC,SArEX,eAqEkC,CAC/CuI,cAAe1E,KAIftG,EACFC,KAAK4f,UAAU7f,EAAQA,EAAOzF,WAAYmiB,GAE1CA,IAMJmD,UAAU7oB,EAAS0W,EAAWtR,GAC5B,MAIMskB,IAJiBhT,GAAqC,OAAvBA,EAAU2I,UAA4C,OAAvB3I,EAAU2I,SAE5E5Q,EAAeM,SAAS2H,EA5EN,WA2ElBjI,EAAeC,KA1EM,wBA0EmBgI,IAGZ,GACxBS,EAAkB/R,GAAaskB,GAAUA,EAAO3lB,UAAUC,SApF5C,QAsFd0hB,EAAW,IAAMzc,KAAK0gB,oBAAoB3pB,EAAS0pB,EAAQtkB,GAEjE,GAAIskB,GAAUvS,EAAiB,CAC7B,MAAMvW,EAAqBD,EAAiC+oB,GAC5DA,EAAO3lB,UAAU2C,OAzFC,QA2FlByC,EAAaS,IAAI8f,EAAQ,gBAAiBhE,GAC1ChkB,EAAqBgoB,EAAQ9oB,QAE7B8kB,IAIJiE,oBAAoB3pB,EAAS0pB,EAAQtkB,GACnC,GAAIskB,EAAQ,CACVA,EAAO3lB,UAAU2C,OAtGG,UAwGpB,MAAMkjB,EAAgBnb,EAAeK,QA9FJ,kCA8F4C4a,EAAOnmB,YAEhFqmB,GACFA,EAAc7lB,UAAU2C,OA3GN,UA8GgB,QAAhCgjB,EAAOxpB,aAAa,SACtBwpB,EAAO5c,aAAa,iBAAiB,GAIzC9M,EAAQ+D,UAAUuP,IAnHI,UAoHe,QAAjCtT,EAAQE,aAAa,SACvBF,EAAQ8M,aAAa,iBAAiB,GAGxCpI,EAAO1E,GAEHA,EAAQ+D,UAAUC,SAzHF,SA0HlBhE,EAAQ+D,UAAUuP,IAzHA,QA4HhBtT,EAAQuD,YAAcvD,EAAQuD,WAAWQ,UAAUC,SA/H1B,mBAgIHhE,EAAQqM,QA3HZ,cA8HlBoC,EAAeC,KAzHU,oBA0HtBlM,QAAQqnB,GAAYA,EAAS9lB,UAAUuP,IAnIxB,WAsIpBtT,EAAQ8M,aAAa,iBAAiB,IAGpC1H,GACFA,IAMkBwG,uBAACxJ,GACrB,OAAO6G,KAAKuD,MAAK,WACf,MAAMC,EAAO3G,EAAKM,IAAI6C,KA7JX,WA6J8B,IAAIqgB,GAAIrgB,MAEjD,GAAsB,iBAAX7G,EAAqB,CAC9B,QAA4B,IAAjBqK,EAAKrK,GACd,MAAM,IAAIe,UAAW,oBAAmBf,MAG1CqK,EAAKrK,UAYb+G,EAAaQ,GAAG9J,SAxKc,wBAWD,4EA6JyC,SAAUoI,GAC9EA,EAAMqD,kBAEOxF,EAAKM,IAAI6C,KAnLP,WAmL0B,IAAIqgB,GAAIrgB,OAC5CsN,UAUPtR,EA/La,MA+LYqkB,IChMzB,MAeMrZ,GAAc,CAClBuQ,UAAW,UACXsJ,SAAU,UACVnJ,MAAO,UAGHjR,GAAU,CACd8Q,WAAW,EACXsJ,UAAU,EACVnJ,MAAO,KAWT,MAAMoJ,WAAcxe,EAClBC,YAAYxL,EAASoC,GACnBmO,MAAMvQ,GAENiJ,KAAK+H,QAAU/H,KAAKgI,WAAW7O,GAC/B6G,KAAK8a,SAAW,KAChB9a,KAAKkb,gBAKelU,yBACpB,OAAOA,GAGSP,qBAChB,OAAOA,GAGUhE,sBACjB,MAtDa,WA2Df6K,OAGE,GAFkBpN,EAAamB,QAAQrB,KAAKwC,SAtD5B,iBAwDFb,iBACZ,OAGF3B,KAAK+gB,gBAED/gB,KAAK+H,QAAQwP,WACfvX,KAAKwC,SAAS1H,UAAUuP,IA5DN,QA+DpB,MAAMoS,EAAW,KACfzc,KAAKwC,SAAS1H,UAAU2C,OA7DH,WA8DrBuC,KAAKwC,SAAS1H,UAAUuP,IA/DN,QAiElBnK,EAAamB,QAAQrB,KAAKwC,SArEX,kBAuEXxC,KAAK+H,QAAQ8Y,WACf7gB,KAAK8a,SAAW9hB,WAAW,KACzBgH,KAAKqN,QACJrN,KAAK+H,QAAQ2P,SAOpB,GAHA1X,KAAKwC,SAAS1H,UAAU2C,OA3EJ,QA4EpBhC,EAAOuE,KAAKwC,UACZxC,KAAKwC,SAAS1H,UAAUuP,IA3ED,WA4EnBrK,KAAK+H,QAAQwP,UAAW,CAC1B,MAAM5f,EAAqBD,EAAiCsI,KAAKwC,UAEjEtC,EAAaS,IAAIX,KAAKwC,SAAU,gBAAiBia,GACjDhkB,EAAqBuH,KAAKwC,SAAU7K,QAEpC8kB,IAIJpP,OACE,IAAKrN,KAAKwC,SAAS1H,UAAUC,SAxFT,QAyFlB,OAKF,GAFkBmF,EAAamB,QAAQrB,KAAKwC,SAnG5B,iBAqGFb,iBACZ,OAGF,MAAM8a,EAAW,KACfzc,KAAKwC,SAAS1H,UAAUuP,IApGN,QAqGlBnK,EAAamB,QAAQrB,KAAKwC,SA1GV,oBA8GlB,GADAxC,KAAKwC,SAAS1H,UAAU2C,OAvGJ,QAwGhBuC,KAAK+H,QAAQwP,UAAW,CAC1B,MAAM5f,EAAqBD,EAAiCsI,KAAKwC,UAEjEtC,EAAaS,IAAIX,KAAKwC,SAAU,gBAAiBia,GACjDhkB,EAAqBuH,KAAKwC,SAAU7K,QAEpC8kB,IAIJ/Z,UACE1C,KAAK+gB,gBAED/gB,KAAKwC,SAAS1H,UAAUC,SArHR,SAsHlBiF,KAAKwC,SAAS1H,UAAU2C,OAtHN,QAyHpByC,EAAaC,IAAIH,KAAKwC,SAjIG,0BAmIzB8E,MAAM5E,UACN1C,KAAK+H,QAAU,KAKjBC,WAAW7O,GAST,OARAA,EAAS,IACJsN,MACAtC,EAAYI,kBAAkBvE,KAAKwC,aAChB,iBAAXrJ,GAAuBA,EAASA,EAAS,IAGtDF,EApJS,QAoJaE,EAAQ6G,KAAKuC,YAAYyE,aAExC7N,EAGT+hB,gBACEhb,EAAaQ,GAAGV,KAAKwC,SAtJI,yBAuBC,4BA+HiD,IAAMxC,KAAKqN,QAGxF0T,gBACE7W,aAAalK,KAAK8a,UAClB9a,KAAK8a,SAAW,KAKInY,uBAACxJ,GACrB,OAAO6G,KAAKuD,MAAK,WACf,IAAIC,EAAO3G,EAAKM,IAAI6C,KArKT,YA4KX,GAJKwD,IACHA,EAAO,IAAIsd,GAAM9gB,KAHe,iBAAX7G,GAAuBA,IAMxB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjBqK,EAAKrK,GACd,MAAM,IAAIe,UAAW,oBAAmBf,MAG1CqK,EAAKrK,GAAQ6G,kBAarBhE,EA/La,QA+LY8kB,ICpMV,CACbje,MAAAA,EACAc,OAAAA,EACA0D,SAAAA,EACAoF,SAAAA,EACAyC,SAAAA,GACA0C,MAAAA,GACAyD,UAAAA,GACA6I,QAAAA,GACAG,UAAAA,GACAgC,IAAAA,GACAS,MAAAA,GACAlG,QAAAA", "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttr = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttr || (!hrefAttr.includes('#') && !hrefAttr.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttr.includes('#') && !hrefAttr.startsWith('#')) {\n      hrefAttr = '#' + hrefAttr.split('#')[1]\n    }\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => (obj[0] || obj).nodeType\n\nconst emulateTransitionEnd = (element, duration) => {\n  let called = false\n  const durationPadding = 5\n  const emulatedDuration = duration + durationPadding\n\n  function listener() {\n    called = true\n    element.removeEventListener(TRANSITION_END, listener)\n  }\n\n  element.addEventListener(TRANSITION_END, listener)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(element)\n    }\n  }, emulatedDuration)\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes).forEach(property => {\n    const expectedTypes = configTypes[property]\n    const value = config[property]\n    const valueType = value && isElement(value) ? 'element' : toType(value)\n\n    if (!new RegExp(expectedTypes).test(valueType)) {\n      throw new TypeError(\n        `${componentName.toUpperCase()}: ` +\n        `Option \"${property}\" provided type \"${valueType}\" ` +\n        `but expected type \"${expectedTypes}\".`\n      )\n    }\n  })\n}\n\nconst isVisible = element => {\n  if (!element) {\n    return false\n  }\n\n  if (element.style && element.parentNode && element.parentNode.style) {\n    const elementStyle = getComputedStyle(element)\n    const parentNodeStyle = getComputedStyle(element.parentNode)\n\n    return elementStyle.display !== 'none' &&\n      parentNodeStyle.display !== 'none' &&\n      elementStyle.visibility !== 'hidden'\n  }\n\n  return false\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => function () {}\n\nconst reflow = element => element.offsetHeight\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    document.addEventListener('DOMContentLoaded', callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = (name, plugin) => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nexport {\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  emulateTransitionEnd,\n  typeCheckConfig,\n  isVisible,\n  isDisabled,\n  findShadowRoot,\n  noop,\n  reflow,\n  getjQuery,\n  onDOMContentLoaded,\n  isRTL,\n  defineJQueryPlugin\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst elementMap = new Map()\n\nexport default {\n  set(element, key, instance) {\n    if (!elementMap.has(element)) {\n      elementMap.set(element, new Map())\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    // make it clear we only want one instance per element\n    // can be removed later when multiple key/instances are fine to be used\n    if (!instanceMap.has(key) && instanceMap.size !== 0) {\n      // eslint-disable-next-line no-console\n      console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(instanceMap.keys())[0]}.`)\n      return\n    }\n\n    instanceMap.set(key, instance)\n  },\n\n  get(element, key) {\n    if (elementMap.has(element)) {\n      return elementMap.get(element).get(key) || null\n    }\n\n    return null\n  },\n\n  remove(element, key) {\n    if (!elementMap.has(element)) {\n      return\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    instanceMap.delete(key)\n\n    // free up element references if there are no instances left for an element\n    if (instanceMap.size === 0) {\n      elementMap.delete(element)\n    }\n  }\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\nconst nativeEvents = new Set([\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n])\n\n/**\n * ------------------------------------------------------------------------\n * Private methods\n * ------------------------------------------------------------------------\n */\n\nfunction getUidEvent(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getEvent(element) {\n  const uid = getUidEvent(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    event.delegateTarget = element\n\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (let i = domElements.length; i--;) {\n        if (domElements[i] === target) {\n          event.delegateTarget = target\n\n          if (handler.oneOff) {\n            // eslint-disable-next-line unicorn/consistent-destructuring\n            EventHandler.off(element, event.type, fn)\n          }\n\n          return fn.apply(target, [event])\n        }\n      }\n    }\n\n    // To please ESLint\n    return null\n  }\n}\n\nfunction findHandler(events, handler, delegationSelector = null) {\n  const uidEventList = Object.keys(events)\n\n  for (let i = 0, len = uidEventList.length; i < len; i++) {\n    const event = events[uidEventList[i]]\n\n    if (event.originalHandler === handler && event.delegationSelector === delegationSelector) {\n      return event\n    }\n  }\n\n  return null\n}\n\nfunction normalizeParams(originalTypeEvent, handler, delegationFn) {\n  const delegation = typeof handler === 'string'\n  const originalHandler = delegation ? delegationFn : handler\n\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  let typeEvent = originalTypeEvent.replace(stripNameRegex, '')\n  const custom = customEvents[typeEvent]\n\n  if (custom) {\n    typeEvent = custom\n  }\n\n  const isNative = nativeEvents.has(typeEvent)\n\n  if (!isNative) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [delegation, originalHandler, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFn, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  if (!handler) {\n    handler = delegationFn\n    delegationFn = null\n  }\n\n  const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n  const events = getEvent(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFn = findHandler(handlers, originalHandler, delegation ? handler : null)\n\n  if (previousFn) {\n    previousFn.oneOff = previousFn.oneOff && oneOff\n\n    return\n  }\n\n  const uid = getUidEvent(originalHandler, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = delegation ?\n    bootstrapDelegationHandler(element, handler, delegationFn) :\n    bootstrapHandler(element, handler)\n\n  fn.delegationSelector = delegation ? handler : null\n  fn.originalHandler = originalHandler\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, delegation)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  Object.keys(storeElementEvent).forEach(handlerKey => {\n    if (handlerKey.includes(namespace)) {\n      const event = storeElementEvent[handlerKey]\n\n      removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n    }\n  })\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, false)\n  },\n\n  one(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFn) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getEvent(element)\n    const isNamespace = originalTypeEvent.startsWith('.')\n\n    if (typeof originalHandler !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!events || !events[typeEvent]) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, originalHandler, delegation ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      Object.keys(events).forEach(elementEvent => {\n        removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n      })\n    }\n\n    const storeElementEvent = events[typeEvent] || {}\n    Object.keys(storeElementEvent).forEach(keyHandlers => {\n      const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n      if (!inNamespace || originalTypeEvent.includes(handlerKey)) {\n        const event = storeElementEvent[keyHandlers]\n\n        removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n      }\n    })\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const $ = getjQuery()\n    const typeEvent = event.replace(stripNameRegex, '')\n    const inNamespace = event !== typeEvent\n    const isNative = nativeEvents.has(typeEvent)\n\n    let jQueryEvent\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n    let evt = null\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    if (isNative) {\n      evt = document.createEvent('HTMLEvents')\n      evt.initEvent(typeEvent, bubbles, true)\n    } else {\n      evt = new CustomEvent(event, {\n        bubbles,\n        cancelable: true\n      })\n    }\n\n    // merge custom information in our event\n    if (typeof args !== 'undefined') {\n      Object.keys(args).forEach(key => {\n        Object.defineProperty(evt, key, {\n          get() {\n            return args[key]\n          }\n        })\n      })\n    }\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && typeof jQueryEvent !== 'undefined') {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nexport default EventHandler\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): base-component.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Data from './dom/data'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst VERSION = '5.0.0-beta3'\n\nclass BaseComponent {\n  constructor(element) {\n    element = typeof element === 'string' ? document.querySelector(element) : element\n\n    if (!element) {\n      return\n    }\n\n    this._element = element\n    Data.set(this._element, this.constructor.DATA_KEY, this)\n  }\n\n  dispose() {\n    Data.remove(this._element, this.constructor.DATA_KEY)\n    this._element = null\n  }\n\n  /** Static */\n\n  static getInstance(element) {\n    return Data.get(element, this.DATA_KEY)\n  }\n\n  static get VERSION() {\n    return VERSION\n  }\n}\n\nexport default BaseComponent\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'alert'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst SELECTOR_DISMISS = '[data-bs-dismiss=\"alert\"]'\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_ALERT = 'alert'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Alert extends BaseComponent {\n  // Getters\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  close(element) {\n    const rootElement = element ? this._getRootElement(element) : this._element\n    const customEvent = this._triggerCloseEvent(rootElement)\n\n    if (customEvent === null || customEvent.defaultPrevented) {\n      return\n    }\n\n    this._removeElement(rootElement)\n  }\n\n  // Private\n\n  _getRootElement(element) {\n    return getElementFromSelector(element) || element.closest(`.${CLASS_NAME_ALERT}`)\n  }\n\n  _triggerCloseEvent(element) {\n    return EventHandler.trigger(element, EVENT_CLOSE)\n  }\n\n  _removeElement(element) {\n    element.classList.remove(CLASS_NAME_SHOW)\n\n    if (!element.classList.contains(CLASS_NAME_FADE)) {\n      this._destroyElement(element)\n      return\n    }\n\n    const transitionDuration = getTransitionDurationFromElement(element)\n\n    EventHandler.one(element, 'transitionend', () => this._destroyElement(element))\n    emulateTransitionEnd(element, transitionDuration)\n  }\n\n  _destroyElement(element) {\n    if (element.parentNode) {\n      element.parentNode.removeChild(element)\n    }\n\n    EventHandler.trigger(element, EVENT_CLOSED)\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n\n      if (!data) {\n        data = new Alert(this)\n      }\n\n      if (config === 'close') {\n        data[config](this)\n      }\n    })\n  }\n\n  static handleDismiss(alertInstance) {\n    return function (event) {\n      if (event) {\n        event.preventDefault()\n      }\n\n      alertInstance.close(this)\n    }\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DISMISS, Alert.handleDismiss(new Alert()))\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Alert to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Alert)\n\nexport default Alert\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'button'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"button\"]'\n\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Button extends BaseComponent {\n  // Getters\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n\n      if (!data) {\n        data = new Button(this)\n      }\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, event => {\n  event.preventDefault()\n\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE)\n\n  let data = Data.get(button, DATA_KEY)\n  if (!data) {\n    data = new Button(button)\n  }\n\n  data.toggle()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Button to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Button)\n\nexport default Button\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(val) {\n  if (val === 'true') {\n    return true\n  }\n\n  if (val === 'false') {\n    return false\n  }\n\n  if (val === Number(val).toString()) {\n    return Number(val)\n  }\n\n  if (val === '' || val === 'null') {\n    return null\n  }\n\n  return val\n}\n\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.to<PERSON><PERSON><PERSON><PERSON>ase()}`)\n}\n\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-bs-${normalizeDataKey(key)}`, value)\n  },\n\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-bs-${normalizeDataKey(key)}`)\n  },\n\n  getDataAttributes(element) {\n    if (!element) {\n      return {}\n    }\n\n    const attributes = {}\n\n    Object.keys(element.dataset)\n      .filter(key => key.startsWith('bs'))\n      .forEach(key => {\n        let pureKey = key.replace(/^bs/, '')\n        pureKey = pureKey.charAt(0).toLowerCase() + pureKey.slice(1, pureKey.length)\n        attributes[pureKey] = normalizeData(element.dataset[key])\n      })\n\n    return attributes\n  },\n\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-bs-${normalizeDataKey(key)}`))\n  },\n\n  offset(element) {\n    const rect = element.getBoundingClientRect()\n\n    return {\n      top: rect.top + document.body.scrollTop,\n      left: rect.left + document.body.scrollLeft\n    }\n  },\n\n  position(element) {\n    return {\n      top: element.offsetTop,\n      left: element.offsetLeft\n    }\n  }\n}\n\nexport default Manipulator\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NODE_TEXT = 3\n\nconst SelectorEngine = {\n  find(selector, element = document.documentElement) {\n    return [].concat(...Element.prototype.querySelectorAll.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return Element.prototype.querySelector.call(element, selector)\n  },\n\n  children(element, selector) {\n    return [].concat(...element.children)\n      .filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n\n    let ancestor = element.parentNode\n\n    while (ancestor && ancestor.nodeType === Node.ELEMENT_NODE && ancestor.nodeType !== NODE_TEXT) {\n      if (ancestor.matches(selector)) {\n        parents.push(ancestor)\n      }\n\n      ancestor = ancestor.parentNode\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (next.matches(selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  }\n}\n\nexport default SelectorEngine\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isRTL,\n  isVisible,\n  reflow,\n  triggerTransitionEnd,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'carousel'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  slide: false,\n  pause: 'hover',\n  wrap: true,\n  touch: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)',\n  keyboard: 'boolean',\n  slide: '(boolean|string)',\n  pause: '(string|boolean)',\n  wrap: 'boolean',\n  touch: 'boolean'\n}\n\nconst ORDER_NEXT = 'next'\nconst ORDER_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_END = 'carousel-item-end'\nconst CLASS_NAME_START = 'carousel-item-start'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_ITEM = '.active.carousel-item'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_NEXT_PREV = '.carousel-item-next, .carousel-item-prev'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_INDICATOR = '[data-bs-target]'\nconst SELECTOR_DATA_SLIDE = '[data-bs-slide], [data-bs-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-bs-ride=\"carousel\"]'\n\nconst POINTER_TYPE_TOUCH = 'touch'\nconst POINTER_TYPE_PEN = 'pen'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\nclass Carousel extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._items = null\n    this._interval = null\n    this._activeElement = null\n    this._isPaused = false\n    this._isSliding = false\n    this.touchTimeout = null\n    this.touchStartX = 0\n    this.touchDeltaX = 0\n\n    this._config = this._getConfig(config)\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element)\n    this._touchSupported = 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n    this._pointerEvent = Boolean(window.PointerEvent)\n\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  next() {\n    if (!this._isSliding) {\n      this._slide(ORDER_NEXT)\n    }\n  }\n\n  nextWhenVisible() {\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next()\n    }\n  }\n\n  prev() {\n    if (!this._isSliding) {\n      this._slide(ORDER_PREV)\n    }\n  }\n\n  pause(event) {\n    if (!event) {\n      this._isPaused = true\n    }\n\n    if (SelectorEngine.findOne(SELECTOR_NEXT_PREV, this._element)) {\n      triggerTransitionEnd(this._element)\n      this.cycle(true)\n    }\n\n    clearInterval(this._interval)\n    this._interval = null\n  }\n\n  cycle(event) {\n    if (!event) {\n      this._isPaused = false\n    }\n\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n\n    if (this._config && this._config.interval && !this._isPaused) {\n      this._updateInterval()\n\n      this._interval = setInterval(\n        (document.visibilityState ? this.nextWhenVisible : this.next).bind(this),\n        this._config.interval\n      )\n    }\n  }\n\n  to(index) {\n    this._activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeIndex = this._getItemIndex(this._activeElement)\n\n    if (index > this._items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    if (activeIndex === index) {\n      this.pause()\n      this.cycle()\n      return\n    }\n\n    const order = index > activeIndex ?\n      ORDER_NEXT :\n      ORDER_PREV\n\n    this._slide(order, this._items[index])\n  }\n\n  dispose() {\n    EventHandler.off(this._element, EVENT_KEY)\n\n    this._items = null\n    this._config = null\n    this._interval = null\n    this._isPaused = null\n    this._isSliding = null\n    this._activeElement = null\n    this._indicatorsElement = null\n\n    super.dispose()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _handleSwipe() {\n    const absDeltax = Math.abs(this.touchDeltaX)\n\n    if (absDeltax <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltax / this.touchDeltaX\n\n    this.touchDeltaX = 0\n\n    if (!direction) {\n      return\n    }\n\n    this._slide(direction > 0 ? DIRECTION_RIGHT : DIRECTION_LEFT)\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      EventHandler.on(this._element, EVENT_MOUSEENTER, event => this.pause(event))\n      EventHandler.on(this._element, EVENT_MOUSELEAVE, event => this.cycle(event))\n    }\n\n    if (this._config.touch && this._touchSupported) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    const start = event => {\n      if (this._pointerEvent && (event.pointerType === POINTER_TYPE_PEN || event.pointerType === POINTER_TYPE_TOUCH)) {\n        this.touchStartX = event.clientX\n      } else if (!this._pointerEvent) {\n        this.touchStartX = event.touches[0].clientX\n      }\n    }\n\n    const move = event => {\n      // ensure swiping with one touch and not pinching\n      this.touchDeltaX = event.touches && event.touches.length > 1 ?\n        0 :\n        event.touches[0].clientX - this.touchStartX\n    }\n\n    const end = event => {\n      if (this._pointerEvent && (event.pointerType === POINTER_TYPE_PEN || event.pointerType === POINTER_TYPE_TOUCH)) {\n        this.touchDeltaX = event.clientX - this.touchStartX\n      }\n\n      this._handleSwipe()\n      if (this._config.pause === 'hover') {\n        // If it's a touch-enabled device, mouseenter/leave are fired as\n        // part of the mouse compatibility events on first tap - the carousel\n        // would stop cycling until user tapped out of it;\n        // here, we listen for touchend, explicitly pause the carousel\n        // (as if it's the second time we tap on it, mouseenter compat event\n        // is NOT fired) and after a timeout (to allow for mouse compatibility\n        // events to fire) we explicitly restart cycling\n\n        this.pause()\n        if (this.touchTimeout) {\n          clearTimeout(this.touchTimeout)\n        }\n\n        this.touchTimeout = setTimeout(event => this.cycle(event), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n      }\n    }\n\n    SelectorEngine.find(SELECTOR_ITEM_IMG, this._element).forEach(itemImg => {\n      EventHandler.on(itemImg, EVENT_DRAG_START, e => e.preventDefault())\n    })\n\n    if (this._pointerEvent) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => start(event))\n      EventHandler.on(this._element, EVENT_POINTERUP, event => end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => start(event))\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => move(event))\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => end(event))\n    }\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    if (event.key === ARROW_LEFT_KEY) {\n      event.preventDefault()\n      this._slide(DIRECTION_LEFT)\n    } else if (event.key === ARROW_RIGHT_KEY) {\n      event.preventDefault()\n      this._slide(DIRECTION_RIGHT)\n    }\n  }\n\n  _getItemIndex(element) {\n    this._items = element && element.parentNode ?\n      SelectorEngine.find(SELECTOR_ITEM, element.parentNode) :\n      []\n\n    return this._items.indexOf(element)\n  }\n\n  _getItemByOrder(order, activeElement) {\n    const isNext = order === ORDER_NEXT\n    const isPrev = order === ORDER_PREV\n    const activeIndex = this._getItemIndex(activeElement)\n    const lastItemIndex = this._items.length - 1\n    const isGoingToWrap = (isPrev && activeIndex === 0) || (isNext && activeIndex === lastItemIndex)\n\n    if (isGoingToWrap && !this._config.wrap) {\n      return activeElement\n    }\n\n    const delta = isPrev ? -1 : 1\n    const itemIndex = (activeIndex + delta) % this._items.length\n\n    return itemIndex === -1 ?\n      this._items[this._items.length - 1] :\n      this._items[itemIndex]\n  }\n\n  _triggerSlideEvent(relatedTarget, eventDirectionName) {\n    const targetIndex = this._getItemIndex(relatedTarget)\n    const fromIndex = this._getItemIndex(SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element))\n\n    return EventHandler.trigger(this._element, EVENT_SLIDE, {\n      relatedTarget,\n      direction: eventDirectionName,\n      from: fromIndex,\n      to: targetIndex\n    })\n  }\n\n  _setActiveIndicatorElement(element) {\n    if (this._indicatorsElement) {\n      const activeIndicator = SelectorEngine.findOne(SELECTOR_ACTIVE, this._indicatorsElement)\n\n      activeIndicator.classList.remove(CLASS_NAME_ACTIVE)\n      activeIndicator.removeAttribute('aria-current')\n\n      const indicators = SelectorEngine.find(SELECTOR_INDICATOR, this._indicatorsElement)\n\n      for (let i = 0; i < indicators.length; i++) {\n        if (Number.parseInt(indicators[i].getAttribute('data-bs-slide-to'), 10) === this._getItemIndex(element)) {\n          indicators[i].classList.add(CLASS_NAME_ACTIVE)\n          indicators[i].setAttribute('aria-current', 'true')\n          break\n        }\n      }\n    }\n  }\n\n  _updateInterval() {\n    const element = this._activeElement || SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n\n    if (!element) {\n      return\n    }\n\n    const elementInterval = Number.parseInt(element.getAttribute('data-bs-interval'), 10)\n\n    if (elementInterval) {\n      this._config.defaultInterval = this._config.defaultInterval || this._config.interval\n      this._config.interval = elementInterval\n    } else {\n      this._config.interval = this._config.defaultInterval || this._config.interval\n    }\n  }\n\n  _slide(directionOrOrder, element) {\n    const order = this._directionToOrder(directionOrOrder)\n    const activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeElementIndex = this._getItemIndex(activeElement)\n    const nextElement = element || this._getItemByOrder(order, activeElement)\n\n    const nextElementIndex = this._getItemIndex(nextElement)\n    const isCycling = Boolean(this._interval)\n\n    const isNext = order === ORDER_NEXT\n    const directionalClassName = isNext ? CLASS_NAME_START : CLASS_NAME_END\n    const orderClassName = isNext ? CLASS_NAME_NEXT : CLASS_NAME_PREV\n    const eventDirectionName = this._orderToDirection(order)\n\n    if (nextElement && nextElement.classList.contains(CLASS_NAME_ACTIVE)) {\n      this._isSliding = false\n      return\n    }\n\n    const slideEvent = this._triggerSlideEvent(nextElement, eventDirectionName)\n    if (slideEvent.defaultPrevented) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      return\n    }\n\n    this._isSliding = true\n\n    if (isCycling) {\n      this.pause()\n    }\n\n    this._setActiveIndicatorElement(nextElement)\n    this._activeElement = nextElement\n\n    if (this._element.classList.contains(CLASS_NAME_SLIDE)) {\n      nextElement.classList.add(orderClassName)\n\n      reflow(nextElement)\n\n      activeElement.classList.add(directionalClassName)\n      nextElement.classList.add(directionalClassName)\n\n      const transitionDuration = getTransitionDurationFromElement(activeElement)\n\n      EventHandler.one(activeElement, 'transitionend', () => {\n        nextElement.classList.remove(directionalClassName, orderClassName)\n        nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n        activeElement.classList.remove(CLASS_NAME_ACTIVE, orderClassName, directionalClassName)\n\n        this._isSliding = false\n\n        setTimeout(() => {\n          EventHandler.trigger(this._element, EVENT_SLID, {\n            relatedTarget: nextElement,\n            direction: eventDirectionName,\n            from: activeElementIndex,\n            to: nextElementIndex\n          })\n        }, 0)\n      })\n\n      emulateTransitionEnd(activeElement, transitionDuration)\n    } else {\n      activeElement.classList.remove(CLASS_NAME_ACTIVE)\n      nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n      this._isSliding = false\n      EventHandler.trigger(this._element, EVENT_SLID, {\n        relatedTarget: nextElement,\n        direction: eventDirectionName,\n        from: activeElementIndex,\n        to: nextElementIndex\n      })\n    }\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  _directionToOrder(direction) {\n    if (![DIRECTION_RIGHT, DIRECTION_LEFT].includes(direction)) {\n      return direction\n    }\n\n    if (isRTL()) {\n      return direction === DIRECTION_RIGHT ? ORDER_PREV : ORDER_NEXT\n    }\n\n    return direction === DIRECTION_RIGHT ? ORDER_NEXT : ORDER_PREV\n  }\n\n  _orderToDirection(order) {\n    if (![ORDER_NEXT, ORDER_PREV].includes(order)) {\n      return order\n    }\n\n    if (isRTL()) {\n      return order === ORDER_NEXT ? DIRECTION_LEFT : DIRECTION_RIGHT\n    }\n\n    return order === ORDER_NEXT ? DIRECTION_RIGHT : DIRECTION_LEFT\n  }\n\n  // Static\n\n  static carouselInterface(element, config) {\n    let data = Data.get(element, DATA_KEY)\n    let _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element)\n    }\n\n    if (typeof config === 'object') {\n      _config = {\n        ..._config,\n        ...config\n      }\n    }\n\n    const action = typeof config === 'string' ? config : _config.slide\n\n    if (!data) {\n      data = new Carousel(element, _config)\n    }\n\n    if (typeof config === 'number') {\n      data.to(config)\n    } else if (typeof action === 'string') {\n      if (typeof data[action] === 'undefined') {\n        throw new TypeError(`No method named \"${action}\"`)\n      }\n\n      data[action]()\n    } else if (_config.interval && _config.ride) {\n      data.pause()\n      data.cycle()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Carousel.carouselInterface(this, config)\n    })\n  }\n\n  static dataApiClickHandler(event) {\n    const target = getElementFromSelector(this)\n\n    if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n      return\n    }\n\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n    const slideIndex = this.getAttribute('data-bs-slide-to')\n\n    if (slideIndex) {\n      config.interval = false\n    }\n\n    Carousel.carouselInterface(target, config)\n\n    if (slideIndex) {\n      Data.get(target, DATA_KEY).to(slideIndex)\n    }\n\n    event.preventDefault()\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, Carousel.dataApiClickHandler)\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE)\n\n  for (let i = 0, len = carousels.length; i < len; i++) {\n    Carousel.carouselInterface(carousels[i], Data.get(carousels[i], DATA_KEY))\n  }\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Carousel to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Carousel)\n\nexport default Carousel\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  emulateTransitionEnd,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isElement,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'collapse'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  toggle: true,\n  parent: ''\n}\n\nconst DefaultType = {\n  toggle: 'boolean',\n  parent: '(string|element)'\n}\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.show, .collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"collapse\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Collapse extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._isTransitioning = false\n    this._config = this._getConfig(config)\n    this._triggerArray = SelectorEngine.find(\n      `${SELECTOR_DATA_TOGGLE}[href=\"#${this._element.id}\"],` +\n      `${SELECTOR_DATA_TOGGLE}[data-bs-target=\"#${this._element.id}\"]`\n    )\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggleList.length; i < len; i++) {\n      const elem = toggleList[i]\n      const selector = getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElem => foundElem === this._element)\n\n      if (selector !== null && filterElement.length) {\n        this._selector = selector\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._parent = this._config.parent ? this._getParent() : null\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._element, this._triggerArray)\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning || this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    let actives\n    let activesData\n\n    if (this._parent) {\n      actives = SelectorEngine.find(SELECTOR_ACTIVES, this._parent)\n        .filter(elem => {\n          if (typeof this._config.parent === 'string') {\n            return elem.getAttribute('data-bs-parent') === this._config.parent\n          }\n\n          return elem.classList.contains(CLASS_NAME_COLLAPSE)\n        })\n\n      if (actives.length === 0) {\n        actives = null\n      }\n    }\n\n    const container = SelectorEngine.findOne(this._selector)\n    if (actives) {\n      const tempActiveData = actives.find(elem => container !== elem)\n      activesData = tempActiveData ? Data.get(tempActiveData, DATA_KEY) : null\n\n      if (activesData && activesData._isTransitioning) {\n        return\n      }\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    if (actives) {\n      actives.forEach(elemActive => {\n        if (container !== elemActive) {\n          Collapse.collapseInterface(elemActive, 'hide')\n        }\n\n        if (!activesData) {\n          Data.set(elemActive, DATA_KEY, null)\n        }\n      })\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    if (this._triggerArray.length) {\n      this._triggerArray.forEach(element => {\n        element.classList.remove(CLASS_NAME_COLLAPSED)\n        element.setAttribute('aria-expanded', true)\n      })\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      this.setTransitioning(false)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n    const transitionDuration = getTransitionDurationFromElement(this._element)\n\n    EventHandler.one(this._element, 'transitionend', complete)\n\n    emulateTransitionEnd(this._element, transitionDuration)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning || !this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    const triggerArrayLength = this._triggerArray.length\n    if (triggerArrayLength > 0) {\n      for (let i = 0; i < triggerArrayLength; i++) {\n        const trigger = this._triggerArray[i]\n        const elem = getElementFromSelector(trigger)\n\n        if (elem && !elem.classList.contains(CLASS_NAME_SHOW)) {\n          trigger.classList.add(CLASS_NAME_COLLAPSED)\n          trigger.setAttribute('aria-expanded', false)\n        }\n      }\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this.setTransitioning(false)\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n    const transitionDuration = getTransitionDurationFromElement(this._element)\n\n    EventHandler.one(this._element, 'transitionend', complete)\n    emulateTransitionEnd(this._element, transitionDuration)\n  }\n\n  setTransitioning(isTransitioning) {\n    this._isTransitioning = isTransitioning\n  }\n\n  dispose() {\n    super.dispose()\n    this._config = null\n    this._parent = null\n    this._triggerArray = null\n    this._isTransitioning = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _getDimension() {\n    return this._element.classList.contains(WIDTH) ? WIDTH : HEIGHT\n  }\n\n  _getParent() {\n    let { parent } = this._config\n\n    if (isElement(parent)) {\n      // it's a jQuery object\n      if (typeof parent.jquery !== 'undefined' || typeof parent[0] !== 'undefined') {\n        parent = parent[0]\n      }\n    } else {\n      parent = SelectorEngine.findOne(parent)\n    }\n\n    const selector = `${SELECTOR_DATA_TOGGLE}[data-bs-parent=\"${parent}\"]`\n\n    SelectorEngine.find(selector, parent)\n      .forEach(element => {\n        const selected = getElementFromSelector(element)\n\n        this._addAriaAndCollapsedClass(\n          selected,\n          [element]\n        )\n      })\n\n    return parent\n  }\n\n  _addAriaAndCollapsedClass(element, triggerArray) {\n    if (!element || !triggerArray.length) {\n      return\n    }\n\n    const isOpen = element.classList.contains(CLASS_NAME_SHOW)\n\n    triggerArray.forEach(elem => {\n      if (isOpen) {\n        elem.classList.remove(CLASS_NAME_COLLAPSED)\n      } else {\n        elem.classList.add(CLASS_NAME_COLLAPSED)\n      }\n\n      elem.setAttribute('aria-expanded', isOpen)\n    })\n  }\n\n  // Static\n\n  static collapseInterface(element, config) {\n    let data = Data.get(element, DATA_KEY)\n    const _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (!data && _config.toggle && typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false\n    }\n\n    if (!data) {\n      data = new Collapse(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Collapse.collapseInterface(this, config)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A' || (event.delegateTarget && event.delegateTarget.tagName === 'A')) {\n    event.preventDefault()\n  }\n\n  const triggerData = Manipulator.getDataAttributes(this)\n  const selector = getSelectorFromElement(this)\n  const selectorElements = SelectorEngine.find(selector)\n\n  selectorElements.forEach(element => {\n    const data = Data.get(element, DATA_KEY)\n    let config\n    if (data) {\n      // update parent attribute\n      if (data._parent === null && typeof triggerData.parent === 'string') {\n        data._config.parent = triggerData.parent\n        data._parent = data._getParent()\n      }\n\n      config = 'toggle'\n    } else {\n      config = triggerData\n    }\n\n    Collapse.collapseInterface(element, config)\n  })\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Collapse to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Collapse)\n\nexport default Collapse\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isElement,\n  isVisible,\n  isRTL,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'dropdown'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst SPACE_KEY = 'Space'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst REGEXP_KEYDOWN = new RegExp(`${ARROW_UP_KEY}|${ARROW_DOWN_KEY}|${ESCAPE_KEY}`)\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DISABLED = 'disabled'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPEND = 'dropend'\nconst CLASS_NAME_DROPSTART = 'dropstart'\nconst CLASS_NAME_NAVBAR = 'navbar'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"dropdown\"]'\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = isRTL() ? 'top-end' : 'top-start'\nconst PLACEMENT_TOPEND = isRTL() ? 'top-start' : 'top-end'\nconst PLACEMENT_BOTTOM = isRTL() ? 'bottom-end' : 'bottom-start'\nconst PLACEMENT_BOTTOMEND = isRTL() ? 'bottom-start' : 'bottom-end'\nconst PLACEMENT_RIGHT = isRTL() ? 'left-start' : 'right-start'\nconst PLACEMENT_LEFT = isRTL() ? 'right-start' : 'left-start'\n\nconst Default = {\n  offset: [0, 2],\n  boundary: 'clippingParents',\n  reference: 'toggle',\n  display: 'dynamic',\n  popperConfig: null\n}\n\nconst DefaultType = {\n  offset: '(array|string|function)',\n  boundary: '(string|element)',\n  reference: '(string|element|object)',\n  display: 'string',\n  popperConfig: '(null|object|function)'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Dropdown extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._popper = null\n    this._config = this._getConfig(config)\n    this._menu = this._getMenuElement()\n    this._inNavbar = this._detectNavbar()\n\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const isActive = this._element.classList.contains(CLASS_NAME_SHOW)\n\n    Dropdown.clearMenus()\n\n    if (isActive) {\n      return\n    }\n\n    this.show()\n  }\n\n  show() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED) || this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    // Totally disable Popper for Dropdowns in Navbar\n    if (this._inNavbar) {\n      Manipulator.setDataAttribute(this._menu, 'popper', 'none')\n    } else {\n      if (typeof Popper === 'undefined') {\n        throw new TypeError('Bootstrap\\'s dropdowns require Popper (https://popper.js.org)')\n      }\n\n      let referenceElement = this._element\n\n      if (this._config.reference === 'parent') {\n        referenceElement = parent\n      } else if (isElement(this._config.reference)) {\n        referenceElement = this._config.reference\n\n        // Check if it's jQuery element\n        if (typeof this._config.reference.jquery !== 'undefined') {\n          referenceElement = this._config.reference[0]\n        }\n      } else if (typeof this._config.reference === 'object') {\n        referenceElement = this._config.reference\n      }\n\n      const popperConfig = this._getPopperConfig()\n      const isDisplayStatic = popperConfig.modifiers.find(modifier => modifier.name === 'applyStyles' && modifier.enabled === false)\n\n      this._popper = Popper.createPopper(referenceElement, this._menu, popperConfig)\n\n      if (isDisplayStatic) {\n        Manipulator.setDataAttribute(this._menu, 'popper', 'static')\n      }\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement &&\n      !parent.closest(SELECTOR_NAVBAR_NAV)) {\n      [].concat(...document.body.children)\n        .forEach(elem => EventHandler.on(elem, 'mouseover', null, noop()))\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    this._menu.classList.toggle(CLASS_NAME_SHOW)\n    this._element.classList.toggle(CLASS_NAME_SHOW)\n    EventHandler.trigger(this._element, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED) || !this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE, relatedTarget)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._menu.classList.toggle(CLASS_NAME_SHOW)\n    this._element.classList.toggle(CLASS_NAME_SHOW)\n    Manipulator.removeDataAttribute(this._menu, 'popper')\n    EventHandler.trigger(this._element, EVENT_HIDDEN, relatedTarget)\n  }\n\n  dispose() {\n    EventHandler.off(this._element, EVENT_KEY)\n    this._menu = null\n\n    if (this._popper) {\n      this._popper.destroy()\n      this._popper = null\n    }\n\n    super.dispose()\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Private\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_CLICK, event => {\n      event.preventDefault()\n      this.toggle()\n    })\n  }\n\n  _getConfig(config) {\n    config = {\n      ...this.constructor.Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...config\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    if (typeof config.reference === 'object' && !isElement(config.reference) &&\n      typeof config.reference.getBoundingClientRect !== 'function'\n    ) {\n      // Popper virtual elements require a getBoundingClientRect method\n      throw new TypeError(`${NAME.toUpperCase()}: Option \"reference\" provided type \"object\" without a required \"getBoundingClientRect\" method.`)\n    }\n\n    return config\n  }\n\n  _getMenuElement() {\n    return SelectorEngine.next(this._element, SELECTOR_MENU)[0]\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._element.parentNode\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPEND)) {\n      return PLACEMENT_RIGHT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPSTART)) {\n      return PLACEMENT_LEFT\n    }\n\n    // We need to trim the value because custom properties can also include spaces\n    const isEnd = getComputedStyle(this._menu).getPropertyValue('--bs-position').trim() === 'end'\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      return isEnd ? PLACEMENT_TOPEND : PLACEMENT_TOP\n    }\n\n    return isEnd ? PLACEMENT_BOTTOMEND : PLACEMENT_BOTTOM\n  }\n\n  _detectNavbar() {\n    return this._element.closest(`.${CLASS_NAME_NAVBAR}`) !== null\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(val => Number.parseInt(val, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const defaultBsPopperConfig = {\n      placement: this._getPlacement(),\n      modifiers: [{\n        name: 'preventOverflow',\n        options: {\n          boundary: this._config.boundary\n        }\n      },\n      {\n        name: 'offset',\n        options: {\n          offset: this._getOffset()\n        }\n      }]\n    }\n\n    // Disable Popper if we have a static display\n    if (this._config.display === 'static') {\n      defaultBsPopperConfig.modifiers = [{\n        name: 'applyStyles',\n        enabled: false\n      }]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...(typeof this._config.popperConfig === 'function' ? this._config.popperConfig(defaultBsPopperConfig) : this._config.popperConfig)\n    }\n  }\n\n  // Static\n\n  static dropdownInterface(element, config) {\n    let data = Data.get(element, DATA_KEY)\n    const _config = typeof config === 'object' ? config : null\n\n    if (!data) {\n      data = new Dropdown(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Dropdown.dropdownInterface(this, config)\n    })\n  }\n\n  static clearMenus(event) {\n    if (event) {\n      if (event.button === RIGHT_MOUSE_BUTTON || (event.type === 'keyup' && event.key !== TAB_KEY)) {\n        return\n      }\n\n      if (/input|select|textarea|form/i.test(event.target.tagName)) {\n        return\n      }\n    }\n\n    const toggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggles.length; i < len; i++) {\n      const context = Data.get(toggles[i], DATA_KEY)\n      const relatedTarget = {\n        relatedTarget: toggles[i]\n      }\n\n      if (event && event.type === 'click') {\n        relatedTarget.clickEvent = event\n      }\n\n      if (!context) {\n        continue\n      }\n\n      const dropdownMenu = context._menu\n      if (!toggles[i].classList.contains(CLASS_NAME_SHOW)) {\n        continue\n      }\n\n      if (event) {\n        // Don't close the menu if the clicked element or one of its parents is the dropdown button\n        if ([context._element].some(element => event.composedPath().includes(element))) {\n          continue\n        }\n\n        // Tab navigation through the dropdown menu shouldn't close the menu\n        if (event.type === 'keyup' && event.key === TAB_KEY && dropdownMenu.contains(event.target)) {\n          continue\n        }\n      }\n\n      const hideEvent = EventHandler.trigger(toggles[i], EVENT_HIDE, relatedTarget)\n      if (hideEvent.defaultPrevented) {\n        continue\n      }\n\n      // If this is a touch-enabled device we remove the extra\n      // empty mouseover listeners we added for iOS support\n      if ('ontouchstart' in document.documentElement) {\n        [].concat(...document.body.children)\n          .forEach(elem => EventHandler.off(elem, 'mouseover', null, noop()))\n      }\n\n      toggles[i].setAttribute('aria-expanded', 'false')\n\n      if (context._popper) {\n        context._popper.destroy()\n      }\n\n      dropdownMenu.classList.remove(CLASS_NAME_SHOW)\n      toggles[i].classList.remove(CLASS_NAME_SHOW)\n      Manipulator.removeDataAttribute(dropdownMenu, 'popper')\n      EventHandler.trigger(toggles[i], EVENT_HIDDEN, relatedTarget)\n    }\n  }\n\n  static getParentFromElement(element) {\n    return getElementFromSelector(element) || element.parentNode\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not input/textarea:\n    //  - And not a key in REGEXP_KEYDOWN => not a dropdown command\n    // If input/textarea:\n    //  - If space key => not a dropdown command\n    //  - If key is other than escape\n    //    - If key is not up or down => not a dropdown command\n    //    - If trigger inside the menu => not a dropdown command\n    if (/input|textarea/i.test(event.target.tagName) ?\n      event.key === SPACE_KEY || (event.key !== ESCAPE_KEY &&\n      ((event.key !== ARROW_DOWN_KEY && event.key !== ARROW_UP_KEY) ||\n        event.target.closest(SELECTOR_MENU))) :\n      !REGEXP_KEYDOWN.test(event.key)) {\n      return\n    }\n\n    event.preventDefault()\n    event.stopPropagation()\n\n    if (this.disabled || this.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this)\n    const isActive = this.classList.contains(CLASS_NAME_SHOW)\n\n    if (event.key === ESCAPE_KEY) {\n      const button = this.matches(SELECTOR_DATA_TOGGLE) ? this : SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0]\n      button.focus()\n      Dropdown.clearMenus()\n      return\n    }\n\n    if (!isActive && (event.key === ARROW_UP_KEY || event.key === ARROW_DOWN_KEY)) {\n      const button = this.matches(SELECTOR_DATA_TOGGLE) ? this : SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0]\n      button.click()\n      return\n    }\n\n    if (!isActive || event.key === SPACE_KEY) {\n      Dropdown.clearMenus()\n      return\n    }\n\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, parent).filter(isVisible)\n\n    if (!items.length) {\n      return\n    }\n\n    let index = items.indexOf(event.target)\n\n    // Up\n    if (event.key === ARROW_UP_KEY && index > 0) {\n      index--\n    }\n\n    // Down\n    if (event.key === ARROW_DOWN_KEY && index < items.length - 1) {\n      index++\n    }\n\n    // index is -1 if the first keydown is an ArrowUp\n    index = index === -1 ? 0 : index\n\n    items[index].focus()\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  Dropdown.dropdownInterface(this)\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Dropdown to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Dropdown)\n\nexport default Dropdown\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isVisible,\n  isRTL,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'modal'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  focus: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  focus: 'boolean'\n}\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEUP_DISMISS = `mouseup.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SCROLLBAR_MEASURER = 'modal-scrollbar-measure'\nconst CLASS_NAME_BACKDROP = 'modal-backdrop'\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"modal\"]'\nconst SELECTOR_DATA_DISMISS = '[data-bs-dismiss=\"modal\"]'\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Modal extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, this._element)\n    this._backdrop = null\n    this._isShown = false\n    this._isBodyOverflowing = false\n    this._ignoreBackdropClick = false\n    this._isTransitioning = false\n    this._scrollbarWidth = 0\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    if (this._isAnimated()) {\n      this._isTransitioning = true\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (this._isShown || showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n\n    this._checkScrollbar()\n    this._setScrollbar()\n\n    this._adjustDialog()\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, event => this.hide(event))\n\n    EventHandler.on(this._dialog, EVENT_MOUSEDOWN_DISMISS, () => {\n      EventHandler.one(this._element, EVENT_MOUSEUP_DISMISS, event => {\n        if (event.target === this._element) {\n          this._ignoreBackdropClick = true\n        }\n      })\n    })\n\n    this._showBackdrop(() => this._showElement(relatedTarget))\n  }\n\n  hide(event) {\n    if (event) {\n      event.preventDefault()\n    }\n\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    const isAnimated = this._isAnimated()\n\n    if (isAnimated) {\n      this._isTransitioning = true\n    }\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n    EventHandler.off(this._dialog, EVENT_MOUSEDOWN_DISMISS)\n\n    if (isAnimated) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, 'transitionend', event => this._hideModal(event))\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      this._hideModal()\n    }\n  }\n\n  dispose() {\n    [window, this._element, this._dialog]\n      .forEach(htmlElement => EventHandler.off(htmlElement, EVENT_KEY))\n\n    super.dispose()\n\n    /**\n     * `document` has 2 events `EVENT_FOCUSIN` and `EVENT_CLICK_DATA_API`\n     * Do not move `document` in `htmlElements` array\n     * It will remove `EVENT_CLICK_DATA_API` event that should remain\n     */\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    this._config = null\n    this._dialog = null\n    this._backdrop = null\n    this._isShown = null\n    this._isBodyOverflowing = null\n    this._ignoreBackdropClick = null\n    this._isTransitioning = null\n    this._scrollbarWidth = null\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _showElement(relatedTarget) {\n    const isAnimated = this._isAnimated()\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n\n    if (!this._element.parentNode || this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {\n      // Don't move modal's DOM position\n      document.body.appendChild(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    if (isAnimated) {\n      reflow(this._element)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    if (this._config.focus) {\n      this._enforceFocus()\n    }\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._element.focus()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    if (isAnimated) {\n      const transitionDuration = getTransitionDurationFromElement(this._dialog)\n\n      EventHandler.one(this._dialog, 'transitionend', transitionComplete)\n      emulateTransitionEnd(this._dialog, transitionDuration)\n    } else {\n      transitionComplete()\n    }\n  }\n\n  _enforceFocus() {\n    EventHandler.off(document, EVENT_FOCUSIN) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => {\n      if (document !== event.target &&\n          this._element !== event.target &&\n          !this._element.contains(event.target)) {\n        this._element.focus()\n      }\n    })\n  }\n\n  _setEscapeEvent() {\n    if (this._isShown) {\n      EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n        if (this._config.keyboard && event.key === ESCAPE_KEY) {\n          event.preventDefault()\n          this.hide()\n        } else if (!this._config.keyboard && event.key === ESCAPE_KEY) {\n          this._triggerBackdropTransition()\n        }\n      })\n    } else {\n      EventHandler.off(this._element, EVENT_KEYDOWN_DISMISS)\n    }\n  }\n\n  _setResizeEvent() {\n    if (this._isShown) {\n      EventHandler.on(window, EVENT_RESIZE, () => this._adjustDialog())\n    } else {\n      EventHandler.off(window, EVENT_RESIZE)\n    }\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n    this._showBackdrop(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._resetScrollbar()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _removeBackdrop() {\n    this._backdrop.parentNode.removeChild(this._backdrop)\n    this._backdrop = null\n  }\n\n  _showBackdrop(callback) {\n    const isAnimated = this._isAnimated()\n    if (this._isShown && this._config.backdrop) {\n      this._backdrop = document.createElement('div')\n      this._backdrop.className = CLASS_NAME_BACKDROP\n\n      if (isAnimated) {\n        this._backdrop.classList.add(CLASS_NAME_FADE)\n      }\n\n      document.body.appendChild(this._backdrop)\n\n      EventHandler.on(this._element, EVENT_CLICK_DISMISS, event => {\n        if (this._ignoreBackdropClick) {\n          this._ignoreBackdropClick = false\n          return\n        }\n\n        if (event.target !== event.currentTarget) {\n          return\n        }\n\n        if (this._config.backdrop === 'static') {\n          this._triggerBackdropTransition()\n        } else {\n          this.hide()\n        }\n      })\n\n      if (isAnimated) {\n        reflow(this._backdrop)\n      }\n\n      this._backdrop.classList.add(CLASS_NAME_SHOW)\n\n      if (!isAnimated) {\n        callback()\n        return\n      }\n\n      const backdropTransitionDuration = getTransitionDurationFromElement(this._backdrop)\n\n      EventHandler.one(this._backdrop, 'transitionend', callback)\n      emulateTransitionEnd(this._backdrop, backdropTransitionDuration)\n    } else if (!this._isShown && this._backdrop) {\n      this._backdrop.classList.remove(CLASS_NAME_SHOW)\n\n      const callbackRemove = () => {\n        this._removeBackdrop()\n        callback()\n      }\n\n      if (isAnimated) {\n        const backdropTransitionDuration = getTransitionDurationFromElement(this._backdrop)\n        EventHandler.one(this._backdrop, 'transitionend', callbackRemove)\n        emulateTransitionEnd(this._backdrop, backdropTransitionDuration)\n      } else {\n        callbackRemove()\n      }\n    } else {\n      callback()\n    }\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_FADE)\n  }\n\n  _triggerBackdropTransition() {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n\n    if (!isModalOverflowing) {\n      this._element.style.overflowY = 'hidden'\n    }\n\n    this._element.classList.add(CLASS_NAME_STATIC)\n    const modalTransitionDuration = getTransitionDurationFromElement(this._dialog)\n    EventHandler.off(this._element, 'transitionend')\n    EventHandler.one(this._element, 'transitionend', () => {\n      this._element.classList.remove(CLASS_NAME_STATIC)\n      if (!isModalOverflowing) {\n        EventHandler.one(this._element, 'transitionend', () => {\n          this._element.style.overflowY = ''\n        })\n        emulateTransitionEnd(this._element, modalTransitionDuration)\n      }\n    })\n    emulateTransitionEnd(this._element, modalTransitionDuration)\n    this._element.focus()\n  }\n\n  // ----------------------------------------------------------------------\n  // the following methods are used to handle overflowing modals\n  // ----------------------------------------------------------------------\n\n  _adjustDialog() {\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n\n    if ((!this._isBodyOverflowing && isModalOverflowing && !isRTL()) || (this._isBodyOverflowing && !isModalOverflowing && isRTL())) {\n      this._element.style.paddingLeft = `${this._scrollbarWidth}px`\n    }\n\n    if ((this._isBodyOverflowing && !isModalOverflowing && !isRTL()) || (!this._isBodyOverflowing && isModalOverflowing && isRTL())) {\n      this._element.style.paddingRight = `${this._scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  _checkScrollbar() {\n    const rect = document.body.getBoundingClientRect()\n    this._isBodyOverflowing = Math.round(rect.left + rect.right) < window.innerWidth\n    this._scrollbarWidth = this._getScrollbarWidth()\n  }\n\n  _setScrollbar() {\n    if (this._isBodyOverflowing) {\n      this._setElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight', calculatedValue => calculatedValue + this._scrollbarWidth)\n      this._setElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight', calculatedValue => calculatedValue - this._scrollbarWidth)\n      this._setElementAttributes('body', 'paddingRight', calculatedValue => calculatedValue + this._scrollbarWidth)\n    }\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n  }\n\n  _setElementAttributes(selector, styleProp, callback) {\n    SelectorEngine.find(selector)\n      .forEach(element => {\n        if (element !== document.body && window.innerWidth > element.clientWidth + this._scrollbarWidth) {\n          return\n        }\n\n        const actualValue = element.style[styleProp]\n        const calculatedValue = window.getComputedStyle(element)[styleProp]\n        Manipulator.setDataAttribute(element, styleProp, actualValue)\n        element.style[styleProp] = callback(Number.parseFloat(calculatedValue)) + 'px'\n      })\n  }\n\n  _resetScrollbar() {\n    this._resetElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight')\n    this._resetElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight')\n    this._resetElementAttributes('body', 'paddingRight')\n  }\n\n  _resetElementAttributes(selector, styleProp) {\n    SelectorEngine.find(selector).forEach(element => {\n      const value = Manipulator.getDataAttribute(element, styleProp)\n      if (typeof value === 'undefined' && element === document.body) {\n        element.style[styleProp] = ''\n      } else {\n        Manipulator.removeDataAttribute(element, styleProp)\n        element.style[styleProp] = value\n      }\n    })\n  }\n\n  _getScrollbarWidth() { // thx d.walsh\n    const scrollDiv = document.createElement('div')\n    scrollDiv.className = CLASS_NAME_SCROLLBAR_MEASURER\n    document.body.appendChild(scrollDiv)\n    const scrollbarWidth = scrollDiv.getBoundingClientRect().width - scrollDiv.clientWidth\n    document.body.removeChild(scrollDiv)\n    return scrollbarWidth\n  }\n\n  // Static\n\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n      const _config = {\n        ...Default,\n        ...Manipulator.getDataAttributes(this),\n        ...(typeof config === 'object' && config ? config : {})\n      }\n\n      if (!data) {\n        data = new Modal(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](relatedTarget)\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (this.tagName === 'A' || this.tagName === 'AREA') {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  let data = Data.get(target, DATA_KEY)\n  if (!data) {\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n\n    data = new Modal(target, config)\n  }\n\n  data.toggle(this)\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Modal to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Modal)\n\nexport default Modal\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): util/scrollBar.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport SelectorEngine from '../dom/selector-engine'\nimport Manipulator from '../dom/manipulator'\n\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\n\nconst getWidth = () => {\n  // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n  const documentWidth = document.documentElement.clientWidth\n  return Math.abs(window.innerWidth - documentWidth)\n}\n\nconst hide = (width = getWidth()) => {\n  document.body.style.overflow = 'hidden'\n  _setElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight', calculatedValue => calculatedValue + width)\n  _setElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight', calculatedValue => calculatedValue - width)\n  _setElementAttributes('body', 'paddingRight', calculatedValue => calculatedValue + width)\n}\n\nconst _setElementAttributes = (selector, styleProp, callback) => {\n  const scrollbarWidth = getWidth()\n  SelectorEngine.find(selector)\n    .forEach(element => {\n      if (element !== document.body && window.innerWidth > element.clientWidth + scrollbarWidth) {\n        return\n      }\n\n      const actualValue = element.style[styleProp]\n      const calculatedValue = window.getComputedStyle(element)[styleProp]\n      Manipulator.setDataAttribute(element, styleProp, actualValue)\n      element.style[styleProp] = callback(Number.parseFloat(calculatedValue)) + 'px'\n    })\n}\n\nconst reset = () => {\n  document.body.style.overflow = 'auto'\n  _resetElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight')\n  _resetElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight')\n  _resetElementAttributes('body', 'paddingRight')\n}\n\nconst _resetElementAttributes = (selector, styleProp) => {\n  SelectorEngine.find(selector).forEach(element => {\n    const value = Manipulator.getDataAttribute(element, styleProp)\n    if (typeof value === 'undefined' && element === document.body) {\n      element.style.removeProperty(styleProp)\n    } else {\n      Manipulator.removeDataAttribute(element, styleProp)\n      element.style[styleProp] = value\n    }\n  })\n}\n\nconst isBodyOverflowing = () => {\n  return getWidth() > 0\n}\n\nexport {\n  getWidth,\n  hide,\n  isBodyOverflowing,\n  reset\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): offcanvas.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  getSelectorFromElement,\n  getTransitionDurationFromElement,\n  isDisabled,\n  isVisible,\n  typeCheckConfig\n} from './util/index'\nimport { hide as scrollBarHide, reset as scrollBarReset } from './util/scrollbar'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\nimport SelectorEngine from './dom/selector-engine'\nimport Manipulator from './dom/manipulator'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'offcanvas'\nconst DATA_KEY = 'bs.offcanvas'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  scroll: false\n}\n\nconst DefaultType = {\n  backdrop: 'boolean',\n  keyboard: 'boolean',\n  scroll: 'boolean'\n}\n\nconst CLASS_NAME_BACKDROP_BODY = 'offcanvas-backdrop'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_TOGGLING = 'offcanvas-toggling'\nconst OPEN_SELECTOR = '.offcanvas.show'\nconst ACTIVE_SELECTOR = `${OPEN_SELECTOR}, .${CLASS_NAME_TOGGLING}`\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\n\nconst SELECTOR_DATA_DISMISS = '[data-bs-dismiss=\"offcanvas\"]'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"offcanvas\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Offcanvas extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._isShown = false\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, { relatedTarget })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._element.style.visibility = 'visible'\n\n    if (this._config.backdrop) {\n      document.body.classList.add(CLASS_NAME_BACKDROP_BODY)\n    }\n\n    if (!this._config.scroll) {\n      scrollBarHide()\n    }\n\n    this._element.classList.add(CLASS_NAME_TOGGLING)\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    const completeCallBack = () => {\n      this._element.classList.remove(CLASS_NAME_TOGGLING)\n      EventHandler.trigger(this._element, EVENT_SHOWN, { relatedTarget })\n      this._enforceFocusOnElement(this._element)\n    }\n\n    setTimeout(completeCallBack, getTransitionDurationFromElement(this._element))\n  }\n\n  hide() {\n    if (!this._isShown) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._element.classList.add(CLASS_NAME_TOGGLING)\n    EventHandler.off(document, EVENT_FOCUSIN)\n    this._element.blur()\n    this._isShown = false\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    const completeCallback = () => {\n      this._element.setAttribute('aria-hidden', true)\n      this._element.removeAttribute('aria-modal')\n      this._element.removeAttribute('role')\n      this._element.style.visibility = 'hidden'\n\n      if (this._config.backdrop) {\n        document.body.classList.remove(CLASS_NAME_BACKDROP_BODY)\n      }\n\n      if (!this._config.scroll) {\n        scrollBarReset()\n      }\n\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n      this._element.classList.remove(CLASS_NAME_TOGGLING)\n    }\n\n    setTimeout(completeCallback, getTransitionDurationFromElement(this._element))\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' ? config : {})\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _enforceFocusOnElement(element) {\n    EventHandler.off(document, EVENT_FOCUSIN) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => {\n      if (document !== event.target &&\n        element !== event.target &&\n        !element.contains(event.target)) {\n        element.focus()\n      }\n    })\n    element.focus()\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, () => this.hide())\n\n    EventHandler.on(document, 'keydown', event => {\n      if (this._config.keyboard && event.key === ESCAPE_KEY) {\n        this.hide()\n      }\n    })\n\n    EventHandler.on(document, EVENT_CLICK_DATA_API, event => {\n      const target = SelectorEngine.findOne(getSelectorFromElement(event.target))\n      if (!this._element.contains(event.target) && target !== this._element) {\n        this.hide()\n      }\n    })\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Data.get(this, DATA_KEY) || new Offcanvas(this, typeof config === 'object' ? config : {})\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  EventHandler.one(target, EVENT_HIDDEN, () => {\n    // focus on trigger when it is closed\n    if (isVisible(this)) {\n      this.focus()\n    }\n  })\n\n  // avoid conflict when clicking a toggler of an offcanvas, while another is open\n  const allReadyOpen = SelectorEngine.findOne(ACTIVE_SELECTOR)\n  if (allReadyOpen && allReadyOpen !== target) {\n    return\n  }\n\n  const data = Data.get(target, DATA_KEY) || new Offcanvas(target)\n\n  data.toggle(this)\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  SelectorEngine.find(OPEN_SELECTOR).forEach(el => (Data.get(el, DATA_KEY) || new Offcanvas(el)).show())\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\ndefineJQueryPlugin(NAME, Offcanvas)\n\nexport default Offcanvas\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttrs = new Set([\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n])\n\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/i\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[\\d+/a-z]+=*$/i\n\nconst allowedAttribute = (attr, allowedAttributeList) => {\n  const attrName = attr.nodeName.toLowerCase()\n\n  if (allowedAttributeList.includes(attrName)) {\n    if (uriAttrs.has(attrName)) {\n      return Boolean(SAFE_URL_PATTERN.test(attr.nodeValue) || DATA_URL_PATTERN.test(attr.nodeValue))\n    }\n\n    return true\n  }\n\n  const regExp = allowedAttributeList.filter(attrRegex => attrRegex instanceof RegExp)\n\n  // Check if a regular expression validates the attribute.\n  for (let i = 0, len = regExp.length; i < len; i++) {\n    if (regExp[i].test(attrName)) {\n      return true\n    }\n  }\n\n  return false\n}\n\nexport const DefaultAllowlist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n\nexport function sanitizeHtml(unsafeHtml, allowList, sanitizeFn) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFn && typeof sanitizeFn === 'function') {\n    return sanitizeFn(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const allowlistKeys = Object.keys(allowList)\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (let i = 0, len = elements.length; i < len; i++) {\n    const el = elements[i]\n    const elName = el.nodeName.toLowerCase()\n\n    if (!allowlistKeys.includes(elName)) {\n      el.parentNode.removeChild(el)\n\n      continue\n    }\n\n    const attributeList = [].concat(...el.attributes)\n    const allowedAttributes = [].concat(allowList['*'] || [], allowList[elName] || [])\n\n    attributeList.forEach(attr => {\n      if (!allowedAttribute(attr, allowedAttributes)) {\n        el.removeAttribute(attr.nodeName)\n      }\n    })\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\n\nimport {\n  defineJQueryPlugin,\n  emulateTransitionEnd,\n  findShadowRoot,\n  getTransitionDurationFromElement,\n  getUID,\n  isElement,\n  isRTL,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport {\n  DefaultAllowlist,\n  sanitizeHtml\n} from './util/sanitizer'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tooltip'\nconst DATA_KEY = 'bs.tooltip'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-tooltip'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\nconst DISALLOWED_ATTRIBUTES = new Set(['sanitize', 'allowList', 'sanitizeFn'])\n\nconst DefaultType = {\n  animation: 'boolean',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string',\n  delay: '(number|object)',\n  html: 'boolean',\n  selector: '(string|boolean)',\n  placement: '(string|function)',\n  offset: '(array|string|function)',\n  container: '(string|element|boolean)',\n  fallbackPlacements: 'array',\n  boundary: '(string|element)',\n  customClass: '(string|function)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  allowList: 'object',\n  popperConfig: '(null|object|function)'\n}\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: isRTL() ? 'left' : 'right',\n  BOTTOM: 'bottom',\n  LEFT: isRTL() ? 'right' : 'left'\n}\n\nconst Default = {\n  animation: true,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n              '<div class=\"tooltip-arrow\"></div>' +\n              '<div class=\"tooltip-inner\"></div>' +\n            '</div>',\n  trigger: 'hover focus',\n  title: '',\n  delay: 0,\n  html: false,\n  selector: false,\n  placement: 'top',\n  offset: [0, 0],\n  container: false,\n  fallbackPlacements: ['top', 'right', 'bottom', 'left'],\n  boundary: 'clippingParents',\n  customClass: '',\n  sanitize: true,\n  sanitizeFn: null,\n  allowList: DefaultAllowlist,\n  popperConfig: null\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst HOVER_STATE_SHOW = 'show'\nconst HOVER_STATE_OUT = 'out'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tooltip extends BaseComponent {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper (https://popper.js.org)')\n    }\n\n    super(element)\n\n    // private\n    this._isEnabled = true\n    this._timeout = 0\n    this._hoverState = ''\n    this._activeTrigger = {}\n    this._popper = null\n\n    // Protected\n    this.config = this._getConfig(config)\n    this.tip = null\n\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle(event) {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (event) {\n      const context = this._initializeOnDelegatedTarget(event)\n\n      context._activeTrigger.click = !context._activeTrigger.click\n\n      if (context._isWithActiveTrigger()) {\n        context._enter(null, context)\n      } else {\n        context._leave(null, context)\n      }\n    } else {\n      if (this.getTipElement().classList.contains(CLASS_NAME_SHOW)) {\n        this._leave(null, this)\n        return\n      }\n\n      this._enter(null, this)\n    }\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    EventHandler.off(this._element, this.constructor.EVENT_KEY)\n    EventHandler.off(this._element.closest(`.${CLASS_NAME_MODAL}`), 'hide.bs.modal', this._hideModalHandler)\n\n    if (this.tip && this.tip.parentNode) {\n      this.tip.parentNode.removeChild(this.tip)\n    }\n\n    this._isEnabled = null\n    this._timeout = null\n    this._hoverState = null\n    this._activeTrigger = null\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._popper = null\n    this.config = null\n    this.tip = null\n    super.dispose()\n  }\n\n  show() {\n    if (this._element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (!(this.isWithContent() && this._isEnabled)) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, this.constructor.Event.SHOW)\n    const shadowRoot = findShadowRoot(this._element)\n    const isInTheDom = shadowRoot === null ?\n      this._element.ownerDocument.documentElement.contains(this._element) :\n      shadowRoot.contains(this._element)\n\n    if (showEvent.defaultPrevented || !isInTheDom) {\n      return\n    }\n\n    const tip = this.getTipElement()\n    const tipId = getUID(this.constructor.NAME)\n\n    tip.setAttribute('id', tipId)\n    this._element.setAttribute('aria-describedby', tipId)\n\n    this.setContent()\n\n    if (this.config.animation) {\n      tip.classList.add(CLASS_NAME_FADE)\n    }\n\n    const placement = typeof this.config.placement === 'function' ?\n      this.config.placement.call(this, tip, this._element) :\n      this.config.placement\n\n    const attachment = this._getAttachment(placement)\n    this._addAttachmentClass(attachment)\n\n    const container = this._getContainer()\n    Data.set(tip, this.constructor.DATA_KEY, this)\n\n    if (!this._element.ownerDocument.documentElement.contains(this.tip)) {\n      container.appendChild(tip)\n      EventHandler.trigger(this._element, this.constructor.Event.INSERTED)\n    }\n\n    if (this._popper) {\n      this._popper.update()\n    } else {\n      this._popper = Popper.createPopper(this._element, tip, this._getPopperConfig(attachment))\n    }\n\n    tip.classList.add(CLASS_NAME_SHOW)\n\n    const customClass = typeof this.config.customClass === 'function' ? this.config.customClass() : this.config.customClass\n    if (customClass) {\n      tip.classList.add(...customClass.split(' '))\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children).forEach(element => {\n        EventHandler.on(element, 'mouseover', noop())\n      })\n    }\n\n    const complete = () => {\n      const prevHoverState = this._hoverState\n\n      this._hoverState = null\n      EventHandler.trigger(this._element, this.constructor.Event.SHOWN)\n\n      if (prevHoverState === HOVER_STATE_OUT) {\n        this._leave(null, this)\n      }\n    }\n\n    if (this.tip.classList.contains(CLASS_NAME_FADE)) {\n      const transitionDuration = getTransitionDurationFromElement(this.tip)\n      EventHandler.one(this.tip, 'transitionend', complete)\n      emulateTransitionEnd(this.tip, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  hide() {\n    if (!this._popper) {\n      return\n    }\n\n    const tip = this.getTipElement()\n    const complete = () => {\n      if (this._isWithActiveTrigger()) {\n        return\n      }\n\n      if (this._hoverState !== HOVER_STATE_SHOW && tip.parentNode) {\n        tip.parentNode.removeChild(tip)\n      }\n\n      this._cleanTipClass()\n      this._element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this._element, this.constructor.Event.HIDDEN)\n\n      if (this._popper) {\n        this._popper.destroy()\n        this._popper = null\n      }\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, this.constructor.Event.HIDE)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children)\n        .forEach(element => EventHandler.off(element, 'mouseover', noop))\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n\n    if (this.tip.classList.contains(CLASS_NAME_FADE)) {\n      const transitionDuration = getTransitionDurationFromElement(tip)\n\n      EventHandler.one(tip, 'transitionend', complete)\n      emulateTransitionEnd(tip, transitionDuration)\n    } else {\n      complete()\n    }\n\n    this._hoverState = ''\n  }\n\n  update() {\n    if (this._popper !== null) {\n      this._popper.update()\n    }\n  }\n\n  // Protected\n\n  isWithContent() {\n    return Boolean(this.getTitle())\n  }\n\n  getTipElement() {\n    if (this.tip) {\n      return this.tip\n    }\n\n    const element = document.createElement('div')\n    element.innerHTML = this.config.template\n\n    this.tip = element.children[0]\n    return this.tip\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TOOLTIP_INNER, tip), this.getTitle())\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  setElementContent(element, content) {\n    if (element === null) {\n      return\n    }\n\n    if (typeof content === 'object' && isElement(content)) {\n      if (content.jquery) {\n        content = content[0]\n      }\n\n      // content is a DOM node or a jQuery\n      if (this.config.html) {\n        if (content.parentNode !== element) {\n          element.innerHTML = ''\n          element.appendChild(content)\n        }\n      } else {\n        element.textContent = content.textContent\n      }\n\n      return\n    }\n\n    if (this.config.html) {\n      if (this.config.sanitize) {\n        content = sanitizeHtml(content, this.config.allowList, this.config.sanitizeFn)\n      }\n\n      element.innerHTML = content\n    } else {\n      element.textContent = content\n    }\n  }\n\n  getTitle() {\n    let title = this._element.getAttribute('data-bs-original-title')\n\n    if (!title) {\n      title = typeof this.config.title === 'function' ?\n        this.config.title.call(this._element) :\n        this.config.title\n    }\n\n    return title\n  }\n\n  updateAttachment(attachment) {\n    if (attachment === 'right') {\n      return 'end'\n    }\n\n    if (attachment === 'left') {\n      return 'start'\n    }\n\n    return attachment\n  }\n\n  // Private\n\n  _initializeOnDelegatedTarget(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || Data.get(event.delegateTarget, dataKey)\n\n    if (!context) {\n      context = new this.constructor(event.delegateTarget, this._getDelegateConfig())\n      Data.set(event.delegateTarget, dataKey, context)\n    }\n\n    return context\n  }\n\n  _getOffset() {\n    const { offset } = this.config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(val => Number.parseInt(val, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _getPopperConfig(attachment) {\n    const defaultBsPopperConfig = {\n      placement: attachment,\n      modifiers: [\n        {\n          name: 'flip',\n          options: {\n            altBoundary: true,\n            fallbackPlacements: this.config.fallbackPlacements\n          }\n        },\n        {\n          name: 'offset',\n          options: {\n            offset: this._getOffset()\n          }\n        },\n        {\n          name: 'preventOverflow',\n          options: {\n            boundary: this.config.boundary\n          }\n        },\n        {\n          name: 'arrow',\n          options: {\n            element: `.${this.constructor.NAME}-arrow`\n          }\n        },\n        {\n          name: 'onChange',\n          enabled: true,\n          phase: 'afterWrite',\n          fn: data => this._handlePopperPlacementChange(data)\n        }\n      ],\n      onFirstUpdate: data => {\n        if (data.options.placement !== data.placement) {\n          this._handlePopperPlacementChange(data)\n        }\n      }\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...(typeof this.config.popperConfig === 'function' ? this.config.popperConfig(defaultBsPopperConfig) : this.config.popperConfig)\n    }\n  }\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${this.updateAttachment(attachment)}`)\n  }\n\n  _getContainer() {\n    if (this.config.container === false) {\n      return document.body\n    }\n\n    if (isElement(this.config.container)) {\n      return this.config.container\n    }\n\n    return SelectorEngine.findOne(this.config.container)\n  }\n\n  _getAttachment(placement) {\n    return AttachmentMap[placement.toUpperCase()]\n  }\n\n  _setListeners() {\n    const triggers = this.config.trigger.split(' ')\n\n    triggers.forEach(trigger => {\n      if (trigger === 'click') {\n        EventHandler.on(this._element, this.constructor.Event.CLICK, this.config.selector, event => this.toggle(event))\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSEENTER :\n          this.constructor.Event.FOCUSIN\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSELEAVE :\n          this.constructor.Event.FOCUSOUT\n\n        EventHandler.on(this._element, eventIn, this.config.selector, event => this._enter(event))\n        EventHandler.on(this._element, eventOut, this.config.selector, event => this._leave(event))\n      }\n    })\n\n    this._hideModalHandler = () => {\n      if (this._element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this._element.closest(`.${CLASS_NAME_MODAL}`), 'hide.bs.modal', this._hideModalHandler)\n\n    if (this.config.selector) {\n      this.config = {\n        ...this.config,\n        trigger: 'manual',\n        selector: ''\n      }\n    } else {\n      this._fixTitle()\n    }\n  }\n\n  _fixTitle() {\n    const title = this._element.getAttribute('title')\n    const originalTitleType = typeof this._element.getAttribute('data-bs-original-title')\n\n    if (title || originalTitleType !== 'string') {\n      this._element.setAttribute('data-bs-original-title', title || '')\n      if (title && !this._element.getAttribute('aria-label') && !this._element.textContent) {\n        this._element.setAttribute('aria-label', title)\n      }\n\n      this._element.setAttribute('title', '')\n    }\n  }\n\n  _enter(event, context) {\n    context = this._initializeOnDelegatedTarget(event, context)\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = true\n    }\n\n    if (context.getTipElement().classList.contains(CLASS_NAME_SHOW) || context._hoverState === HOVER_STATE_SHOW) {\n      context._hoverState = HOVER_STATE_SHOW\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_SHOW\n\n    if (!context.config.delay || !context.config.delay.show) {\n      context.show()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_SHOW) {\n        context.show()\n      }\n    }, context.config.delay.show)\n  }\n\n  _leave(event, context) {\n    context = this._initializeOnDelegatedTarget(event, context)\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = context._element.contains(event.relatedTarget)\n    }\n\n    if (context._isWithActiveTrigger()) {\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_OUT\n\n    if (!context.config.delay || !context.config.delay.hide) {\n      context.hide()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_OUT) {\n        context.hide()\n      }\n    }, context.config.delay.hide)\n  }\n\n  _isWithActiveTrigger() {\n    for (const trigger in this._activeTrigger) {\n      if (this._activeTrigger[trigger]) {\n        return true\n      }\n    }\n\n    return false\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this._element)\n\n    Object.keys(dataAttributes).forEach(dataAttr => {\n      if (DISALLOWED_ATTRIBUTES.has(dataAttr)) {\n        delete dataAttributes[dataAttr]\n      }\n    })\n\n    if (config && typeof config.container === 'object' && config.container.jquery) {\n      config.container = config.container[0]\n    }\n\n    config = {\n      ...this.constructor.Default,\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    if (config.sanitize) {\n      config.template = sanitizeHtml(config.template, config.allowList, config.sanitizeFn)\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    if (this.config) {\n      for (const key in this.config) {\n        if (this.constructor.Default[key] !== this.config[key]) {\n          config[key] = this.config[key]\n        }\n      }\n    }\n\n    return config\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  _handlePopperPlacementChange(popperData) {\n    const { state } = popperData\n\n    if (!state) {\n      return\n    }\n\n    this.tip = state.elements.popper\n    this._cleanTipClass()\n    this._addAttachmentClass(this._getAttachment(state.placement))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Tooltip(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tooltip to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Tooltip)\n\nexport default Tooltip\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport Data from './dom/data'\nimport SelectorEngine from './dom/selector-engine'\nimport Tooltip from './tooltip'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'popover'\nconst DATA_KEY = 'bs.popover'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-popover'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\n\nconst Default = {\n  ...Tooltip.Default,\n  placement: 'right',\n  offset: [0, 8],\n  trigger: 'click',\n  content: '',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n              '<div class=\"popover-arrow\"></div>' +\n                '<h3 class=\"popover-header\"></h3>' +\n              '<div class=\"popover-body\"></div>' +\n            '</div>'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(string|element|function)'\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Popover extends Tooltip {\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Overrides\n\n  isWithContent() {\n    return this.getTitle() || this._getContent()\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n\n    // we use append for html objects to maintain js events\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TITLE, tip), this.getTitle())\n    let content = this._getContent()\n    if (typeof content === 'function') {\n      content = content.call(this._element)\n    }\n\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_CONTENT, tip), content)\n\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  // Private\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${this.updateAttachment(attachment)}`)\n  }\n\n  _getContent() {\n    return this._element.getAttribute('data-bs-content') || this.config.content\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n      const _config = typeof config === 'object' ? config : null\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Popover(this, _config)\n        Data.set(this, DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Popover to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Popover)\n\nexport default Popover\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getSelectorFromElement,\n  getUID,\n  isElement,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'scrollspy'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  offset: 10,\n  method: 'auto',\n  target: ''\n}\n\nconst DefaultType = {\n  offset: 'number',\n  method: 'string',\n  target: '(string|element)'\n}\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_SCROLL = `scroll${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-bs-spy=\"scroll\"]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst METHOD_OFFSET = 'offset'\nconst METHOD_POSITION = 'position'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass ScrollSpy extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n    this._scrollElement = this._element.tagName === 'BODY' ? window : this._element\n    this._config = this._getConfig(config)\n    this._selector = `${this._config.target} ${SELECTOR_NAV_LINKS}, ${this._config.target} ${SELECTOR_LIST_ITEMS}, ${this._config.target} .${CLASS_NAME_DROPDOWN_ITEM}`\n    this._offsets = []\n    this._targets = []\n    this._activeTarget = null\n    this._scrollHeight = 0\n\n    EventHandler.on(this._scrollElement, EVENT_SCROLL, () => this._process())\n\n    this.refresh()\n    this._process()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  refresh() {\n    const autoMethod = this._scrollElement === this._scrollElement.window ?\n      METHOD_OFFSET :\n      METHOD_POSITION\n\n    const offsetMethod = this._config.method === 'auto' ?\n      autoMethod :\n      this._config.method\n\n    const offsetBase = offsetMethod === METHOD_POSITION ?\n      this._getScrollTop() :\n      0\n\n    this._offsets = []\n    this._targets = []\n    this._scrollHeight = this._getScrollHeight()\n\n    const targets = SelectorEngine.find(this._selector)\n\n    targets.map(element => {\n      const targetSelector = getSelectorFromElement(element)\n      const target = targetSelector ? SelectorEngine.findOne(targetSelector) : null\n\n      if (target) {\n        const targetBCR = target.getBoundingClientRect()\n        if (targetBCR.width || targetBCR.height) {\n          return [\n            Manipulator[offsetMethod](target).top + offsetBase,\n            targetSelector\n          ]\n        }\n      }\n\n      return null\n    })\n      .filter(item => item)\n      .sort((a, b) => a[0] - b[0])\n      .forEach(item => {\n        this._offsets.push(item[0])\n        this._targets.push(item[1])\n      })\n  }\n\n  dispose() {\n    super.dispose()\n    EventHandler.off(this._scrollElement, EVENT_KEY)\n\n    this._scrollElement = null\n    this._config = null\n    this._selector = null\n    this._offsets = null\n    this._targets = null\n    this._activeTarget = null\n    this._scrollHeight = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (typeof config.target !== 'string' && isElement(config.target)) {\n      let { id } = config.target\n      if (!id) {\n        id = getUID(NAME)\n        config.target.id = id\n      }\n\n      config.target = `#${id}`\n    }\n\n    typeCheckConfig(NAME, config, DefaultType)\n\n    return config\n  }\n\n  _getScrollTop() {\n    return this._scrollElement === window ?\n      this._scrollElement.pageYOffset :\n      this._scrollElement.scrollTop\n  }\n\n  _getScrollHeight() {\n    return this._scrollElement.scrollHeight || Math.max(\n      document.body.scrollHeight,\n      document.documentElement.scrollHeight\n    )\n  }\n\n  _getOffsetHeight() {\n    return this._scrollElement === window ?\n      window.innerHeight :\n      this._scrollElement.getBoundingClientRect().height\n  }\n\n  _process() {\n    const scrollTop = this._getScrollTop() + this._config.offset\n    const scrollHeight = this._getScrollHeight()\n    const maxScroll = this._config.offset + scrollHeight - this._getOffsetHeight()\n\n    if (this._scrollHeight !== scrollHeight) {\n      this.refresh()\n    }\n\n    if (scrollTop >= maxScroll) {\n      const target = this._targets[this._targets.length - 1]\n\n      if (this._activeTarget !== target) {\n        this._activate(target)\n      }\n\n      return\n    }\n\n    if (this._activeTarget && scrollTop < this._offsets[0] && this._offsets[0] > 0) {\n      this._activeTarget = null\n      this._clear()\n      return\n    }\n\n    for (let i = this._offsets.length; i--;) {\n      const isActiveTarget = this._activeTarget !== this._targets[i] &&\n          scrollTop >= this._offsets[i] &&\n          (typeof this._offsets[i + 1] === 'undefined' || scrollTop < this._offsets[i + 1])\n\n      if (isActiveTarget) {\n        this._activate(this._targets[i])\n      }\n    }\n  }\n\n  _activate(target) {\n    this._activeTarget = target\n\n    this._clear()\n\n    const queries = this._selector.split(',')\n      .map(selector => `${selector}[data-bs-target=\"${target}\"],${selector}[href=\"${target}\"]`)\n\n    const link = SelectorEngine.findOne(queries.join(','))\n\n    if (link.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE, link.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n\n      link.classList.add(CLASS_NAME_ACTIVE)\n    } else {\n      // Set triggered link as active\n      link.classList.add(CLASS_NAME_ACTIVE)\n\n      SelectorEngine.parents(link, SELECTOR_NAV_LIST_GROUP)\n        .forEach(listGroup => {\n          // Set triggered links parents as active\n          // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n          SelectorEngine.prev(listGroup, `${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`)\n            .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n\n          // Handle special case when .nav-link is inside .nav-item\n          SelectorEngine.prev(listGroup, SELECTOR_NAV_ITEMS)\n            .forEach(navItem => {\n              SelectorEngine.children(navItem, SELECTOR_NAV_LINKS)\n                .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n            })\n        })\n    }\n\n    EventHandler.trigger(this._scrollElement, EVENT_ACTIVATE, {\n      relatedTarget: target\n    })\n  }\n\n  _clear() {\n    SelectorEngine.find(this._selector)\n      .filter(node => node.classList.contains(CLASS_NAME_ACTIVE))\n      .forEach(node => node.classList.remove(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new ScrollSpy(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  SelectorEngine.find(SELECTOR_DATA_SPY)\n    .forEach(spy => new ScrollSpy(spy, Manipulator.getDataAttributes(spy)))\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .ScrollSpy to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, ScrollSpy)\n\nexport default ScrollSpy\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isDisabled,\n  reflow\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tab'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_MENU = 'dropdown-menu'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_UL = ':scope > li > .active'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"tab\"], [data-bs-toggle=\"pill\"], [data-bs-toggle=\"list\"]'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_ACTIVE_CHILD = ':scope > .dropdown-menu .active'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tab extends BaseComponent {\n  // Getters\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  show() {\n    if ((this._element.parentNode &&\n      this._element.parentNode.nodeType === Node.ELEMENT_NODE &&\n      this._element.classList.contains(CLASS_NAME_ACTIVE)) ||\n      isDisabled(this._element)) {\n      return\n    }\n\n    let previous\n    const target = getElementFromSelector(this._element)\n    const listElement = this._element.closest(SELECTOR_NAV_LIST_GROUP)\n\n    if (listElement) {\n      const itemSelector = listElement.nodeName === 'UL' || listElement.nodeName === 'OL' ? SELECTOR_ACTIVE_UL : SELECTOR_ACTIVE\n      previous = SelectorEngine.find(itemSelector, listElement)\n      previous = previous[previous.length - 1]\n    }\n\n    const hideEvent = previous ?\n      EventHandler.trigger(previous, EVENT_HIDE, {\n        relatedTarget: this._element\n      }) :\n      null\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget: previous\n    })\n\n    if (showEvent.defaultPrevented || (hideEvent !== null && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._activate(this._element, listElement)\n\n    const complete = () => {\n      EventHandler.trigger(previous, EVENT_HIDDEN, {\n        relatedTarget: this._element\n      })\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget: previous\n      })\n    }\n\n    if (target) {\n      this._activate(target, target.parentNode, complete)\n    } else {\n      complete()\n    }\n  }\n\n  // Private\n\n  _activate(element, container, callback) {\n    const activeElements = container && (container.nodeName === 'UL' || container.nodeName === 'OL') ?\n      SelectorEngine.find(SELECTOR_ACTIVE_UL, container) :\n      SelectorEngine.children(container, SELECTOR_ACTIVE)\n\n    const active = activeElements[0]\n    const isTransitioning = callback && (active && active.classList.contains(CLASS_NAME_FADE))\n\n    const complete = () => this._transitionComplete(element, active, callback)\n\n    if (active && isTransitioning) {\n      const transitionDuration = getTransitionDurationFromElement(active)\n      active.classList.remove(CLASS_NAME_SHOW)\n\n      EventHandler.one(active, 'transitionend', complete)\n      emulateTransitionEnd(active, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  _transitionComplete(element, active, callback) {\n    if (active) {\n      active.classList.remove(CLASS_NAME_ACTIVE)\n\n      const dropdownChild = SelectorEngine.findOne(SELECTOR_DROPDOWN_ACTIVE_CHILD, active.parentNode)\n\n      if (dropdownChild) {\n        dropdownChild.classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      if (active.getAttribute('role') === 'tab') {\n        active.setAttribute('aria-selected', false)\n      }\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n    if (element.getAttribute('role') === 'tab') {\n      element.setAttribute('aria-selected', true)\n    }\n\n    reflow(element)\n\n    if (element.classList.contains(CLASS_NAME_FADE)) {\n      element.classList.add(CLASS_NAME_SHOW)\n    }\n\n    if (element.parentNode && element.parentNode.classList.contains(CLASS_NAME_DROPDOWN_MENU)) {\n      const dropdownElement = element.closest(SELECTOR_DROPDOWN)\n\n      if (dropdownElement) {\n        SelectorEngine.find(SELECTOR_DROPDOWN_TOGGLE)\n          .forEach(dropdown => dropdown.classList.add(CLASS_NAME_ACTIVE))\n      }\n\n      element.setAttribute('aria-expanded', true)\n    }\n\n    if (callback) {\n      callback()\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Data.get(this, DATA_KEY) || new Tab(this)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n\n  const data = Data.get(this, DATA_KEY) || new Tab(this)\n  data.show()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tab to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Tab)\n\nexport default Tab\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  emulateTransitionEnd,\n  getTransitionDurationFromElement,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'toast'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 5000\n}\n\nconst SELECTOR_DATA_DISMISS = '[data-bs-dismiss=\"toast\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Toast extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._timeout = null\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      this._element.classList.add(CLASS_NAME_SHOW)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      if (this._config.autohide) {\n        this._timeout = setTimeout(() => {\n          this.hide()\n        }, this._config.delay)\n      }\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE)\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    if (this._config.animation) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, 'transitionend', complete)\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  hide() {\n    if (!this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    if (this._config.animation) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, 'transitionend', complete)\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n\n    super.dispose()\n    this._config = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    return config\n  }\n\n  _setListeners() {\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, () => this.hide())\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new Toast(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Toast to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Toast)\n\nexport default Toast\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): index.umd.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport <PERSON><PERSON> from './src/alert'\nimport But<PERSON> from './src/button'\nimport Carousel from './src/carousel'\nimport Collapse from './src/collapse'\nimport Dropdown from './src/dropdown'\nimport Modal from './src/modal'\nimport Offcanvas from './src/offcanvas'\nimport Popover from './src/popover'\nimport ScrollSpy from './src/scrollspy'\nimport Tab from './src/tab'\nimport Toast from './src/toast'\nimport Tooltip from './src/tooltip'\n\nexport default {\n  <PERSON><PERSON>,\n  Button,\n  Carousel,\n  Collapse,\n  Dropdown,\n  Modal,\n  Offcanvas,\n  Popover,\n  ScrollSpy,\n  Tab,\n  Toast,\n  Tooltip\n}\n"]}