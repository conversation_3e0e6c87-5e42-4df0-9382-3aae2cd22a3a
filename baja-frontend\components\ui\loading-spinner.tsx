'use client';

import React from 'react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  text?: string;
  fullScreen?: boolean;
  className?: string;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  size = 'md', 
  text = 'Memuat...', 
  fullScreen = false,
  className = ''
}) => {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-16 h-16',
    lg: 'w-24 h-24',
    xl: 'w-32 h-32'
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl'
  };

  const containerClasses = fullScreen 
    ? 'min-h-screen bg-black flex items-center justify-center'
    : 'flex items-center justify-center py-8';

  return (
    <div className={`${containerClasses} ${className}`}>
      <div className="text-center">
        {/* Main Spinner */}
        <div className={`relative ${sizeClasses[size]} mx-auto mb-4`}>
          {/* Outer ring */}
          <div className="absolute inset-0 border-4 border-gray-800 rounded-full"></div>
          {/* Spinning gold ring */}
          <div className="absolute inset-0 border-4 border-transparent border-t-yellow-400 rounded-full animate-spin"></div>
          {/* Inner spinning ring */}
          <div 
            className="absolute inset-2 border-2 border-transparent border-b-yellow-500 rounded-full animate-spin" 
            style={{ animationDirection: 'reverse', animationDuration: '1.5s' }}
          ></div>
          {/* Center dot */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
          </div>
        </div>
        
        {/* Loading text */}
        {text && (
          <p className={`text-yellow-400 animate-pulse ${textSizeClasses[size]}`}>
            {text}
          </p>
        )}
        
        {/* Loading dots */}
        <div className="flex justify-center space-x-1 mt-2">
          <div 
            className="w-1.5 h-1.5 bg-yellow-400 rounded-full animate-bounce" 
            style={{ animationDelay: '0ms' }}
          ></div>
          <div 
            className="w-1.5 h-1.5 bg-yellow-400 rounded-full animate-bounce" 
            style={{ animationDelay: '150ms' }}
          ></div>
          <div 
            className="w-1.5 h-1.5 bg-yellow-400 rounded-full animate-bounce" 
            style={{ animationDelay: '300ms' }}
          ></div>
        </div>
      </div>
    </div>
  );
};

export default LoadingSpinner;
