'use client';

import React, { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import CountUp from 'react-countup';
import Beams from '@/components/ui/Beams';
import Silk from '@/components/ui/Silk';
import Squares from '@/components/ui/Squares';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import Button from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import PWAInstallButton from '@/components/pwa/PWAInstallButton';
import Link from 'next/link';
import Image from 'next/image';
import {
  TrophyIcon,
  UsersIcon,
  CalendarDaysIcon,
  StarIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import { adminService } from '@/lib/admin';
import { AdminPackage } from '@/types';
import { uploadService } from '@/lib/upload.service';
import PackageFeatures from '@/components/ui/PackageFeatures';
import LoadingSpinner from '@/components/ui/loading-spinner';

// Animated Text Component
const AnimatedTitle = () => {
  const text = "BAJA EVENT ORGANIZER";
  const [displayText, setDisplayText] = useState("");
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isComplete, setIsComplete] = useState(false);

  useEffect(() => {
    if (currentIndex < text.length) {
      const timer = setTimeout(() => {
        setDisplayText(text.slice(0, currentIndex + 1));
        setCurrentIndex(currentIndex + 1);
      }, 100);
      return () => clearTimeout(timer);
    } else {
      setIsComplete(true);
    }
  }, [currentIndex, text]);

  return (
    <div className="relative h-[100px] md:h-[120px] flex items-center justify-center">
      <h1 className="text-4xl md:text-6xl lg:text-7xl font-black text-center relative">
        {/* Main text with gradient and shadow */}
        <span className="bg-gradient-to-r from-yellow-300 via-yellow-400 to-yellow-500 bg-clip-text text-transparent drop-shadow-[0_4px_8px_rgba(0,0,0,0.8)] filter">
          {displayText}
        </span>
        
        {/* Animated cursor */}
        {!isComplete && (
          <span className="animate-pulse text-yellow-400 ml-1">|</span>
        )}
        
        {/* Glowing effect - only show when animation is complete */}
        {isComplete && (
          <span 
            className="absolute inset-0 bg-gradient-to-r from-yellow-300 via-yellow-400 to-yellow-500 bg-clip-text text-transparent opacity-50 blur-sm"
            style={{ zIndex: -1 }}
          >
            {displayText}
          </span>
        )}
        
        {/* Text stroke/outline for better contrast */}
        <span 
          className="absolute inset-0 text-black opacity-60"
          style={{ 
            
            zIndex: -2,
            WebkitTextFillColor: 'transparent'
          }}
        >
          {displayText}
        </span>
      </h1>
    </div>
  );
};

const HomePage = () => {
  const [featuredPackages, setFeaturedPackages] = useState<AdminPackage[]>([]);
  const [popularPackage, setPopularPackage] = useState<AdminPackage | null>(null);
  const [packagesLoading, setPackagesLoading] = useState(true);
  const [mounted, setMounted] = useState(false);

  const features = [
    {
      icon: TrophyIcon,
      title: 'Event Management',
      description: 'Kelola event olahraga bela diri dengan mudah dan efisien'
    },
    {
      icon: UsersIcon,
      title: 'Manajemen Atlet',
      description: 'Daftarkan dan kelola data atlet serta kontingen'
    },
    {
      icon: CalendarDaysIcon,
      title: 'Penjadwalan',
      description: 'Atur jadwal pertandingan dan acara dengan sistematis'
    },
    {
      icon: StarIcon,
      title: 'Sistem Penilaian',
      description: 'Catat hasil pertandingan dan tentukan pemenang'
    }
  ];

  const fallbackPackages = [
    {
      id: 0,
      name: 'Basic',
      description: 'Paket dasar untuk event kecil',
      images: '',
      is_popular: false,
      is_featured: false,
      created_at: '',
      updated_at: ''
    },
    {
      id: 0,
      name: 'Professional',
      description: 'Paket lengkap untuk event menengah',
      images: '',
      is_popular: true,
      is_featured: false,
      created_at: '',
      updated_at: ''
    },
    {
      id: 0,
      name: 'Enterprise',
      description: 'Paket premium untuk event besar',
      images: '',
      is_popular: false,
      is_featured: false,
      created_at: '',
      updated_at: ''
    }
  ];

  useEffect(() => {
    setMounted(true);

    const fetchPackages = async () => {
      try {
        setPackagesLoading(true);
        const [featured, popular] = await Promise.all([
          adminService.getFeaturedPackages().catch(() => []),
          adminService.getPopularPackage().catch(() => null)
        ]);

        const nonPopularFeatured = featured.filter(pkg => pkg.id !== popular?.id);

        setFeaturedPackages(nonPopularFeatured);
        setPopularPackage(popular);
      } catch (error) {
        console.error('Error fetching packages:', error);
        // Use fallback packages if API fails
        setFeaturedPackages(fallbackPackages.slice(0, 2));
        setPopularPackage(fallbackPackages[1]);
      } finally {
        setPackagesLoading(false);
      }
    };

    fetchPackages();
  }, []);

  const stats = [
    { label: 'Event Terselenggara', value: 150, suffix: '+' },
    { label: 'Atlet Terdaftar', value: 5000, suffix: '+' },
    { label: 'Kontingen Aktif', value: 200, suffix: '+' },
    { label: 'Kepuasan Pengguna', value: 98, suffix: '%' }
  ];

  // Show loading spinner until mounted (prevents hydration error)
  if (!mounted) {
    return <LoadingSpinner size="lg" text="Memuat pengalaman terbaik..." fullScreen={true} />;
  }

  return (
    <div className="min-h-screen bg-black">
      <Navbar />
      
      {/* Hero Section */}
      <section className="relative pt-16 bg-gradient-to-br from-black via-gray-900 to-yellow-900 overflow-hidden">
        <div className="absolute inset-0 z-0">
          <Beams
            beamWidth={2}
            beamHeight={15}
            beamNumber={12}
            lightColor="#ffffff"
            speed={2}
            noiseIntensity={1.75}
            scale={0.2}
            rotation={15}
          />
        </div>
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 md:py-16">
          <div className="text-center">
            <AnimatedTitle />

            {/* Silat Image */}
            <div className="flex justify-center mb-8 opacity-0 animate-fade-in-up" style={{ animationDelay: '0.6s', animationFillMode: 'forwards' }}>
              <Image
                src="/silat.png"
                alt="Silat Martial Arts"
                width={200}
                height={200}
                className="object-contain drop-shadow-[0_10px_20px_rgba(250,204,21,0.6)] hover:drop-shadow-[0_30px_45px_rgba(250,204,21,1)] transition-all duration-500 cursor-pointer transform scale-100 hover:scale-110"
                priority
                onLoad={() => console.log('Image loaded')}
              />
            </div>

            <p className="text-lg md:text-xl text-gray-300 mb-8 max-w-3xl mx-auto opacity-0 animate-fade-in-up" style={{ animationDelay: '1s', animationFillMode: 'forwards' }}>
              Platform terpercaya untuk mengelola event olahraga bela diri.
              Sistem informasi yang cerdas dan mudah digunakan untuk semua kebutuhan event organizer Anda.
            </p>
            {/* <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Link href="/auth/register">
                <Button size="md" className="w-full sm:w-auto">
                  Mulai Sekarang
                </Button>
              </Link>
              <Link href="/events">
                <Button variant="outline" size="md" className="w-full sm:w-auto">
                  Lihat Event
                </Button>
              </Link>
            </div> */}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-6 bg-black">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {stats.map((stat, index) => (
              <div key={index} className="text-center opacity-0 animate-fade-in-up" style={{ animationDelay: `${1.4 + index * 0.1}s`, animationFillMode: 'forwards' }}>
                <div className="text-2xl md:text-3xl font-bold text-yellow-400 mb-1">
                  <CountUp start={0} end={stat.value} duration={2} separator="." suffix={stat.suffix} />
                </div>
                <div className="text-sm md:text-base text-gray-300">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section with Squares Background */}
      <section className="relative py-16 overflow-hidden bg-black">
        <div className="absolute inset-0 z-0">
          <Squares
            speed={0.5}
            squareSize={20}
            direction="diagonal"
            borderColor="#1E1E1E"
            hoverFillColor="#121212"
          />
        </div>
        {/* Gradient halus di bagian bawah untuk fade ke hitam */}
        <div className="absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-t from-black to-transparent z-5 pointer-events-none"></div>
        
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white">
              Fitur Unggulan
            </h2>
            <p className="text-xl text-white max-w-2xl mx-auto">
              Dapatkan semua yang Anda butuhkan untuk mengelola event olahraga bela diri
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="text-center hover:shadow-xl hover:shadow-yellow-500/20 transition-all duration-300 bg-gray-900 border-gray-700">
                <CardHeader>
                  <feature.icon className="h-12 w-12 text-black-400 mx-auto mb-4 drop-shadow-lg" />
                  <CardTitle className="text-xl text-black">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-300">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Packages Section - Background hitam bersih tanpa squares */}
      <section className="relative py-16 bg-black overflow-hidden">
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">Paket Unggulan</h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">Pilih paket yang sesuai dengan kebutuhan event Anda</p>
          </div>
          {packagesLoading ? (
            <LoadingSpinner size="md" text="Memuat paket..." />
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* First Featured Package */}
              {featuredPackages[0] && (
                <div
                  key={`featured-${featuredPackages[0].id}`}
                  className="relative bg-gray-900/90 backdrop-blur-sm rounded-lg border border-gray-800 hover:border-yellow-400/50 hover:shadow-xl hover:shadow-yellow-400/10 transition-all duration-300"
                >
                  <div className="p-6">
                    <div className="mb-4">
                      <img
                        src={featuredPackages[0].images ? uploadService.getOptimizedUrl(featuredPackages[0].images) : '/placeholder-package.jpg'}
                        alt={featuredPackages[0].name}
                        className="w-full h-64 object-cover rounded-lg aspect-[3/4]"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = '/placeholder-package.jpg';
                        }}
                      />
                    </div>
                    <h3 className="text-2xl font-bold text-white text-center mb-2">{featuredPackages[0].name}</h3>
                    {featuredPackages[0].featured_order && (
                      <div className="text-lg text-yellow-400 mt-2 text-center font-semibold">
                        Unggulan #{featuredPackages[0].featured_order}
                      </div>
                    )}
                    <PackageFeatures
                      description={featuredPackages[0].description}
                      className="mt-4"
                      iconColor="text-yellow-400"
                    />
                  </div>

                  <div className="px-6 pb-6">
                    <Link href="/packages">
                      <Button
                        className="w-full"
                        variant="outline"
                        size="lg"
                      >
                        Lihat Detail
                      </Button>
                    </Link>
                  </div>
                </div>
              )}

              {/* Popular Package - Always in the middle */}
              {popularPackage && (
                <div
                  key={`popular-${popularPackage.id}`}
                  className="relative bg-gray-900/90 backdrop-blur-sm rounded-lg border border-gray-800 hover:border-yellow-400/50 hover:shadow-xl hover:shadow-yellow-400/10 transition-all duration-300 ring-2 ring-yellow-400 shadow-lg shadow-yellow-400/20 scale-105"
                >
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-yellow-400 text-black px-4 py-1 rounded-full text-sm font-bold shadow-lg">
                      Paling Populer
                    </span>
                  </div>

                  <div className="p-6">
                    <div className="mb-4">
                      <img
                        src={popularPackage.images ? uploadService.getOptimizedUrl(popularPackage.images) : '/placeholder-package.jpg'}
                        alt={popularPackage.name}
                        className="w-full h-64 object-cover rounded-lg aspect-[3/4]"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = '/placeholder-package.jpg';
                        }}
                      />
                    </div>
                    <h3 className="text-2xl font-bold text-white text-center mb-2">{popularPackage.name}</h3>
                    <div className="text-lg text-yellow-400 mt-2 text-center font-semibold">
                      Paket Premium
                    </div>
                    <PackageFeatures
                      description={popularPackage.description}
                      className="mt-4"
                      iconColor="text-yellow-400"
                    />
                  </div>

                  <div className="px-6 pb-6">
                    <Link href="/packages">
                      <Button
                        className="w-full"
                        variant="primary"
                        size="lg"
                      >
                        Lihat Detail
                      </Button>
                    </Link>
                  </div>
                </div>
              )}

              {/* Second Featured Package */}
              {featuredPackages[1] && (
                <div
                  key={`featured-${featuredPackages[1].id}`}
                  className="relative bg-gray-900/90 backdrop-blur-sm rounded-lg border border-gray-800 hover:border-yellow-400/50 hover:shadow-xl hover:shadow-yellow-400/10 transition-all duration-300"
                >
                  <div className="p-6">
                    <div className="mb-4">
                      <img
                        src={featuredPackages[1].images ? uploadService.getOptimizedUrl(featuredPackages[1].images) : '/placeholder-package.jpg'}
                        alt={featuredPackages[1].name}
                        className="w-full h-64 object-cover rounded-lg aspect-[3/4]"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = '/placeholder-package.jpg';
                        }}
                      />
                    </div>
                    <h3 className="text-2xl font-bold text-white text-center mb-2">{featuredPackages[1].name}</h3>
                    {featuredPackages[1].featured_order && (
                      <div className="text-lg text-yellow-400 mt-2 text-center font-semibold">
                        Unggulan #{featuredPackages[1].featured_order}
                      </div>
                    )}
                    <PackageFeatures
                      description={featuredPackages[1].description}
                      className="mt-4"
                      iconColor="text-yellow-400"
                    />
                  </div>

                  <div className="px-6 pb-6">
                    <Link href="/packages">
                      <Button
                        className="w-full"
                        variant="outline"
                        size="lg"
                      >
                        Lihat Detail
                      </Button>
                    </Link>
                  </div>
                </div>
              )}

              {/* Fallback packages if no popular or featured packages */}
              {!popularPackage && featuredPackages.length === 0 && fallbackPackages.slice(0, 3).map((pkg, index) => (
                <div
                  key={`fallback-${index}`}
                  className={`relative bg-gray-900/90 backdrop-blur-sm rounded-lg border border-gray-800 hover:border-yellow-400/50 hover:shadow-xl hover:shadow-yellow-400/10 transition-all duration-300 ${
                    pkg.is_popular
                      ? 'ring-2 ring-yellow-400 shadow-lg shadow-yellow-400/20 scale-105'
                      : ''
                  }`}
                >
                  {pkg.is_popular && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <span className="bg-yellow-400 text-black px-4 py-1 rounded-full text-sm font-bold shadow-lg">
                        Paling Populer
                      </span>
                    </div>
                  )}

                  <div className="p-6">
                    <div className="mb-4">
                      <img
                        src="/placeholder-package.jpg"
                        alt={pkg.name}
                        className="w-full h-64 object-cover rounded-lg aspect-[3/4]"
                      />
                    </div>
                    <h3 className="text-2xl font-bold text-white text-center mb-2">{pkg.name}</h3>
                    <div className="text-lg text-yellow-400 mt-2 text-center font-semibold">
                      Paket Premium
                    </div>

                    <p className="text-gray-300 text-center mt-4 mb-4">
                      {pkg.description}
                    </p>

                    <div className="space-y-3">
                      <div className="flex items-start">
                        <CheckCircleIcon className="h-4 w-4 text-yellow-400 mr-2 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-300 text-sm">Manajemen Event Lengkap</span>
                      </div>
                      <div className="flex items-start">
                        <CheckCircleIcon className="h-4 w-4 text-yellow-400 mr-2 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-300 text-sm">Registrasi Peserta</span>
                      </div>
                      <div className="flex items-start">
                        <CheckCircleIcon className="h-4 w-4 text-yellow-400 mr-2 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-300 text-sm">Laporan & Analitik</span>
                      </div>
                      <div className="flex items-start">
                        <CheckCircleIcon className="h-4 w-4 text-yellow-400 mr-2 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-300 text-sm">Support 24/7</span>
                      </div>
                    </div>
                  </div>

                 <div className="px-6 pb-6">
                    <Link href="/packages">
                      <Button
                        className="w-full bg-black text-white hover:bg-yellow-400 hover:text-black border border-gray-700 hover:border-yellow-400 transition-all duration-300"
                        variant={pkg.is_popular ? 'primary' : 'outline'}
                        size="lg"
                      >
                        Lihat Detail
                      </Button>
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          )}

          <div className="text-center mt-12">
            <Link href="/packages">
              <Button variant="outline" size="lg">
                Lihat Semua Paket
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative py-16 bg-gradient-to-r from-yellow-600 to-yellow-500 overflow-hidden">
        {/* Background Silk */}
        <div className="absolute inset-0">
          <Silk
            speed={5}
            scale={1}
            color="#202020"
            noiseIntensity={1.5}
            rotation={0}
          />
        </div>
        
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Siap Memulai Event Anda?
          </h2>
          <p className="text-xl text-gray-100 mb-8 max-w-2xl mx-auto">
            Bergabunglah dengan ribuan event organizer yang telah mempercayai platform kami
          </p>
          <Link href="/auth/register">
            <Button 
              size="lg" 
              className="bg-black text-white hover:bg-gray-800 border-2 border-black hover:border-gray-800 hover:shadow-xl hover:shadow-black/30 transition-all duration-300 font-semibold"
            >
              Daftar Sekarang
            </Button>
          </Link>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default HomePage;