<?= $this->extend('layouts/backend/template') ?>
<?= $this->section('content') ?>

<style>
    body {
    background: rgb(99, 39, 120)
}

.form-control:focus {
    box-shadow: none;
    border-color: #BA68C8
}

.profile-button {
    background: rgb(99, 39, 120);
    box-shadow: none;
    border: none
}

.profile-button:hover {
    background: #682773
}

.profile-button:focus {
    background: #682773;
    box-shadow: none
}

.profile-button:active {
    background: #682773;
    box-shadow: none
}

.back:hover {
    color: #682773;
    cursor: pointer
}

.labels {
    font-size: 11px
}

.add-experience:hover {
    background: #BA68C8;
    color: #fff;
    cursor: pointer;
    border: solid 1px #BA68C8
}
</style>

<div class="container rounded bg-white mt-2 mb-2">
    <?php if (session('success')): ?>
            <div class="alert alert-success"><?= session('success') ?></div>
        <?php endif ?>
        
        <?php if (session('error')): ?>
            <div class="alert alert-danger"><?= session('error') ?></div>
        <?php endif ?>
    <div class="row">
        <div class="col-md-3 border-right">
            <div class="d-flex flex-column align-items-center text-center p-3 py-5">
                <img class="rounded-circle mt-5" width="150px" src="https://st3.depositphotos.com/15648834/17930/v/600/depositphotos_179308454-stock-illustration-unknown-person-silhouette-glasses-profile.jpg">
                <span class="font-weight-bold"><p>KETUA KONTINGEN</p></span>
            </div>
        </div>
        <div class="col-md-5 border-right">
            <div class="p-3 py-5">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div class="d-flex justify-content-between align-items-center experience">
                        <span>Data Kontingen</span>
                    </div>
                </div>

                <form action="<?= base_url('kontingen/store') ?>" method="POST" enctype="multipart/form-data">
                    <div class="row mt-2">
                        <div class="col-md-12">
                            <label class="labels">Nama Kontingen</label>
                            <input type="text" name="name" class="form-control" placeholder="Nama Kontingen" value="<?= isset($kontingen['name']) ? $kontingen['name'] : '' ?>" required>
                        </div>
                    </div>
                    <div class="row mt-0">
                        <div class="col-md-12">
                            <label class="labels">Negara</label>
                            <select name="negara" id="negara" class="form-control" required>
                                <option selected disabled>Pilih Negara</option>
                                <?php foreach ($negara as $row): ?>
                                    <option value="<?= $row['id'] ?>" <?= isset($kontingen['negara']) && $kontingen['negara'] == $row['name'] ? 'selected' : '' ?>>
                                        <?= $row['name'] ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-12">
                            <label class="labels">Provinsi</label>
                            <select name="provinsi" id="provinsi" class="form-control" required>
                                <option selected disabled>Pilih Provinsi</option>
                            </select>
                        </div>
                        <div class="col-md-12">
                            <label class="labels">Kabupaten/Kota</label>
                            <select name="kabupaten_kota" id="kabupaten_kota" class="form-control" required>
                                <option selected disabled>Pilih Kabupaten/Kota</option>
                            </select>
                        </div>
                    </div>
                    <div class="mt-3 text-center">
                        <button class="btn btn-primary profile-button" type="submit">Simpan</button>
                    </div>
                </form>
            </div>
        </div>
        <div class="col-md-4">
            <div class="p-3 py-5">
                <div class="d-flex justify-content-between align-items-center experience">
                    <span>Akun Ketua Kontingen</span>
                </div><br>
                <div class="col-md-12">
                    <label class="labels">Nama</label>
                    <input type="text" class="form-control" readonly value="<?= $user['name'] ?>">
                </div><br>
                <div class="col-md-12">
                    <label class="labels">Email</label>
                    <input type="text" class="form-control" readonly value="<?= $user['email'] ?>">
                </div>
                <div class="col-md-12">
                    <label class="labels">No Handphone</label>
                    <input type="text" class="form-control" readonly value="<?= $user['no_hp'] ?>">
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
$(document).ready(function() {
    // Load Provinsi berdasarkan Negara
    $('#negara').on('change', function() {
        const negaraId = $(this).val();
        $('#provinsi').empty().append('<option selected disabled>Pilih Provinsi</option>');
        $('#kabupaten_kota').empty().append('<option selected disabled>Pilih Kabupaten/Kota</option>');

        if (negaraId) {
            $.ajax({
                url: `/kontingen/getProvinsi/${negaraId}`,
                method: 'GET',
                success: function(data) {
                    data.forEach(provinsi => {
                        $('#provinsi').append(`<option value="${provinsi.id}">${provinsi.name}</option>`);
                    });
                },
                error: function() {
                    alert('Gagal memuat data provinsi.');
                }
            });
        }
    });

    // Load Kabupaten/Kota berdasarkan Provinsi
    $('#provinsi').on('change', function() {
        const provinsiId = $(this).val();
        $('#kabupaten_kota').empty().append('<option selected disabled>Pilih Kabupaten/Kota</option>');

        if (provinsiId) {
            $.ajax({
                url: `/kontingen/getKabupatenKota/${provinsiId}`,
                method: 'GET',
                success: function(data) {
                    data.forEach(kabupaten_kota => {
                        $('#kabupaten_kota').append(`<option value="${kabupaten_kota.id}">${kabupaten_kota.name}</option>`);
                    });
                },
                error: function() {
                    alert('Gagal memuat data kabupaten/kota.');
                }
            });
        }
    });

    // Pre-select Provinsi dan Kabupaten/Kota jika ada data kontingen
    <?php if (isset($kontingen['negara'])): ?>
        $('#negara').val(<?= json_encode($kontingen['negara']) ?>).trigger('change');
        setTimeout(() => {
            $('#provinsi').val(<?= json_encode($kontingen['provinsi']) ?>).trigger('change');
            setTimeout(() => {
                $('#kabupaten_kota').val(<?= json_encode($kontingen['kabupaten_kota']) ?>);
            }, 500);
        }, 500);
    <?php endif; ?>
});
</script>


<?= $this->endSection() ?>