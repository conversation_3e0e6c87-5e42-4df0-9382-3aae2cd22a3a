'use client';

import React, { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import {
  CalendarDaysIcon,
  UsersIcon,
  TrophyIcon,
  UserGroupIcon,
} from '@heroicons/react/24/outline';
import { DashboardStats } from '@/types';
import api from '@/lib/api';

const DashboardPage = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState<DashboardStats>({
    count_event: 0,
    count_kontingen: 0,
    count_atlet: 0,
    count_official: 0,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await api.get('/dashboard/stats');
        if (response.data.success) {
          setStats(response.data.data);
        }
      } catch (error) {
        console.error('Error fetching dashboard stats:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  const getWelcomeMessage = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Selamat Pagi';
    if (hour < 15) return 'Selamat Siang';
    if (hour < 18) return 'Selamat Sore';
    return 'Selamat Malam';
  };

  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case 'admin':
        return 'Administrator';
      case 'admin-event':
        return 'Admin Event';
      case 'ketua-kontingen':
        return 'Ketua Kontingen';
      default:
        return role;
    }
  };

  const getStatsCards = () => {
    const baseCards = [
      {
        title: 'Total Event',
        value: stats.count_event,
        icon: CalendarDaysIcon,
        color: 'text-gold-400',
        bgColor: 'bg-gold-500/10',
      },
      {
        title: 'Total Kontingen',
        value: stats.count_kontingen,
        icon: UserGroupIcon,
        color: 'text-gold-400',
        bgColor: 'bg-gold-500/10',
      },
      {
        title: 'Total Atlet',
        value: stats.count_atlet,
        icon: UsersIcon,
        color: 'text-gold-400',
        bgColor: 'bg-gold-500/10',
      },
      {
        title: 'Total Official',
        value: stats.count_official,
        icon: TrophyIcon,
        color: 'text-gold-400',
        bgColor: 'bg-gold-500/10',
      },
    ];

    if (user?.role === 'admin') {
      return baseCards;
    }

    if (user?.role === 'admin-event') {
      return [
        {
          title: 'Event Saya',
          value: stats.count_event,
          icon: CalendarDaysIcon,
          color: 'text-gold-400',
          bgColor: 'bg-gold-500/10',
        },
        {
          title: 'Kontingen Terdaftar',
          value: stats.count_kontingen,
          icon: UserGroupIcon,
          color: 'text-gold-400',
          bgColor: 'bg-gold-500/10',
        },
        {
          title: 'Atlet Terdaftar',
          value: stats.count_atlet,
          icon: UsersIcon,
          color: 'text-gold-400',
          bgColor: 'bg-gold-500/10',
        },
        {
          title: 'Official Terdaftar',
          value: stats.count_official,
          icon: TrophyIcon,
          color: 'text-gold-400',
          bgColor: 'bg-gold-500/10',
        },
      ];
    }

    if (user?.role === 'ketua-kontingen') {
      return [
        {
          title: 'Event Diikuti',
          value: stats.count_event,
          icon: CalendarDaysIcon,
          color: 'text-gold-400',
          bgColor: 'bg-gold-500/10',
        },
        {
          title: 'Kontingen Saya',
          value: stats.count_kontingen,
          icon: UserGroupIcon,
          color: 'text-gold-400',
          bgColor: 'bg-gold-500/10',
        },
        {
          title: 'Atlet Saya',
          value: stats.count_atlet,
          icon: UsersIcon,
          color: 'text-gold-400',
          bgColor: 'bg-gold-500/10',
        },
        {
          title: 'Official Saya',
          value: stats.count_official,
          icon: TrophyIcon,
          color: 'text-gold-400',
          bgColor: 'bg-gold-500/10',
        },
      ];
    }

    return baseCards;
  };

  const statsCards = getStatsCards();

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="bg-gradient-to-r from-gold-600 to-gold-500 rounded-lg p-6 text-black">
          <h1 className="text-2xl font-bold">
            {getWelcomeMessage()}, {user?.name}!
          </h1>
          <p className="text-black/80 mt-2 font-medium">
            Selamat datang di dashboard {getRoleDisplayName(user?.role || '')}
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {statsCards.map((card, index) => (
            <Card key={index} className="hover:shadow-lg hover:shadow-gold-500/20 transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className={`p-3 rounded-lg ${card.bgColor} border border-gold-500/20`}>
                    <card.icon className={`h-6 w-6 ${card.color}`} />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-300">{card.title}</p>
                    <p className="text-2xl font-bold text-white">
                      {loading ? '...' : card.value.toLocaleString()}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Aksi Cepat</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {user?.role === 'admin' && (
                  <>
                    <a
                      href="/dashboard/users/create"
                      className="block p-3 bg-gray-800/50 rounded-lg hover:bg-gold-500/10 border border-gold-500/20 hover:border-gold-500/50 transition-all duration-300"
                    >
                      <div className="font-medium text-white">Tambah User Baru</div>
                      <div className="text-sm text-gray-300">Daftarkan user admin atau ketua kontingen</div>
                    </a>
                    <a
                      href="/dashboard/events/create"
                      className="block p-3 bg-gray-800/50 rounded-lg hover:bg-gold-500/10 border border-gold-500/20 hover:border-gold-500/50 transition-all duration-300"
                    >
                      <div className="font-medium text-white">Buat Event Baru</div>
                      <div className="text-sm text-gray-300">Tambahkan event olahraga baru</div>
                    </a>
                  </>
                )}

                {user?.role === 'admin-event' && (
                  <>
                    <a
                      href="/dashboard/my-events/create"
                      className="block p-3 bg-gray-800/50 rounded-lg hover:bg-gold-500/10 border border-gold-500/20 hover:border-gold-500/50 transition-all duration-300"
                    >
                      <div className="font-medium text-white">Buat Event</div>
                      <div className="text-sm text-gray-300">Tambahkan event baru yang Anda kelola</div>
                    </a>
                    <a
                      href="/dashboard/athletes"
                      className="block p-3 bg-gray-800/50 rounded-lg hover:bg-gold-500/10 border border-gold-500/20 hover:border-gold-500/50 transition-all duration-300"
                    >
                      <div className="font-medium text-white">Kelola Atlet</div>
                      <div className="text-sm text-gray-300">Verifikasi dan kelola data atlet</div>
                    </a>
                  </>
                )}

                {user?.role === 'ketua-kontingen' && (
                  <>
                    <a
                      href="/dashboard/my-athletes/create"
                      className="block p-3 bg-gray-800/50 rounded-lg hover:bg-gold-500/10 border border-gold-500/20 hover:border-gold-500/50 transition-all duration-300"
                    >
                      <div className="font-medium text-white">Daftarkan Atlet</div>
                      <div className="text-sm text-gray-300">Tambahkan atlet baru ke kontingen</div>
                    </a>
                    <a
                      href="/dashboard/event-registration"
                      className="block p-3 bg-gray-800/50 rounded-lg hover:bg-gold-500/10 border border-gold-500/20 hover:border-gold-500/50 transition-all duration-300"
                    >
                      <div className="font-medium text-white">Daftar Event</div>
                      <div className="text-sm text-gray-300">Daftarkan kontingen ke event</div>
                    </a>
                  </>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Aktivitas Terbaru</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-white">Sistem berjalan normal</p>
                    <p className="text-xs text-gray-400">2 menit yang lalu</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-gold-500 rounded-full"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-white">Data berhasil disinkronisasi</p>
                    <p className="text-xs text-gray-400">5 menit yang lalu</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-white">Backup otomatis selesai</p>
                    <p className="text-xs text-gray-400">1 jam yang lalu</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default DashboardPage;
