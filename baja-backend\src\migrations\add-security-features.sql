-- Add security features to users table
ALTER TABLE users 
ADD COLUMN google_id VARCHAR(255) UNIQUE NULL,
ADD COLUMN email_verified BOOLEAN DEFAULT FALSE NOT NULL,
ADD COLUMN two_factor_enabled BOOLEAN DEFAULT FALSE NOT NULL,
ADD COLUMN two_factor_secret VARCHAR(255) NULL;

-- Create OTPs table
CREATE TABLE IF NOT EXISTS otps (
  id INT AUTO_INCREMENT PRIMARY KEY,
  email VARCHAR(255) NOT NULL,
  otp_code VARCHAR(6) NOT NULL,
  otp_type ENUM('registration', 'login', 'password_reset') NOT NULL,
  expires_at DATETIME NOT NULL,
  is_used BOOLEAN DEFAULT FALSE NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_email_type (email, otp_type),
  INDEX idx_expires_at (expires_at)
);

-- Update existing users to have email_verified = true (for existing users)
UPDATE users SET email_verified = TRUE WHERE status = '1';
