<?= $this->extend('layouts/backend/template') ?>
<?= $this->section('content') ?>

<div id="content">
    <div class="container-fluid">

        <!-- Page Heading -->
        <h1 class="h3 mb-2 text-gray-800">Edit Atlet</h1>

        <!-- Form Tambah Users -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Form Edit Atlet</h6>
            </div>
            <div class="card-body">
                <form action="<?= base_url('atlet/update') ?>" method="post" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="nik">NIK</label>
                        <input type="text" class="form-control" id="nik" name="nik" value="<?= $atlet['nik'] ?>" required>
                        <?php if(isset(session('errors')['nik'])): ?>
                            <small class="text-danger"><?= session('errors')['nik'] ?></small>
                        <?php endif; ?>
                    </div>
                    <div class="form-group">
                        <label for="name">Nama Lengkap</label>
                        <input type="text" class="form-control" id="name" name="name" value="<?= $atlet['name'] ?>" required>
                        <?php if(isset(session('errors')['name'])): ?>
                            <small class="text-danger"><?= session('errors')['name'] ?></small>
                        <?php endif; ?>
                    </div>
                    <div class="form-group">
                        <label for="date">Tanggal Lahir</label>
                        <input type="date" class="form-control" id="date" name="tanggal_lahir" value="<?= $atlet['tanggal_lahir'] ?>" required>
                        <?php if(isset(session('errors')['tanggal_lahir'])): ?>
                            <small class="text-danger"><?= session('errors')['tanggal_lahir'] ?></small>
                        <?php endif; ?>
                    </div>
                    <div class="form-group">
                        <label for="no_hp">No HP</label>
                        <input type="text" class="form-control" id="no_hp" name="no_hp" value="<?= $atlet['no_hp'] ?>" required>
                        <?php if(isset(session('errors')['no_hp'])): ?>
                            <small class="text-danger"><?= session('errors')['no_hp'] ?></small>
                        <?php endif; ?>
                    </div>
                    <div class="form-group">
                        <label for="alamat">Alamat</label>
                        <textarea class="form-control" id="alamat" name="alamat" rows="3" value="<?= $atlet['alamat'] ?>" required></textarea>
                        <?php if(isset(session('errors')['alamat'])): ?>
                            <small class="text-danger"><?= session('errors')['alamat'] ?></small>
                        <?php endif; ?>
                    </div>
                    <div class="form-group">
                        <label for="agama">Agama</label>
                        <select class="form-control" id="agama" name="agama" value="<?= $atlet['agama'] ?>" required>
                            <option value="">-- Pilih Agama --</option>
                            <option value="Islam">Islam</option>
                            <option value="Kristen">Kristen</option>
                            <option value="Katolik">Katolik</option>
                            <option value="Hindu">Hindu</option>
                            <option value="Buddha">Buddha</option>
                            <option value="Konghucu">Konghucu</option>
                        </select>
                        <?php if(isset(session('errors')['agama'])): ?>
                            <small class="text-danger"><?= session('errors')['agama'] ?></small>
                        <?php endif; ?>
                    </div>
                    <div class="form-group">
                        <label for="jenis_kelamin">Jenis Kelamin</label>
                        <select class="form-control" id="jenis_kelamin" name="jenis_kelamin" value="<?= $atlet['jenis_kelamin'] ?>" required>
                            <option value="">-- Pilih Jenis Kelamin --</option>
                            <option value="M">Laki-laki</option>
                            <option value="F">Perempuan</option>
                        </select>
                        <?php if(isset(session('errors')['jenis_kelamin'])): ?>
                            <small class="text-danger"><?= session('errors')['jenis_kelamin'] ?></small>
                        <?php endif; ?>
                    </div>
                    <div class="form-group">
                        <label for="umur">Umur</label>
                        <input type="number" class="form-control" id="umur" name="umur" value="<?= $atlet['umur'] ?>" required>
                        <?php if(isset(session('errors')['umur'])): ?>
                            <small class="text-danger"><?= session('errors')['umur'] ?></small>
                        <?php endif; ?>
                    </div>
                    <div class="form-group">
                        <label for="berat_badan">Berat Badan</label>
                        <input type="number" class="form-control" id="berat_badan" name="berat_badan" value="<?= $atlet['berat_badan'] ?>" required>
                        <?php if(isset(session('errors')['berat_badan'])): ?>
                            <small class="text-danger"><?= session('errors')['berat_badan'] ?></small>
                        <?php endif; ?>
                    </div>
                    <div class="form-group">
                        <label for="tinggi_badan">Tinggi Badan</label>
                        <input type="number" class="form-control" id="tinggi_badan" name="tinggi_badan" value="<?= $atlet['tinggi_badan'] ?>" required>
                        <?php if(isset(session('errors')['tinggi_badan'])): ?>
                            <small class="text-danger"><?= session('errors')['tinggi_badan'] ?></small>
                        <?php endif; ?>
                    </div>
                    <div class="form-group">
                        <label for="rapor">Upload Foto Rapor Terakhir (MAX 1MB PDF, JPG, PNG)</label>
                        <input type="file" class="form-control" id="rapor" name="rapor" required>
                        <?php if(isset(session('errors')['rapor'])): ?>
                            <small class="text-danger"><?= session('errors')['rapor'] ?></small>
                        <?php endif; ?>
                    </div>
                    <div class="form-group">
                        <label for="kk_ktp">Upload KK/KTP (MAX 1MB PDF, JPG, PNG)</label>
                        <input type="file" class="form-control" id="kk_ktp" name="kk_ktp" required>
                        <?php if(isset(session('errors')['kk_ktp'])): ?>
                            <small class="text-danger"><?= session('errors')['kk_ktp'] ?></small>
                        <?php endif; ?>
                    </div>
                    <div class="form-group">
                        <label for="surat_kesehatan">Upload Surat Kesehatan (MAX 1MB PDF, JPG, PNG)</label>
                        <input type="file" class="form-control" id="surat_kesehatan" name="surat_kesehatan" required>
                        <?php if(isset(session('errors')['surat_kesehatan'])): ?>
                            <small class="text-danger"><?= session('errors')['surat_kesehatan'] ?></small>
                        <?php endif; ?>
                    </div>
                    <button type="submit" class="btn btn-primary">Perbarui</button>
                    <a href="<?= base_url('atlet') ?>" class="btn btn-secondary">Batal</a>
                </form>
            </div>
        </div>

    </div>
    <!-- /.container-fluid -->

</div>
<!-- End of Main Content -->

<?= $this->endSection(); ?>
