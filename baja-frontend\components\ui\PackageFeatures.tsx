'use client';

import React from 'react';
import { CheckCircleIcon } from '@heroicons/react/24/outline';

interface PackageFeaturesProps {
  description?: string;
  className?: string;
  iconColor?: string;
  textColor?: string;
  iconSize?: string;
}

const PackageFeatures: React.FC<PackageFeaturesProps> = ({
  description,
  className = '',
  iconColor = 'text-yellow-400',
  textColor = 'text-gray-300',
  iconSize = 'h-4 w-4'
}) => {
  if (!description) return null;

  // Split description by newlines and filter out empty lines
  const features = description
    .split('\n')
    .map(line => line.trim())
    .filter(line => line.length > 0);

  if (features.length === 0) return null;

  return (
    <div className={`space-y-2 ${className}`}>
      {features.map((feature, index) => (
        <div key={index} className="flex items-start">
          <CheckCircleIcon className={`${iconSize} ${iconColor} mr-2 mt-0.5 flex-shrink-0`} />
          <span className={`${textColor} text-sm leading-relaxed`}>{feature}</span>
        </div>
      ))}
    </div>
  );
};

export default PackageFeatures;
