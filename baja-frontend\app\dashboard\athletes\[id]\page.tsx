'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useParams } from 'next/navigation';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import Badge from '@/components/ui/Badge';
import { api } from '@/lib/api';
import { downloadPdfFromCloudinary, createPdfFilename } from '@/lib/pdf.utils';
import Link from 'next/link';
import {
  ArrowLeftIcon,
  UserIcon,
  CalendarDaysIcon,
  PhoneIcon,
  MapPinIcon,
  IdentificationIcon,
  DocumentTextIcon,
  ArrowDownTrayIcon,
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon,
} from '@heroicons/react/24/outline';

interface AtletFile {
  id: number;
  file: string;
  file_type: 'rapor' | 'kk_ktp' | 'surat_kesehatan';
  created_at: string;
  updated_at: string;
}

interface Athlete {
  id: number;
  nik: string;
  name: string;
  no_hp?: string;
  tanggal_lahir: string;
  jenis_kelamin: 'M' | 'F';
  agama?: string;
  alamat?: string;
  umur: number;
  berat_badan: string;
  tinggi_badan: string;
  status_verifikasi: 'pending' | 'verified';
  foto?: string;
  id_user: number;
  id_kontingen: number;
  created_at: string;
  updated_at: string;
  atletKontingen?: {
    id: number;
    name: string;
  };
  files?: AtletFile[];
}

const AthleteDetailPage = () => {
  const { user } = useAuth();
  const params = useParams();
  const athleteId = params.id as string;
  
  const [athlete, setAthlete] = useState<Athlete | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchAthlete = async () => {
    try {
      setLoading(true);
      const response = await api.get(`/atlet/${athleteId}`);
      
      if (response.data.success) {
        setAthlete(response.data.data);
      }
    } catch (error: any) {
      console.error('Error fetching athlete:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (athleteId) {
      fetchAthlete();
    }
  }, [athleteId]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'verified':
        return (
          <Badge variant="success" className="flex items-center">
            <CheckCircleIcon className="h-4 w-4 mr-1" />
            Terverifikasi
          </Badge>
        );
      case 'pending':
        return (
          <Badge variant="warning" className="flex items-center">
            <ClockIcon className="h-4 w-4 mr-1" />
            Menunggu
          </Badge>
        );
      default:
        return (
          <Badge variant="danger" className="flex items-center">
            <XCircleIcon className="h-4 w-4 mr-1" />
            Ditolak
          </Badge>
        );
    }
  };

  const getFileTypeLabel = (fileType: string) => {
    switch (fileType) {
      case 'rapor':
        return 'Rapor';
      case 'kk_ktp':
        return 'KK/KTP';
      case 'surat_kesehatan':
        return 'Surat Kesehatan';
      default:
        return fileType;
    }
  };

  const handleDownloadFile = async (file: AtletFile) => {
    const filename = createPdfFilename(
      `${athlete?.name}_${getFileTypeLabel(file.file_type)}`,
      'document'
    );
    await downloadPdfFromCloudinary(file.file, filename);
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center h-64">
          <LoadingSpinner size="lg" />
        </div>
      </DashboardLayout>
    );
  }

  if (!athlete) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold text-foreground">Atlet Tidak Ditemukan</h1>
          <p className="text-muted-foreground mt-2">Atlet yang Anda cari tidak ada.</p>
          <Link href="/dashboard/athletes" className="mt-4 inline-block">
            <Button>Kembali ke Daftar Atlet</Button>
          </Link>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/dashboard/athletes">
              <Button variant="outline" size="sm">
                <ArrowLeftIcon className="h-4 w-4 mr-2" />
                Kembali ke Daftar Atlet
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-foreground">{athlete.name}</h1>
              <p className="text-muted-foreground mt-1">Detail Atlet</p>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            {getStatusBadge(athlete.status_verifikasi)}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Athlete Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <Card className="p-6">
              <h2 className="text-xl font-semibold text-foreground mb-4">Informasi Pribadi</h2>
              
              {athlete.foto && (
                <div className="mb-6 flex justify-center">
                  <img
                    src={athlete.foto}
                    alt={athlete.name}
                    className="w-32 h-32 object-cover rounded-full border-4 border-gold-500"
                  />
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-start space-x-3">
                  <IdentificationIcon className="h-5 w-5 text-gold-400 mt-1" />
                  <div>
                    <p className="text-foreground font-medium">NIK</p>
                    <p className="text-muted-foreground">{athlete.nik}</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <UserIcon className="h-5 w-5 text-gold-400 mt-1" />
                  <div>
                    <p className="text-foreground font-medium">Jenis Kelamin</p>
                    <p className="text-muted-foreground">{athlete.jenis_kelamin === 'M' ? 'Laki-laki' : 'Perempuan'}</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <CalendarDaysIcon className="h-5 w-5 text-gold-400 mt-1" />
                  <div>
                    <p className="text-foreground font-medium">Tanggal Lahir</p>
                    <p className="text-muted-foreground">{formatDate(athlete.tanggal_lahir)}</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <UserIcon className="h-5 w-5 text-gold-400 mt-1" />
                  <div>
                    <p className="text-foreground font-medium">Usia</p>
                    <p className="text-muted-foreground">{athlete.umur} tahun</p>
                  </div>
                </div>

                {athlete.no_hp && (
                  <div className="flex items-start space-x-3">
                    <PhoneIcon className="h-5 w-5 text-gold-400 mt-1" />
                    <div>
                      <p className="text-foreground font-medium">Nomor HP</p>
                      <p className="text-muted-foreground">{athlete.no_hp}</p>
                    </div>
                  </div>
                )}

                {athlete.agama && (
                  <div className="flex items-start space-x-3">
                    <UserIcon className="h-5 w-5 text-gold-400 mt-1" />
                    <div>
                      <p className="text-foreground font-medium">Agama</p>
                      <p className="text-muted-foreground">{athlete.agama}</p>
                    </div>
                  </div>
                )}

                <div className="flex items-start space-x-3">
                  <UserIcon className="h-5 w-5 text-gold-400 mt-1" />
                  <div>
                    <p className="text-foreground font-medium">Berat Badan</p>
                    <p className="text-muted-foreground">{athlete.berat_badan} kg</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <UserIcon className="h-5 w-5 text-gold-400 mt-1" />
                  <div>
                    <p className="text-foreground font-medium">Tinggi Badan</p>
                    <p className="text-muted-foreground">{athlete.tinggi_badan} cm</p>
                  </div>
                </div>

                {athlete.alamat && (
                  <div className="flex items-start space-x-3 md:col-span-2">
                    <MapPinIcon className="h-5 w-5 text-gold-400 mt-1" />
                    <div>
                      <p className="text-foreground font-medium">Alamat</p>
                      <p className="text-muted-foreground">{athlete.alamat}</p>
                    </div>
                  </div>
                )}

                {athlete.atletKontingen && (
                  <div className="flex items-start space-x-3 md:col-span-2">
                    <UserIcon className="h-5 w-5 text-gold-400 mt-1" />
                    <div>
                      <p className="text-foreground font-medium">Kontingen</p>
                      <p className="text-muted-foreground">{athlete.atletKontingen.name}</p>
                    </div>
                  </div>
                )}
              </div>
            </Card>
          </div>

          {/* Documents */}
          <div className="space-y-6">
            <Card className="p-6">
              <h2 className="text-xl font-semibold text-foreground mb-4">Dokumen</h2>

              {athlete.files && athlete.files.length > 0 ? (
                <div className="space-y-3">
                  {athlete.files.map((file) => (
                    <div key={file.id} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                      <div className="flex items-center space-x-3">
                        <DocumentTextIcon className="h-6 w-6 text-red-400" />
                        <div>
                          <p className="text-foreground font-medium">{getFileTypeLabel(file.file_type)}</p>
                          <p className="text-muted-foreground text-sm">
                            Diunggah: {new Date(file.created_at).toLocaleDateString('id-ID')}
                          </p>
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDownloadFile(file)}
                        className="border-green-500/30 text-green-400 hover:bg-green-500/10"
                      >
                        <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                        Unduh
                      </Button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <DocumentTextIcon className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
                  <p className="text-muted-foreground">Belum ada dokumen yang diunggah</p>
                </div>
              )}
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default AthleteDetailPage;
