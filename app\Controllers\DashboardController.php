<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\Event;
use App\Models\Kontingen;
use App\Models\Atlet;
use App\Models\Official;
use App\Models\PendaftaranEvent;

class DashboardController extends BaseController
{
    public function index()
    {
        $userId = session()->get('id');
        $role   = session()->get('role'); // Pastikan session role sudah di-set

        $data = [];

        if ($role === 'admin') {
            // Untuk Admin: hitung semua data
            $eventModel    = new Event();
            $kontingenModel = new Kontingen();
            $atletModel    = new Atlet();
            $officialModel = new Official();

            $data['count_event']     = $eventModel->countAll();
            $data['count_kontingen'] = $kontingenModel->countAll();
            $data['count_atlet']     = $atletModel->countAll();
            $data['count_official']  = $officialModel->countAll();
        } elseif ($role === 'admin-event') {
            // Untuk Admin-Event: 
            // 1. Event: hanya event yang dibuat oleh admin-event
            $eventModel = new Event();
            $data['count_event'] = $eventModel->where('id_user', $userId)->countAllResults();

            // 2. Dapatkan daftar event yang dibuat oleh admin-event
            $events = $eventModel->where('id_user', $userId)->findAll();
            $eventIds = array_column($events, 'id');

            // 3. Kontingen: kontingen yang mengikuti event-event tersebut
            $pendaftaranEventModel = new PendaftaranEvent();
            if (!empty($eventIds)) {
                $data['count_kontingen'] = $pendaftaranEventModel
                    ->distinct()
                    ->select('id_kontingen')
                    ->whereIn('id_event', $eventIds)
                    ->countAllResults();
            } else {
                $data['count_kontingen'] = 0;
            }

            // 4. Untuk menghitung atlet dan official, kita asumsikan kedua data tersebut berasal dari kontingen yang mengikuti event tersebut.
            $kontingenIds = [];
            if (!empty($eventIds)) {
                $kontingenRecords = $pendaftaranEventModel
                    ->distinct()
                    ->select('id_kontingen')
                    ->whereIn('id_event', $eventIds)
                    ->findAll();
                $kontingenIds = array_column($kontingenRecords, 'id_kontingen');
            }
            $atletModel    = new Atlet();
            $officialModel = new Official();
            $data['count_atlet']    = !empty($kontingenIds) ? $atletModel->whereIn('id_kontingen', $kontingenIds)->countAllResults() : 0;
            $data['count_official'] = !empty($kontingenIds) ? $officialModel->whereIn('id_kontingen', $kontingenIds)->countAllResults() : 0;
        } elseif ($role === 'ketua-kontingen') {
            // Untuk Ketua Kontingen:
            // 1. Ambil data kontingen berdasarkan id_user
            $kontingenModel = new Kontingen();
            $kontingen = $kontingenModel->where('id_user', $userId)->first();

            if ($kontingen) {
                // Ketua kontingen hanya memiliki 1 kontingen
                $data['count_kontingen'] = 1;

                // 2. Event yang diikuti oleh kontingen (misalnya via pendaftaran event)
                $pendaftaranEventModel = new \App\Models\PendaftaranEvent();
                $data['count_event'] = $pendaftaranEventModel->where('id_kontingen', $kontingen['id'])->countAllResults();

                // 3. Data atlet milik kontingen tersebut
                $atletModel = new Atlet();
                $data['count_atlet'] = $atletModel->where('id_kontingen', $kontingen['id'])->countAllResults();

                // 4. Data official milik kontingen tersebut
                $officialModel = new Official();
                $data['count_official'] = $officialModel->where('id_kontingen', $kontingen['id'])->countAllResults();
            } else {
                $data['count_kontingen'] = 0;
                $data['count_event']     = 0;
                $data['count_atlet']     = 0;
                $data['count_official']  = 0;
            }
        } else {
            // Default jika role tidak dikenali
            $data['count_event']     = 0;
            $data['count_kontingen'] = 0;
            $data['count_atlet']     = 0;
            $data['count_official']  = 0;
        }

        return view('dashboard/index', $data);
    }
}
