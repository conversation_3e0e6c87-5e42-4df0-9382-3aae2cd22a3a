/**
 * PDF utility functions for handling Cloudinary PDF files
 */

/**
 * Downloads a PDF file from Cloudinary with proper filename
 * @param url - The Cloudinary PDF URL
 * @param filename - The desired filename for download
 */
export const downloadPdfFromCloudinary = async (url: string, filename: string): Promise<void> => {
  try {
    // For Cloudinary raw files, we need to fetch the file and create a blob
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error('Failed to fetch PDF file');
    }
    
    const blob = await response.blob();
    const blobUrl = window.URL.createObjectURL(blob);
    
    // Create a temporary link to download the file
    const link = document.createElement('a');
    link.href = blobUrl;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Clean up the blob URL
    window.URL.revokeObjectURL(blobUrl);
  } catch (error) {
    console.error('Error downloading PDF:', error);
    // Fallback: open in new tab
    window.open(url, '_blank');
  }
};

/**
 * Gets a proper PDF viewer URL for iframe display
 * @param url - The Cloudinary PDF URL
 * @returns The URL suitable for iframe display
 */
export const getPdfViewerUrl = (url: string): string => {
  // For Cloudinary raw files (PDFs), we can use the direct URL
  // Modern browsers can handle PDF display in iframes
  if (url && url.includes('cloudinary.com')) {
    return url;
  }
  return url;
};

/**
 * Checks if a URL is a valid Cloudinary PDF URL
 * @param url - The URL to check
 * @returns True if it's a valid Cloudinary PDF URL
 */
export const isCloudinaryPdfUrl = (url: string): boolean => {
  return url && url.includes('cloudinary.com') && url.includes('/raw/');
};

/**
 * Sanitizes a filename for safe download
 * @param filename - The original filename
 * @returns A sanitized filename safe for download
 */
export const sanitizeFilename = (filename: string): string => {
  return filename.replace(/[^a-zA-Z0-9._-]/g, '_');
};

/**
 * Creates a PDF download filename from event name and type
 * @param eventName - The event name
 * @param type - The document type (proposal, winners, etc.)
 * @returns A sanitized filename
 */
export const createPdfFilename = (eventName: string, type: string): string => {
  const sanitizedName = sanitizeFilename(eventName);
  const sanitizedType = sanitizeFilename(type);
  return `${sanitizedName}_${sanitizedType}.pdf`;
};

/**
 * Handles PDF preview with error handling
 * @param url - The PDF URL
 * @param onSuccess - Callback when preview is successful
 * @param onError - Callback when preview fails
 */
export const handlePdfPreview = (
  url: string,
  onSuccess?: () => void,
  onError?: (error: Error) => void
): void => {
  try {
    if (!url) {
      throw new Error('No PDF URL provided');
    }
    
    if (!isCloudinaryPdfUrl(url)) {
      console.warn('URL may not be a valid Cloudinary PDF URL:', url);
    }
    
    onSuccess?.();
  } catch (error) {
    console.error('PDF preview error:', error);
    onError?.(error as Error);
  }
};

/**
 * Opens PDF in a new tab as fallback
 * @param url - The PDF URL
 */
export const openPdfInNewTab = (url: string): void => {
  if (url) {
    window.open(url, '_blank');
  }
};
