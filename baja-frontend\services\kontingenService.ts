import { api } from '@/lib/api';

export interface Kontingen {
  id: number;
  name: string;
  negara: string;
  provinsi: string;
  kabupaten_kota: string;
  negaraName?: string;
  provinsiName?: string;
  kabupatenKotaName?: string;
  id_user: number;
  atletCount?: number;
  officialCount?: number;
  kontingenUser?: {
    id: number;
    name: string;
    email: string;
    no_hp: string;
  };
  created_at: string;
  updated_at: string;
}

export interface CreateKontingenData {
  name: string;
  negara: string;
  provinsi: string;
  kabupaten_kota: string;
  id_user?: number;
}

export interface UpdateKontingenData {
  name?: string;
  negara?: string;
  provinsi?: string;
  kabupaten_kota?: string;
}

class KontingenService {
  async getAllKontingen(page = 1, limit = 10, search = ''): Promise<{
    kontingen: Kontingen[];
    pagination: {
      total: number;
      page: number;
      limit: number;
      totalPages: number;
    };
  }> {
    try {
      const response = await api.get('/kontingen', {
        params: { page, limit, search }
      });
      return response.data.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch kontingen');
    }
  }

  async getKontingenById(id: number): Promise<Kontingen> {
    try {
      const response = await api.get(`/kontingen/${id}`);
      return response.data.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch kontingen');
    }
  }

  async getMyKontingen(): Promise<Kontingen> {
    try {
      const response = await api.get('/kontingen/my');
      return response.data.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch your kontingen');
    }
  }

  async createKontingen(data: CreateKontingenData): Promise<Kontingen> {
    try {
      const response = await api.post('/kontingen', data);
      return response.data.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to create kontingen');
    }
  }

  async updateKontingen(id: number, data: UpdateKontingenData): Promise<Kontingen> {
    try {
      const response = await api.put(`/kontingen/${id}`, data);
      return response.data.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to update kontingen');
    }
  }

  async deleteKontingen(id: number): Promise<void> {
    try {
      await api.delete(`/kontingen/${id}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to delete kontingen');
    }
  }
}

export const kontingenService = new KontingenService();
