import { OAuth2Client } from 'google-auth-library';

export class GoogleAuthService {
  private client: OAuth2Client;

  constructor() {
    this.client = new OAuth2Client(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      process.env.GOOGLE_REDIRECT_URI
    );
  }

  // Generate Google OAuth URL
  generateAuthUrl(): string {
    const scopes = [
      'https://www.googleapis.com/auth/userinfo.email',
      'https://www.googleapis.com/auth/userinfo.profile'
    ];

    return this.client.generateAuthUrl({
      access_type: 'offline',
      scope: scopes,
      include_granted_scopes: true,
    });
  }

  // Verify Google ID token
  async verifyIdToken(idToken: string): Promise<any> {
    try {
      console.log('🔐 Verifying Google ID token...');

      if (!idToken) {
        throw new Error('No ID token provided');
      }

      if (!process.env.GOOGLE_CLIENT_ID) {
        throw new Error('Google Client ID not configured');
      }

      const ticket = await this.client.verifyIdToken({
        idToken,
        audience: process.env.GOOGLE_CLIENT_ID,
      });

      const payload = ticket.getPayload();

      if (!payload) {
        throw new Error('Invalid token payload');
      }

      if (!payload.email) {
        throw new Error('Email not provided by Google');
      }

      console.log('✅ Google token verified successfully for:', payload.email);

      return {
        googleId: payload.sub,
        email: payload.email,
        name: payload.name || payload.email,
        picture: payload.picture,
        emailVerified: payload.email_verified || false
      };
    } catch (error: any) {
      console.error('❌ Error verifying Google ID token:', error);

      if (error.message.includes('Token used too early')) {
        throw new Error('Token belum valid. Silakan coba lagi dalam beberapa detik.');
      } else if (error.message.includes('Token used too late')) {
        throw new Error('Token sudah kadaluarsa. Silakan login ulang.');
      } else if (error.message.includes('Invalid token signature')) {
        throw new Error('Token tidak valid. Silakan login ulang.');
      } else if (error.message.includes('audience')) {
        throw new Error('Konfigurasi Google OAuth tidak valid.');
      }

      throw new Error('Token Google tidak valid');
    }
  }

  // Get user info from authorization code
  async getUserInfo(code: string): Promise<any> {
    try {
      const { tokens } = await this.client.getToken(code);
      this.client.setCredentials(tokens);

      const userInfoResponse = await fetch(
        `https://www.googleapis.com/oauth2/v2/userinfo?access_token=${tokens.access_token}`
      );

      if (!userInfoResponse.ok) {
        throw new Error('Failed to fetch user info');
      }

      const userInfo: any = await userInfoResponse.json();

      return {
        googleId: userInfo.id,
        email: userInfo.email,
        name: userInfo.name,
        picture: userInfo.picture,
        emailVerified: userInfo.verified_email
      };
    } catch (error) {
      console.error('Error getting user info from Google:', error);
      throw new Error('Failed to get user information from Google');
    }
  }

  // Revoke Google token
  async revokeToken(token: string): Promise<void> {
    try {
      await this.client.revokeToken(token);
    } catch (error) {
      console.error('Error revoking Google token:', error);
      throw new Error('Failed to revoke Google token');
    }
  }
}

export const googleAuthService = new GoogleAuthService();
