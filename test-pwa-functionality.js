const axios = require('axios');

async function testPWAFunctionality() {
  try {
    console.log('🧪 Testing PWA Functionality...\n');

    // Test 1: Check if PWA files are generated
    console.log('1. 📄 Checking PWA files...');
    
    try {
      const manifestResponse = await axios.get('http://localhost:3001/manifest.json');
      console.log('✅ Manifest file accessible');
      console.log(`📱 App name: ${manifestResponse.data.name}`);
      console.log(`🎨 Theme color: ${manifestResponse.data.theme_color}`);
      console.log(`📱 Display mode: ${manifestResponse.data.display}`);
    } catch (error) {
      console.log('❌ Manifest file not accessible');
    }

    try {
      const swResponse = await axios.get('http://localhost:3001/sw.js');
      console.log('✅ Service Worker file accessible');
    } catch (error) {
      console.log('❌ Service Worker file not accessible');
    }

    // Test 2: Check PWA icons
    console.log('\n2. 🖼️ Checking PWA icons...');
    const iconSizes = ['72x72', '96x96', '128x128', '144x144', '152x152', '192x192', '384x384', '512x512'];
    
    for (const size of iconSizes) {
      try {
        await axios.head(`http://localhost:3001/icons/icon-${size}.png`);
        console.log(`✅ Icon ${size} available`);
      } catch (error) {
        console.log(`❌ Icon ${size} not available`);
      }
    }

    // Test 3: Test API endpoints for caching
    console.log('\n3. 🔐 Testing API endpoints...');
    
    try {
      // Login as ketua-kontingen
      const loginResponse = await axios.post('http://localhost:5000/api/v1/auth/login', {
        email: '<EMAIL>',
        password: 'password123'
      });
      
      const token = loginResponse.data.data.token;
      const userId = loginResponse.data.data.user.id;
      console.log('✅ Login successful');

      // Test athletes endpoint
      try {
        const athletesResponse = await axios.get(`http://localhost:5000/api/v1/atlet?user_id=${userId}`, {
          headers: { Authorization: `Bearer ${token}` }
        });
        console.log(`✅ Athletes API accessible (${athletesResponse.data.data.atlet.length} athletes)`);
      } catch (error) {
        console.log('❌ Athletes API not accessible');
      }

      // Test events endpoint
      try {
        const eventsResponse = await axios.get('http://localhost:5000/api/v1/events', {
          headers: { Authorization: `Bearer ${token}` }
        });
        console.log(`✅ Events API accessible (${eventsResponse.data.data.events.length} events)`);
      } catch (error) {
        console.log('❌ Events API not accessible');
      }

      // Test dashboard stats
      try {
        const statsResponse = await axios.get('http://localhost:5000/api/v1/dashboard/stats', {
          headers: { Authorization: `Bearer ${token}` }
        });
        console.log('✅ Dashboard stats API accessible');
      } catch (error) {
        console.log('❌ Dashboard stats API not accessible');
      }

    } catch (error) {
      console.log('❌ Authentication failed');
    }

    // Test 4: Check PWA Settings page
    console.log('\n4. ⚙️ Testing PWA Settings page...');
    try {
      const pwaSettingsResponse = await axios.get('http://localhost:3001/dashboard/pwa-settings');
      console.log('✅ PWA Settings page accessible');
    } catch (error) {
      console.log('❌ PWA Settings page not accessible');
    }

    console.log('\n🎉 PWA Testing completed!');
    console.log('\n📋 PWA Features Summary:');
    console.log('✅ Service Worker: Auto-generated by next-pwa');
    console.log('✅ Manifest: Configured with app details and icons');
    console.log('✅ Offline Caching: API responses and static assets');
    console.log('✅ Install Prompt: Available for supported browsers');
    console.log('✅ Offline Indicator: Shows connection status');
    console.log('✅ Background Sync: Queues requests when offline');
    console.log('✅ PWA Settings: Management interface for offline data');

    console.log('\n🚀 How to test PWA:');
    console.log('1. Open http://localhost:3001 in Chrome/Edge');
    console.log('2. Open DevTools > Application > Service Workers');
    console.log('3. Check "Offline" to simulate offline mode');
    console.log('4. Navigate the app - cached data should load');
    console.log('5. Look for install prompt in address bar');
    console.log('6. Visit PWA Settings page for offline management');

    console.log('\n📱 Installation:');
    console.log('- Desktop: Look for install icon in address bar');
    console.log('- Mobile: Use browser menu "Add to Home Screen"');
    console.log('- PWA will work offline with cached data');

  } catch (error) {
    console.error('❌ PWA test failed:', error.message);
  }
}

// Test PWA manifest validation
async function validatePWAManifest() {
  try {
    console.log('\n🔍 Validating PWA Manifest...');
    
    const manifestResponse = await axios.get('http://localhost:3001/manifest.json');
    const manifest = manifestResponse.data;
    
    // Check required fields
    const requiredFields = ['name', 'short_name', 'start_url', 'display', 'theme_color', 'background_color', 'icons'];
    const missingFields = requiredFields.filter(field => !manifest[field]);
    
    if (missingFields.length === 0) {
      console.log('✅ All required manifest fields present');
    } else {
      console.log(`❌ Missing manifest fields: ${missingFields.join(', ')}`);
    }

    // Check icons
    if (manifest.icons && manifest.icons.length > 0) {
      console.log(`✅ ${manifest.icons.length} icons configured`);
      
      // Check for required icon sizes
      const requiredSizes = ['192x192', '512x512'];
      const availableSizes = manifest.icons.map(icon => icon.sizes);
      const missingIconSizes = requiredSizes.filter(size => !availableSizes.includes(size));
      
      if (missingIconSizes.length === 0) {
        console.log('✅ Required icon sizes (192x192, 512x512) present');
      } else {
        console.log(`⚠️ Missing recommended icon sizes: ${missingIconSizes.join(', ')}`);
      }
    } else {
      console.log('❌ No icons configured in manifest');
    }

    // Check display mode
    if (['standalone', 'fullscreen', 'minimal-ui'].includes(manifest.display)) {
      console.log(`✅ Display mode "${manifest.display}" is PWA-compatible`);
    } else {
      console.log(`⚠️ Display mode "${manifest.display}" may not provide native app experience`);
    }

    console.log('✅ PWA Manifest validation completed');

  } catch (error) {
    console.error('❌ Manifest validation failed:', error.message);
  }
}

// Run all tests
async function runAllPWATests() {
  await testPWAFunctionality();
  await validatePWAManifest();
  
  console.log('\n🎯 Next Steps:');
  console.log('1. Test offline functionality in browser DevTools');
  console.log('2. Try installing the PWA on desktop/mobile');
  console.log('3. Test background sync by going offline and making changes');
  console.log('4. Check PWA Settings page for cache management');
  console.log('5. Verify install prompt appears on supported devices');
}

runAllPWATests();
