import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../config/database';
import { Event as EventInterface } from '../types';
import User from './User';

interface EventCreationAttributes extends Optional<EventInterface, 'id' | 'created_at' | 'updated_at'> {}

class Event extends Model<EventInterface, EventCreationAttributes> implements EventInterface {
  public id!: number;
  public name!: string;
  public description?: string;
  public start_date!: Date;
  public end_date!: Date;
  public lokasi!: string;
  public biaya_registrasi!: number;
  public metode_pembayaran!: string;
  public status!: 'active' | 'completed';
  public event_image?: string;
  public event_proposal?: string;
  public event_pemenang?: string;
  public id_user!: number;
  public readonly created_at!: Date;
  public readonly updated_at!: Date;
}

Event.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    start_date: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    end_date: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    lokasi: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    biaya_registrasi: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0,
    },
    metode_pembayaran: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'transfer',
    },
    status: {
      type: DataTypes.ENUM('active', 'completed'),
      allowNull: false,
      defaultValue: 'active',
    },
    event_image: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    event_proposal: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    event_pemenang: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    id_user: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: User,
        key: 'id',
      },
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    tableName: 'event',
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  }
);

// Associations
Event.belongsTo(User, { foreignKey: 'id_user', as: 'user' });
User.hasMany(Event, { foreignKey: 'id_user', as: 'events' });

export default Event;
