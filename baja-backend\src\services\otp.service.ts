import crypto from 'crypto';
import speakeasy from 'speakeasy';
import qrcode from 'qrcode';
import OTP from '../models/OTP';
import { emailService } from './email.service';

export class OTPService {
  // Generate 6-digit OTP
  static generateOTP(): string {
    return crypto.randomInt(100000, 999999).toString();
  }

  // Create and save OTP to database
  static async createOTP(
    email: string, 
    type: 'registration' | 'login' | 'password_reset',
    expiryMinutes: number = 10
  ): Promise<string> {
    // Delete any existing unused OTPs for this email and type
    await OTP.destroy({
      where: {
        email,
        otp_type: type,
        is_used: false
      }
    });

    const otpCode = this.generateOTP();
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + expiryMinutes);

    await OTP.create({
      email,
      otp_code: otpCode,
      otp_type: type,
      expires_at: expiresAt,
      is_used: false
    });

    return otpCode;
  }

  // Verify OTP
  static async verifyOTP(
    email: string,
    otpCode: string,
    type: 'registration' | 'login' | 'password_reset'
  ): Promise<boolean> {
    console.log(`🔍 Looking for OTP: email=${email}, code=${otpCode}, type=${type}`);

    const otpRecord = await OTP.findOne({
      where: {
        email,
        otp_code: otpCode,
        otp_type: type,
        is_used: false
      }
    });

    console.log(`📋 OTP record found: ${!!otpRecord}`);

    if (!otpRecord) {
      // Check if there are any OTPs for this email
      const allOTPs = await OTP.findAll({
        where: { email, otp_type: type },
        order: [['created_at', 'DESC']],
        limit: 5
      });
      console.log(`📋 All OTPs for ${email}:`, allOTPs.map(otp => ({
        code: otp.otp_code,
        expires_at: otp.expires_at,
        is_used: otp.is_used,
        created_at: otp.created_at
      })));
      return false;
    }

    // Check if OTP has expired
    const now = new Date();
    const expiresAt = new Date(otpRecord.expires_at);
    console.log(`⏰ Current time: ${now.toISOString()}`);
    console.log(`⏰ OTP expires at: ${expiresAt.toISOString()}`);
    console.log(`⏰ Is expired: ${now > expiresAt}`);

    if (now > expiresAt) {
      console.log(`❌ OTP expired`);
      return false;
    }

    // Mark OTP as used
    await otpRecord.update({ is_used: true });
    console.log(`✅ OTP verified and marked as used`);
    return true;
  }

  // Send OTP via email
  static async sendOTPEmail(
    email: string,
    otpCode: string,
    type: 'registration' | 'login' | 'password_reset'
  ): Promise<void> {
    console.log('📧 Attempting to send OTP email...');
    console.log(`📧 To: ${email}`);
    console.log(`🔑 OTP: ${otpCode}`);
    console.log(`📝 Type: ${type}`);

    // For development/testing - log OTP to console if email not configured
    if (!process.env.EMAIL_USER ||
        !process.env.EMAIL_PASS ||
        process.env.EMAIL_PASS === 'rengatkota2' ||
        process.env.EMAIL_PASS === 'your-gmail-app-password') {
      console.log('🔐 OTP for testing (email not configured):');
      console.log(`📧 Email: ${email}`);
      console.log(`🔑 OTP Code: ${otpCode}`);
      console.log(`📝 Type: ${type}`);
      console.log('⚠️  Setup Gmail App Password for actual email delivery');
      return;
    }

    let subject: string;
    let message: string;

    switch (type) {
      case 'registration':
        subject = 'Kode Verifikasi Pendaftaran BAJA';
        message = `
          <h2>Verifikasi Pendaftaran Akun BAJA</h2>
          <p>Terima kasih telah mendaftar di BAJA Event Organizer.</p>
          <p>Gunakan kode verifikasi berikut untuk menyelesaikan pendaftaran Anda:</p>
          <div style="background-color: #f4f4f4; padding: 20px; text-align: center; font-size: 24px; font-weight: bold; letter-spacing: 5px; margin: 20px 0;">
            ${otpCode}
          </div>
          <p>Kode ini akan kedaluwarsa dalam 10 menit.</p>
          <p>Jika Anda tidak melakukan pendaftaran ini, abaikan email ini.</p>
        `;
        break;
      case 'login':
        subject = 'Kode Verifikasi Login BAJA';
        message = `
          <h2>Verifikasi Login BAJA</h2>
          <p>Seseorang mencoba masuk ke akun BAJA Anda.</p>
          <p>Gunakan kode verifikasi berikut untuk melanjutkan login:</p>
          <div style="background-color: #f4f4f4; padding: 20px; text-align: center; font-size: 24px; font-weight: bold; letter-spacing: 5px; margin: 20px 0;">
            ${otpCode}
          </div>
          <p>Kode ini akan kedaluwarsa dalam 10 menit.</p>
          <p>Jika Anda tidak mencoba login, segera ubah password Anda.</p>
        `;
        break;
      case 'password_reset':
        subject = 'Kode Reset Password BAJA';
        message = `
          <h2>Reset Password BAJA</h2>
          <p>Anda telah meminta untuk mereset password akun BAJA Anda.</p>
          <p>Gunakan kode verifikasi berikut untuk melanjutkan reset password:</p>
          <div style="background-color: #f4f4f4; padding: 20px; text-align: center; font-size: 24px; font-weight: bold; letter-spacing: 5px; margin: 20px 0;">
            ${otpCode}
          </div>
          <p>Kode ini akan kedaluwarsa dalam 10 menit.</p>
          <p>Jika Anda tidak meminta reset password, abaikan email ini.</p>
        `;
        break;
    }

    try {
      await emailService.sendEmail(email, subject, message);
      console.log('✅ OTP email sent successfully');
    } catch (error: any) {
      console.error('❌ Failed to send OTP email:', error.message);
      console.log('🔐 OTP for testing (email failed):');
      console.log(`📧 Email: ${email}`);
      console.log(`🔑 OTP Code: ${otpCode}`);
      console.log(`📝 Type: ${type}`);
      // Don't throw error, just log it so registration can continue
    }
  }

  // Generate 2FA secret for user
  static generate2FASecret(userEmail: string): { secret: string; qrCodeUrl: string } {
    const secret = speakeasy.generateSecret({
      name: `BAJA (${userEmail})`,
      issuer: 'BAJA Event Organizer',
      length: 32
    });

    return {
      secret: secret.base32,
      qrCodeUrl: secret.otpauth_url || ''
    };
  }

  // Generate QR code for 2FA setup
  static async generate2FAQRCode(otpauthUrl: string): Promise<string> {
    try {
      const qrCodeDataUrl = await qrcode.toDataURL(otpauthUrl);
      return qrCodeDataUrl;
    } catch (error) {
      throw new Error('Failed to generate QR code');
    }
  }

  // Verify 2FA token
  static verify2FAToken(secret: string, token: string): boolean {
    return speakeasy.totp.verify({
      secret,
      encoding: 'base32',
      token,
      window: 2 // Allow 2 time steps before and after current time
    });
  }

  // Clean up expired OTPs (should be run periodically)
  static async cleanupExpiredOTPs(): Promise<void> {
    await OTP.destroy({
      where: {
        expires_at: {
          [require('sequelize').Op.lt]: new Date()
        }
      }
    });
  }
}

export default OTPService;
