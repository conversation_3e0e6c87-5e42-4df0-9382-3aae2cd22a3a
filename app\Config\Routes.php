<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */

//Auth
$routes->get('/', 'HomeController::index');
$routes->get('/login', 'AuthController::index');
$routes->post('/login', 'AuthController::login');
$routes->post('/logout', 'AuthController::logout');
$routes->get('/register', 'AuthController::register');
$routes->post('/create', 'AuthController::create');

$routes->group('', ['filter' => 'auth'], static function (RouteCollection $routes) {
    $routes->get('/dashboard', 'DashboardController::index');
    $routes->get('/profile', 'ProfileController::index');
    $routes->post('/profile/update', 'ProfileController::update');
});

//Admin
$routes->group('', ['filter' => 'auth'], static function (RouteCollection $routes){
    
    $routes->get('/users', 'Admin\UserController::index');
    $routes->get('/users/create', 'Admin\UserController::create');
    $routes->post('/users/store', 'Admin\UserController::store');
    $routes->get('/users/edit/(:segment)', 'Admin\UserController::edit/$1');
    $routes->post('/users/update/(:segment)', 'Admin\UserController::update/$1');
    $routes->get('/users/edit-password/(:segment)', 'Admin\UserController::editPassword/$1');
    $routes->post('/users/update-password/(:segment)', 'Admin\UserController::updatePassword/$1');
    $routes->get('/users/delete/(:segment)', 'Admin\UserController::delete/$1');

    $routes->get('/gallery', 'Admin\GalleryController::index');
    $routes->get('/gallery/create', 'Admin\GalleryController::create');
    $routes->post('/gallery/store', 'Admin\GalleryController::store');
    $routes->get('/gallery/edit/(:segment)', 'Admin\GalleryController::edit/$1');
    $routes->post('/gallery/update/(:segment)', 'Admin\GalleryController::update/$1');
    $routes->get('/gallery/delete/(:segment)', 'Admin\GalleryController::delete/$1');

    $routes->get('/paket', 'Admin\PaketController::index');
    $routes->get('/paket/create', 'Admin\PaketController::create');
    $routes->post('/paket/store', 'Admin\PaketController::store');
    $routes->get('/paket/edit/(:segment)', 'Admin\PaketController::edit/$1');
    $routes->post('/paket/update/(:segment)', 'Admin\PaketController::update/$1');
    $routes->get('/paket/delete/(:segment)', 'Admin\PaketController::delete/$1');

    $routes->get('/event-master', 'Admin\EventMasterController::index');
    $routes->get('/event-master/delete/(:segment)', 'Admin\EventMasterController::delete/$1');
});

//Admin Event
$routes->group('', ['filter' => 'auth'], static function (RouteCollection $routes){
    
    $routes->get('/informasi-event', 'AdminEvent\InformasiEventController::index');

    $routes->get('/event', 'AdminEvent\EventController::index');
    $routes->get('/event/create', 'AdminEvent\EventController::create');
    $routes->post('/event/store', 'AdminEvent\EventController::store');
    $routes->get('/event/edit/(:segment)', 'AdminEvent\EventController::edit/$1');
    $routes->post('/event/update/(:segment)', 'AdminEvent\EventController::update/$1');
    $routes->post('/event/upload/(:segment)', 'AdminEvent\EventController::upload/$1');

    $routes->get('/master', 'AdminEvent\MasterController::index');
    $routes->get('/master/export', 'AdminEvent\MasterController::export');
    $routes->get('/master/konfirmasi/(:segment)', 'AdminEvent\MasterController::konfirmasi/$1');

    $routes->get('/atlet-kontingen', 'AdminEvent\AtletController::index');
    $routes->post('/atlet-kontingen/verify/(:segment)', 'AdminEvent\AtletController::verify/$1');
});

//Ketua Kontingen
$routes->group('', ['filter' => 'auth'], static function (RouteCollection $routes){

    $routes->get('/kontingen', 'KetuaKontingen\KontingenController::index');
    $routes->get('/kontingen/getProvinsi/(:segment)', 'KetuaKontingen\KontingenController::getProvinsi/$1');
    $routes->get('/kontingen/getKabupatenKota/(:segment)', 'KetuaKontingen\KontingenController::getKabupatenKota/$1');
    $routes->post('/kontingen/store', 'KetuaKontingen\KontingenController::store');

    $routes->get('/atlet', 'KetuaKontingen\AtletController::index');
    $routes->get('/atlet/create', 'KetuaKontingen\AtletController::create');
    $routes->post('/atlet/store', 'KetuaKontingen\AtletController::store');
    $routes->get('/atlet/edit/(:segment)', 'KetuaKontingen\AtletController::edit/$1');
    $routes->get('/atlet/delete/(:segment)', 'KetuaKontingen\AtletController::delete/$1');

    $routes->get('/official', 'KetuaKontingen\OfficialController::index');
    $routes->get('/official/create', 'KetuaKontingen\OfficialController::create');
    $routes->post('/official/store', 'KetuaKontingen\OfficialController::store');
    $routes->get('/official/edit/(:segment)', 'KetuaKontingen\OfficialController::edit/$1');
    $routes->post('/official/delete/(:segment)', 'KetuaKontingen\OfficialController::delete/$1');

    $routes->get('/pendaftaran-event', 'KetuaKontingen\PendaftaranEventController::index');
    $routes->post('/pendaftaran-event/store', 'KetuaKontingen\PendaftaranEventController::store');

    $routes->get('/pendaftaran-atlet', 'KetuaKontingen\PendaftaranAtletController::index');
    $routes->get('/pendaftaran-atlet/detail/(:segment)', 'KetuaKontingen\PendaftaranAtletController::detail/$1');
    $routes->get('/pendaftaran-atlet/detail/(:segment)/create', 'KetuaKontingen\PendaftaranAtletController::create/$1');
    $routes->post('/pendaftaran-atlet/store', 'KetuaKontingen\PendaftaranAtletController::store');
    $routes->post('pendaftaran-atlet/uploadBukti/(:segment)', 'KetuaKontingen\PendaftaranAtletController::uploadBukti/$1');
    $routes->get('/pendaftaran-atlet/delete/(:segment)', 'KetuaKontingen\PendaftaranAtletController::delete/$1');
});



